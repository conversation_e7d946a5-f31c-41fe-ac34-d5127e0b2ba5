# 实时视频播放与音频模块技术实现文档

## 📋 文档概述

本文档详细描述了IoT Android应用中实时视频播放和音频交互模块的技术实现流程，重点说明华为云OBS集成的关键技术细节，为开发者提供完整的验证指南。

## 🎯 系统架构概览

### **核心模块架构**
```
IoT Android App
├── 实时视频播放模块
│   ├── RealTimeVideoManager (核心管理器)
│   ├── HuaweiObsApiClient (OBS API客户端)
│   ├── VideoPlayerComponent (播放组件)
│   └── RealTimeVideoVerifier (验证系统)
└── 音频交互模块
    ├── ParentVoiceInteractionManager (语音交互管理器)
    ├── VoiceRecordingManager (录音管理器)
    ├── TokenIoTManager (IoT命令发送)
    └── VoiceModuleTestHelper (测试验证)
```

## 🎥 实时视频播放模块

### **1. 核心组件：RealTimeVideoManager**

#### **文件位置**
```
app/src/main/java/com/example/iotandroidv20/obs/RealTimeVideoManager.kt
```

#### **主要功能**
- **一键实时查看** - 家长即时查看孩子当前状况
- **智能流获取** - 优先实时流，备选最新录制
- **质量监控** - 自动监控连接质量并优化
- **故障恢复** - 自动重连和流刷新

#### **关键技术实现**

##### **A. 实时流获取流程**
```kotlin
suspend fun startInstantLiveView(deviceId: String): Boolean {
    // 1. 状态管理
    _liveStreamStatus.value = LiveStreamStatus.CONNECTING
    
    // 2. 获取实时流URL
    val liveStreamUrl = getCurrentLiveStreamUrl(deviceId)
    
    // 3. 启动质量监控
    startStreamQualityMonitoring(deviceId)
    
    return liveStreamUrl != null
}
```

##### **B. 华为云OBS集成关键点**
```kotlin
private suspend fun getLatestRecordingUrl(deviceId: String): String? {
    // 1. 构建OBS查询前缀
    val today = SimpleDateFormat("yyyyMMdd").format(Date())
    val prefix = "${HuaweiCloudConfig.OBS_VIDEO_PATH}$deviceId/$today/"
    
    // 2. 调用OBS API列举对象
    val result = obsApiClient.listObjects(prefix, 100)
    
    // 3. 筛选最新视频文件
    val latestObject = objects
        .filter { it.key.endsWith(".mp4") || it.key.endsWith(".m3u8") }
        .maxByOrNull { it.lastModified }
    
    // 4. 构建访问URL
    return "${HuaweiCloudConfig.OBS_ENDPOINT}/${HuaweiCloudConfig.OBS_BUCKET_NAME}/${latestObject.key}"
}
```

### **2. OBS API客户端：HuaweiObsApiClient**

#### **文件位置**
```
app/src/main/java/com/example/iotandroidv20/obs/HuaweiObsApiClient.kt
```

#### **OBS签名算法实现**

##### **A. 虚拟主机域名格式**
```kotlin
// 使用虚拟主机域名格式（华为云OBS要求）
val virtualHostEndpoint = "https://$BUCKET_NAME.obs.$REGION.myhuaweicloud.com"
val url = "$virtualHostEndpoint/$objectKey"

// Host头设置
.header("Host", "$BUCKET_NAME.obs.$REGION.myhuaweicloud.com")
```

##### **B. 签名计算（基于官方文档）**
```kotlin
private fun generateAuthorization(
    method: String,
    objectKey: String,
    contentType: String,
    date: String,
    contentMD5: String
): String {
    // 1. 构建CanonicalizedResource（包含bucket名称）
    val canonicalizedResource = "/$BUCKET_NAME/$objectKey"
    
    // 2. 构建StringToSign
    val stringToSign = "$method\n$contentMD5\n$contentType\n$date\n$canonicalizedResource"
    
    // 3. HMAC-SHA1签名
    val mac = Mac.getInstance("HmacSHA1")
    val secretKeySpec = SecretKeySpec(SECRET_KEY.toByteArray(), "HmacSHA1")
    mac.init(secretKeySpec)
    val signature = mac.doFinal(stringToSign.toByteArray())
    
    // 4. Base64编码
    val signatureBase64 = android.util.Base64.encodeToString(signature, android.util.Base64.NO_WRAP)
    
    return "OBS $ACCESS_KEY:$signatureBase64"
}
```

### **3. 视频播放组件：VideoPlayerComponent**

#### **文件位置**
```
app/src/main/java/com/example/iotandroidv20/ui/components/VideoPlayerComponent.kt
```

#### **关键特性**
- **全屏支持** - 横竖屏自适应
- **手势控制** - 缩放、拖拽、旋转
- **播放控制** - 播放/暂停/停止
- **状态管理** - 实时状态反馈

## 🎙️ 音频交互模块

### **1. 核心组件：ParentVoiceInteractionManager**

#### **文件位置**
```
app/src/main/java/com/example/iotandroidv20/obs/ParentVoiceInteractionManager.kt
```

#### **完整语音交互流程**

##### **A. 录音阶段**
```kotlin
suspend fun startRecordingVoiceMessage(context: Context): Boolean {
    // 1. 权限检查
    if (!checkRecordingPermissions(context)) return false
    
    // 2. 创建录音文件
    val voiceFile = createVoiceFile()
    
    // 3. 配置MediaRecorder
    mediaRecorder = MediaRecorder().apply {
        setAudioSource(MediaRecorder.AudioSource.MIC)
        setOutputFormat(MediaRecorder.OutputFormat.THREE_GPP)
        setAudioEncoder(MediaRecorder.AudioEncoder.AMR_NB)
        setOutputFile(voiceFile.absolutePath)
        setMaxDuration(MAX_RECORDING_DURATION)
        prepare()
        start()
    }
    
    // 4. 状态更新
    _isRecording.value = true
    _interactionStatus.value = InteractionStatus.RECORDING
    
    return true
}
```

##### **B. OBS上传阶段（关键实现）**
```kotlin
private suspend fun uploadVoiceToOBS(voiceFile: File): UploadResult {
    // 1. 生成OBS对象键
    val timestamp = System.currentTimeMillis()
    val fileExtension = voiceFile.extension
    val objectKey = "${HuaweiCloudConfig.OBS_VOICE_PATH}parent/${timestamp}_voice_message.$fileExtension"
    
    // 2. 计算文件MD5
    val md5Hash = calculateMD5(voiceFile)
    
    // 3. 确定Content-Type
    val contentType = when (fileExtension.lowercase()) {
        "3gp" -> "audio/3gpp"
        "aac" -> "audio/aac"
        "mp4" -> "audio/mp4"
        else -> "audio/mpeg"
    }
    
    // 4. 调用OBS API上传
    val result = obsApiClient.putObject(objectKey, voiceFile, contentType)
    
    // 5. 构建访问URL
    val obsUrl = "https://${HuaweiCloudConfig.OBS_BUCKET_NAME}.obs.${HuaweiCloudConfig.OBS_REGION}.myhuaweicloud.com/$objectKey"
    
    return when (result) {
        is ObsApiResult.Success -> UploadResult(true, obsUrl, null)
        is ObsApiResult.Error -> UploadResult(false, null, result.message)
    }
}
```

##### **C. IoT命令发送阶段**
```kotlin
private suspend fun sendVoiceNotificationToDevice(context: Context, obsUrl: String): Boolean {
    // 1. 构造设备命令参数
    val commandParams = mapOf(
        "voice_url" to obsUrl,
        "play_mode" to "immediate",
        "volume" to 80,
        "priority" to "high",
        "timestamp" to System.currentTimeMillis()
    )
    
    // 2. 通过TokenIoTManager发送命令
    val iotManager = TokenIoTManager(context)
    val success = iotManager.sendCommandToDevice(
        commandName = HuaweiCloudConfig.COMMAND_PLAY_PARENT_VOICE,
        parameters = commandParams
    )
    
    // 3. 发送TTS备用命令
    if (success) {
        sendTTSBackupCommand(context, "家长发来了语音消息，请注意查收")
    }
    
    return success
}
```

### **2. IoT命令管理：TokenIoTManager**

#### **文件位置**
```
app/src/main/java/com/example/iotandroidv20/iot/TokenIoTManager.kt
```

#### **Token认证与命令发送**
```kotlin
suspend fun sendCommandToDevice(commandName: String, parameters: Map<String, Any>): Boolean {
    // 1. 获取有效Token
    val token = tokenManager.getValidToken()
    if (token == null) return false
    
    // 2. 构建API URL
    val url = "${HuaweiCloudConfig.IOT_API_ENDPOINT}${HuaweiCloudConfig.DEVICE_COMMANDS_PATH}"
        .replace("{project_id}", HuaweiCloudConfig.PROJECT_ID)
        .replace("{device_id}", HuaweiCloudConfig.DEVICE_ID)
    
    // 3. 构建命令请求体
    val commandBody = JSONObject().apply {
        put("command_name", commandName)
        put("paras", JSONObject(parameters))
        put("expire_time", 3600)
        put("send_strategy", "immediately")
    }
    
    // 4. 发送HTTP请求
    val request = Request.Builder()
        .url(url)
        .post(commandBody.toString().toRequestBody("application/json".toMediaType()))
        .header("X-Auth-Token", token)
        .header("Content-Type", "application/json")
        .build()
    
    // 5. 处理响应
    val response = httpClient.newCall(request).execute()
    return response.isSuccessful
}
```

## 🔧 关键技术细节

### **1. 华为云OBS集成要点**

#### **A. 虚拟主机域名要求**
- **URL格式**: `https://bucket-name.obs.region.myhuaweicloud.com/object-key`
- **Host头**: `bucket-name.obs.region.myhuaweicloud.com`
- **签名路径**: `/bucket-name/object-key`（仍需包含bucket名称）

#### **B. 签名算法关键点**
```kotlin
// StringToSign格式（严格按照官方文档）
StringToSign = HTTP-Verb + "\n" +
               Content-MD5 + "\n" +
               Content-Type + "\n" +
               Date + "\n" +
               CanonicalizedResource
```

#### **C. 文件上传最佳实践**
- **MD5校验** - 确保文件完整性
- **Content-Type设置** - 根据文件扩展名正确设置
- **错误处理** - 完善的重试和错误恢复机制

### **2. 状态管理与协程**

#### **A. StateFlow状态管理**
```kotlin
// 实时状态更新
private val _isRecording = MutableStateFlow(false)
val isRecording: StateFlow<Boolean> = _isRecording.asStateFlow()

private val _interactionStatus = MutableStateFlow(InteractionStatus.IDLE)
val interactionStatus: StateFlow<InteractionStatus> = _interactionStatus.asStateFlow()
```

#### **B. 协程异步处理**
```kotlin
// 非阻塞的录音和上传
suspend fun startRecording(context: Context): Boolean = withContext(Dispatchers.IO) {
    // 录音逻辑在IO线程执行
}

// 主线程状态更新
viewModelScope.launch {
    val success = parentVoiceManager.startRecording(context)
    _errorMessage.value = if (success) "开始录音..." else "录音失败"
}
```

## 📊 验证与测试

### **1. 实时视频验证系统**

#### **验证组件**
```
app/src/main/java/com/example/iotandroidv20/obs/RealTimeVideoVerifier.kt
```

#### **5步验证流程**
1. **初始状态检查** - 验证管理器初始化
2. **OBS连接测试** - 验证OBS API连通性
3. **流获取验证** - 测试视频流获取
4. **播放功能测试** - 验证播放器功能
5. **完整流程验证** - 端到端测试

### **2. 音频模块测试系统**

#### **测试组件**
```
app/src/main/java/com/example/iotandroidv20/voice/VoiceModuleTestHelper.kt
```

#### **7项测试内容**
1. **初始状态检查** - 验证管理器状态
2. **录音启动测试** - 测试录音功能
3. **录音状态监控** - 验证状态更新
4. **录音停止测试** - 测试停止和上传
5. **状态描述验证** - 检查状态描述
6. **文件系统测试** - 验证文件操作
7. **权限检查** - 确认权限状态

## 🎯 开发者验证指南

### **1. 功能验证步骤**

#### **实时视频模块验证**
```kotlin
// 在MainActivity中调用
val verifier = RealTimeVideoVerifier()
viewModel.testRealTimeVideoModule() // 执行完整验证

// 预期结果
📊 验证结果概览:
  ✅ 初始状态: 管理器初始化正常
  ✅ OBS连接: OBS API连接成功
  ✅ 流获取: 视频流获取正常
  ✅ 播放功能: 播放器功能正常
  ✅ 完整流程: 端到端流程正常

📈 通过率: 5/5 (100%)
```

#### **音频模块验证**
```kotlin
// 在MainActivity中调用
viewModel.testVoiceModule() // 执行语音模块测试

// 预期结果
📊 测试结果概览:
  ✅ 初始状态: 初始状态正常
  ✅ 录音启动: 录音启动成功
  ✅ 录音状态: 录音状态正常
  ✅ 录音停止: 录音停止成功
  ✅ 状态描述: 状态描述正常
  ✅ 文件系统: 文件系统正常
  ✅ 权限检查: 权限检查通过

📈 通过率: 7/7 (100%)
```

### **2. 关键验证点**

#### **OBS上传验证**
- **签名正确性** - StringToSign格式符合官方规范
- **文件完整性** - MD5校验通过
- **访问权限** - 返回200状态码
- **URL有效性** - 生成的OBS URL可访问

#### **IoT命令验证**
- **Token有效性** - Token认证成功
- **命令格式** - JSON格式正确
- **设备响应** - 设备接收命令（如果在线）
- **错误处理** - 设备离线时正确处理

### **3. 性能指标**

#### **实时视频性能**
- **连接时间** - < 3秒建立连接
- **流切换** - < 1秒完成流切换
- **质量监控** - 3秒间隔检查
- **内存使用** - 合理的内存占用

#### **音频交互性能**
- **录音延迟** - < 500ms启动录音
- **上传速度** - 根据网络条件优化
- **命令发送** - < 2秒完成命令发送
- **状态更新** - 实时状态反馈

## 🚀 部署与优化建议

### **1. 生产环境配置**
- **OBS配置** - 使用生产环境的AK/SK和Bucket
- **IoT设备** - 配置真实的设备ID和认证信息
- **网络优化** - 配置CDN加速和负载均衡

### **2. 性能优化**
- **缓存策略** - 实现Token和流URL缓存
- **重试机制** - 网络失败时的智能重试
- **资源管理** - 及时释放录音和播放资源

### **3. 监控与日志**
- **详细日志** - 完整的操作日志记录
- **性能监控** - 关键指标的实时监控
- **错误追踪** - 异常情况的详细追踪

## 📞 技术支持

如需技术支持或有疑问，请参考：
- **华为云OBS官方文档** - 最新的API规范
- **华为云IoT平台文档** - 设备接入指南
- **项目源码注释** - 详细的代码注释说明

## 🔍 OBS实现深度解析

### **1. OBS API完整实现**

#### **A. 核心API方法**
```kotlin
class HuaweiObsApiClient {
    // 上传对象 - 语音文件上传
    suspend fun putObject(objectKey: String, file: File, contentType: String): ObsApiResult<String>

    // 列举对象 - 获取视频文件列表
    suspend fun listObjects(prefix: String, maxKeys: Int): ObsApiResult<List<ObsObject>>

    // 获取对象元数据 - 检查文件状态
    suspend fun headObject(objectKey: String): ObsApiResult<ObsObjectMetadata>

    // 删除对象 - 清理临时文件
    suspend fun deleteObject(objectKey: String): ObsApiResult<Unit>
}
```

#### **B. 签名算法详细实现**
```kotlin
private fun generateAuthorization(
    method: String,
    objectKey: String,
    contentType: String,
    date: String,
    contentMD5: String
): String {
    // 1. 构建CanonicalizedResource（华为云OBS特殊要求）
    // 即使使用虚拟主机域名，签名中仍需包含bucket名称
    val canonicalizedResource = "/$BUCKET_NAME/$objectKey"

    // 2. 构建StringToSign（严格按照官方文档格式）
    val stringToSign = buildString {
        append(method).append("\n")           // HTTP方法
        append(contentMD5).append("\n")       // Content-MD5
        append(contentType).append("\n")      // Content-Type
        append(date).append("\n")             // Date
        append(canonicalizedResource)         // CanonicalizedResource
    }

    // 3. HMAC-SHA1签名计算
    val mac = Mac.getInstance("HmacSHA1")
    val secretKeySpec = SecretKeySpec(SECRET_KEY.toByteArray(Charsets.UTF_8), "HmacSHA1")
    mac.init(secretKeySpec)
    val signature = mac.doFinal(stringToSign.toByteArray(Charsets.UTF_8))

    // 4. Base64编码（无换行符）
    val signatureBase64 = android.util.Base64.encodeToString(signature, android.util.Base64.NO_WRAP)

    // 5. 构建Authorization头
    return "OBS $ACCESS_KEY:$signatureBase64"
}
```

#### **C. HTTP请求构建**
```kotlin
private suspend fun putObject(objectKey: String, file: File, contentType: String): ObsApiResult<String> {
    // 1. 计算文件MD5
    val md5Hash = calculateMD5(file)

    // 2. 生成GMT时间
    val date = getGMTDate()

    // 3. 生成签名
    val authorization = generateAuthorization("PUT", objectKey, contentType, date, md5Hash)

    // 4. 构建虚拟主机域名URL
    val virtualHostEndpoint = "https://$BUCKET_NAME.obs.$REGION.myhuaweicloud.com"
    val url = "$virtualHostEndpoint/$objectKey"

    // 5. 创建请求体
    val requestBody = file.asRequestBody(contentType.toMediaType())

    // 6. 构建HTTP请求
    val request = Request.Builder()
        .url(url)
        .put(requestBody)
        .header("Host", "$BUCKET_NAME.obs.$REGION.myhuaweicloud.com")  // 虚拟主机Host头
        .header("Date", date)
        .header("Authorization", authorization)
        .header("Content-Type", contentType)
        .header("Content-MD5", md5Hash)
        .header("Content-Length", file.length().toString())
        .build()

    // 7. 执行请求
    return executeRequest(request)
}
```

### **2. 错误处理与重试机制**

#### **A. OBS错误码处理**
```kotlin
private fun handleObsError(response: Response): String {
    return when (response.code) {
        403 -> when {
            response.body?.string()?.contains("SignatureDoesNotMatch") == true ->
                "签名不匹配，请检查AK/SK配置"
            response.body?.string()?.contains("VirtualHostDomainRequired") == true ->
                "需要使用虚拟主机域名访问"
            else -> "访问被拒绝，请检查权限配置"
        }
        404 -> "存储桶或对象不存在"
        409 -> "存储桶已存在或状态冲突"
        500 -> "服务器内部错误，请稍后重试"
        else -> "未知错误: ${response.code}"
    }
}
```

#### **B. 智能重试策略**
```kotlin
private suspend fun <T> executeWithRetry(
    maxRetries: Int = 3,
    delayMs: Long = 1000,
    operation: suspend () -> ObsApiResult<T>
): ObsApiResult<T> {
    repeat(maxRetries) { attempt ->
        val result = operation()

        when (result) {
            is ObsApiResult.Success -> return result
            is ObsApiResult.Error -> {
                if (attempt == maxRetries - 1) return result

                // 可重试的错误码
                if (isRetryableError(result.code)) {
                    delay(delayMs * (attempt + 1)) // 指数退避
                    continue
                } else {
                    return result // 不可重试的错误直接返回
                }
            }
        }
    }

    return ObsApiResult.Error(-1, "重试次数已用完")
}

private fun isRetryableError(code: Int): Boolean {
    return code in listOf(500, 502, 503, 504, 408) // 服务器错误和超时
}
```

### **3. 文件管理与优化**

#### **A. 文件路径规范**
```kotlin
object HuaweiCloudConfig {
    // OBS路径配置
    const val OBS_VIDEO_PATH = "video/"           // 视频文件路径
    const val OBS_VOICE_PATH = "voice/"           // 语音文件路径
    const val OBS_IMAGE_PATH = "image/"           // 图片文件路径

    // 文件命名规范
    fun generateVideoPath(deviceId: String, date: String): String {
        return "${OBS_VIDEO_PATH}$deviceId/$date/"
    }

    fun generateVoicePath(type: String): String {
        return "${OBS_VOICE_PATH}$type/"  // parent/ 或 child/
    }
}
```

#### **B. 文件压缩与优化**
```kotlin
private fun optimizeAudioFile(inputFile: File): File {
    // 音频文件压缩优化
    val outputFile = File(inputFile.parent, "optimized_${inputFile.name}")

    // 使用MediaMetadataRetriever检查文件信息
    val retriever = MediaMetadataRetriever()
    retriever.setDataSource(inputFile.absolutePath)

    val duration = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)?.toLongOrNull()
    val bitrate = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_BITRATE)?.toIntOrNull()

    // 根据文件大小决定是否需要压缩
    if (inputFile.length() > MAX_VOICE_FILE_SIZE) {
        // 执行压缩逻辑
        compressAudioFile(inputFile, outputFile)
        return outputFile
    }

    return inputFile
}
```

### **4. 性能监控与诊断**

#### **A. 上传性能监控**
```kotlin
class UploadPerformanceMonitor {
    private val uploadMetrics = mutableMapOf<String, UploadMetric>()

    fun startMonitoring(objectKey: String): String {
        val monitorId = UUID.randomUUID().toString()
        uploadMetrics[monitorId] = UploadMetric(
            objectKey = objectKey,
            startTime = System.currentTimeMillis(),
            status = UploadStatus.STARTED
        )
        return monitorId
    }

    fun recordProgress(monitorId: String, bytesUploaded: Long, totalBytes: Long) {
        uploadMetrics[monitorId]?.let { metric ->
            metric.bytesUploaded = bytesUploaded
            metric.totalBytes = totalBytes
            metric.progress = (bytesUploaded.toFloat() / totalBytes * 100).toInt()
        }
    }

    fun finishMonitoring(monitorId: String, success: Boolean) {
        uploadMetrics[monitorId]?.let { metric ->
            metric.endTime = System.currentTimeMillis()
            metric.status = if (success) UploadStatus.SUCCESS else UploadStatus.FAILED
            metric.duration = metric.endTime - metric.startTime

            // 记录性能指标
            Logger.d("上传性能: ${metric.objectKey}, 耗时: ${metric.duration}ms, 速度: ${metric.getUploadSpeed()}KB/s")
        }
    }
}
```

#### **B. 网络质量检测**
```kotlin
private suspend fun checkNetworkQuality(): NetworkQuality {
    return try {
        val startTime = System.currentTimeMillis()

        // 发送小文件测试网络速度
        val testData = ByteArray(1024) { 0 } // 1KB测试数据
        val testFile = File.createTempFile("network_test", ".tmp")
        testFile.writeBytes(testData)

        val result = obsApiClient.putObject("test/network_test_${System.currentTimeMillis()}.tmp", testFile, "application/octet-stream")

        val duration = System.currentTimeMillis() - startTime
        testFile.delete()

        when {
            result is ObsApiResult.Success && duration < 1000 -> NetworkQuality.EXCELLENT
            result is ObsApiResult.Success && duration < 3000 -> NetworkQuality.GOOD
            result is ObsApiResult.Success && duration < 5000 -> NetworkQuality.FAIR
            else -> NetworkQuality.POOR
        }
    } catch (e: Exception) {
        NetworkQuality.POOR
    }
}
```

## 🧪 高级测试与验证

### **1. 压力测试**
```kotlin
class ObsStressTest {
    suspend fun performStressTest(concurrentUploads: Int = 5, fileCount: Int = 20): TestResult {
        val testFiles = generateTestFiles(fileCount)
        val results = mutableListOf<UploadResult>()

        // 并发上传测试
        val jobs = (1..concurrentUploads).map { batchIndex ->
            async {
                testFiles.chunked(fileCount / concurrentUploads)[batchIndex - 1].map { file ->
                    async {
                        obsApiClient.putObject("stress_test/${file.name}", file, "application/octet-stream")
                    }
                }.awaitAll()
            }
        }

        val allResults = jobs.awaitAll().flatten()

        return TestResult(
            totalFiles = fileCount,
            successCount = allResults.count { it is ObsApiResult.Success },
            failureCount = allResults.count { it is ObsApiResult.Error },
            averageUploadTime = calculateAverageUploadTime(allResults)
        )
    }
}
```

### **2. 端到端集成测试**
```kotlin
class EndToEndTest {
    suspend fun testCompleteVoiceFlow(): TestResult {
        val steps = listOf(
            "权限检查" to { checkPermissions() },
            "录音启动" to { startRecording() },
            "录音停止" to { stopRecording() },
            "文件验证" to { verifyRecordedFile() },
            "OBS上传" to { uploadToObs() },
            "URL验证" to { verifyObsUrl() },
            "IoT命令" to { sendIotCommand() },
            "清理资源" to { cleanup() }
        )

        val results = mutableMapOf<String, Boolean>()

        for ((stepName, stepAction) in steps) {
            try {
                val success = stepAction()
                results[stepName] = success

                if (!success) {
                    Logger.e("步骤失败: $stepName")
                    break
                }
            } catch (e: Exception) {
                Logger.e("步骤异常: $stepName, ${e.message}")
                results[stepName] = false
                break
            }
        }

        return TestResult(
            steps = results,
            overallSuccess = results.values.all { it },
            failedStep = results.entries.firstOrNull { !it.value }?.key
        )
    }
}
```

---

**文档版本**: v1.0
**最后更新**: 2025-07-06
**适用版本**: IoT Android V2.0
