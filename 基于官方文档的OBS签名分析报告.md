# 基于官方文档的OBS签名分析报告

## 🎯 官方文档关键信息

### **华为云OBS签名机制概述**
根据官方文档 `obs_04_0009.html`，华为云OBS签名机制的目的是：
1. **身份验证** - 防止未授权用户访问
2. **数据完整性** - 防止传输数据被篡改
3. **防重放攻击** - 设置签名有效期

### **URL中携带签名的规范**
根据官方文档 `obs_04_0011.html`，URL签名的构建规则：

#### **StringToSign构造规则**
```
StringToSign = 
     HTTP-Verb + "\n" +   
     Content-MD5 + "\n" +   
     Content-Type + "\n" +   
     Expires + "\n" +   
     CanonicalizedHeaders + "\n" +    
     CanonicalizedResource
```

#### **CanonicalizedResource构建规则**
```
CanonicalizedResource = "/" + 桶名 + "/" + 对象名 + "?" + 子资源
```

**重要说明**：
- 如果桶未绑定自定义域名，则直接使用原始桶名
- 如果桶绑定了自定义域名，则桶名由自定义域名表示

## 🔍 官方Java示例分析

### **关键代码片段**
从官方文档的Java示例中：
```java
// 构造StringToSign，拼接CanonicalizedResource
stringToSign.append("/");
if (this.isValid(bucketName)) {
    stringToSign.append(bucketName).append("/");
    if (this.isValid(objectName)) {
        stringToSign.append(this.encodeObjectName(objectName));
    }
}
```

### **签名计算流程**
```java
Signature = URL-Encode( Base64( HMAC-SHA1( UTF-8-Encoding-Of( Your_SK ), UTF-8-Encoding-Of( StringToSign ) ) ) )
```

## 🔧 我们的实现对比分析

### **当前实现 vs 官方规范**

| 组件 | 我们的实现 | 官方规范 | 状态 |
|------|------------|----------|------|
| **HTTP-Verb** | `PUT` | `PUT` | ✅ 正确 |
| **Content-MD5** | `cqlwSDWBjeOB46PrbE1XKw==` | Base64编码的MD5 | ✅ 正确 |
| **Content-Type** | `audio/3gpp` | 正确的MIME类型 | ✅ 正确 |
| **Date/Expires** | GMT格式时间 | GMT格式时间 | ✅ 正确 |
| **CanonicalizedResource** | `/iotdavideo/voice/parent/file.3gp` | `/桶名/对象名` | ✅ 正确 |

### **我们的StringToSign格式**
```
PUT
cqlwSDWBjeOB46PrbE1XKw==
audio/3gpp
Sat, 05 Jul 2025 16:21:44 GMT
/iotdavideo/voice/parent/1751732504174_voice_message.3gp
```

### **服务器期望的StringToSign格式**
```
PUT
cqlwSDWBjeOB46PrbE1XKw==
audio/3gpp
Sat, 05 Jul 2025 16:21:44 GMT
/iotdavideo/voice/parent/1751732504174_voice_message.3gp
```

**结论**: 我们的StringToSign格式与服务器期望的**完全一致**！

## 🤔 问题深度分析

### **为什么仍然签名不匹配？**

既然StringToSign格式正确，问题可能在于：

#### **1. Secret Key问题**
- **可能原因**: 使用的SECRET_KEY不正确
- **验证方法**: 确认SECRET_KEY是否为 `OtPzqzS5FOA3spl75ISujwv7OIfjQHCee38cAgsH`

#### **2. 字符编码问题**
- **可能原因**: UTF-8编码处理不一致
- **官方要求**: 必须使用UTF-8编码

#### **3. HMAC-SHA1计算问题**
- **可能原因**: HMAC-SHA1算法实现差异
- **官方要求**: 严格按照RFC标准实现

#### **4. Base64编码问题**
- **可能原因**: Base64编码格式不一致
- **官方要求**: 标准Base64编码，无换行符

#### **5. URL编码问题**
- **可能原因**: URL编码规则差异
- **官方要求**: 特定的URL编码规则

## 🔧 基于官方文档的修复方案

### **修复1: 验证SECRET_KEY**
确认我们使用的SECRET_KEY是否正确：
```kotlin
const val OBS_SECRET_KEY = "OtPzqzS5FOA3spl75ISujwv7OIfjQHCee38cAgsH"
```

### **修复2: 严格按照官方算法实现**
```kotlin
// 1. UTF-8编码StringToSign
val stringToSignBytes = stringToSign.toByteArray(Charsets.UTF_8)

// 2. UTF-8编码SECRET_KEY
val secretKeyBytes = SECRET_KEY.toByteArray(Charsets.UTF_8)

// 3. HMAC-SHA1计算
val mac = Mac.getInstance("HmacSHA1")
val secretKeySpec = SecretKeySpec(secretKeyBytes, "HmacSHA1")
mac.init(secretKeySpec)
val signature = mac.doFinal(stringToSignBytes)

// 4. Base64编码
val signatureBase64 = android.util.Base64.encodeToString(signature, android.util.Base64.NO_WRAP)

// 5. URL编码（如果需要）
val finalSignature = URLEncoder.encode(signatureBase64, "UTF-8")
```

### **修复3: 对比官方SDK实现**
根据官方SDK `AbstractAuthentication.java`：
```java
public static String calculateSignature(String stringToSign, String sk) {
    return ServiceUtils.signWithHmacSha1(sk, stringToSign);
}
```

## 📊 调试建议

### **详细对比测试**
1. **使用官方工具验证** - 使用华为云提供的签名计算工具
2. **逐步对比** - 与官方Java示例逐步对比每个环节
3. **字节级对比** - 对比StringToSign的字节数组

### **可能的解决方案**

#### **方案1: 使用官方SDK的签名算法**
直接参考官方SDK的签名实现，确保100%一致。

#### **方案2: 简化测试**
创建一个最简单的PUT请求，逐步验证签名计算。

#### **方案3: 联系华为云技术支持**
如果问题仍然存在，可以联系华为云技术支持获取帮助。

## 🎯 下一步行动计划

### **立即验证**
1. **确认SECRET_KEY** - 验证是否使用正确的密钥
2. **重新测试** - 使用当前实现重新测试
3. **对比日志** - 详细对比我们的签名与服务器期望

### **如果仍有问题**
1. **使用官方工具** - 用华为云签名计算工具验证
2. **参考官方SDK** - 直接移植官方SDK的签名算法
3. **简化测试** - 创建最小化的测试用例

## 💡 重要发现

### **我们的实现基本正确**
根据官方文档分析，我们的签名实现基本符合华为云OBS规范：
- ✅ StringToSign格式正确
- ✅ CanonicalizedResource构建正确
- ✅ 虚拟主机域名格式正确
- ✅ HTTP请求格式正确

### **问题可能在细节**
签名不匹配的问题可能在于：
- 字符编码的细微差异
- HMAC-SHA1算法的实现差异
- Base64编码的格式差异
- SECRET_KEY的准确性

## 🚀 测试建议

### **重新测试步骤**
1. **运行应用** - 使用当前的实现
2. **查看详细日志** - 特别关注签名计算过程
3. **对比StringToSign** - 确认与服务器期望完全一致
4. **验证SECRET_KEY** - 确认密钥的准确性

### **如果成功**
- 语音文件应该能成功上传到OBS
- 返回200状态码而不是403
- 整个语音交互流程正常工作

### **如果仍失败**
- 我们可以进一步参考官方SDK的具体实现
- 或者使用华为云提供的签名计算工具进行验证

## 📞 总结

基于华为云官方文档的深入分析，我们的OBS签名实现在理论上是正确的。StringToSign的格式与服务器期望完全一致，这说明我们的算法逻辑是对的。

问题可能在于实现的细节，特别是字符编码、HMAC-SHA1计算或SECRET_KEY的准确性。

现在请重新测试语音功能，让我们看看基于官方文档的实现是否能解决签名问题！🌟
