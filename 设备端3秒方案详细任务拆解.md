# 设备端3秒方案详细任务拆解

## 🎯 方案目标

实现3秒片段的超高频视频录制和上传，提供准直播级别的用户体验：
- **首次响应**: 7秒
- **更新频率**: 每3秒新内容
- **用户体验**: 准直播级别

## 📊 技术规格要求

### **核心参数**
```
片段时长: 3秒
目标文件大小: 0.6MB
压缩质量: 中等 (平衡大小和清晰度)
缓冲区大小: 20个片段 (1分钟历史)
上传延时: 2秒 (0.6MB ÷ 375KB/s)
```

### **设备资源需求**
```
存储空间: 20MB (20个片段 × 1MB)
CPU使用: 25-30% (高频录制和压缩)
内存使用: 100-150MB
电池消耗: +35-40% (需要外接电源)
网络流量: 按需上传 (家长请求时)
```

## 🔧 核心模块设计

### **1. 超短片段录制器**

#### **A. 录制管理器**
```python
class UltraShortSegmentRecorder:
    def __init__(self):
        self.segment_duration = 3           # 3秒片段
        self.buffer_size = 20              # 保留20个片段
        self.recording_quality = "medium"   # 中等质量
        self.compression_preset = "fast"    # 快速压缩
        self.segments_buffer = deque(maxlen=20)
        self.is_recording = False
        self.upload_queue = Queue()
        
    def start_continuous_recording(self):
        """启动持续3秒片段录制"""
        self.is_recording = True
        
        # 启动录制线程
        recording_thread = Thread(target=self._recording_loop)
        recording_thread.daemon = True
        recording_thread.start()
        
        # 启动压缩线程
        compression_thread = Thread(target=self._compression_loop)
        compression_thread.daemon = True
        compression_thread.start()
        
        # 启动上传线程
        upload_thread = Thread(target=self._upload_loop)
        upload_thread.daemon = True
        upload_thread.start()
        
        Logger.info("3秒片段持续录制已启动")
    
    def _recording_loop(self):
        """录制循环 - 每3秒生成一个片段"""
        segment_counter = 0
        
        while self.is_recording:
            try:
                # 生成片段文件名
                timestamp = int(time.time() * 1000)
                segment_file = f"segment_3s_{timestamp}_{segment_counter}.mp4"
                
                # 录制3秒片段
                self._record_segment(
                    output_file=segment_file,
                    duration=3,
                    quality=self.recording_quality
                )
                
                # 添加到缓冲区
                segment_info = {
                    'file': segment_file,
                    'timestamp': timestamp,
                    'duration': 3,
                    'size': os.path.getsize(segment_file),
                    'compressed': False
                }
                
                self.segments_buffer.append(segment_info)
                
                # 添加到压缩队列
                self.compression_queue.put(segment_info)
                
                segment_counter += 1
                Logger.debug(f"录制完成片段: {segment_file}")
                
            except Exception as e:
                Logger.error(f"录制片段失败: {e}")
                time.sleep(1)  # 错误后短暂等待
    
    def _record_segment(self, output_file, duration, quality):
        """录制单个3秒片段"""
        # 使用FFmpeg录制
        cmd = [
            'ffmpeg',
            '-f', 'v4l2',                    # Linux视频设备
            '-i', '/dev/video0',             # 摄像头设备
            '-t', str(duration),             # 录制时长
            '-c:v', 'libx264',               # H.264编码
            '-preset', 'ultrafast',          # 最快编码
            '-crf', '25',                    # 质量参数
            '-s', '640x480',                 # 分辨率
            '-r', '20',                      # 帧率20fps
            '-an',                           # 无音频 (减少文件大小)
            '-y',                            # 覆盖输出文件
            output_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, timeout=5)
            if result.returncode != 0:
                raise Exception(f"FFmpeg录制失败: {result.stderr}")
        except subprocess.TimeoutExpired:
            raise Exception("录制超时")
```

#### **B. 智能压缩器**
```python
class UltraFastCompressor:
    def __init__(self):
        self.target_size = 0.6 * 1024 * 1024  # 0.6MB目标大小
        self.compression_queue = Queue()
        
    def _compression_loop(self):
        """压缩循环 - 并行压缩片段"""
        while True:
            try:
                segment_info = self.compression_queue.get(timeout=1)
                
                if segment_info:
                    compressed_file = self._compress_segment(segment_info)
                    segment_info['compressed_file'] = compressed_file
                    segment_info['compressed'] = True
                    segment_info['compressed_size'] = os.path.getsize(compressed_file)
                    
                    Logger.debug(f"压缩完成: {compressed_file}")
                    
            except queue.Empty:
                continue
            except Exception as e:
                Logger.error(f"压缩失败: {e}")
    
    def _compress_segment(self, segment_info):
        """压缩单个片段到目标大小"""
        input_file = segment_info['file']
        output_file = input_file.replace('.mp4', '_compressed.mp4')
        
        # 计算目标码率
        duration = segment_info['duration']
        target_bitrate = int((self.target_size * 8) / duration)  # bps
        
        cmd = [
            'ffmpeg',
            '-i', input_file,
            '-c:v', 'libx264',
            '-preset', 'fast',               # 快速压缩
            '-b:v', f'{target_bitrate}',     # 目标码率
            '-maxrate', f'{target_bitrate}', # 最大码率
            '-bufsize', f'{target_bitrate}', # 缓冲区大小
            '-s', '640x480',                 # 保持分辨率
            '-r', '15',                      # 降低到15fps
            '-an',                           # 无音频
            '-y',
            output_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, timeout=3)
            if result.returncode == 0:
                # 删除原始文件节省空间
                os.remove(input_file)
                return output_file
            else:
                raise Exception(f"压缩失败: {result.stderr}")
        except subprocess.TimeoutExpired:
            raise Exception("压缩超时")
```

### **2. 即时上传管理器**

#### **A. 上传队列管理**
```python
class InstantUploadManager:
    def __init__(self):
        self.obs_client = HuaweiOBSClient()
        self.upload_queue = Queue()
        self.is_uploading = False
        self.current_upload = None
        
    def handle_parent_request(self, request_id):
        """处理家长的实时查看请求"""
        try:
            # 1. 立即响应 (1秒内)
            self._send_ack_response(request_id, "开始准备视频...")
            
            # 2. 获取最新的压缩片段
            latest_segment = self._get_latest_compressed_segment()
            if not latest_segment:
                self._send_error_response(request_id, "暂无可用视频片段")
                return
            
            # 3. 立即开始上传
            upload_task = {
                'request_id': request_id,
                'segment': latest_segment,
                'priority': 'high',
                'timestamp': time.time()
            }
            
            # 高优先级插入队列前端
            self.upload_queue.put(upload_task)
            
            Logger.info(f"家长请求已加入上传队列: {request_id}")
            
        except Exception as e:
            Logger.error(f"处理家长请求失败: {e}")
            self._send_error_response(request_id, f"处理失败: {str(e)}")
    
    def _upload_loop(self):
        """上传循环 - 处理上传队列"""
        while True:
            try:
                upload_task = self.upload_queue.get(timeout=1)
                
                if upload_task:
                    self._process_upload_task(upload_task)
                    
            except queue.Empty:
                continue
            except Exception as e:
                Logger.error(f"上传处理失败: {e}")
    
    def _process_upload_task(self, upload_task):
        """处理单个上传任务"""
        request_id = upload_task['request_id']
        segment = upload_task['segment']
        
        try:
            # 生成OBS对象键
            timestamp = int(time.time() * 1000)
            object_key = f"video/device_001/live/3s_{timestamp}.mp4"
            
            # 上传到OBS
            upload_result = self.obs_client.upload_file(
                file_path=segment['compressed_file'],
                object_key=object_key,
                content_type='video/mp4'
            )
            
            if upload_result.success:
                # 构建访问URL
                video_url = f"https://iotdavideo.obs.cn-north-4.myhuaweicloud.com/{object_key}"
                
                # 通知家长视频已就绪
                self._send_video_ready_notification(request_id, video_url)
                
                Logger.info(f"视频上传成功: {video_url}")
                
                # 清理本地文件
                os.remove(segment['compressed_file'])
                
            else:
                self._send_error_response(request_id, "上传失败，请重试")
                
        except Exception as e:
            Logger.error(f"上传任务处理失败: {e}")
            self._send_error_response(request_id, f"上传失败: {str(e)}")
    
    def _get_latest_compressed_segment(self):
        """获取最新的已压缩片段"""
        for segment in reversed(self.segments_buffer):
            if segment.get('compressed', False):
                return segment
        return None
```

### **3. 存储空间管理器**

#### **A. 智能清理策略**
```python
class StorageManager:
    def __init__(self):
        self.max_storage = 50 * 1024 * 1024    # 50MB最大存储
        self.cleanup_threshold = 0.8            # 80%时开始清理
        self.min_segments = 10                  # 最少保留10个片段
        
    def manage_storage(self):
        """智能存储管理"""
        current_usage = self._calculate_storage_usage()
        
        if current_usage > self.max_storage * self.cleanup_threshold:
            self._perform_cleanup()
    
    def _perform_cleanup(self):
        """执行存储清理"""
        # 按时间排序，删除最老的片段
        sorted_segments = sorted(
            self.segments_buffer, 
            key=lambda x: x['timestamp']
        )
        
        # 保留最新的10个片段
        segments_to_delete = sorted_segments[:-self.min_segments]
        
        for segment in segments_to_delete:
            try:
                # 删除原始文件
                if os.path.exists(segment['file']):
                    os.remove(segment['file'])
                
                # 删除压缩文件
                if segment.get('compressed_file') and os.path.exists(segment['compressed_file']):
                    os.remove(segment['compressed_file'])
                
                # 从缓冲区移除
                self.segments_buffer.remove(segment)
                
                Logger.debug(f"清理片段: {segment['file']}")
                
            except Exception as e:
                Logger.error(f"清理片段失败: {e}")
    
    def _calculate_storage_usage(self):
        """计算当前存储使用量"""
        total_size = 0
        
        for segment in self.segments_buffer:
            if os.path.exists(segment['file']):
                total_size += segment['size']
            
            if segment.get('compressed_file') and os.path.exists(segment['compressed_file']):
                total_size += segment.get('compressed_size', 0)
        
        return total_size
```

### **4. 电源管理优化器**

#### **A. 智能省电策略**
```python
class PowerManager:
    def __init__(self):
        self.battery_level = 100
        self.is_charging = False
        self.power_save_mode = False
        
    def optimize_for_battery(self):
        """根据电池状态优化性能"""
        self.battery_level = self._get_battery_level()
        self.is_charging = self._is_charging()
        
        if not self.is_charging:
            if self.battery_level < 20:
                self._enter_ultra_power_save()
            elif self.battery_level < 50:
                self._enter_power_save()
            else:
                self._exit_power_save()
    
    def _enter_ultra_power_save(self):
        """进入超级省电模式"""
        self.power_save_mode = True
        
        # 降低录制质量
        self.recording_quality = "low"
        self.compression_preset = "ultrafast"
        
        # 降低帧率
        self.frame_rate = 10
        
        # 增加片段时长到5秒
        self.segment_duration = 5
        
        Logger.warning("进入超级省电模式")
    
    def _enter_power_save(self):
        """进入省电模式"""
        self.power_save_mode = True
        
        # 中等质量
        self.recording_quality = "medium"
        self.frame_rate = 15
        
        Logger.info("进入省电模式")
    
    def _exit_power_save(self):
        """退出省电模式"""
        if self.power_save_mode:
            self.power_save_mode = False
            
            # 恢复正常质量
            self.recording_quality = "medium"
            self.frame_rate = 20
            self.segment_duration = 3
            
            Logger.info("退出省电模式")
```

## 🔄 主控制器集成

### **完整的设备端控制器**
```python
class Device3SecondVideoController:
    def __init__(self):
        self.recorder = UltraShortSegmentRecorder()
        self.compressor = UltraFastCompressor()
        self.uploader = InstantUploadManager()
        self.storage_manager = StorageManager()
        self.power_manager = PowerManager()
        self.iot_client = HuaweiIoTClient()
        
        self.is_running = False
        
    def start_system(self):
        """启动3秒视频系统"""
        try:
            Logger.info("启动3秒视频系统...")
            
            # 1. 启动持续录制
            self.recorder.start_continuous_recording()
            
            # 2. 启动IoT命令监听
            self.iot_client.start_command_listener(self._handle_iot_command)
            
            # 3. 启动定时任务
            self._start_periodic_tasks()
            
            self.is_running = True
            Logger.info("3秒视频系统启动成功")
            
        except Exception as e:
            Logger.error(f"系统启动失败: {e}")
            raise
    
    def _handle_iot_command(self, command):
        """处理IoT命令"""
        if command['command_name'] == 'REQUEST_INSTANT_VIDEO':
            request_id = command.get('request_id', 'unknown')
            self.uploader.handle_parent_request(request_id)
        
        elif command['command_name'] == 'SET_VIDEO_QUALITY':
            quality = command.get('quality', 'medium')
            self.recorder.set_recording_quality(quality)
        
        elif command['command_name'] == 'SYSTEM_STATUS':
            self._send_system_status()
    
    def _start_periodic_tasks(self):
        """启动定时任务"""
        # 每30秒检查存储空间
        storage_timer = Timer(30, self._periodic_storage_check)
        storage_timer.daemon = True
        storage_timer.start()
        
        # 每60秒检查电池状态
        battery_timer = Timer(60, self._periodic_battery_check)
        battery_timer.daemon = True
        battery_timer.start()
    
    def _periodic_storage_check(self):
        """定期存储检查"""
        if self.is_running:
            self.storage_manager.manage_storage()
            
            # 重新启动定时器
            storage_timer = Timer(30, self._periodic_storage_check)
            storage_timer.daemon = True
            storage_timer.start()
    
    def _periodic_battery_check(self):
        """定期电池检查"""
        if self.is_running:
            self.power_manager.optimize_for_battery()
            
            # 重新启动定时器
            battery_timer = Timer(60, self._periodic_battery_check)
            battery_timer.daemon = True
            battery_timer.start()
    
    def stop_system(self):
        """停止系统"""
        self.is_running = False
        self.recorder.stop_recording()
        self.iot_client.stop_command_listener()
        Logger.info("3秒视频系统已停止")

# 主程序入口
if __name__ == "__main__":
    controller = Device3SecondVideoController()
    
    try:
        controller.start_system()
        
        # 保持运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        Logger.info("收到停止信号")
        controller.stop_system()
    except Exception as e:
        Logger.error(f"系统运行错误: {e}")
        controller.stop_system()
```

## 📊 性能监控

### **系统监控器**
```python
class SystemMonitor:
    def __init__(self):
        self.metrics = {
            'segments_recorded': 0,
            'segments_compressed': 0,
            'segments_uploaded': 0,
            'upload_failures': 0,
            'average_upload_time': 0,
            'storage_usage': 0,
            'cpu_usage': 0,
            'memory_usage': 0,
            'battery_level': 100
        }
    
    def update_metrics(self):
        """更新系统指标"""
        self.metrics['storage_usage'] = self._get_storage_usage()
        self.metrics['cpu_usage'] = self._get_cpu_usage()
        self.metrics['memory_usage'] = self._get_memory_usage()
        self.metrics['battery_level'] = self._get_battery_level()
    
    def get_system_report(self):
        """生成系统报告"""
        return {
            'timestamp': time.time(),
            'metrics': self.metrics,
            'status': 'running' if self.is_running else 'stopped',
            'uptime': time.time() - self.start_time
        }
```

## 🎯 部署配置

### **系统要求**
```
硬件要求:
- CPU: ARM Cortex-A7 或更高
- 内存: 512MB RAM (推荐1GB)
- 存储: 2GB可用空间
- 摄像头: USB UVC兼容或CSI接口
- 网络: 4G模块 (广和通L610)

软件要求:
- 操作系统: Linux (Ubuntu/Debian)
- Python: 3.7+
- FFmpeg: 4.0+
- 依赖库: opencv-python, requests, threading
```

### **安装脚本**
```bash
#!/bin/bash
# 3秒视频系统安装脚本

echo "安装3秒视频系统..."

# 更新系统
apt-get update

# 安装FFmpeg
apt-get install -y ffmpeg

# 安装Python依赖
pip3 install opencv-python requests

# 创建工作目录
mkdir -p /opt/3s-video-system
mkdir -p /opt/3s-video-system/segments
mkdir -p /opt/3s-video-system/logs

# 复制程序文件
cp *.py /opt/3s-video-system/

# 设置权限
chmod +x /opt/3s-video-system/*.py

# 创建系统服务
cat > /etc/systemd/system/3s-video.service << EOF
[Unit]
Description=3 Second Video System
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/3s-video-system
ExecStart=/usr/bin/python3 /opt/3s-video-system/main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 启用服务
systemctl enable 3s-video.service
systemctl start 3s-video.service

echo "3秒视频系统安装完成!"
```

## 🎉 总结

设备端3秒方案的核心特点：

### **✅ 技术优势**
- **超短延时**: 7秒首次响应
- **高频更新**: 每3秒新内容
- **智能压缩**: 0.6MB小文件
- **资源优化**: 智能存储和电源管理

### **📊 性能指标**
- **录制延时**: 0秒 (持续录制)
- **压缩时间**: 1-2秒
- **上传时间**: 2秒
- **总响应**: 7秒

### **🔧 关键技术**
- **多线程并行**: 录制、压缩、上传同时进行
- **循环缓冲**: 智能管理存储空间
- **自适应质量**: 根据电池和网络状况调整
- **即时响应**: 收到命令立即处理

这个设备端实现将为3秒方案提供坚实的技术基础！🌟
