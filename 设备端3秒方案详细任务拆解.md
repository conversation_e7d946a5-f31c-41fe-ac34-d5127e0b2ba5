# STM32设备端3秒方案详细任务拆解

## 🎯 方案目标

基于STM32单片机实现3秒片段的超高频视频录制和上传，提供准直播级别的用户体验：
- **首次响应**: 7秒
- **更新频率**: 每3秒新内容
- **用户体验**: 准直播级别

## � 硬件平台规格

### **STM32主控配置**
```
主控芯片: STM32H7系列 (推荐STM32H743/H753)
主频: 480MHz
Flash: 2MB (程序存储)
SRAM: 1MB (运行内存)
外部SDRAM: 32MB (视频缓冲)
外部Flash: 64MB (视频片段存储)
```

### **外设模块配置**
```
摄像头: OV2640/OV5640 (DCMI接口)
4G模块: 广和通L610 (UART接口)
存储: MicroSD卡 (SDIO接口)
电源管理: 支持外接电源和电池
状态指示: LED指示灯
调试接口: UART/SWD
```

### **资源需求评估**
```
Flash使用: 1.5MB (程序 + 固件)
SRAM使用: 800KB (系统 + 缓冲)
外部SDRAM: 用于视频帧缓冲
外部Flash: 循环存储视频片段
功耗: 工作电流约500mA@5V
```

## 🔧 核心模块设计

### **1. STM32视频采集模块**

#### **A. DCMI摄像头接口**
```c
// 摄像头配置结构体
typedef struct {
    uint16_t width;          // 图像宽度
    uint16_t height;         // 图像高度
    uint8_t fps;             // 帧率
    uint8_t quality;         // 压缩质量 1-10
    uint8_t format;          // 图像格式 (JPEG)
} Camera_Config_t;

// 视频片段结构体
typedef struct {
    uint32_t timestamp;      // 时间戳
    uint32_t size;           // 文件大小
    uint8_t segment_id;      // 片段ID
    uint8_t status;          // 状态 (录制中/完成/上传中)
    char filename[32];       // 文件名
} Video_Segment_t;

// 摄像头初始化
HAL_StatusTypeDef Camera_Init(Camera_Config_t *config) {
    // 1. 配置DCMI接口
    hdcmi.Instance = DCMI;
    hdcmi.Init.SynchroMode = DCMI_SYNCHRO_HARDWARE;
    hdcmi.Init.PCKPolarity = DCMI_PCKPOLARITY_RISING;
    hdcmi.Init.VSPolarity = DCMI_VSPOLARITY_LOW;
    hdcmi.Init.HSPolarity = DCMI_HSPOLARITY_LOW;
    hdcmi.Init.CaptureRate = DCMI_CR_ALL_FRAME;
    hdcmi.Init.ExtendedDataMode = DCMI_EXTEND_DATA_8B;

    if (HAL_DCMI_Init(&hdcmi) != HAL_OK) {
        return HAL_ERROR;
    }

    // 2. 配置摄像头传感器 (OV2640)
    if (OV2640_Init(config) != HAL_OK) {
        return HAL_ERROR;
    }

    // 3. 配置DMA传输
    if (Camera_DMA_Config() != HAL_OK) {
        return HAL_ERROR;
    }

    return HAL_OK;
}

// 开始连续采集
HAL_StatusTypeDef Camera_Start_Continuous_Capture(void) {
    // 启动DCMI连续采集模式
    return HAL_DCMI_Start_DMA(&hdcmi, DCMI_MODE_CONTINUOUS,
                              (uint32_t)frame_buffer,
                              FRAME_BUFFER_SIZE/4);
}
    
#### **B. 3秒片段录制管理**
```c
// 片段录制状态机
typedef enum {
    SEGMENT_IDLE = 0,
    SEGMENT_RECORDING,
    SEGMENT_PROCESSING,
    SEGMENT_READY,
    SEGMENT_UPLOADING,
    SEGMENT_ERROR
} Segment_State_t;

// 片段缓冲区管理
#define MAX_SEGMENTS 20
#define SEGMENT_DURATION_MS 3000
#define FRAME_RATE 15
#define FRAMES_PER_SEGMENT (FRAME_RATE * 3)

static Video_Segment_t segment_buffer[MAX_SEGMENTS];
static uint8_t current_segment_index = 0;
static uint8_t segment_count = 0;
static uint32_t frame_counter = 0;

// 3秒片段录制任务
void Video_Segment_Task(void *argument) {
    uint32_t segment_start_time;
    uint8_t current_frames = 0;

    while (1) {
        // 等待开始录制信号
        if (recording_state == RECORDING_ACTIVE) {

            // 开始新的3秒片段
            segment_start_time = HAL_GetTick();
            current_frames = 0;

            // 创建新片段
            Video_Segment_t *current_segment = &segment_buffer[current_segment_index];
            current_segment->timestamp = segment_start_time;
            current_segment->segment_id = current_segment_index;
            current_segment->status = SEGMENT_RECORDING;

            // 生成文件名
            snprintf(current_segment->filename, sizeof(current_segment->filename),
                    "3s_%lu_%d.mjpg", segment_start_time, current_segment_index);

            // 录制3秒内的所有帧
            while ((HAL_GetTick() - segment_start_time) < SEGMENT_DURATION_MS) {

                // 等待新帧就绪
                if (frame_ready_flag) {
                    frame_ready_flag = 0;

                    // 处理当前帧
                    if (Process_Frame_To_Segment(current_segment) == HAL_OK) {
                        current_frames++;
                    }

                    // 检查是否达到目标帧数
                    if (current_frames >= FRAMES_PER_SEGMENT) {
                        break;
                    }
                }

                osDelay(1); // 1ms延时
            }

            // 完成片段录制
            current_segment->status = SEGMENT_PROCESSING;
            current_segment->size = Get_Segment_File_Size(current_segment);

            // 压缩和优化片段
            if (Compress_Video_Segment(current_segment) == HAL_OK) {
                current_segment->status = SEGMENT_READY;

                // 通知上传任务
                osMessageQueuePut(upload_queue, &current_segment_index, 0, 0);
            } else {
                current_segment->status = SEGMENT_ERROR;
            }

            // 移动到下一个片段槽位
            current_segment_index = (current_segment_index + 1) % MAX_SEGMENTS;
            if (segment_count < MAX_SEGMENTS) {
                segment_count++;
            }
        }

        osDelay(10); // 10ms任务周期
    }
}
    
### **2. 视频压缩处理模块**

#### **A. JPEG压缩引擎**
```c
// JPEG压缩配置
typedef struct {
    uint8_t quality;         // 压缩质量 1-10
    uint16_t target_size;    // 目标文件大小 (KB)
    uint8_t chroma_subsampling; // 色度子采样
} JPEG_Config_t;

// 帧压缩处理
HAL_StatusTypeDef Process_Frame_To_Segment(Video_Segment_t *segment) {
    static uint8_t jpeg_buffer[64*1024]; // 64KB JPEG缓冲区
    uint32_t jpeg_size;

    // 1. 获取当前帧数据
    uint8_t *frame_data = Get_Current_Frame_Buffer();
    if (frame_data == NULL) {
        return HAL_ERROR;
    }

    // 2. JPEG压缩
    JPEG_Config_t jpeg_config = {
        .quality = 7,           // 中等质量
        .target_size = 20,      // 目标20KB每帧
        .chroma_subsampling = 1 // 4:2:0子采样
    };

    if (JPEG_Compress_Frame(frame_data, jpeg_buffer, &jpeg_size, &jpeg_config) != HAL_OK) {
        return HAL_ERROR;
    }

    // 3. 写入片段文件
    if (Write_Frame_To_Segment_File(segment, jpeg_buffer, jpeg_size) != HAL_OK) {
        return HAL_ERROR;
    }

    return HAL_OK;
}

// 片段压缩优化
HAL_StatusTypeDef Compress_Video_Segment(Video_Segment_t *segment) {
    uint32_t original_size = segment->size;
    uint32_t target_size = 600 * 1024; // 目标600KB

    if (original_size <= target_size) {
        return HAL_OK; // 已经满足大小要求
    }

    // 重新压缩以减小文件大小
    JPEG_Config_t compress_config = {
        .quality = 5,           // 降低质量
        .target_size = target_size / FRAMES_PER_SEGMENT,
        .chroma_subsampling = 2 // 更激进的子采样
    };

    return Recompress_Segment_File(segment, &compress_config);
}
```

### **3. 4G通信模块 (广和通L610)**

#### **A. L610初始化和配置**
```c
// L610 4G模块配置
typedef struct {
    UART_HandleTypeDef *huart;  // UART句柄
    uint32_t baudrate;          // 波特率
    uint8_t power_pin;          // 电源控制引脚
    uint8_t reset_pin;          // 复位引脚
    uint8_t status_pin;         // 状态检测引脚
} L610_Config_t;

// AT命令结构体
typedef struct {
    char command[128];          // AT命令
    char expected_response[64]; // 期望响应
    uint32_t timeout_ms;        // 超时时间
} AT_Command_t;

// L610初始化
HAL_StatusTypeDef L610_Init(L610_Config_t *config) {
    // 1. 硬件初始化
    HAL_GPIO_WritePin(GPIOB, config->power_pin, GPIO_PIN_SET);
    HAL_Delay(1000);

    // 2. 基础AT命令测试
    if (L610_Send_AT_Command("AT", "OK", 1000) != HAL_OK) {
        return HAL_ERROR;
    }

    // 3. 配置网络参数
    AT_Command_t init_commands[] = {
        {"AT+CFUN=1", "OK", 5000},           // 启用射频功能
        {"AT+CPIN?", "READY", 3000},         // 检查SIM卡
        {"AT+CREG?", "+CREG: 0,1", 10000},   // 检查网络注册
        {"AT+CGATT=1", "OK", 10000},         // 附着GPRS
        {"AT+CGDCONT=1,\"IP\",\"CMNET\"", "OK", 3000}, // 设置APN
    };

    for (int i = 0; i < sizeof(init_commands)/sizeof(AT_Command_t); i++) {
        if (L610_Send_AT_Command(init_commands[i].command,
                                init_commands[i].expected_response,
                                init_commands[i].timeout_ms) != HAL_OK) {
            return HAL_ERROR;
        }
    }

    return HAL_OK;
}

// HTTP上传文件到OBS
HAL_StatusTypeDef L610_Upload_File_To_OBS(Video_Segment_t *segment) {
    char url[256];
    char headers[512];
    uint32_t file_size = segment->size;

    // 1. 构建OBS上传URL
    snprintf(url, sizeof(url),
            "https://iotdavideo.obs.cn-north-4.myhuaweicloud.com/video/device_001/live/%s",
            segment->filename);

    // 2. 构建HTTP头 (包含OBS签名)
    Generate_OBS_Authorization_Header(headers, sizeof(headers), segment);

    // 3. 初始化HTTP连接
    if (L610_HTTP_Init(url) != HAL_OK) {
        return HAL_ERROR;
    }

    // 4. 设置HTTP头
    if (L610_HTTP_Set_Headers(headers) != HAL_OK) {
        return HAL_ERROR;
    }

    // 5. 上传文件数据
    if (L610_HTTP_Upload_Data(segment) != HAL_OK) {
        return HAL_ERROR;
    }

    // 6. 获取响应
    uint16_t response_code;
    if (L610_HTTP_Get_Response(&response_code) != HAL_OK) {
        return HAL_ERROR;
    }

    // 7. 检查上传结果
    if (response_code == 200) {
        segment->status = SEGMENT_UPLOADED;
        return HAL_OK;
    } else {
        segment->status = SEGMENT_ERROR;
        return HAL_ERROR;
    }
}
```

### **4. IoT命令处理模块**

#### **A. 华为云IoT命令接收**
```c
// IoT命令类型
typedef enum {
    IOT_CMD_REQUEST_VIDEO = 1,
    IOT_CMD_SET_QUALITY,
    IOT_CMD_SYSTEM_STATUS,
    IOT_CMD_POWER_SAVE,
    IOT_CMD_UNKNOWN
} IoT_Command_Type_t;

// IoT命令结构体
typedef struct {
    IoT_Command_Type_t type;
    uint32_t request_id;
    uint32_t timestamp;
    uint8_t priority;
    char params[128];
} IoT_Command_t;

// IoT命令处理任务
void IoT_Command_Task(void *argument) {
    IoT_Command_t received_command;

    while (1) {
        // 等待接收IoT命令
        if (osMessageQueueGet(iot_command_queue, &received_command, NULL, osWaitForever) == osOK) {

            switch (received_command.type) {
                case IOT_CMD_REQUEST_VIDEO:
                    Handle_Video_Request_Command(&received_command);
                    break;

                case IOT_CMD_SET_QUALITY:
                    Handle_Quality_Change_Command(&received_command);
                    break;

                case IOT_CMD_SYSTEM_STATUS:
                    Handle_Status_Request_Command(&received_command);
                    break;

                case IOT_CMD_POWER_SAVE:
                    Handle_Power_Save_Command(&received_command);
                    break;

                default:
                    // 未知命令，记录日志
                    Log_Unknown_Command(&received_command);
                    break;
            }
        }
    }
}

// 处理视频请求命令
HAL_StatusTypeDef Handle_Video_Request_Command(IoT_Command_t *command) {
    // 1. 立即响应确认
    Send_IoT_Response(command->request_id, "VIDEO_REQUEST_RECEIVED", HAL_GetTick());

    // 2. 获取最新的已完成片段
    Video_Segment_t *latest_segment = Get_Latest_Ready_Segment();
    if (latest_segment == NULL) {
        Send_IoT_Response(command->request_id, "NO_VIDEO_AVAILABLE", HAL_GetTick());
        return HAL_ERROR;
    }

    // 3. 标记为高优先级上传
    latest_segment->status = SEGMENT_UPLOADING;

    // 4. 添加到上传队列 (高优先级)
    Upload_Request_t upload_request = {
        .segment = latest_segment,
        .request_id = command->request_id,
        .priority = UPLOAD_PRIORITY_HIGH,
        .timestamp = HAL_GetTick()
    };

    if (osMessageQueuePut(upload_queue, &upload_request, UPLOAD_PRIORITY_HIGH, 0) != osOK) {
        return HAL_ERROR;
    }

    return HAL_OK;
}
        
    def handle_parent_request(self, request_id):
        """处理家长的实时查看请求"""
        try:
            # 1. 立即响应 (1秒内)
            self._send_ack_response(request_id, "开始准备视频...")
            
            # 2. 获取最新的压缩片段
            latest_segment = self._get_latest_compressed_segment()
            if not latest_segment:
                self._send_error_response(request_id, "暂无可用视频片段")
                return
            
            # 3. 立即开始上传
            upload_task = {
                'request_id': request_id,
                'segment': latest_segment,
                'priority': 'high',
                'timestamp': time.time()
            }
            
            # 高优先级插入队列前端
            self.upload_queue.put(upload_task)
            
            Logger.info(f"家长请求已加入上传队列: {request_id}")
            
        except Exception as e:
            Logger.error(f"处理家长请求失败: {e}")
            self._send_error_response(request_id, f"处理失败: {str(e)}")
    
    def _upload_loop(self):
        """上传循环 - 处理上传队列"""
        while True:
            try:
                upload_task = self.upload_queue.get(timeout=1)
                
                if upload_task:
                    self._process_upload_task(upload_task)
                    
            except queue.Empty:
                continue
            except Exception as e:
                Logger.error(f"上传处理失败: {e}")
    
    def _process_upload_task(self, upload_task):
        """处理单个上传任务"""
        request_id = upload_task['request_id']
        segment = upload_task['segment']
        
        try:
            # 生成OBS对象键
            timestamp = int(time.time() * 1000)
            object_key = f"video/device_001/live/3s_{timestamp}.mp4"
            
            # 上传到OBS
            upload_result = self.obs_client.upload_file(
                file_path=segment['compressed_file'],
                object_key=object_key,
                content_type='video/mp4'
            )
            
            if upload_result.success:
                # 构建访问URL
                video_url = f"https://iotdavideo.obs.cn-north-4.myhuaweicloud.com/{object_key}"
                
                # 通知家长视频已就绪
                self._send_video_ready_notification(request_id, video_url)
                
                Logger.info(f"视频上传成功: {video_url}")
                
                # 清理本地文件
                os.remove(segment['compressed_file'])
                
            else:
                self._send_error_response(request_id, "上传失败，请重试")
                
        except Exception as e:
            Logger.error(f"上传任务处理失败: {e}")
            self._send_error_response(request_id, f"上传失败: {str(e)}")
    
    def _get_latest_compressed_segment(self):
        """获取最新的已压缩片段"""
        for segment in reversed(self.segments_buffer):
            if segment.get('compressed', False):
                return segment
        return None
```

### **3. 存储空间管理器**

#### **A. 智能清理策略**
```python
class StorageManager:
    def __init__(self):
        self.max_storage = 50 * 1024 * 1024    # 50MB最大存储
        self.cleanup_threshold = 0.8            # 80%时开始清理
        self.min_segments = 10                  # 最少保留10个片段
        
    def manage_storage(self):
        """智能存储管理"""
        current_usage = self._calculate_storage_usage()
        
        if current_usage > self.max_storage * self.cleanup_threshold:
            self._perform_cleanup()
    
    def _perform_cleanup(self):
        """执行存储清理"""
        # 按时间排序，删除最老的片段
        sorted_segments = sorted(
            self.segments_buffer, 
            key=lambda x: x['timestamp']
        )
        
        # 保留最新的10个片段
        segments_to_delete = sorted_segments[:-self.min_segments]
        
        for segment in segments_to_delete:
            try:
                # 删除原始文件
                if os.path.exists(segment['file']):
                    os.remove(segment['file'])
                
                # 删除压缩文件
                if segment.get('compressed_file') and os.path.exists(segment['compressed_file']):
                    os.remove(segment['compressed_file'])
                
                # 从缓冲区移除
                self.segments_buffer.remove(segment)
                
                Logger.debug(f"清理片段: {segment['file']}")
                
            except Exception as e:
                Logger.error(f"清理片段失败: {e}")
    
    def _calculate_storage_usage(self):
        """计算当前存储使用量"""
        total_size = 0
        
        for segment in self.segments_buffer:
            if os.path.exists(segment['file']):
                total_size += segment['size']
            
            if segment.get('compressed_file') and os.path.exists(segment['compressed_file']):
                total_size += segment.get('compressed_size', 0)
        
        return total_size
```

### **4. 电源管理优化器**

#### **A. 智能省电策略**
```python
class PowerManager:
    def __init__(self):
        self.battery_level = 100
        self.is_charging = False
        self.power_save_mode = False
        
    def optimize_for_battery(self):
        """根据电池状态优化性能"""
        self.battery_level = self._get_battery_level()
        self.is_charging = self._is_charging()
        
        if not self.is_charging:
            if self.battery_level < 20:
                self._enter_ultra_power_save()
            elif self.battery_level < 50:
                self._enter_power_save()
            else:
                self._exit_power_save()
    
    def _enter_ultra_power_save(self):
        """进入超级省电模式"""
        self.power_save_mode = True
        
        # 降低录制质量
        self.recording_quality = "low"
        self.compression_preset = "ultrafast"
        
        # 降低帧率
        self.frame_rate = 10
        
        # 增加片段时长到5秒
        self.segment_duration = 5
        
        Logger.warning("进入超级省电模式")
    
    def _enter_power_save(self):
        """进入省电模式"""
        self.power_save_mode = True
        
        # 中等质量
        self.recording_quality = "medium"
        self.frame_rate = 15
        
        Logger.info("进入省电模式")
    
    def _exit_power_save(self):
        """退出省电模式"""
        if self.power_save_mode:
            self.power_save_mode = False
            
            # 恢复正常质量
            self.recording_quality = "medium"
            self.frame_rate = 20
            self.segment_duration = 3
            
            Logger.info("退出省电模式")
```

### **5. 主控制器集成**

#### **A. STM32主控制器**
```c
// 系统状态枚举
typedef enum {
    SYSTEM_INIT = 0,
    SYSTEM_READY,
    SYSTEM_RECORDING,
    SYSTEM_UPLOADING,
    SYSTEM_ERROR,
    SYSTEM_POWER_SAVE
} System_State_t;

// 系统配置结构体
typedef struct {
    Camera_Config_t camera_config;
    L610_Config_t l610_config;
    uint8_t segment_duration;
    uint8_t max_segments;
    uint8_t upload_quality;
    uint8_t power_save_mode;
} System_Config_t;

// 全局系统状态
static System_State_t system_state = SYSTEM_INIT;
static System_Config_t system_config;
static osMessageQueueId_t iot_command_queue;
static osMessageQueueId_t upload_queue;

// 主控制器初始化
HAL_StatusTypeDef System_Controller_Init(void) {
    // 1. 硬件初始化
    if (Hardware_Init() != HAL_OK) {
        return HAL_ERROR;
    }

    // 2. 创建RTOS队列
    iot_command_queue = osMessageQueueNew(10, sizeof(IoT_Command_t), NULL);
    upload_queue = osMessageQueueNew(5, sizeof(Upload_Request_t), NULL);

    if (iot_command_queue == NULL || upload_queue == NULL) {
        return HAL_ERROR;
    }

    // 3. 创建任务
    osThreadId_t video_task = osThreadNew(Video_Segment_Task, NULL, NULL);
    osThreadId_t iot_task = osThreadNew(IoT_Command_Task, NULL, NULL);
    osThreadId_t upload_task = osThreadNew(Upload_Task, NULL, NULL);
    osThreadId_t monitor_task = osThreadNew(System_Monitor_Task, NULL, NULL);

    if (video_task == NULL || iot_task == NULL ||
        upload_task == NULL || monitor_task == NULL) {
        return HAL_ERROR;
    }

    // 4. 初始化外设
    if (Camera_Init(&system_config.camera_config) != HAL_OK) {
        return HAL_ERROR;
    }

    if (L610_Init(&system_config.l610_config) != HAL_OK) {
        return HAL_ERROR;
    }

    // 5. 启动连续录制
    if (Camera_Start_Continuous_Capture() != HAL_OK) {
        return HAL_ERROR;
    }

    system_state = SYSTEM_READY;
    return HAL_OK;
}
        
    def start_system(self):
        """启动3秒视频系统"""
        try:
            Logger.info("启动3秒视频系统...")
            
            # 1. 启动持续录制
            self.recorder.start_continuous_recording()
            
            # 2. 启动IoT命令监听
            self.iot_client.start_command_listener(self._handle_iot_command)
            
            # 3. 启动定时任务
            self._start_periodic_tasks()
            
            self.is_running = True
            Logger.info("3秒视频系统启动成功")
            
        except Exception as e:
            Logger.error(f"系统启动失败: {e}")
            raise
    
    def _handle_iot_command(self, command):
        """处理IoT命令"""
        if command['command_name'] == 'REQUEST_INSTANT_VIDEO':
            request_id = command.get('request_id', 'unknown')
            self.uploader.handle_parent_request(request_id)
        
        elif command['command_name'] == 'SET_VIDEO_QUALITY':
            quality = command.get('quality', 'medium')
            self.recorder.set_recording_quality(quality)
        
        elif command['command_name'] == 'SYSTEM_STATUS':
            self._send_system_status()
    
    def _start_periodic_tasks(self):
        """启动定时任务"""
        # 每30秒检查存储空间
        storage_timer = Timer(30, self._periodic_storage_check)
        storage_timer.daemon = True
        storage_timer.start()
        
        # 每60秒检查电池状态
        battery_timer = Timer(60, self._periodic_battery_check)
        battery_timer.daemon = True
        battery_timer.start()
    
    def _periodic_storage_check(self):
        """定期存储检查"""
        if self.is_running:
            self.storage_manager.manage_storage()
            
            # 重新启动定时器
            storage_timer = Timer(30, self._periodic_storage_check)
            storage_timer.daemon = True
            storage_timer.start()
    
    def _periodic_battery_check(self):
        """定期电池检查"""
        if self.is_running:
            self.power_manager.optimize_for_battery()
            
            # 重新启动定时器
            battery_timer = Timer(60, self._periodic_battery_check)
            battery_timer.daemon = True
            battery_timer.start()
    
    def stop_system(self):
        """停止系统"""
        self.is_running = False
        self.recorder.stop_recording()
        self.iot_client.stop_command_listener()
        Logger.info("3秒视频系统已停止")

# 主程序入口
if __name__ == "__main__":
    controller = Device3SecondVideoController()
    
    try:
        controller.start_system()
        
        # 保持运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        Logger.info("收到停止信号")
        controller.stop_system()
    except Exception as e:
        Logger.error(f"系统运行错误: {e}")
        controller.stop_system()
```

## 📊 性能监控

### **系统监控器**
```python
class SystemMonitor:
    def __init__(self):
        self.metrics = {
            'segments_recorded': 0,
            'segments_compressed': 0,
            'segments_uploaded': 0,
            'upload_failures': 0,
            'average_upload_time': 0,
            'storage_usage': 0,
            'cpu_usage': 0,
            'memory_usage': 0,
            'battery_level': 100
        }
    
    def update_metrics(self):
        """更新系统指标"""
        self.metrics['storage_usage'] = self._get_storage_usage()
        self.metrics['cpu_usage'] = self._get_cpu_usage()
        self.metrics['memory_usage'] = self._get_memory_usage()
        self.metrics['battery_level'] = self._get_battery_level()
    
    def get_system_report(self):
        """生成系统报告"""
        return {
            'timestamp': time.time(),
            'metrics': self.metrics,
            'status': 'running' if self.is_running else 'stopped',
            'uptime': time.time() - self.start_time
        }
```

#### **B. 系统监控任务**
```c
// 系统监控任务
void System_Monitor_Task(void *argument) {
    uint32_t last_monitor_time = 0;
    System_Status_t status;

    while (1) {
        uint32_t current_time = HAL_GetTick();

        // 每5秒执行一次监控
        if (current_time - last_monitor_time >= 5000) {

            // 1. 检查系统状态
            status.system_state = system_state;
            status.free_heap = xPortGetFreeHeapSize();
            status.cpu_usage = Get_CPU_Usage_Percentage();
            status.temperature = Get_MCU_Temperature();

            // 2. 检查存储空间
            status.storage_usage = Get_Storage_Usage_Percentage();
            if (status.storage_usage > 80) {
                Cleanup_Old_Segments();
            }

            // 3. 检查网络状态
            status.network_signal = L610_Get_Signal_Strength();
            status.network_status = L610_Get_Network_Status();

            // 4. 检查电源状态
            status.battery_voltage = Get_Battery_Voltage();
            status.charging_status = Get_Charging_Status();

            // 5. 电源管理
            if (status.battery_voltage < 3.3f && !status.charging_status) {
                Enter_Power_Save_Mode();
            }

            // 6. 发送状态到云端 (可选)
            if (current_time % 60000 == 0) { // 每分钟发送一次
                Send_System_Status_To_Cloud(&status);
            }

            last_monitor_time = current_time;
        }

        osDelay(1000); // 1秒监控周期
    }
}

// 电源管理
void Enter_Power_Save_Mode(void) {
    system_state = SYSTEM_POWER_SAVE;

    // 降低系统时钟
    SystemClock_Config_LowPower();

    // 降低摄像头帧率
    Camera_Set_Frame_Rate(10);

    // 降低录制质量
    Camera_Set_Quality(3);

    // 增加片段时长到5秒
    system_config.segment_duration = 5;
}

void Exit_Power_Save_Mode(void) {
    system_state = SYSTEM_READY;

    // 恢复正常时钟
    SystemClock_Config();

    // 恢复正常帧率
    Camera_Set_Frame_Rate(15);

    // 恢复正常质量
    Camera_Set_Quality(7);

    // 恢复3秒片段
    system_config.segment_duration = 3;
}
```

## 🎯 STM32部署配置

### **硬件要求**
```
主控芯片: STM32H743VIT6 或 STM32H753VIT6
- ARM Cortex-M7 @ 480MHz
- 2MB Flash + 1MB SRAM
- 外部SDRAM: 32MB (IS42S32800G)
- 外部Flash: 64MB (W25Q512JV)

外设配置:
- 摄像头: OV2640 (DCMI接口)
- 4G模块: 广和通L610 (UART3)
- 存储卡: MicroSD (SDIO)
- 调试接口: UART1 (115200bps)
- 状态LED: GPIO输出
- 电源管理: ADC监控电池电压
```

### **STM32CubeIDE配置**
```c
// main.c - 主程序入口
int main(void) {
    // 1. HAL库初始化
    HAL_Init();

    // 2. 系统时钟配置
    SystemClock_Config();

    // 3. GPIO初始化
    MX_GPIO_Init();

    // 4. 外设初始化
    MX_DCMI_Init();
    MX_UART3_Init();  // L610通信
    MX_UART1_Init();  // 调试串口
    MX_SDIO_Init();   // SD卡
    MX_ADC1_Init();   // 电池监控

    // 5. FreeRTOS初始化
    osKernelInitialize();

    // 6. 系统控制器初始化
    if (System_Controller_Init() != HAL_OK) {
        Error_Handler();
    }

    // 7. 启动调度器
    osKernelStart();

    // 不应该到达这里
    while (1) {
        HAL_Delay(1000);
    }
}

// FreeRTOS配置 (FreeRTOSConfig.h)
#define configUSE_PREEMPTION                     1
#define configUSE_IDLE_HOOK                      0
#define configUSE_TICK_HOOK                      0
#define configCPU_CLOCK_HZ                       480000000
#define configTICK_RATE_HZ                       1000
#define configMAX_PRIORITIES                     7
#define configMINIMAL_STACK_SIZE                 128
#define configTOTAL_HEAP_SIZE                    65536
#define configMAX_TASK_NAME_LEN                  16
#define configUSE_16_BIT_TICKS                   0
#define configIDLE_SHOULD_YIELD                  1
#define configUSE_MUTEXES                        1
#define configUSE_RECURSIVE_MUTEXES              1
#define configUSE_COUNTING_SEMAPHORES            1
#define configUSE_QUEUE_SETS                     1
#define configUSE_MESSAGE_BUFFERS                1
```

### **编译和烧录**
```makefile
# Makefile配置
TARGET = STM32_3S_Video_System
DEBUG = 1
OPT = -Og

# 源文件
C_SOURCES = \
Core/Src/main.c \
Core/Src/camera.c \
Core/Src/l610.c \
Core/Src/video_segment.c \
Core/Src/iot_command.c \
Core/Src/system_controller.c \
Middlewares/Third_Party/FreeRTOS/Source/croutine.c \
Middlewares/Third_Party/FreeRTOS/Source/event_groups.c \
# ... 其他源文件

# 编译选项
CFLAGS = -mcpu=cortex-m7 -mthumb -mfpu=fpv5-d16 -mfloat-abi=hard
CFLAGS += -DUSE_HAL_DRIVER -DSTM32H743xx
CFLAGS += -Wall -fdata-sections -ffunction-sections

# 链接选项
LDFLAGS = -mcpu=cortex-m7 -mthumb -mfpu=fpv5-d16 -mfloat-abi=hard
LDFLAGS += -specs=nano.specs -T$(LDSCRIPT) -Wl,--gc-sections
```

## 🎉 STM32设备端方案总结

### **✅ 技术优势**
- **嵌入式优化**: 专为STM32单片机设计
- **实时性能**: 硬件级视频采集和处理
- **低功耗设计**: 智能电源管理
- **高可靠性**: 无操作系统依赖，稳定运行

### **📊 性能指标**
- **录制延时**: 0秒 (DCMI连续采集)
- **压缩时间**: 硬件JPEG编码，<1秒
- **上传时间**: 2秒 (0.6MB @ 3Mbps)
- **总响应**: 7秒首次响应
- **功耗**: 工作电流500mA@5V

### **🔧 关键技术特点**
- **DCMI硬件采集**: 无CPU占用的视频采集
- **FreeRTOS多任务**: 录制、压缩、上传并行处理
- **循环缓冲管理**: 20个片段循环存储
- **L610 4G通信**: 稳定的网络连接
- **智能电源管理**: 低电量自动省电

### **🎯 开发任务分解**

#### **第1阶段: 硬件驱动开发 (2周)**
- [ ] STM32H7基础工程搭建
- [ ] DCMI摄像头驱动开发
- [ ] L610 4G模块驱动开发
- [ ] 存储系统(SD卡/外部Flash)驱动

#### **第2阶段: 视频处理模块 (2周)**
- [ ] JPEG压缩算法集成
- [ ] 3秒片段录制逻辑
- [ ] 视频质量自适应算法
- [ ] 存储空间管理

#### **第3阶段: 通信模块 (2周)**
- [ ] OBS HTTP上传协议
- [ ] 华为云IoT命令接收
- [ ] 网络状态监控
- [ ] 错误处理和重连机制

#### **第4阶段: 系统集成 (1周)**
- [ ] FreeRTOS任务调度优化
- [ ] 电源管理系统
- [ ] 系统监控和诊断
- [ ] 完整功能测试

#### **第5阶段: 优化和测试 (1周)**
- [ ] 性能调优
- [ ] 稳定性测试
- [ ] 功耗优化
- [ ] 生产版本固件

### **💡 技术创新点**
1. **硬件级视频流水线**: DCMI → JPEG → 4G上传
2. **零延时启动**: 持续录制，即时响应
3. **智能资源管理**: 动态调整质量和功耗
4. **嵌入式实时系统**: 无OS开销，高效稳定

### **🔌 硬件接口定义**
```
STM32H743VIT6 引脚分配:
- DCMI: PE0-PE6, PA4-PA6 (摄像头接口)
- UART3: PB10-PB11 (L610通信)
- UART1: PA9-PA10 (调试串口)
- SDIO: PC8-PC12, PD2 (SD卡)
- SPI1: PA5-PA7 (外部Flash)
- ADC1: PA0 (电池电压监控)
- GPIO: PB0-PB2 (LED状态指示)
- GPIO: PC13-PC15 (L610控制)
```

这个基于STM32的设备端方案将提供工业级的稳定性和实时性能，完美支持3秒超高频视频方案！🌟
