package com.example.iotandroidv20.test

import android.content.Context
import android.content.pm.PackageManager
import androidx.core.content.ContextCompat
import com.example.iotandroidv20.obs.ParentVoiceInteractionManager
import com.example.iotandroidv20.obs.InteractionStatus
import com.example.iotandroidv20.utils.Logger
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.first
import java.io.File

/**
 * 语音模块测试助手
 * 用于验证语音交互功能的各个环节
 */
class VoiceModuleTestHelper {
    
    companion object {
        private const val TAG = "VoiceModuleTestHelper"
    }
    
    private val parentVoiceManager = ParentVoiceInteractionManager.getInstance()
    
    /**
     * 执行完整的语音模块测试
     */
    suspend fun runCompleteVoiceTest(context: Context): VoiceTestReport {
        Logger.d("🧪 [测试] 开始完整的语音模块测试", tag = TAG)
        
        val report = VoiceTestReport()
        
        try {
            // 测试1: 初始状态检查
            report.initialStateTest = testInitialState()
            
            // 测试2: 录音启动测试
            report.recordingStartTest = testRecordingStart(context)
            
            // 测试3: 录音状态检查
            val startTest = report.recordingStartTest
            if (startTest?.success == true) {
                report.recordingStateTest = testRecordingState()

                // 等待一段时间模拟录音
                delay(3000)

                // 测试4: 录音停止和发送测试
                report.recordingStopTest = testRecordingStop(context)
            }
            
            // 测试5: 状态描述测试
            report.statusDescriptionTest = testStatusDescription()
            
            // 测试6: 文件系统测试
            report.fileSystemTest = testFileSystem(context)
            
            // 测试7: 权限检查测试
            report.permissionTest = testPermissions(context)
            
            Logger.d("🎯 [测试] 语音模块测试完成", tag = TAG)
            
        } catch (e: Exception) {
            Logger.e("❌ [测试] 语音模块测试异常: ${e.message}", tag = TAG)
            report.overallError = e.message
        }
        
        return report
    }
    
    /**
     * 测试初始状态
     */
    private suspend fun testInitialState(): TestResult {
        return try {
            Logger.d("🔍 [测试] 检查初始状态", tag = TAG)
            
            val isRecording = parentVoiceManager.isRecording.first()
            val status = parentVoiceManager.interactionStatus.first()
            
            val success = !isRecording && status == InteractionStatus.IDLE
            
            TestResult(
                success = success,
                message = if (success) "初始状态正常" else "初始状态异常",
                details = "录音状态: $isRecording, 交互状态: $status"
            )
        } catch (e: Exception) {
            TestResult(false, "初始状态检查失败", "异常: ${e.message}")
        }
    }
    
    /**
     * 测试录音启动
     */
    private suspend fun testRecordingStart(context: Context): TestResult {
        return try {
            Logger.d("🎙️ [测试] 测试录音启动", tag = TAG)
            
            val success = parentVoiceManager.startRecording(context)
            
            // 检查状态是否正确更新
            delay(500) // 等待状态更新
            val isRecording = parentVoiceManager.isRecording.first()
            val status = parentVoiceManager.interactionStatus.first()
            
            val finalSuccess = success && isRecording && status == InteractionStatus.RECORDING
            
            TestResult(
                success = finalSuccess,
                message = if (finalSuccess) "录音启动成功" else "录音启动失败",
                details = "启动结果: $success, 录音状态: $isRecording, 交互状态: $status"
            )
        } catch (e: Exception) {
            TestResult(false, "录音启动测试失败", "异常: ${e.message}")
        }
    }
    
    /**
     * 测试录音状态
     */
    private suspend fun testRecordingState(): TestResult {
        return try {
            Logger.d("📊 [测试] 检查录音状态", tag = TAG)
            
            val isRecording = parentVoiceManager.isRecording.first()
            val status = parentVoiceManager.interactionStatus.first()
            val statusDescription = parentVoiceManager.getStatusDescription()
            
            val success = isRecording && status == InteractionStatus.RECORDING
            
            TestResult(
                success = success,
                message = if (success) "录音状态正常" else "录音状态异常",
                details = "录音状态: $isRecording, 交互状态: $status, 描述: $statusDescription"
            )
        } catch (e: Exception) {
            TestResult(false, "录音状态检查失败", "异常: ${e.message}")
        }
    }
    
    /**
     * 测试录音停止
     */
    private suspend fun testRecordingStop(context: Context): TestResult {
        return try {
            Logger.d("🛑 [测试] 测试录音停止", tag = TAG)
            
            val success = parentVoiceManager.stopRecordingAndSend(context)
            
            // 等待状态更新
            delay(1000)
            val isRecording = parentVoiceManager.isRecording.first()
            val status = parentVoiceManager.interactionStatus.first()
            
            TestResult(
                success = success,
                message = if (success) "录音停止成功" else "录音停止失败",
                details = "停止结果: $success, 录音状态: $isRecording, 交互状态: $status"
            )
        } catch (e: Exception) {
            TestResult(false, "录音停止测试失败", "异常: ${e.message}")
        }
    }
    
    /**
     * 测试状态描述
     */
    private suspend fun testStatusDescription(): TestResult {
        return try {
            Logger.d("📝 [测试] 测试状态描述", tag = TAG)
            
            val description = parentVoiceManager.getStatusDescription()
            val success = description.isNotEmpty()
            
            TestResult(
                success = success,
                message = if (success) "状态描述正常" else "状态描述异常",
                details = "状态描述: $description"
            )
        } catch (e: Exception) {
            TestResult(false, "状态描述测试失败", "异常: ${e.message}")
        }
    }
    
    /**
     * 测试文件系统
     */
    private suspend fun testFileSystem(context: Context): TestResult {
        return try {
            Logger.d("📁 [测试] 测试文件系统", tag = TAG)
            
            val voiceDir = File(context.getExternalFilesDir(null), "voice")
            val canCreateDir = voiceDir.exists() || voiceDir.mkdirs()
            val canWrite = voiceDir.canWrite()
            
            val success = canCreateDir && canWrite
            
            TestResult(
                success = success,
                message = if (success) "文件系统正常" else "文件系统异常",
                details = "目录存在: ${voiceDir.exists()}, 可写: $canWrite, 路径: ${voiceDir.absolutePath}"
            )
        } catch (e: Exception) {
            TestResult(false, "文件系统测试失败", "异常: ${e.message}")
        }
    }
    
    /**
     * 测试权限
     */
    private suspend fun testPermissions(context: Context): TestResult {
        return try {
            Logger.d("🔐 [测试] 测试权限", tag = TAG)

            val recordPermission = ContextCompat.checkSelfPermission(
                context,
                android.Manifest.permission.RECORD_AUDIO
            ) == PackageManager.PERMISSION_GRANTED

            val storagePermission = ContextCompat.checkSelfPermission(
                context,
                android.Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED

            val success = recordPermission && storagePermission

            Logger.d("🔐 [测试] 权限状态 - 录音: $recordPermission, 存储: $storagePermission", tag = TAG)

            TestResult(
                success = success,
                message = if (success) "权限检查通过" else "权限检查失败",
                details = "录音权限: ${if (recordPermission) "已授予" else "未授予"}, 存储权限: ${if (storagePermission) "已授予" else "未授予"}"
            )
        } catch (e: Exception) {
            TestResult(false, "权限测试失败", "异常: ${e.message}")
        }
    }
    
    /**
     * 生成测试报告
     */
    fun generateTestReport(report: VoiceTestReport): String {
        return buildString {
            appendLine("=== 语音模块测试报告 ===")
            appendLine("测试时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date())}")
            appendLine()
            
            appendLine("📊 测试结果概览:")
            val tests = listOf(
                "初始状态" to report.initialStateTest,
                "录音启动" to report.recordingStartTest,
                "录音状态" to report.recordingStateTest,
                "录音停止" to report.recordingStopTest,
                "状态描述" to report.statusDescriptionTest,
                "文件系统" to report.fileSystemTest,
                "权限检查" to report.permissionTest
            )
            
            var passedCount = 0
            tests.forEach { (name, result) ->
                val status = if (result?.success == true) {
                    passedCount++
                    "✅"
                } else "❌"
                appendLine("  $status $name: ${result?.message ?: "未执行"}")
            }
            
            appendLine()
            appendLine("📈 通过率: $passedCount/${tests.size} (${(passedCount * 100 / tests.size)}%)")
            
            if (report.overallError != null) {
                appendLine()
                appendLine("❌ 整体错误: ${report.overallError}")
            }
            
            appendLine()
            appendLine("📋 详细信息:")
            tests.forEach { (name, result) ->
                if (result != null) {
                    appendLine("  $name: ${result.details}")
                }
            }
        }
    }
}

/**
 * 测试结果
 */
data class TestResult(
    val success: Boolean,
    val message: String,
    val details: String
)

/**
 * 语音测试报告
 */
data class VoiceTestReport(
    var initialStateTest: TestResult? = null,
    var recordingStartTest: TestResult? = null,
    var recordingStateTest: TestResult? = null,
    var recordingStopTest: TestResult? = null,
    var statusDescriptionTest: TestResult? = null,
    var fileSystemTest: TestResult? = null,
    var permissionTest: TestResult? = null,
    var overallError: String? = null
)
