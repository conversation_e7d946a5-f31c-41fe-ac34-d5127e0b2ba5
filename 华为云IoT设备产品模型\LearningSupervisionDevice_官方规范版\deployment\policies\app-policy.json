{"Version": "1.1", "Statement": [{"Effect": "Allow", "Action": ["obs:object:GetObject", "obs:object:GetObjectAcl", "obs:object:GetObjectMetadata"], "Resource": ["obs:*:*:object:learning-supervision-obs/video/*/processed/*", "obs:*:*:object:learning-supervision-obs/audio/*/processed/*"], "Condition": {"StringLike": {"obs:object-key": ["video/*/processed/*", "audio/*/processed/*"]}}}, {"Effect": "Allow", "Action": ["obs:bucket:ListBucket", "obs:bucket:GetBucketLocation"], "Resource": ["obs:*:*:bucket:learning-supervision-obs"], "Condition": {"StringLike": {"obs:prefix": ["video/*/processed/*", "audio/*/processed/*"]}}}, {"Effect": "Allow", "Action": ["iotda:device:queryDevice", "iotda:device:queryDeviceData"], "Resource": ["iotda:*:*:device:*"]}]}