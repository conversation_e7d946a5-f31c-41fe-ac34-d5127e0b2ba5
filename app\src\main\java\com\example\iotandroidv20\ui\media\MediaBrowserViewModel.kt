package com.example.iotandroidv20.ui.media

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.iotandroidv20.obs.AudioSession
import com.example.iotandroidv20.obs.ObsConfig
import com.example.iotandroidv20.obs.VideoSession
import com.example.iotandroidv20.repository.MediaRepository
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.text.SimpleDateFormat
import java.util.*

/**
 * 媒体浏览器界面的ViewModel
 */
class MediaBrowserViewModel : ViewModel() {
    
    private val mediaRepository = MediaRepository.getInstance()
    private val obsConfig = ObsConfig.getInstance()
    
    private val _uiState = MutableStateFlow(MediaBrowserUiState())
    val uiState: StateFlow<MediaBrowserUiState> = _uiState.asStateFlow()
    
    /**
     * 初始化
     */
    fun initialize(context: Context) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = "")
                
                // 初始化OBS配置
                obsConfig.initialize(context)
                
                // 检查配置是否完整
                val isConfigured = obsConfig.isConfigured()
                _uiState.value = _uiState.value.copy(isConfigured = isConfigured)
                
                if (!isConfigured) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = ""
                    )
                    return@launch
                }
                
                // 初始化OBS管理器
                com.example.iotandroidv20.obs.ObsManager.getInstance().initialize(
                    obsConfig.getAccessKey(),
                    obsConfig.getSecretKey()
                )
                
                // 加载数据
                loadMediaData()
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "初始化失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 刷新数据
     */
    fun refreshData() {
        if (_uiState.value.isConfigured) {
            loadMediaData(forceRefresh = true)
        }
    }
    
    /**
     * 选择设备
     */
    fun selectDevice(deviceId: String) {
        _uiState.value = _uiState.value.copy(selectedDevice = deviceId)
        loadDeviceData(deviceId)
    }
    
    /**
     * 显示视频列表
     */
    fun showVideoList() {
        // TODO: 导航到视频列表界面
    }
    
    /**
     * 显示音频列表
     */
    fun showAudioList() {
        // TODO: 导航到音频列表界面
    }
    
    /**
     * 显示媒体列表
     */
    fun showMediaList() {
        // TODO: 导航到媒体列表界面
    }
    
    /**
     * 加载媒体数据
     */
    private fun loadMediaData(forceRefresh: Boolean = false) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = "")
                
                // 获取可用设备列表
                val availableDevices = getAvailableDevices()
                _uiState.value = _uiState.value.copy(availableDevices = availableDevices)
                
                if (availableDevices.isEmpty()) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "没有找到可用的设备"
                    )
                    return@launch
                }
                
                // 获取最近几天的数据
                val recentDates = getRecentDates(7) // 最近7天
                val allVideos = mutableListOf<VideoSession>()
                val allAudios = mutableListOf<AudioSession>()
                
                // 并行加载所有设备和日期的数据
                val jobs = availableDevices.flatMap { deviceId ->
                    recentDates.map { date ->
                        kotlinx.coroutines.async {
                            val videos = mediaRepository.getVideoSessions(deviceId, date, forceRefresh)
                            val audios = mediaRepository.getAudioSessions(deviceId, date, forceRefresh)
                            videos to audios
                        }
                    }
                }
                
                jobs.forEach { deferred ->
                    val (videos, audios) = deferred.await()
                    allVideos.addAll(videos)
                    allAudios.addAll(audios)
                }
                
                // 按时间排序，取最新的
                val recentVideos = allVideos
                    .sortedByDescending { it.lastModified }
                    .take(10)
                
                val recentAudios = allAudios
                    .sortedByDescending { it.lastModified }
                    .take(10)
                
                // 计算总大小
                val totalSize = allVideos.sumOf { it.fileSize } + allAudios.sumOf { it.fileSize }
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    recentVideos = recentVideos,
                    recentAudios = recentAudios,
                    totalVideos = allVideos.size,
                    totalAudios = allAudios.size,
                    totalSize = totalSize,
                    error = ""
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "加载数据失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 加载指定设备的数据
     */
    private fun loadDeviceData(deviceId: String) {
        viewModelScope.launch {
            try {
                // 获取最近几天的数据
                val recentDates = getRecentDates(3) // 最近3天
                val deviceVideos = mutableListOf<VideoSession>()
                val deviceAudios = mutableListOf<AudioSession>()
                
                recentDates.forEach { date ->
                    val videos = mediaRepository.getVideoSessions(deviceId, date)
                    val audios = mediaRepository.getAudioSessions(deviceId, date)
                    deviceVideos.addAll(videos)
                    deviceAudios.addAll(audios)
                }
                
                // 更新UI状态，显示该设备的数据
                _uiState.value = _uiState.value.copy(
                    recentVideos = deviceVideos.sortedByDescending { it.lastModified }.take(10),
                    recentAudios = deviceAudios.sortedByDescending { it.lastModified }.take(10),
                    selectedDevice = deviceId
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "加载设备数据失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 获取可用设备列表
     */
    private suspend fun getAvailableDevices(): List<String> {
        return try {
            // 模拟设备列表，实际应该从API获取
            listOf("LS_DEVICE_001", "LS_DEVICE_002", "LS_DEVICE_003")
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    /**
     * 获取最近的日期列表
     */
    private fun getRecentDates(days: Int): List<String> {
        val dateFormat = SimpleDateFormat("yyyyMMdd", Locale.getDefault())
        val calendar = Calendar.getInstance()
        val dates = mutableListOf<String>()
        
        repeat(days) {
            dates.add(dateFormat.format(calendar.time))
            calendar.add(Calendar.DAY_OF_MONTH, -1)
        }
        
        return dates
    }
    
    /**
     * 清除错误
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = "")
    }
}

/**
 * 媒体浏览器界面的UI状态
 */
data class MediaBrowserUiState(
    val isLoading: Boolean = false,
    val isConfigured: Boolean = false,
    val error: String = "",
    val selectedDevice: String = "",
    val availableDevices: List<String> = emptyList(),
    val recentVideos: List<VideoSession> = emptyList(),
    val recentAudios: List<AudioSession> = emptyList(),
    val totalVideos: Int = 0,
    val totalAudios: Int = 0,
    val totalSize: Long = 0L
)
