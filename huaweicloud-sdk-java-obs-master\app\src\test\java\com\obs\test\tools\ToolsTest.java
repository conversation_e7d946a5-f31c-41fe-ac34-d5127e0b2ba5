/**
 * Copyright 2019 Huawei Technologies Co.,Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License.  You may obtain a copy of the
 * License at
 * 
 * http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software distributed
 * under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
 * CONDITIONS OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

package com.obs.test.tools;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import static org.junit.Assert.assertEquals;

import org.junit.Test;

public class ToolsTest {
    @Test
    public void test_properties_tools() throws FileNotFoundException, IllegalArgumentException, IOException {
        File file = new File("./app/src/test/resource/test_data_me.properties");
        
        PropertiesTools tools = PropertiesTools.getInstance(file);
        
        assertEquals("http://8.45.130.11", tools.getProperties("environment.1.endpoint"));
    }
}
