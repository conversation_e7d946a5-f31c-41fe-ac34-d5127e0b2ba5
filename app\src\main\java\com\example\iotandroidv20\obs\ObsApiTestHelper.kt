package com.example.iotandroidv20.obs

import android.content.Context
import com.example.iotandroidv20.utils.Logger
import kotlinx.coroutines.*
import java.io.File

/**
 * 华为云OBS API测试助手
 * 用于验证OBS功能的正确性
 */
class ObsApiTestHelper {
    
    private val obsApiClient = HuaweiObsApiClient.getInstance()
    
    companion object {
        private const val TAG = "ObsApiTestHelper"
    }
    
    /**
     * 测试所有OBS API功能
     */
    suspend fun runAllTests(context: Context): TestResults {
        val results = TestResults()
        
        Logger.d("开始OBS API功能测试", tag = TAG)
        
        try {
            // 1. 测试列举对象
            results.listObjectsTest = testListObjects()
            
            // 2. 测试上传对象
            results.putObjectTest = testPutObject(context)
            
            // 3. 测试获取对象元数据
            if (results.putObjectTest.success) {
                results.headObjectTest = testHeadObject(results.putObjectTest.objectKey!!)
            }
            
            // 4. 测试下载对象
            if (results.putObjectTest.success) {
                results.getObjectTest = testGetObject(results.putObjectTest.objectKey!!)
            }
            
            // 5. 测试删除对象
            if (results.putObjectTest.success) {
                results.deleteObjectTest = testDeleteObject(results.putObjectTest.objectKey!!)
            }
            
            Logger.d("OBS API功能测试完成", tag = TAG)
            
        } catch (e: Exception) {
            Logger.e("OBS API测试异常: ${e.message}", tag = TAG)
            results.overallError = e.message
        }
        
        return results
    }
    
    /**
     * 测试列举对象
     */
    private suspend fun testListObjects(): TestResult {
        return try {
            Logger.d("测试列举对象", tag = TAG)
            
            val result = obsApiClient.listObjects("", 10)
            
            when (result) {
                is ObsApiResult.Success -> {
                    val objects = result.data
                    Logger.d("列举对象成功: 找到 ${objects.size} 个对象", tag = TAG)
                    TestResult(true, "列举对象成功，找到 ${objects.size} 个对象")
                }
                is ObsApiResult.Error -> {
                    Logger.e("列举对象失败: ${result.message}", tag = TAG)
                    TestResult(false, "列举对象失败: ${result.message}")
                }
            }
        } catch (e: Exception) {
            Logger.e("列举对象测试异常: ${e.message}", tag = TAG)
            TestResult(false, "列举对象测试异常: ${e.message}")
        }
    }
    
    /**
     * 测试上传对象
     */
    private suspend fun testPutObject(context: Context): TestResult {
        return try {
            Logger.d("测试上传对象", tag = TAG)
            
            // 创建测试文件
            val testFile = createTestFile(context)
            val objectKey = "test/api_test_${System.currentTimeMillis()}.txt"
            
            val result = obsApiClient.putObject(objectKey, testFile, "text/plain")
            
            // 清理测试文件
            testFile.delete()
            
            when (result) {
                is ObsApiResult.Success -> {
                    Logger.d("上传对象成功: $objectKey", tag = TAG)
                    TestResult(true, "上传对象成功", objectKey)
                }
                is ObsApiResult.Error -> {
                    Logger.e("上传对象失败: ${result.message}", tag = TAG)
                    TestResult(false, "上传对象失败: ${result.message}")
                }
            }
        } catch (e: Exception) {
            Logger.e("上传对象测试异常: ${e.message}", tag = TAG)
            TestResult(false, "上传对象测试异常: ${e.message}")
        }
    }
    
    /**
     * 测试获取对象元数据
     */
    private suspend fun testHeadObject(objectKey: String): TestResult {
        return try {
            Logger.d("测试获取对象元数据: $objectKey", tag = TAG)
            
            val result = obsApiClient.headObject(objectKey)
            
            when (result) {
                is ObsApiResult.Success -> {
                    val metadata = result.data
                    Logger.d("获取对象元数据成功: 大小=${metadata.contentLength}", tag = TAG)
                    TestResult(true, "获取对象元数据成功，大小=${metadata.contentLength}字节")
                }
                is ObsApiResult.Error -> {
                    Logger.e("获取对象元数据失败: ${result.message}", tag = TAG)
                    TestResult(false, "获取对象元数据失败: ${result.message}")
                }
            }
        } catch (e: Exception) {
            Logger.e("获取对象元数据测试异常: ${e.message}", tag = TAG)
            TestResult(false, "获取对象元数据测试异常: ${e.message}")
        }
    }
    
    /**
     * 测试下载对象
     */
    private suspend fun testGetObject(objectKey: String): TestResult {
        return try {
            Logger.d("测试下载对象: $objectKey", tag = TAG)
            
            val result = obsApiClient.getObject(objectKey)
            
            when (result) {
                is ObsApiResult.Success -> {
                    val data = result.data
                    Logger.d("下载对象成功: 大小=${data.size}字节", tag = TAG)
                    TestResult(true, "下载对象成功，大小=${data.size}字节")
                }
                is ObsApiResult.Error -> {
                    Logger.e("下载对象失败: ${result.message}", tag = TAG)
                    TestResult(false, "下载对象失败: ${result.message}")
                }
            }
        } catch (e: Exception) {
            Logger.e("下载对象测试异常: ${e.message}", tag = TAG)
            TestResult(false, "下载对象测试异常: ${e.message}")
        }
    }
    
    /**
     * 测试删除对象
     */
    private suspend fun testDeleteObject(objectKey: String): TestResult {
        return try {
            Logger.d("测试删除对象: $objectKey", tag = TAG)
            
            val result = obsApiClient.deleteObject(objectKey)
            
            when (result) {
                is ObsApiResult.Success -> {
                    Logger.d("删除对象成功: $objectKey", tag = TAG)
                    TestResult(true, "删除对象成功")
                }
                is ObsApiResult.Error -> {
                    Logger.e("删除对象失败: ${result.message}", tag = TAG)
                    TestResult(false, "删除对象失败: ${result.message}")
                }
            }
        } catch (e: Exception) {
            Logger.e("删除对象测试异常: ${e.message}", tag = TAG)
            TestResult(false, "删除对象测试异常: ${e.message}")
        }
    }
    
    /**
     * 创建测试文件
     */
    private fun createTestFile(context: Context): File {
        val testFile = File(context.cacheDir, "obs_api_test.txt")
        val testContent = "这是OBS API测试文件\n创建时间: ${System.currentTimeMillis()}\n测试内容: Hello Huawei Cloud OBS!"
        testFile.writeText(testContent)
        return testFile
    }
    
    /**
     * 生成测试报告
     */
    fun generateTestReport(results: TestResults): String {
        val report = StringBuilder()
        report.appendLine("=== 华为云OBS API测试报告 ===")
        report.appendLine("测试时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date())}")
        report.appendLine()
        
        report.appendLine("1. 列举对象测试: ${if (results.listObjectsTest.success) "✅ 通过" else "❌ 失败"}")
        report.appendLine("   ${results.listObjectsTest.message}")
        report.appendLine()
        
        report.appendLine("2. 上传对象测试: ${if (results.putObjectTest.success) "✅ 通过" else "❌ 失败"}")
        report.appendLine("   ${results.putObjectTest.message}")
        report.appendLine()
        
        report.appendLine("3. 获取元数据测试: ${if (results.headObjectTest.success) "✅ 通过" else "❌ 失败"}")
        report.appendLine("   ${results.headObjectTest.message}")
        report.appendLine()
        
        report.appendLine("4. 下载对象测试: ${if (results.getObjectTest.success) "✅ 通过" else "❌ 失败"}")
        report.appendLine("   ${results.getObjectTest.message}")
        report.appendLine()
        
        report.appendLine("5. 删除对象测试: ${if (results.deleteObjectTest.success) "✅ 通过" else "❌ 失败"}")
        report.appendLine("   ${results.deleteObjectTest.message}")
        report.appendLine()
        
        val passedTests = listOf(
            results.listObjectsTest,
            results.putObjectTest,
            results.headObjectTest,
            results.getObjectTest,
            results.deleteObjectTest
        ).count { it.success }
        
        report.appendLine("总结: $passedTests/5 项测试通过")
        
        if (results.overallError != null) {
            report.appendLine("整体错误: ${results.overallError}")
        }
        
        return report.toString()
    }
}

// ==================== 数据类 ====================

/**
 * 测试结果
 */
data class TestResult(
    val success: Boolean,
    val message: String,
    val objectKey: String? = null
)

/**
 * 所有测试结果
 */
data class TestResults(
    var listObjectsTest: TestResult = TestResult(false, "未执行"),
    var putObjectTest: TestResult = TestResult(false, "未执行"),
    var headObjectTest: TestResult = TestResult(false, "未执行"),
    var getObjectTest: TestResult = TestResult(false, "未执行"),
    var deleteObjectTest: TestResult = TestResult(false, "未执行"),
    var overallError: String? = null
)
