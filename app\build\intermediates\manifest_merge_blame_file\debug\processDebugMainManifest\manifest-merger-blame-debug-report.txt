1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.iotandroidv20"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- 网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:8:5-76
14-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:8:22-73
15
16    <!-- MQTT服务权限 -->
17    <uses-permission android:name="android.permission.WAKE_LOCK" />
17-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:11:5-68
17-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:11:22-65
18    <uses-permission
18-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:12:5-13:38
19        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
19-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:12:22-78
20        android:maxSdkVersion="28" />
20-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:13:9-35
21
22    <permission
22-->[androidx.core:core:1.16.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
23        android:name="com.example.iotandroidv20.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
23-->[androidx.core:core:1.16.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
24        android:protectionLevel="signature" />
24-->[androidx.core:core:1.16.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
25
26    <uses-permission android:name="com.example.iotandroidv20.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
26-->[androidx.core:core:1.16.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
26-->[androidx.core:core:1.16.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
27
28    <application
28-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:15:5-41:19
29        android:allowBackup="true"
29-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:16:9-35
30        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
30-->[androidx.core:core:1.16.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
31        android:dataExtractionRules="@xml/data_extraction_rules"
31-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:17:9-65
32        android:debuggable="true"
33        android:extractNativeLibs="false"
34        android:fullBackupContent="@xml/backup_rules"
34-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:18:9-54
35        android:icon="@mipmap/ic_launcher"
35-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:19:9-43
36        android:label="@string/app_name"
36-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:20:9-41
37        android:networkSecurityConfig="@xml/network_security_config"
37-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:25:9-69
38        android:roundIcon="@mipmap/ic_launcher_round"
38-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:21:9-54
39        android:supportsRtl="true"
39-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:22:9-35
40        android:testOnly="true"
41        android:theme="@style/Theme.IotAndroidV20"
41-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:23:9-51
42        android:usesCleartextTraffic="true" >
42-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:24:9-44
43        <activity
43-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:27:9-37:20
44            android:name="com.example.iotandroidv20.MainActivity"
44-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:28:13-41
45            android:exported="true"
45-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:29:13-36
46            android:label="@string/app_name"
46-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:30:13-45
47            android:theme="@style/Theme.IotAndroidV20" >
47-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:31:13-55
48            <intent-filter>
48-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:32:13-36:29
49                <action android:name="android.intent.action.MAIN" />
49-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:33:17-69
49-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:33:25-66
50
51                <category android:name="android.intent.category.LAUNCHER" />
51-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:35:17-77
51-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:35:27-74
52            </intent-filter>
53        </activity>
54
55        <!-- MQTT服务 -->
56        <service android:name="org.eclipse.paho.android.service.MqttService" />
56-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:40:9-80
56-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:40:18-77
57
58        <activity
58-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\bd785d79675de8add6b4b0fb3fb2ccc7\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
59            android:name="androidx.compose.ui.tooling.PreviewActivity"
59-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\bd785d79675de8add6b4b0fb3fb2ccc7\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
60            android:exported="true" />
60-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\bd785d79675de8add6b4b0fb3fb2ccc7\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
61
62        <provider
62-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
63            android:name="androidx.startup.InitializationProvider"
63-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
64            android:authorities="com.example.iotandroidv20.androidx-startup"
64-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
65            android:exported="false" >
65-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
66            <meta-data
66-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
67                android:name="androidx.emoji2.text.EmojiCompatInitializer"
67-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
68                android:value="androidx.startup" />
68-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
69            <meta-data
69-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\0005777f67894f5f1d095ec8641c55b8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
70                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
70-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\0005777f67894f5f1d095ec8641c55b8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
71                android:value="androidx.startup" />
71-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\0005777f67894f5f1d095ec8641c55b8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
72            <meta-data
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
73                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
74                android:value="androidx.startup" />
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
75        </provider>
76
77        <activity
77-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\82f7be9abeb980aa60080eceafa53f63\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
78            android:name="androidx.activity.ComponentActivity"
78-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\82f7be9abeb980aa60080eceafa53f63\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
79            android:exported="true" />
79-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\82f7be9abeb980aa60080eceafa53f63\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
80
81        <receiver
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
82            android:name="androidx.profileinstaller.ProfileInstallReceiver"
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
83            android:directBootAware="false"
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
84            android:enabled="true"
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
85            android:exported="true"
85-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
86            android:permission="android.permission.DUMP" >
86-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
87            <intent-filter>
87-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
88                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
88-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
88-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
89            </intent-filter>
90            <intent-filter>
90-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
91                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
91-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
91-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
92            </intent-filter>
93            <intent-filter>
93-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
94                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
94-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
94-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
95            </intent-filter>
96            <intent-filter>
96-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
97                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
97-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
97-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
98            </intent-filter>
99        </receiver>
100    </application>
101
102</manifest>
