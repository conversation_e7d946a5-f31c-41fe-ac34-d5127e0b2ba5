1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.iotandroidv20"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- 网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:8:5-76
14-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:8:22-73
15
16    <!-- 语音交互权限 -->
17    <uses-permission android:name="android.permission.RECORD_AUDIO" />
17-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:11:5-71
17-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:11:22-68
18    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
18-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:12:5-80
18-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:12:22-77
19
20    <!-- 存储权限 -->
21    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
21-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:15:5-81
21-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:15:22-78
22    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
22-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:16:5-80
22-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:16:22-77
23
24    <!-- 相机和媒体权限（用于视频功能） -->
25    <uses-permission android:name="android.permission.CAMERA" />
25-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:19:5-65
25-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:19:22-62
26    <uses-permission android:name="android.permission.RECORD_VIDEO" />
26-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:20:5-71
26-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:20:22-68
27
28    <!-- MQTT服务权限 -->
29    <uses-permission android:name="android.permission.WAKE_LOCK" />
29-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:23:5-68
29-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:23:22-65
30
31    <!-- 设备状态权限 -->
32    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
32-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:26:5-79
32-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:26:22-76
33    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
33-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:27:5-81
33-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:27:22-78
34
35    <!-- 硬件特性声明 -->
36    <uses-feature
36-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:30:5-32:35
37        android:name="android.hardware.microphone"
37-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:31:9-51
38        android:required="true" />
38-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:32:9-32
39    <uses-feature
39-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:33:5-35:36
40        android:name="android.hardware.camera"
40-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:34:9-47
41        android:required="false" />
41-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:35:9-33
42    <uses-feature
42-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:36:5-38:36
43        android:name="android.hardware.camera.autofocus"
43-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:37:9-57
44        android:required="false" />
44-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:38:9-33
45
46    <permission
46-->[androidx.core:core:1.16.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
47        android:name="com.example.iotandroidv20.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
47-->[androidx.core:core:1.16.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
48        android:protectionLevel="signature" />
48-->[androidx.core:core:1.16.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
49
50    <uses-permission android:name="com.example.iotandroidv20.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
50-->[androidx.core:core:1.16.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
50-->[androidx.core:core:1.16.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
51
52    <application
52-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:40:5-66:19
53        android:allowBackup="true"
53-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:41:9-35
54        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
54-->[androidx.core:core:1.16.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
55        android:dataExtractionRules="@xml/data_extraction_rules"
55-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:42:9-65
56        android:debuggable="true"
57        android:extractNativeLibs="false"
58        android:fullBackupContent="@xml/backup_rules"
58-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:43:9-54
59        android:icon="@mipmap/ic_launcher"
59-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:44:9-43
60        android:label="@string/app_name"
60-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:45:9-41
61        android:networkSecurityConfig="@xml/network_security_config"
61-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:50:9-69
62        android:roundIcon="@mipmap/ic_launcher_round"
62-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:46:9-54
63        android:supportsRtl="true"
63-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:47:9-35
64        android:testOnly="true"
65        android:theme="@style/Theme.IotAndroidV20"
65-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:48:9-51
66        android:usesCleartextTraffic="true" >
66-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:49:9-44
67        <activity
67-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:52:9-62:20
68            android:name="com.example.iotandroidv20.MainActivity"
68-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:53:13-41
69            android:exported="true"
69-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:54:13-36
70            android:label="@string/app_name"
70-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:55:13-45
71            android:theme="@style/Theme.IotAndroidV20" >
71-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:56:13-55
72            <intent-filter>
72-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:57:13-61:29
73                <action android:name="android.intent.action.MAIN" />
73-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:58:17-69
73-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:58:25-66
74
75                <category android:name="android.intent.category.LAUNCHER" />
75-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:60:17-77
75-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:60:27-74
76            </intent-filter>
77        </activity>
78
79        <!-- MQTT服务 -->
80        <service android:name="org.eclipse.paho.android.service.MqttService" />
80-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:65:9-80
80-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:65:18-77
81
82        <activity
82-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\bd785d79675de8add6b4b0fb3fb2ccc7\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
83            android:name="androidx.compose.ui.tooling.PreviewActivity"
83-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\bd785d79675de8add6b4b0fb3fb2ccc7\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
84            android:exported="true" />
84-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\bd785d79675de8add6b4b0fb3fb2ccc7\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
85
86        <provider
86-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
87            android:name="androidx.startup.InitializationProvider"
87-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
88            android:authorities="com.example.iotandroidv20.androidx-startup"
88-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
89            android:exported="false" >
89-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
90            <meta-data
90-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
91                android:name="androidx.emoji2.text.EmojiCompatInitializer"
91-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
92                android:value="androidx.startup" />
92-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
93            <meta-data
93-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\0005777f67894f5f1d095ec8641c55b8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
94                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
94-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\0005777f67894f5f1d095ec8641c55b8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
95                android:value="androidx.startup" />
95-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\0005777f67894f5f1d095ec8641c55b8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
96            <meta-data
96-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
97                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
97-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
98                android:value="androidx.startup" />
98-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
99        </provider>
100
101        <activity
101-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\82f7be9abeb980aa60080eceafa53f63\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
102            android:name="androidx.activity.ComponentActivity"
102-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\82f7be9abeb980aa60080eceafa53f63\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
103            android:exported="true" />
103-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\82f7be9abeb980aa60080eceafa53f63\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
104
105        <receiver
105-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
106            android:name="androidx.profileinstaller.ProfileInstallReceiver"
106-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
107            android:directBootAware="false"
107-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
108            android:enabled="true"
108-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
109            android:exported="true"
109-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
110            android:permission="android.permission.DUMP" >
110-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
111            <intent-filter>
111-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
112                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
112-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
112-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
113            </intent-filter>
114            <intent-filter>
114-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
115                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
115-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
115-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
116            </intent-filter>
117            <intent-filter>
117-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
118                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
118-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
118-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
119            </intent-filter>
120            <intent-filter>
120-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
121                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
121-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
121-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\AndroidStudioProjects\IotAndroidv10\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
122            </intent-filter>
123        </receiver>
124    </application>
125
126</manifest>
