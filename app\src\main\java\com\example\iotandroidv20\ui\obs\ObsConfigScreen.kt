package com.example.iotandroidv20.ui.obs

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.iotandroidv20.obs.ObsConfig

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ObsConfigScreen(
    onNavigateBack: () -> Unit,
    viewModel: ObsConfigViewModel = viewModel()
) {
    val context = LocalContext.current
    val uiState by viewModel.uiState.collectAsState()
    
    // 初始化ObsConfig
    LaunchedEffect(Unit) {
        ObsConfig.getInstance().initialize(context)
        viewModel.loadConfig()
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("OBS配置") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 配置说明
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "华为云OBS配置",
                        style = MaterialTheme.typography.titleMedium
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "请配置华为云对象存储服务的访问凭据，用于获取设备上传的音视频文件。",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            // 访问密钥配置
            OutlinedTextField(
                value = uiState.accessKey,
                onValueChange = viewModel::updateAccessKey,
                label = { Text("Access Key") },
                placeholder = { Text("请输入华为云Access Key") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                isError = uiState.accessKeyError.isNotEmpty(),
                supportingText = if (uiState.accessKeyError.isNotEmpty()) {
                    { Text(uiState.accessKeyError) }
                } else null
            )
            
            // 密钥配置
            var secretKeyVisible by remember { mutableStateOf(false) }
            OutlinedTextField(
                value = uiState.secretKey,
                onValueChange = viewModel::updateSecretKey,
                label = { Text("Secret Key") },
                placeholder = { Text("请输入华为云Secret Key") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                visualTransformation = if (secretKeyVisible) VisualTransformation.None else PasswordVisualTransformation(),
                trailingIcon = {
                    IconButton(onClick = { secretKeyVisible = !secretKeyVisible }) {
                        Icon(
                            imageVector = if (secretKeyVisible) Icons.Default.VisibilityOff else Icons.Default.Visibility,
                            contentDescription = if (secretKeyVisible) "隐藏密钥" else "显示密钥"
                        )
                    }
                },
                isError = uiState.secretKeyError.isNotEmpty(),
                supportingText = if (uiState.secretKeyError.isNotEmpty()) {
                    { Text(uiState.secretKeyError) }
                } else null
            )
            
            // 端点配置
            OutlinedTextField(
                value = uiState.endpoint,
                onValueChange = viewModel::updateEndpoint,
                label = { Text("端点地址") },
                placeholder = { Text("https://obs.cn-north-4.myhuaweicloud.com") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                isError = uiState.endpointError.isNotEmpty(),
                supportingText = if (uiState.endpointError.isNotEmpty()) {
                    { Text(uiState.endpointError) }
                } else null
            )
            
            // 存储桶配置
            OutlinedTextField(
                value = uiState.bucketName,
                onValueChange = viewModel::updateBucketName,
                label = { Text("存储桶名称") },
                placeholder = { Text("learning-supervision-obs") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                isError = uiState.bucketNameError.isNotEmpty(),
                supportingText = if (uiState.bucketNameError.isNotEmpty()) {
                    { Text(uiState.bucketNameError) }
                } else null
            )
            
            // 区域配置
            OutlinedTextField(
                value = uiState.region,
                onValueChange = viewModel::updateRegion,
                label = { Text("区域") },
                placeholder = { Text("cn-north-4") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )
            
            // 操作按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // 测试连接按钮
                OutlinedButton(
                    onClick = viewModel::testConnection,
                    modifier = Modifier.weight(1f),
                    enabled = !uiState.isLoading
                ) {
                    if (uiState.isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                    }
                    Text("测试连接")
                }
                
                // 保存配置按钮
                Button(
                    onClick = viewModel::saveConfig,
                    modifier = Modifier.weight(1f),
                    enabled = !uiState.isLoading && uiState.isValid
                ) {
                    Text("保存配置")
                }
            }
            
            // 重置为默认配置按钮
            TextButton(
                onClick = viewModel::resetToDefault,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("重置为默认配置")
            }
            
            // 状态消息
            if (uiState.message.isNotEmpty()) {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = if (uiState.isError) {
                            MaterialTheme.colorScheme.errorContainer
                        } else {
                            MaterialTheme.colorScheme.primaryContainer
                        }
                    )
                ) {
                    Text(
                        text = uiState.message,
                        modifier = Modifier.padding(16.dp),
                        color = if (uiState.isError) {
                            MaterialTheme.colorScheme.onErrorContainer
                        } else {
                            MaterialTheme.colorScheme.onPrimaryContainer
                        }
                    )
                }
            }
            
            // 配置状态
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "配置状态",
                        style = MaterialTheme.typography.titleSmall
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        val statusColor = if (uiState.isConfigured) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.error
                        }
                        
                        Box(
                            modifier = Modifier
                                .size(8.dp)
                                .background(
                                    color = statusColor,
                                    shape = CircleShape
                                )
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = if (uiState.isConfigured) "已配置" else "未配置",
                            color = statusColor
                        )
                    }
                    
                    if (uiState.isConfigured) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "配置完成，可以使用OBS音视频功能",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }
    }
}
