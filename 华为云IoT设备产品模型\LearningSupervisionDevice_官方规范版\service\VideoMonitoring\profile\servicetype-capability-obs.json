{"services": [{"serviceId": "VideoMonitoring", "serviceType": "VideoMonitoring", "properties": [{"propertyName": "streamStatus", "dataType": "string", "required": true, "enumList": ["STREAMING", "STOPPED", "BUFFERING", "ERROR", "CONNECTING"], "min": null, "max": null, "maxLength": 15, "step": 0, "unit": null, "method": "R", "description": "实时视频流状态", "defaultValue": null}, {"propertyName": "streamUrl", "dataType": "string", "required": false, "enumList": null, "min": null, "max": null, "maxLength": 500, "step": 0, "unit": null, "method": "R", "description": "实时视频流地址", "defaultValue": null}, {"propertyName": "recordingStatus", "dataType": "string", "required": false, "enumList": ["RECORDING", "STOPPED", "PAUSED", "ERROR", "UPLOADING"], "min": null, "max": null, "maxLength": 15, "step": 0, "unit": null, "method": "R", "description": "录制状态", "defaultValue": null}, {"propertyName": "currentSegmentId", "dataType": "string", "required": false, "enumList": null, "min": null, "max": null, "maxLength": 100, "step": 0, "unit": null, "method": "R", "description": "当前录制分片ID", "defaultValue": null}, {"propertyName": "segmentDuration", "dataType": "int", "required": false, "enumList": null, "min": "5", "max": "60", "maxLength": 0, "step": 0, "unit": "seconds", "method": "RW", "description": "分片时长设置", "defaultValue": null}, {"propertyName": "uploadProgress", "dataType": "decimal", "required": false, "enumList": null, "min": "0.0", "max": "100.0", "maxLength": 0, "step": 0.1, "unit": "%", "method": "R", "description": "当前分片上传进度", "defaultValue": null}, {"propertyName": "storageUsed", "dataType": "decimal", "required": false, "enumList": null, "min": "0.0", "max": "1000.0", "maxLength": 0, "step": 0.1, "unit": "MB", "method": "R", "description": "本地存储使用量", "defaultValue": null}, {"propertyName": "encodingFormat", "dataType": "string", "required": false, "enumList": ["H264", "H265", "MP4"], "min": null, "max": null, "maxLength": 10, "step": 0, "unit": null, "method": "RW", "description": "视频编码格式", "defaultValue": null}, {"propertyName": "resolution", "dataType": "string", "required": false, "enumList": ["640x480", "1280x720", "1920x1080"], "min": null, "max": null, "maxLength": 15, "step": 0, "unit": null, "method": "RW", "description": "视频分辨率", "defaultValue": null}, {"propertyName": "frameRate", "dataType": "int", "required": false, "enumList": null, "min": "15", "max": "30", "maxLength": 0, "step": 0, "unit": "fps", "method": "RW", "description": "视频帧率", "defaultValue": null}, {"propertyName": "bitrate", "dataType": "int", "required": false, "enumList": null, "min": "500", "max": "2000", "maxLength": 0, "step": 0, "unit": "kbps", "method": "RW", "description": "视频比特率", "defaultValue": null}, {"propertyName": "faceDetected", "dataType": "boolean", "required": false, "enumList": null, "min": null, "max": null, "maxLength": 0, "step": 0, "unit": null, "method": "R", "description": "是否检测到人脸", "defaultValue": null}, {"propertyName": "faceBboxX", "dataType": "int", "required": false, "enumList": null, "min": "0", "max": "1920", "maxLength": 0, "step": 0, "unit": "pixel", "method": "R", "description": "人脸边界框X坐标", "defaultValue": null}, {"propertyName": "faceBboxY", "dataType": "int", "required": false, "enumList": null, "min": "0", "max": "1080", "maxLength": 0, "step": 0, "unit": "pixel", "method": "R", "description": "人脸边界框Y坐标", "defaultValue": null}, {"propertyName": "networkQuality", "dataType": "string", "required": false, "enumList": ["EXCELLENT", "GOOD", "FAIR", "POOR"], "min": null, "max": null, "maxLength": 10, "step": 0, "unit": null, "method": "R", "description": "网络质量状态", "defaultValue": null}, {"propertyName": "lastUploadTime", "dataType": "DateTime", "required": false, "enumList": null, "min": null, "max": null, "maxLength": 20, "step": 0, "unit": null, "method": "R", "description": "最后上传时间", "defaultValue": null}], "commands": [{"commandName": "START_VIDEO_RECORDING", "paras": [{"paraName": "duration", "dataType": "int", "required": false, "enumList": null, "min": "60", "max": "3600", "maxLength": 0, "step": 0, "unit": "seconds", "description": "录制时长"}, {"paraName": "segmentDuration", "dataType": "int", "required": false, "enumList": null, "min": "5", "max": "60", "maxLength": 0, "step": 0, "unit": "seconds", "description": "分片时长"}, {"paraName": "quality", "dataType": "string", "required": false, "enumList": ["HIGH", "MEDIUM", "LOW"], "min": null, "max": null, "maxLength": 10, "step": 0, "unit": null, "description": "录制质量"}], "responses": [{"responseName": "cmdResponses", "paras": [{"paraName": "result", "dataType": "string", "required": true, "enumList": ["success", "failed"], "min": null, "max": null, "maxLength": 10, "step": 0, "unit": null, "description": "录制启动结果"}, {"paraName": "sessionId", "dataType": "string", "required": false, "enumList": null, "min": null, "max": null, "maxLength": 50, "step": 0, "unit": null, "description": "录制会话ID"}]}]}, {"commandName": "STOP_VIDEO_RECORDING", "paras": [], "responses": [{"responseName": "cmdResponses", "paras": [{"paraName": "result", "dataType": "string", "required": true, "enumList": ["success", "failed"], "min": null, "max": null, "maxLength": 10, "step": 0, "unit": null, "description": "录制停止结果"}]}]}, {"commandName": "REQUEST_OBS_UPLOAD_URL", "paras": [{"paraName": "fileName", "dataType": "string", "required": true, "enumList": null, "min": null, "max": null, "maxLength": 200, "step": 0, "unit": null, "description": "上传文件名"}, {"paraName": "fileSize", "dataType": "int", "required": true, "enumList": null, "min": "1", "max": "104857600", "maxLength": 0, "step": 0, "unit": "bytes", "description": "文件大小"}], "responses": [{"responseName": "cmdResponses", "paras": [{"paraName": "result", "dataType": "string", "required": true, "enumList": ["success", "failed"], "min": null, "max": null, "maxLength": 10, "step": 0, "unit": null, "description": "URL请求结果"}, {"paraName": "uploadUrl", "dataType": "string", "required": false, "enumList": null, "min": null, "max": null, "maxLength": 1000, "step": 0, "unit": null, "description": "OBS预签名上传URL"}, {"paraName": "expireTime", "dataType": "DateTime", "required": false, "enumList": null, "min": null, "max": null, "maxLength": 20, "step": 0, "unit": null, "description": "URL过期时间"}]}]}], "events": [{"eventType": "video_segment_uploaded", "paras": [{"paraName": "segmentId", "dataType": "string", "required": true, "enumList": null, "min": null, "max": null, "maxLength": 100, "step": 0, "unit": null, "description": "分片ID"}, {"paraName": "uploadStatus", "dataType": "string", "required": true, "enumList": ["SUCCESS", "FAILED", "RETRY"], "min": null, "max": null, "maxLength": 10, "step": 0, "unit": null, "description": "上传状态"}, {"paraName": "fileSize", "dataType": "int", "required": false, "enumList": null, "min": "0", "max": "104857600", "maxLength": 0, "step": 0, "unit": "bytes", "description": "文件大小"}]}, {"eventType": "recording_session_complete", "paras": [{"paraName": "sessionId", "dataType": "string", "required": true, "enumList": null, "min": null, "max": null, "maxLength": 50, "step": 0, "unit": null, "description": "录制会话ID"}, {"paraName": "totalSegments", "dataType": "int", "required": true, "enumList": null, "min": "1", "max": "1000", "maxLength": 0, "step": 0, "unit": "count", "description": "总分片数量"}, {"paraName": "totalDuration", "dataType": "int", "required": false, "enumList": null, "min": "1", "max": "3600", "maxLength": 0, "step": 0, "unit": "seconds", "description": "总录制时长"}]}], "description": "视频监控服务 - 支持实时流和OBS分片上传，设备端负责采集编码，应用端负责播放分析", "option": "Optional"}]}