{"logs": [{"outputFile": "com.example.iotandroidv20.app-mergeReleaseResources-58:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5dca3f3744b4fa46ea145e6d4fc9225c\\transformed\\media3-exoplayer-1.2.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,246,308,390,471,572,667", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "115,174,241,303,385,466,567,662,746"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4614,4679,4738,4805,4867,4949,5030,5131,5226", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "4674,4733,4800,4862,4944,5025,5126,5221,5305"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\398f61ea23c76807315a7831064b1215\\transformed\\material3-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,290,400,516,614,719,842,979,1100,1243,1330,1435,1527,1627,1745,1871,1981,2127,2271,2408,2560,2686,2806,2929,3047,3140,3238,3361,3485,3585,3688,3796,3941,4091,4198,4300,4380,4474,4567,4684,4773,4858,4958,5037,5121,5222,5325,5424,5522,5609,5715,5815,5915,6044,6123,6224", "endColumns": "117,116,109,115,97,104,122,136,120,142,86,104,91,99,117,125,109,145,143,136,151,125,119,122,117,92,97,122,123,99,102,107,144,149,106,101,79,93,92,116,88,84,99,78,83,100,102,98,97,86,105,99,99,128,78,100,92", "endOffsets": "168,285,395,511,609,714,837,974,1095,1238,1325,1430,1522,1622,1740,1866,1976,2122,2266,2403,2555,2681,2801,2924,3042,3135,3233,3356,3480,3580,3683,3791,3936,4086,4193,4295,4375,4469,4562,4679,4768,4853,4953,5032,5116,5217,5320,5419,5517,5604,5710,5810,5910,6039,6118,6219,6312"}, "to": {"startLines": "94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6621,6739,6856,6966,7082,7180,7285,7408,7545,7666,7809,7896,8001,8093,8193,8311,8437,8547,8693,8837,8974,9126,9252,9372,9495,9613,9706,9804,9927,10051,10151,10254,10362,10507,10657,10764,10866,10946,11040,11133,11250,11339,11424,11524,11603,11687,11788,11891,11990,12088,12175,12281,12381,12481,12610,12689,12790", "endColumns": "117,116,109,115,97,104,122,136,120,142,86,104,91,99,117,125,109,145,143,136,151,125,119,122,117,92,97,122,123,99,102,107,144,149,106,101,79,93,92,116,88,84,99,78,83,100,102,98,97,86,105,99,99,128,78,100,92", "endOffsets": "6734,6851,6961,7077,7175,7280,7403,7540,7661,7804,7891,7996,8088,8188,8306,8432,8542,8688,8832,8969,9121,9247,9367,9490,9608,9701,9799,9922,10046,10146,10249,10357,10502,10652,10759,10861,10941,11035,11128,11245,11334,11419,11519,11598,11682,11783,11886,11985,12083,12170,12276,12376,12476,12605,12684,12785,12878"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\01b6e8b8fea8bb45b55852d4590f3437\\transformed\\foundation-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,85", "endOffsets": "134,220"}, "to": {"startLines": "169,170", "startColumns": "4,4", "startOffsets": "14363,14447", "endColumns": "83,85", "endOffsets": "14442,14528"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\835843a1fca13b839a80c71cfb075e12\\transformed\\media3-session-1.2.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,123,202,275,342,414,489,582", "endColumns": "67,78,72,66,71,74,92,96", "endOffsets": "118,197,270,337,409,484,577,674"}, "to": {"startLines": "27,38,151,152,153,154,155,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1364,2411,12883,12956,13023,13095,13170,13263", "endColumns": "67,78,72,66,71,74,92,96", "endOffsets": "1427,2485,12951,13018,13090,13165,13258,13355"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2c60f935d2795008c652ca71073c0e75\\transformed\\media3-ui-1.2.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,11,19,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,349,849,1314,1393,1471,1547,1641,1733,1807,1872,1964,2054,2124,2188,2251,2320,2428,2537,2652,2718,2801,2873,2945,3037,3128,3192,3255,3308,3379,3434,3495,3553,3627,3691,3755,3815,3880,3944,4006,4072,4124,4181,4252,4323", "endLines": "10,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,107,108,114,65,82,71,71,91,90,63,62,52,70,54,60,57,73,63,63,59,64,63,61,65,51,56,70,70,55", "endOffsets": "344,844,1309,1388,1466,1542,1636,1728,1802,1867,1959,2049,2119,2183,2246,2315,2423,2532,2647,2713,2796,2868,2940,3032,3123,3187,3250,3303,3374,3429,3490,3548,3622,3686,3750,3810,3875,3939,4001,4067,4119,4176,4247,4318,4374"}, "to": {"startLines": "2,11,19,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,399,899,2673,2752,2830,2906,3000,3092,3166,3231,3323,3413,3483,3547,3610,3679,3787,3896,4011,4077,4160,4232,4304,4396,4487,4551,5310,5363,5434,5489,5550,5608,5682,5746,5810,5870,5935,5999,6061,6127,6179,6236,6307,6378", "endLines": "10,18,26,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,107,108,114,65,82,71,71,91,90,63,62,52,70,54,60,57,73,63,63,59,64,63,61,65,51,56,70,70,55", "endOffsets": "394,894,1359,2747,2825,2901,2995,3087,3161,3226,3318,3408,3478,3542,3605,3674,3782,3891,4006,4072,4155,4227,4299,4391,4482,4546,4609,5358,5429,5484,5545,5603,5677,5741,5805,5865,5930,5994,6056,6122,6174,6231,6302,6373,6429"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\31c73cb6dfb12faac49eb1bae4282a74\\transformed\\core-1.16.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "28,29,30,31,32,33,34,165", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1432,1525,1627,1722,1825,1928,2030,13991", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "1520,1622,1717,1820,1923,2025,2139,14087"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\752167a3e67da85263f648420d161268\\transformed\\ui-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,277,372,470,555,636,742,826,907,988,1071,1141,1220,1299,1373,1449,1523", "endColumns": "88,82,94,97,84,80,105,83,80,80,82,69,78,78,73,75,73,120", "endOffsets": "189,272,367,465,550,631,737,821,902,983,1066,1136,1215,1294,1368,1444,1518,1639"}, "to": {"startLines": "35,36,37,39,40,92,93,157,158,159,160,161,162,163,164,166,167,168", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2144,2233,2316,2490,2588,6434,6515,13360,13444,13525,13606,13689,13759,13838,13917,14092,14168,14242", "endColumns": "88,82,94,97,84,80,105,83,80,80,82,69,78,78,73,75,73,120", "endOffsets": "2228,2311,2406,2583,2668,6510,6616,13439,13520,13601,13684,13754,13833,13912,13986,14163,14237,14358"}}]}]}