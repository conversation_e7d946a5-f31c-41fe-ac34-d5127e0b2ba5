package com.example.iotandroidv20.obs

import android.content.Context
import android.content.pm.PackageManager
import android.media.MediaRecorder
import androidx.core.content.ContextCompat
import com.example.iotandroidv20.utils.Logger
import com.example.iotandroidv20.utils.PermissionHelper
import com.example.iotandroidv20.config.HuaweiCloudConfig
import com.example.iotandroidv20.iot.TokenIoTManager
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

/**
 * 家长语音互动管理器
 * 通过OBS服务实现家长与孩子的实时语音互动
 */
class ParentVoiceInteractionManager private constructor() {
    
    private val obsApiClient = HuaweiObsApiClient.getInstance()
    
    private val _interactionStatus = MutableStateFlow(InteractionStatus.IDLE)
    val interactionStatus: StateFlow<InteractionStatus> = _interactionStatus.asStateFlow()
    
    private val _isRecording = MutableStateFlow(false)
    val isRecording: StateFlow<Boolean> = _isRecording.asStateFlow()
    
    private val _isSending = MutableStateFlow(false)
    val isSending: StateFlow<Boolean> = _isSending.asStateFlow()
    
    private val _lastInteractionTime = MutableStateFlow(0L)
    val lastInteractionTime: StateFlow<Long> = _lastInteractionTime.asStateFlow()
    
    private val _interactionHistory = MutableStateFlow<List<VoiceInteraction>>(emptyList())
    val interactionHistory: StateFlow<List<VoiceInteraction>> = _interactionHistory.asStateFlow()
    
    private var mediaRecorder: MediaRecorder? = null
    private var currentRecordingFile: File? = null
    private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    companion object {
        private const val TAG = "ParentVoiceInteractionManager"
        private const val MAX_RECORDING_DURATION = 30000L // 30秒最大录音时长
        private const val AUDIO_SAMPLE_RATE = 44100
        private const val AUDIO_BIT_RATE = 128000
        
        @Volatile
        private var INSTANCE: ParentVoiceInteractionManager? = null
        
        fun getInstance(): ParentVoiceInteractionManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ParentVoiceInteractionManager().also { INSTANCE = it }
            }
        }
    }
    
    /**
     * 检查录音权限
     */
    private fun checkRecordingPermissions(context: Context): Boolean {
        val hasPermissions = PermissionHelper.hasAllVoicePermissions(context)

        if (!hasPermissions) {
            val report = PermissionHelper.getPermissionReport(context)
            Logger.w("🔐 [测试] 权限检查失败:\n$report", tag = TAG)
        }

        return hasPermissions
    }

    /**
     * 重置错误状态
     */
    private fun resetErrorState() {
        if (_interactionStatus.value == InteractionStatus.ERROR) {
            Logger.d("🔄 [测试] 重置错误状态", tag = TAG)
            _interactionStatus.value = InteractionStatus.IDLE
        }
    }

    /**
     * 开始录制家长语音消息
     */
    suspend fun startRecordingVoiceMessage(context: Context): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Logger.d("🎙️ [测试] 开始录制家长语音消息", tag = TAG)
                Logger.d("🔍 [测试] 当前录音状态: ${_isRecording.value}", tag = TAG)
                Logger.d("🔍 [测试] 当前交互状态: ${_interactionStatus.value}", tag = TAG)

                // 重置错误状态
                resetErrorState()

                if (_isRecording.value) {
                    Logger.w("⚠️ [测试] 已在录音中，忽略重复请求", tag = TAG)
                    return@withContext false
                }

                // 检查权限
                if (!checkRecordingPermissions(context)) {
                    Logger.e("❌ [测试] 录音权限未授予", tag = TAG)
                    _interactionStatus.value = InteractionStatus.ERROR
                    return@withContext false
                }
                
                Logger.d("🎵 [测试] 开始录制家长语音消息", tag = TAG)
                _interactionStatus.value = InteractionStatus.RECORDING
                _isRecording.value = true
                Logger.d("✅ [测试] 状态更新完成 - 录音中", tag = TAG)

                // 创建录音文件
                currentRecordingFile = createVoiceMessageFile(context)
                Logger.d("📁 [测试] 录音文件路径: ${currentRecordingFile?.absolutePath}", tag = TAG)
                
                // 初始化MediaRecorder - 使用更兼容的配置
                mediaRecorder = MediaRecorder().apply {
                    try {
                        Logger.d("🎵 [测试] 配置MediaRecorder", tag = TAG)

                        // 使用更兼容的音频源
                        setAudioSource(MediaRecorder.AudioSource.MIC)
                        Logger.d("✅ [测试] 音频源设置成功", tag = TAG)

                        // 使用更兼容的输出格式
                        setOutputFormat(MediaRecorder.OutputFormat.THREE_GPP)
                        Logger.d("✅ [测试] 输出格式设置成功", tag = TAG)

                        // 使用更兼容的编码器
                        setAudioEncoder(MediaRecorder.AudioEncoder.AMR_NB)
                        Logger.d("✅ [测试] 音频编码器设置成功", tag = TAG)

                        // 设置输出文件
                        setOutputFile(currentRecordingFile?.absolutePath)
                        Logger.d("✅ [测试] 输出文件设置成功", tag = TAG)

                        // 设置最大录音时长
                        setMaxDuration(MAX_RECORDING_DURATION.toInt())
                        Logger.d("✅ [测试] 最大时长设置成功", tag = TAG)
                    
                        setOnInfoListener { _, what, _ ->
                            if (what == MediaRecorder.MEDIA_RECORDER_INFO_MAX_DURATION_REACHED) {
                                Logger.d("达到最大录音时长，自动停止", tag = TAG)
                                coroutineScope.launch {
                                    stopRecordingVoiceMessage(context)
                                }
                            }
                        }

                        // 准备录音器
                        prepare()
                        Logger.d("✅ [测试] MediaRecorder准备成功", tag = TAG)

                        // 开始录音
                        start()
                        Logger.d("✅ [测试] MediaRecorder启动成功", tag = TAG)

                    } catch (e: Exception) {
                        Logger.e("❌ [测试] MediaRecorder配置失败: ${e.message}", tag = TAG)
                        throw e
                    }
                }
                
                Logger.d("语音录制已开始", tag = TAG)
                true
                
            } catch (e: Exception) {
                Logger.e("开始录音失败: ${e.message}", tag = TAG)
                _interactionStatus.value = InteractionStatus.ERROR
                _isRecording.value = false
                cleanupRecorder()
                false
            }
        }
    }
    
    /**
     * 停止录制并发送语音消息
     */
    suspend fun stopRecordingVoiceMessage(context: Context): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Logger.d("🛑 [测试] 停止录制语音消息", tag = TAG)
                Logger.d("🔍 [测试] 当前录音状态: ${_isRecording.value}", tag = TAG)

                if (!_isRecording.value) {
                    Logger.w("⚠️ [测试] 当前未在录音，忽略停止请求", tag = TAG)
                    return@withContext false
                }

                Logger.d("🎵 [测试] 停止录制语音消息", tag = TAG)
                _isRecording.value = false
                Logger.d("✅ [测试] 录音状态已更新为false", tag = TAG)
                
                // 停止录音
                mediaRecorder?.apply {
                    stop()
                    release()
                }
                mediaRecorder = null
                
                // 检查录音文件
                val recordingFile = currentRecordingFile
                if (recordingFile != null && recordingFile.exists() && recordingFile.length() > 0) {
                    // 发送语音消息到设备
                    sendVoiceMessageToDevice(context, recordingFile)
                } else {
                    Logger.e("录音文件无效", tag = TAG)
                    _interactionStatus.value = InteractionStatus.ERROR
                    false
                }
                
            } catch (e: Exception) {
                Logger.e("停止录音失败: ${e.message}", tag = TAG)
                _interactionStatus.value = InteractionStatus.ERROR
                cleanupRecorder()
                false
            }
        }
    }
    
    /**
     * 发送语音消息到设备端
     */
    private suspend fun sendVoiceMessageToDevice(context: Context, voiceFile: File): Boolean {
        return try {
            Logger.d("发送语音消息到设备端", tag = TAG)
            _interactionStatus.value = InteractionStatus.SENDING
            _isSending.value = true
            
            // 1. 上传语音文件到OBS
            val uploadResult = uploadVoiceToOBS(voiceFile)
            if (!uploadResult.success) {
                Logger.e("上传语音文件失败: ${uploadResult.error}", tag = TAG)
                _interactionStatus.value = InteractionStatus.ERROR
                return false
            }
            
            // 2. 通过IoT平台发送语音消息通知到设备
            val notificationResult = sendVoiceNotificationToDevice(context, uploadResult.obsUrl!!)
            if (!notificationResult) {
                Logger.e("发送语音通知失败", tag = TAG)
                _interactionStatus.value = InteractionStatus.ERROR
                return false
            }
            
            // 3. 记录互动历史
            val interaction = VoiceInteraction(
                id = generateInteractionId(),
                timestamp = System.currentTimeMillis(),
                duration = calculateRecordingDuration(voiceFile),
                obsUrl = uploadResult.obsUrl,
                status = VoiceInteractionStatus.SENT
            )
            
            addToInteractionHistory(interaction)
            
            _lastInteractionTime.value = System.currentTimeMillis()
            _interactionStatus.value = InteractionStatus.SENT
            _isSending.value = false
            
            Logger.d("语音消息发送成功", tag = TAG)
            true
            
        } catch (e: Exception) {
            Logger.e("发送语音消息失败: ${e.message}", tag = TAG)
            _interactionStatus.value = InteractionStatus.ERROR
            _isSending.value = false
            false
        } finally {
            // 清理本地录音文件
            voiceFile.delete()
            currentRecordingFile = null
        }
    }
    
    /**
     * 上传语音文件到OBS
     * 使用华为云OBS官方API
     */
    private suspend fun uploadVoiceToOBS(voiceFile: File): UploadResult {
        return try {
            // 生成OBS对象键
            val timestamp = System.currentTimeMillis()
            val objectKey = "${HuaweiCloudConfig.OBS_VOICE_PATH}parent/${timestamp}_voice_message.aac"

            Logger.d("开始上传语音文件到OBS: $objectKey", tag = TAG)

            // 使用华为云OBS API上传文件
            val result = obsApiClient.putObject(objectKey, voiceFile, "audio/aac")

            when (result) {
                is ObsApiResult.Success -> {
                    val obsUrl = result.data
                    Logger.d("语音文件上传成功: $obsUrl", tag = TAG)
                    UploadResult(true, obsUrl, null)
                }
                is ObsApiResult.Error -> {
                    Logger.e("语音文件上传失败: ${result.message}", tag = TAG)
                    UploadResult(false, null, result.message)
                }
            }

        } catch (e: Exception) {
            Logger.e("上传语音文件异常: ${e.message}", tag = TAG)
            UploadResult(false, null, e.message)
        }
    }
    
    /**
     * 发送语音通知到设备
     * 实现"指令解析→文本转语音（TTS）→音频播放"流程
     */
    private suspend fun sendVoiceNotificationToDevice(context: Context, obsUrl: String): Boolean {
        return try {
            Logger.d("发送语音播放命令到设备", tag = TAG)

            // 构造华为云IoT设备命令
            val commandParams = mapOf(
                "voice_url" to obsUrl,
                "play_mode" to "immediate",  // 立即播放
                "volume" to 80,              // 音量设置
                "priority" to "high",        // 高优先级
                "timestamp" to System.currentTimeMillis()
            )

            // 通过TokenIoTManager发送命令到设备
            val iotManager = TokenIoTManager(context)
            val success = iotManager.sendCommandToDevice(
                commandName = HuaweiCloudConfig.COMMAND_PLAY_PARENT_VOICE,
                parameters = commandParams
            )

            if (success) {
                Logger.d("语音播放命令发送成功", tag = TAG)

                // 可选：发送TTS备用命令（如果设备支持文本转语音）
                sendTTSBackupCommand(context, "家长发来了语音消息，请注意查收")

            } else {
                Logger.e("语音播放命令发送失败", tag = TAG)
            }

            success

        } catch (e: Exception) {
            Logger.e("发送设备通知失败: ${e.message}", tag = TAG)
            false
        }
    }

    /**
     * 发送TTS备用命令
     * 当语音文件播放失败时，使用TTS作为备用方案
     */
    private suspend fun sendTTSBackupCommand(context: Context, message: String): Boolean {
        return try {
            val ttsParams = mapOf(
                "text" to message,
                "voice_type" to "female",    // 女声
                "speed" to "normal",         // 正常语速
                "volume" to 70               // 音量
            )

            val iotManager = TokenIoTManager(context)
            val success = iotManager.sendCommandToDevice(
                commandName = HuaweiCloudConfig.COMMAND_TTS_SPEAK,
                parameters = ttsParams
            )

            Logger.d("TTS备用命令发送${if (success) "成功" else "失败"}", tag = TAG)
            success

        } catch (e: Exception) {
            Logger.e("发送TTS备用命令失败: ${e.message}", tag = TAG)
            false
        }
    }
    
    /**
     * 取消当前录音
     */
    fun cancelRecording() {
        Logger.d("取消语音录音", tag = TAG)
        
        if (_isRecording.value) {
            _isRecording.value = false
            cleanupRecorder()
            
            // 删除录音文件
            currentRecordingFile?.delete()
            currentRecordingFile = null
        }
        
        _interactionStatus.value = InteractionStatus.IDLE
    }
    
    /**
     * 清理录音器资源
     */
    private fun cleanupRecorder() {
        try {
            mediaRecorder?.apply {
                stop()
                release()
            }
        } catch (e: Exception) {
            Logger.e("清理录音器异常: ${e.message}", tag = TAG)
        } finally {
            mediaRecorder = null
        }
    }
    
    /**
     * 创建语音消息文件
     */
    private fun createVoiceMessageFile(context: Context): File {
        try {
            val timestamp = System.currentTimeMillis()
            // 使用3gp格式匹配MediaRecorder配置
            val fileName = "parent_voice_${timestamp}.3gp"

            // 创建voice目录
            val voiceDir = File(context.getExternalFilesDir(null), "voice")
            if (!voiceDir.exists()) {
                val created = voiceDir.mkdirs()
                Logger.d("📁 [测试] 创建voice目录: $created", tag = TAG)
            }

            val file = File(voiceDir, fileName)
            Logger.d("📁 [测试] 创建录音文件: ${file.absolutePath}", tag = TAG)

            return file
        } catch (e: Exception) {
            Logger.e("❌ [测试] 创建录音文件失败: ${e.message}", tag = TAG)
            // 降级到缓存目录
            val timestamp = System.currentTimeMillis()
            val fileName = "parent_voice_${timestamp}.3gp"
            return File(context.cacheDir, fileName)
        }
    }
    
    /**
     * 计算录音时长
     */
    private fun calculateRecordingDuration(voiceFile: File): Long {
        // 这里应该实现实际的音频时长计算
        // 暂时返回估算值
        return (voiceFile.length() / 16000) * 1000 // 粗略估算
    }
    
    /**
     * 生成互动ID
     */
    private fun generateInteractionId(): String {
        return "voice_${System.currentTimeMillis()}_${(1000..9999).random()}"
    }
    
    /**
     * 添加到互动历史
     */
    private fun addToInteractionHistory(interaction: VoiceInteraction) {
        val currentHistory = _interactionHistory.value.toMutableList()
        currentHistory.add(0, interaction) // 添加到列表开头
        
        // 保持最多50条历史记录
        if (currentHistory.size > 50) {
            currentHistory.removeAt(currentHistory.size - 1)
        }
        
        _interactionHistory.value = currentHistory
    }
    
    /**
     * 获取状态描述
     */
    fun getStatusDescription(): String {
        return when (_interactionStatus.value) {
            InteractionStatus.IDLE -> "准备就绪"
            InteractionStatus.RECORDING -> "正在录音..."
            InteractionStatus.SENDING -> "正在发送..."
            InteractionStatus.SENT -> "发送成功"
            InteractionStatus.ERROR -> "操作失败"
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        cancelRecording()
        coroutineScope.cancel()
    }

    // ==================== 简化方法名 ====================

    /**
     * 开始录制（简化方法名）
     */
    suspend fun startRecording(context: Context): Boolean {
        return startRecordingVoiceMessage(context)
    }

    /**
     * 停止录制并发送（简化方法名）
     */
    suspend fun stopRecordingAndSend(context: Context): Boolean {
        return stopRecordingVoiceMessage(context)
    }
}

/**
 * 互动状态
 */
enum class InteractionStatus {
    IDLE,       // 空闲
    RECORDING,  // 录音中
    SENDING,    // 发送中
    SENT,       // 已发送
    ERROR       // 错误
}

/**
 * 语音互动记录
 */
data class VoiceInteraction(
    val id: String,
    val timestamp: Long,
    val duration: Long,
    val obsUrl: String,
    val status: VoiceInteractionStatus
)

/**
 * 语音互动状态
 */
enum class VoiceInteractionStatus {
    SENT,       // 已发送
    DELIVERED,  // 已送达
    PLAYED      // 已播放
}

/**
 * 上传结果
 */
data class UploadResult(
    val success: Boolean,
    val obsUrl: String?,
    val error: String?
)
