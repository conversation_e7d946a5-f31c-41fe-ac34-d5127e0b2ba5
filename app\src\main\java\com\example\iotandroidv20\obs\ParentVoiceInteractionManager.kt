package com.example.iotandroidv20.obs

import android.content.Context
import android.media.MediaRecorder
import com.example.iotandroidv20.utils.Logger
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import java.io.File

/**
 * 家长语音互动管理器
 * 通过OBS服务实现家长与孩子的实时语音互动
 */
class ParentVoiceInteractionManager private constructor() {
    
    private val obsManager = ObsManager.getInstance()
    
    private val _interactionStatus = MutableStateFlow(InteractionStatus.IDLE)
    val interactionStatus: StateFlow<InteractionStatus> = _interactionStatus.asStateFlow()
    
    private val _isRecording = MutableStateFlow(false)
    val isRecording: StateFlow<Boolean> = _isRecording.asStateFlow()
    
    private val _isSending = MutableStateFlow(false)
    val isSending: StateFlow<Boolean> = _isSending.asStateFlow()
    
    private val _lastInteractionTime = MutableStateFlow(0L)
    val lastInteractionTime: StateFlow<Long> = _lastInteractionTime.asStateFlow()
    
    private val _interactionHistory = MutableStateFlow<List<VoiceInteraction>>(emptyList())
    val interactionHistory: StateFlow<List<VoiceInteraction>> = _interactionHistory.asStateFlow()
    
    private var mediaRecorder: MediaRecorder? = null
    private var currentRecordingFile: File? = null
    private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    companion object {
        private const val TAG = "ParentVoiceInteractionManager"
        private const val MAX_RECORDING_DURATION = 30000L // 30秒最大录音时长
        private const val AUDIO_SAMPLE_RATE = 44100
        private const val AUDIO_BIT_RATE = 128000
        
        @Volatile
        private var INSTANCE: ParentVoiceInteractionManager? = null
        
        fun getInstance(): ParentVoiceInteractionManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ParentVoiceInteractionManager().also { INSTANCE = it }
            }
        }
    }
    
    /**
     * 开始录制家长语音消息
     */
    suspend fun startRecordingVoiceMessage(context: Context): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                if (_isRecording.value) {
                    Logger.w("已在录音中，忽略重复请求", tag = TAG)
                    return@withContext false
                }
                
                Logger.d("开始录制家长语音消息", tag = TAG)
                _interactionStatus.value = InteractionStatus.RECORDING
                _isRecording.value = true
                
                // 创建录音文件
                currentRecordingFile = createVoiceMessageFile(context)
                
                // 初始化MediaRecorder
                mediaRecorder = MediaRecorder().apply {
                    setAudioSource(MediaRecorder.AudioSource.MIC)
                    setOutputFormat(MediaRecorder.OutputFormat.AAC_ADTS)
                    setAudioEncoder(MediaRecorder.AudioEncoder.AAC)
                    setAudioSamplingRate(AUDIO_SAMPLE_RATE)
                    setAudioEncodingBitRate(AUDIO_BIT_RATE)
                    setOutputFile(currentRecordingFile?.absolutePath)
                    setMaxDuration(MAX_RECORDING_DURATION.toInt())
                    
                    setOnInfoListener { _, what, _ ->
                        if (what == MediaRecorder.MEDIA_RECORDER_INFO_MAX_DURATION_REACHED) {
                            Logger.d("达到最大录音时长，自动停止", tag = TAG)
                            coroutineScope.launch {
                                stopRecordingVoiceMessage()
                            }
                        }
                    }
                    
                    prepare()
                    start()
                }
                
                Logger.d("语音录制已开始", tag = TAG)
                true
                
            } catch (e: Exception) {
                Logger.e("开始录音失败: ${e.message}", tag = TAG)
                _interactionStatus.value = InteractionStatus.ERROR
                _isRecording.value = false
                cleanupRecorder()
                false
            }
        }
    }
    
    /**
     * 停止录制并发送语音消息
     */
    suspend fun stopRecordingVoiceMessage(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                if (!_isRecording.value) {
                    Logger.w("当前未在录音，忽略停止请求", tag = TAG)
                    return@withContext false
                }
                
                Logger.d("停止录制语音消息", tag = TAG)
                _isRecording.value = false
                
                // 停止录音
                mediaRecorder?.apply {
                    stop()
                    release()
                }
                mediaRecorder = null
                
                // 检查录音文件
                val recordingFile = currentRecordingFile
                if (recordingFile != null && recordingFile.exists() && recordingFile.length() > 0) {
                    // 发送语音消息到设备
                    sendVoiceMessageToDevice(recordingFile)
                } else {
                    Logger.e("录音文件无效", tag = TAG)
                    _interactionStatus.value = InteractionStatus.ERROR
                    false
                }
                
            } catch (e: Exception) {
                Logger.e("停止录音失败: ${e.message}", tag = TAG)
                _interactionStatus.value = InteractionStatus.ERROR
                cleanupRecorder()
                false
            }
        }
    }
    
    /**
     * 发送语音消息到设备端
     */
    private suspend fun sendVoiceMessageToDevice(voiceFile: File): Boolean {
        return try {
            Logger.d("发送语音消息到设备端", tag = TAG)
            _interactionStatus.value = InteractionStatus.SENDING
            _isSending.value = true
            
            // 1. 上传语音文件到OBS
            val uploadResult = uploadVoiceToOBS(voiceFile)
            if (!uploadResult.success) {
                Logger.e("上传语音文件失败: ${uploadResult.error}", tag = TAG)
                _interactionStatus.value = InteractionStatus.ERROR
                return false
            }
            
            // 2. 通过IoT平台发送语音消息通知到设备
            val notificationResult = sendVoiceNotificationToDevice(uploadResult.obsUrl!!)
            if (!notificationResult) {
                Logger.e("发送语音通知失败", tag = TAG)
                _interactionStatus.value = InteractionStatus.ERROR
                return false
            }
            
            // 3. 记录互动历史
            val interaction = VoiceInteraction(
                id = generateInteractionId(),
                timestamp = System.currentTimeMillis(),
                duration = calculateRecordingDuration(voiceFile),
                obsUrl = uploadResult.obsUrl,
                status = VoiceInteractionStatus.SENT
            )
            
            addToInteractionHistory(interaction)
            
            _lastInteractionTime.value = System.currentTimeMillis()
            _interactionStatus.value = InteractionStatus.SENT
            _isSending.value = false
            
            Logger.d("语音消息发送成功", tag = TAG)
            true
            
        } catch (e: Exception) {
            Logger.e("发送语音消息失败: ${e.message}", tag = TAG)
            _interactionStatus.value = InteractionStatus.ERROR
            _isSending.value = false
            false
        } finally {
            // 清理本地录音文件
            voiceFile.delete()
            currentRecordingFile = null
        }
    }
    
    /**
     * 上传语音文件到OBS
     */
    private suspend fun uploadVoiceToOBS(voiceFile: File): UploadResult {
        return try {
            // 生成OBS对象键
            val timestamp = System.currentTimeMillis()
            val objectKey = "voice/parent/${timestamp}_voice_message.aac"
            
            // 这里应该调用实际的OBS上传API
            // 暂时返回模拟结果
            val obsUrl = "https://obs.example.com/bucket/$objectKey"
            
            Logger.d("语音文件上传成功: $obsUrl", tag = TAG)
            UploadResult(true, obsUrl, null)
            
        } catch (e: Exception) {
            Logger.e("上传语音文件异常: ${e.message}", tag = TAG)
            UploadResult(false, null, e.message)
        }
    }
    
    /**
     * 发送语音通知到设备
     */
    private suspend fun sendVoiceNotificationToDevice(obsUrl: String): Boolean {
        return try {
            // 这里应该通过IoT平台发送命令到设备
            // 告诉设备有新的家长语音消息需要播放
            
            // 构造设备命令
            val command = mapOf(
                "command" to "PLAY_PARENT_VOICE",
                "voice_url" to obsUrl,
                "timestamp" to System.currentTimeMillis(),
                "priority" to "HIGH"
            )
            
            // 发送命令到设备（这里需要集成IoT管理器）
            Logger.d("发送语音播放命令到设备: $command", tag = TAG)
            
            // 暂时返回成功
            true
            
        } catch (e: Exception) {
            Logger.e("发送设备通知失败: ${e.message}", tag = TAG)
            false
        }
    }
    
    /**
     * 取消当前录音
     */
    fun cancelRecording() {
        Logger.d("取消语音录音", tag = TAG)
        
        if (_isRecording.value) {
            _isRecording.value = false
            cleanupRecorder()
            
            // 删除录音文件
            currentRecordingFile?.delete()
            currentRecordingFile = null
        }
        
        _interactionStatus.value = InteractionStatus.IDLE
    }
    
    /**
     * 清理录音器资源
     */
    private fun cleanupRecorder() {
        try {
            mediaRecorder?.apply {
                stop()
                release()
            }
        } catch (e: Exception) {
            Logger.e("清理录音器异常: ${e.message}", tag = TAG)
        } finally {
            mediaRecorder = null
        }
    }
    
    /**
     * 创建语音消息文件
     */
    private fun createVoiceMessageFile(context: Context): File {
        val timestamp = System.currentTimeMillis()
        val fileName = "parent_voice_${timestamp}.aac"
        return File(context.cacheDir, fileName)
    }
    
    /**
     * 计算录音时长
     */
    private fun calculateRecordingDuration(voiceFile: File): Long {
        // 这里应该实现实际的音频时长计算
        // 暂时返回估算值
        return (voiceFile.length() / 16000) * 1000 // 粗略估算
    }
    
    /**
     * 生成互动ID
     */
    private fun generateInteractionId(): String {
        return "voice_${System.currentTimeMillis()}_${(1000..9999).random()}"
    }
    
    /**
     * 添加到互动历史
     */
    private fun addToInteractionHistory(interaction: VoiceInteraction) {
        val currentHistory = _interactionHistory.value.toMutableList()
        currentHistory.add(0, interaction) // 添加到列表开头
        
        // 保持最多50条历史记录
        if (currentHistory.size > 50) {
            currentHistory.removeAt(currentHistory.size - 1)
        }
        
        _interactionHistory.value = currentHistory
    }
    
    /**
     * 获取状态描述
     */
    fun getStatusDescription(): String {
        return when (_interactionStatus.value) {
            InteractionStatus.IDLE -> "准备就绪"
            InteractionStatus.RECORDING -> "正在录音..."
            InteractionStatus.SENDING -> "正在发送..."
            InteractionStatus.SENT -> "发送成功"
            InteractionStatus.ERROR -> "操作失败"
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        cancelRecording()
        coroutineScope.cancel()
    }
}

/**
 * 互动状态
 */
enum class InteractionStatus {
    IDLE,       // 空闲
    RECORDING,  // 录音中
    SENDING,    // 发送中
    SENT,       // 已发送
    ERROR       // 错误
}

/**
 * 语音互动记录
 */
data class VoiceInteraction(
    val id: String,
    val timestamp: Long,
    val duration: Long,
    val obsUrl: String,
    val status: VoiceInteractionStatus
)

/**
 * 语音互动状态
 */
enum class VoiceInteractionStatus {
    SENT,       // 已发送
    DELIVERED,  // 已送达
    PLAYED      // 已播放
}

/**
 * 上传结果
 */
data class UploadResult(
    val success: Boolean,
    val obsUrl: String?,
    val error: String?
)
