package com.example.iotandroidv20.ui.media

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.iotandroidv20.obs.*
import com.example.iotandroidv20.repository.MediaRepository
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import java.text.SimpleDateFormat
import java.util.*

/**
 * 媒体类型枚举
 */
enum class MediaType {
    VIDEO, AUDIO
}

/**
 * 媒体列表界面的ViewModel
 */
class MediaListViewModel : ViewModel() {
    
    private val mediaRepository = MediaRepository.getInstance()
    private val obsConfig = ObsConfig.getInstance()
    
    private val _uiState = MutableStateFlow(MediaListUiState())
    val uiState: StateFlow<MediaListUiState> = _uiState.asStateFlow()
    
    init {
        // 监听下载进度
        viewModelScope.launch {
            mediaRepository.downloadProgress.collect { progressMap ->
                _uiState.value = _uiState.value.copy(downloadProgress = progressMap)
            }
        }
        
        // 监听视频会话缓存
        viewModelScope.launch {
            mediaRepository.cachedVideoSessions.collect { videoCache ->
                val currentState = _uiState.value
                val cacheKey = "${currentState.selectedDevice}_${currentState.selectedDate}"
                val sessions = videoCache[cacheKey] ?: emptyList()
                _uiState.value = currentState.copy(videoSessions = sessions)
            }
        }
        
        // 监听音频会话缓存
        viewModelScope.launch {
            mediaRepository.cachedAudioSessions.collect { audioCache ->
                val currentState = _uiState.value
                val cacheKey = "${currentState.selectedDevice}_${currentState.selectedDate}"
                val sessions = audioCache[cacheKey] ?: emptyList()
                _uiState.value = currentState.copy(audioSessions = sessions)
            }
        }
    }
    
    /**
     * 初始化
     */
    fun initialize(context: Context) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = "")
                
                // 初始化OBS配置
                obsConfig.initialize(context)
                
                // 检查配置是否完整
                if (!obsConfig.isConfigured()) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "请先配置OBS访问凭据"
                    )
                    return@launch
                }
                
                // 初始化OBS管理器
                ObsManager.getInstance().initialize(
                    obsConfig.getAccessKey(),
                    obsConfig.getSecretKey()
                )
                
                // 加载可用设备列表
                loadAvailableDevices()
                
                // 设置默认日期为今天
                val today = SimpleDateFormat("yyyyMMdd", Locale.getDefault()).format(Date())
                _uiState.value = _uiState.value.copy(selectedDate = today)
                
                // 加载数据
                loadData()
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "初始化失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 选择设备
     */
    fun selectDevice(deviceId: String) {
        _uiState.value = _uiState.value.copy(selectedDevice = deviceId)
        loadAvailableDates()
        loadData()
    }
    
    /**
     * 选择日期
     */
    fun selectDate(date: String) {
        _uiState.value = _uiState.value.copy(selectedDate = date)
        loadData()
    }
    
    /**
     * 选择媒体类型
     */
    fun selectMediaType(mediaType: MediaType) {
        _uiState.value = _uiState.value.copy(selectedMediaType = mediaType)
    }
    
    /**
     * 刷新数据
     */
    fun refreshData() {
        loadData(forceRefresh = true)
    }
    
    /**
     * 下载视频
     */
    fun downloadVideo(context: Context, videoSession: VideoSession) {
        viewModelScope.launch {
            try {
                mediaRepository.downloadVideoFile(context, videoSession)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "下载失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 下载音频
     */
    fun downloadAudio(context: Context, audioSession: AudioSession) {
        viewModelScope.launch {
            try {
                mediaRepository.downloadAudioFile(context, audioSession)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "下载失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 显示设置
     */
    fun showSettings() {
        // TODO: 导航到设置界面
    }
    
    /**
     * 加载可用设备列表
     */
    private fun loadAvailableDevices() {
        viewModelScope.launch {
            try {
                // 模拟设备列表，实际应该从API获取
                val devices = listOf("LS_DEVICE_001", "LS_DEVICE_002", "LS_DEVICE_003")
                _uiState.value = _uiState.value.copy(
                    availableDevices = devices,
                    selectedDevice = devices.firstOrNull() ?: ""
                )
                
                // 加载第一个设备的可用日期
                if (devices.isNotEmpty()) {
                    loadAvailableDates()
                }
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "加载设备列表失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 加载可用日期列表
     */
    private fun loadAvailableDates() {
        viewModelScope.launch {
            try {
                val deviceId = _uiState.value.selectedDevice
                if (deviceId.isNotEmpty()) {
                    val dates = mediaRepository.getAvailableDates(deviceId)
                    _uiState.value = _uiState.value.copy(
                        availableDates = dates,
                        selectedDate = dates.firstOrNull() ?: _uiState.value.selectedDate
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "加载日期列表失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 加载数据
     */
    private fun loadData(forceRefresh: Boolean = false) {
        viewModelScope.launch {
            try {
                val currentState = _uiState.value
                if (currentState.selectedDevice.isEmpty() || currentState.selectedDate.isEmpty()) {
                    return@launch
                }
                
                _uiState.value = currentState.copy(isLoading = true, error = "")
                
                // 并行加载视频和音频数据
                val videoDeferred = kotlinx.coroutines.async {
                    mediaRepository.getVideoSessions(
                        currentState.selectedDevice,
                        currentState.selectedDate,
                        forceRefresh
                    )
                }
                
                val audioDeferred = kotlinx.coroutines.async {
                    mediaRepository.getAudioSessions(
                        currentState.selectedDevice,
                        currentState.selectedDate,
                        forceRefresh
                    )
                }
                
                val videoSessions = videoDeferred.await()
                val audioSessions = audioDeferred.await()
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    videoSessions = videoSessions,
                    audioSessions = audioSessions,
                    error = ""
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "加载数据失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 清除错误
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = "")
    }
}

/**
 * 媒体列表界面的UI状态
 */
data class MediaListUiState(
    val isLoading: Boolean = false,
    val error: String = "",
    val selectedDevice: String = "",
    val selectedDate: String = "",
    val selectedMediaType: MediaType = MediaType.VIDEO,
    val availableDevices: List<String> = emptyList(),
    val availableDates: List<String> = emptyList(),
    val videoSessions: List<VideoSession> = emptyList(),
    val audioSessions: List<AudioSession> = emptyList(),
    val downloadProgress: Map<String, DownloadProgress> = emptyMap()
)
