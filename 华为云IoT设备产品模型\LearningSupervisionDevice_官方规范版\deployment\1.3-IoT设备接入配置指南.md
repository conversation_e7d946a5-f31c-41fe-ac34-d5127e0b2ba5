# IoT设备接入配置指南

## 🎯 配置目标

配置华为云IoT设备接入服务(IoTDA)，创建学习监督设备的产品模型，注册设备，配置MQTT连接参数和设备证书。

## 📋 配置需求分析

### **产品模型设计**
- **产品名称**: LearningSupervisionDevice
- **设备类型**: 智能学习监督设备
- **协议类型**: MQTT
- **数据格式**: JSON
- **认证方式**: 设备证书认证

### **服务能力定义**
- VideoMonitoring: 视频监控服务
- VoiceInteraction: 语音交互服务  
- PostureMonitoring: 坐姿监控服务
- EEGMonitoring: 脑电波监测服务
- EnvironmentMonitoring: 环境监测服务
- DeviceManagement: 设备管理服务

## 🔧 实施步骤

### **步骤1：创建IoT产品**

```bash
# 创建产品
hcloud iotda create-product \
  --product-name "LearningSupervisionDevice" \
  --device-type "LearningSupervisionDevice" \
  --protocol-type "MQTT" \
  --data-format "json" \
  --manufacturer-name "智能学习监督科技" \
  --industry "教育" \
  --description "儿童学习监督智能设备"
```

### **步骤2：上传产品模型**

首先需要将我们已经设计好的产品模型打包上传：

```bash
# 打包产品模型
cd 华为云IoT设备产品模型/LearningSupervisionDevice_官方规范版
zip -r LearningSupervisionDevice_Model.zip profile/ service/

# 上传产品模型
hcloud iotda import-device-model \
  --product-id "your-product-id" \
  --model-file "LearningSupervisionDevice_Model.zip"
```

### **步骤3：创建设备组**

```bash
# 创建设备组用于批量管理
hcloud iotda create-device-group \
  --group-name "LearningSupervisionDevices" \
  --description "学习监督设备组" \
  --group-type "STATIC"
```

### **步骤4：注册设备**

```bash
# 注册单个设备
hcloud iotda create-device \
  --device-id "LS_DEVICE_001" \
  --node-id "LS_DEVICE_001" \
  --product-id "your-product-id" \
  --device-name "学习监督设备001" \
  --description "教室A座位1号设备" \
  --auth-type "SECRET" \
  --secret "device_secret_001"

# 批量注册设备（使用CSV文件）
hcloud iotda batch-create-devices \
  --product-id "your-product-id" \
  --device-list-file "devices.csv"
```

### **步骤5：配置设备证书**

```json
{
  "device_id": "LS_DEVICE_001",
  "device_secret": "generated_secret_key",
  "mqtt_broker": "iot-mqtts.cn-north-4.myhuaweicloud.com",
  "mqtt_port": 8883,
  "client_id": "LS_DEVICE_001",
  "username": "LS_DEVICE_001",
  "password": "generated_secret_key",
  "ca_cert": "-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----"
}
```

### **步骤6：配置MQTT主题**

```bash
# 数据上报主题
TOPIC_DATA_UP="$oc/devices/{device_id}/sys/properties/report"

# 命令下发主题  
TOPIC_COMMAND_DOWN="$oc/devices/{device_id}/sys/commands/#"

# 命令响应主题
TOPIC_COMMAND_RESPONSE="$oc/devices/{device_id}/sys/commands/response/request_id={request_id}"

# 事件上报主题
TOPIC_EVENT_UP="$oc/devices/{device_id}/sys/events/up"

# OBS上传URL请求主题
TOPIC_OBS_REQUEST="$oc/devices/{device_id}/sys/obs/url/request"

# OBS上传URL响应主题
TOPIC_OBS_RESPONSE="$oc/devices/{device_id}/sys/obs/url/response"
```

### **步骤7：配置设备影子**

```json
{
  "shadow": {
    "desired": {
      "videoRecording": {
        "enabled": true,
        "quality": "HIGH",
        "duration": 3600
      },
      "audioRecording": {
        "enabled": true,
        "quality": "MEDIUM",
        "duration": 1800
      },
      "postureMonitoring": {
        "enabled": true,
        "sensitivity": "MEDIUM",
        "alertThreshold": 30
      }
    },
    "reported": {
      "deviceStatus": "ONLINE",
      "batteryLevel": 85,
      "signalStrength": -45,
      "lastUpdateTime": "2025-01-01T12:00:00Z"
    }
  }
}
```

## 🔐 安全配置

### **设备认证配置**
```bash
# 生成设备证书
hcloud iotda create-device-certificate \
  --device-id "LS_DEVICE_001" \
  --certificate-type "X509" \
  --validity-period 365

# 下载CA根证书
curl -o GlobalSign_Root_CA.crt \
  https://iot-mqtts.cn-north-4.myhuaweicloud.com/certificate/GlobalSign_Root_CA.crt
```

### **访问控制配置**
```json
{
  "access_policy": {
    "device_access": {
      "allowed_operations": [
        "PROPERTY_REPORT",
        "EVENT_REPORT", 
        "COMMAND_RESPONSE",
        "OBS_UPLOAD_REQUEST"
      ],
      "denied_operations": [
        "DEVICE_MANAGEMENT",
        "PRODUCT_MANAGEMENT"
      ]
    },
    "topic_access": {
      "publish_topics": [
        "$oc/devices/{device_id}/sys/properties/report",
        "$oc/devices/{device_id}/sys/events/up",
        "$oc/devices/{device_id}/sys/commands/response/request_id=+",
        "$oc/devices/{device_id}/sys/obs/url/request"
      ],
      "subscribe_topics": [
        "$oc/devices/{device_id}/sys/commands/#",
        "$oc/devices/{device_id}/sys/obs/url/response"
      ]
    }
  }
}
```

## 📊 设备管理配置

### **设备分组策略**
```bash
# 按地理位置分组
hcloud iotda create-device-group \
  --group-name "Classroom_A" \
  --description "A教室设备组"

hcloud iotda create-device-group \
  --group-name "Classroom_B" \
  --description "B教室设备组"

# 按设备类型分组
hcloud iotda create-device-group \
  --group-name "Video_Devices" \
  --description "视频监控设备组"

hcloud iotda create-device-group \
  --group-name "Audio_Devices" \
  --description "音频监控设备组"
```

### **设备标签管理**
```bash
# 为设备添加标签
hcloud iotda add-device-tags \
  --device-id "LS_DEVICE_001" \
  --tags "location=classroom_a,type=video,version=v1.0"

# 批量标签操作
hcloud iotda batch-tag-devices \
  --tag-key "firmware_version" \
  --tag-value "v2.1.0" \
  --device-ids "LS_DEVICE_001,LS_DEVICE_002,LS_DEVICE_003"
```

### **设备状态监控**
```json
{
  "monitoring_rules": [
    {
      "rule_name": "device_offline_alert",
      "condition": "device_status == 'OFFLINE'",
      "duration": 300,
      "action": {
        "type": "NOTIFICATION",
        "target": "<EMAIL>"
      }
    },
    {
      "rule_name": "low_battery_alert", 
      "condition": "battery_level < 20",
      "duration": 0,
      "action": {
        "type": "SMS",
        "target": "+86138xxxxxxxx"
      }
    },
    {
      "rule_name": "upload_failure_alert",
      "condition": "upload_success_rate < 90",
      "duration": 600,
      "action": {
        "type": "WEBHOOK",
        "target": "https://api.learningsupervision.com/alerts"
      }
    }
  ]
}
```

## 🔄 数据流配置

### **数据上报格式**
```json
{
  "services": [
    {
      "service_id": "VideoMonitoring",
      "properties": {
        "streamStatus": "STREAMING",
        "recordingStatus": "RECORDING", 
        "currentSegmentId": "session001_segment_001",
        "uploadProgress": 75.5,
        "resolution": "1280x720",
        "frameRate": 30,
        "bitrate": 1500
      },
      "event_time": "2025-01-01T12:00:00Z"
    },
    {
      "service_id": "VoiceInteraction",
      "properties": {
        "voiceStatus": "RECORDING",
        "audioLevel": 65.2,
        "microphoneStatus": "ACTIVE",
        "speakerStatus": "ACTIVE",
        "audioQuality": 0.85
      },
      "event_time": "2025-01-01T12:00:00Z"
    }
  ]
}
```

### **命令下发格式**
```json
{
  "command_name": "START_VIDEO_RECORDING",
  "paras": {
    "duration": 3600,
    "segmentDuration": 10,
    "quality": "HIGH"
  },
  "service_id": "VideoMonitoring",
  "command_id": "cmd_001"
}
```

### **事件上报格式**
```json
{
  "services": [
    {
      "service_id": "VideoMonitoring",
      "event_type": "video_segment_uploaded",
      "paras": {
        "segmentId": "session001_segment_001",
        "uploadStatus": "SUCCESS",
        "fileSize": 2048576
      },
      "event_time": "2025-01-01T12:00:00Z"
    }
  ]
}
```

## 🧪 连接测试

### **MQTT连接测试**
```bash
#!/bin/bash

# MQTT连接测试脚本
DEVICE_ID="LS_DEVICE_001"
DEVICE_SECRET="your_device_secret"
BROKER="iot-mqtts.cn-north-4.myhuaweicloud.com"
PORT=8883

# 使用mosquitto客户端测试连接
mosquitto_pub \
  -h $BROKER \
  -p $PORT \
  -i $DEVICE_ID \
  -u $DEVICE_ID \
  -P $DEVICE_SECRET \
  --cafile GlobalSign_Root_CA.crt \
  -t "\$oc/devices/$DEVICE_ID/sys/properties/report" \
  -m '{"services":[{"service_id":"DeviceManagement","properties":{"deviceStatus":"ONLINE"},"event_time":"2025-01-01T12:00:00Z"}]}'

echo "MQTT连接测试完成"
```

### **设备数据模拟**
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import time
import random
from datetime import datetime
import paho.mqtt.client as mqtt

class IoTDeviceSimulator:
    def __init__(self, device_id, device_secret, broker, port=8883):
        self.device_id = device_id
        self.device_secret = device_secret
        self.broker = broker
        self.port = port
        self.client = mqtt.Client(client_id=device_id)
        
    def connect(self):
        self.client.username_pw_set(self.device_id, self.device_secret)
        self.client.tls_set(ca_certs="GlobalSign_Root_CA.crt")
        self.client.connect(self.broker, self.port, 60)
        self.client.loop_start()
        
    def simulate_video_data(self):
        data = {
            "services": [{
                "service_id": "VideoMonitoring",
                "properties": {
                    "streamStatus": "STREAMING",
                    "recordingStatus": random.choice(["RECORDING", "STOPPED"]),
                    "resolution": "1280x720",
                    "frameRate": random.randint(25, 30),
                    "bitrate": random.randint(1400, 1600),
                    "uploadProgress": round(random.uniform(0, 100), 1)
                },
                "event_time": datetime.now().isoformat() + "Z"
            }]
        }
        
        topic = f"$oc/devices/{self.device_id}/sys/properties/report"
        self.client.publish(topic, json.dumps(data))
        print(f"发送视频数据: {data}")
        
    def simulate_audio_data(self):
        data = {
            "services": [{
                "service_id": "VoiceInteraction", 
                "properties": {
                    "voiceStatus": random.choice(["ACTIVE", "INACTIVE"]),
                    "audioLevel": round(random.uniform(30, 80), 1),
                    "microphoneStatus": "ACTIVE",
                    "speakerStatus": "ACTIVE",
                    "audioQuality": round(random.uniform(0.7, 1.0), 2)
                },
                "event_time": datetime.now().isoformat() + "Z"
            }]
        }
        
        topic = f"$oc/devices/{self.device_id}/sys/properties/report"
        self.client.publish(topic, json.dumps(data))
        print(f"发送音频数据: {data}")

if __name__ == "__main__":
    simulator = IoTDeviceSimulator(
        device_id="LS_DEVICE_001",
        device_secret="your_device_secret",
        broker="iot-mqtts.cn-north-4.myhuaweicloud.com"
    )
    
    simulator.connect()
    
    # 模拟数据上报
    for i in range(10):
        simulator.simulate_video_data()
        time.sleep(2)
        simulator.simulate_audio_data()
        time.sleep(3)
```

## ✅ 完成检查清单

- [ ] 创建IoT产品
- [ ] 上传产品模型
- [ ] 创建设备组
- [ ] 注册设备
- [ ] 配置设备证书
- [ ] 配置MQTT主题
- [ ] 配置设备影子
- [ ] 设置访问控制
- [ ] 配置设备监控
- [ ] 测试MQTT连接
- [ ] 验证数据上报

---

**注意**: 请妥善保管设备证书和密钥，定期更新设备固件，监控设备在线状态。
