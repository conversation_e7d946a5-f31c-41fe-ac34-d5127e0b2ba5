<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.huaweicloud</groupId>
    <artifactId>esdk-obs-java</artifactId>
    <version>3.25.5</version>
    <packaging>jar</packaging>

    <name>OBS SDK for Java</name>
    <description>The OBS SDK for Java used for accessing Object Storage Service</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
		<dependency>
		    <groupId>com.squareup.okhttp3</groupId>
		    <artifactId>okhttp</artifactId>
		    <version>4.12.0</version>
		</dependency>
		<dependency>
			<groupId>com.squareup.okio</groupId>
			<artifactId>okio</artifactId>
			<version>3.8.0</version>
		</dependency>
		
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>2.15.4</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.15.4</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>2.15.4</version>
		</dependency>

		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>2.20.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>2.20.0</version>
		</dependency>

		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4</artifactId>
			<version>2.0.9</version>
			<scope>test</scope>
		</dependency>
		
		<dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <sourceDirectory>app/src/main/java</sourceDirectory>
        <resources>
            <resource>
                <directory>app/src/main/resource</directory>
                <excludes>
                    <exclude>log4j2.xml</exclude>
                </excludes>
            </resource>
        </resources>
        <testSourceDirectory>app/src/test/java</testSourceDirectory>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <configuration>
                    <filesets>
                        <fileset>
                            <directory>logs</directory>
                        </fileset>
                    </filesets>
                </configuration>
            </plugin>


            <!-- compile plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <source>1.8</source>
                    <target>1.8</target>
                    <compilerArgs>
                        <arg>-Xlint:deprecation</arg>
                        <arg>-Xlint:unchecked</arg>
                    </compilerArgs>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.0.1</version>
                <configuration>
                    <source>1.8</source>
                    <docencoding>UTF-8</docencoding>
                    <charset>UTF-8</charset>
                    <doclint>none</doclint>
                    <excludePackageNames>*.internal.*:*.log:*.proxy:okio</excludePackageNames>
                    <destDir>${project.build.directory}/doc</destDir>
                </configuration>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <phase>install</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>okio/*</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>install</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <appendAssemblyId>false</appendAssemblyId>
                    <descriptors>
                        <descriptor>assembly-java.xml</descriptor>
                    </descriptors>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <artifactSet>
                                <excludes>
                                    <exclude>com.squareup.okhttp3:okhttp:jar:</exclude>
                                    <exclude>com.fasterxml.jackson.core:jackson-core:jar:</exclude>
                                    <exclude>com.fasterxml.jackson.core:jackson-databind:jar:</exclude>
                                    <exclude>com.fasterxml.jackson.core:jackson-annotations:jar:</exclude>
                                    <exclude>org.jetbrains.kotlin:kotlin-stdlib:jar:</exclude>
                                    <exclude>org.jetbrains:annotations:jar:</exclude>
                                    <exclude>org.jetbrains.kotlin:kotlin-stdlib-common:jar:</exclude>
                                    <exclude>com.squareup.okio:okio:jar:</exclude>
                                    <exclude>org.apache.logging.log4j:log4j-core:jar:</exclude>
                                    <exclude>org.apache.logging.log4j:log4j-api:jar:</exclude>
                                </excludes>
                            </artifactSet>
                            <relocations>
                                <relocation>
                                    <pattern>com.jamesmurty.utils</pattern>
                                    <shadedPattern>com.obs.shade.jamesmurty.utils</shadedPattern>
                                </relocation>
                            </relocations>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
