# 完整项目音视频模块重新设计方案

## 🎯 总体架构重新设计

基于华为云OBS SDK和预签名URL直传技术，对整个学习监督项目的音视频模块进行全面重新设计，涵盖：
- **设备端**: L610模组 + 嵌入式开发
- **云平台**: 华为云IoT + OBS + MPS
- **应用端**: Android Kotlin + OBS SDK

## 📊 一、设备端到云平台部分重新设计

### **（一）嵌入式设备端开发架构**

#### **1. 硬件架构设计**

**主控单元**：
```
STM32H7系列 (ARM Cortex-M7, 480MHz)
├── 视频处理单元
│   ├── USB摄像头接口 (USB 2.0)
│   ├── MIPI CSI接口 (备用)
│   └── H.264硬件编码器
├── 音频处理单元
│   ├── I2S麦克风接口
│   ├── PCM音频接口
│   └── 音频编解码芯片
├── 通信模块
│   ├── L610 4G模组 (UART接口)
│   └── WiFi模块 (备用)
└── 存储单元
    ├── eMMC 8GB (本地缓存)
    └── SD卡槽 (扩展存储)
```

#### **2. 视频采集与处理模块**

**摄像头驱动适配**：
```c
// 摄像头初始化配置
typedef struct {
    uint32_t width;          // 分辨率宽度
    uint32_t height;         // 分辨率高度
    uint32_t fps;            // 帧率
    uint32_t format;         // YUV422/YUV420
    uint32_t bitrate;        // 目标码率
} camera_config_t;

// 摄像头控制接口
int camera_init(camera_config_t *config);
int camera_start_capture(void);
int camera_stop_capture(void);
int camera_get_frame(uint8_t *buffer, uint32_t *size);
int camera_set_quality(uint32_t quality);
```

**视频编码模块**：
```c
// H.264编码器配置
typedef struct {
    uint32_t width;          // 编码宽度
    uint32_t height;         // 编码高度
    uint32_t fps;            // 目标帧率
    uint32_t bitrate;        // 目标码率 (1-2Mbps)
    uint32_t gop_size;       // GOP大小
    uint32_t profile;        // H.264 Profile
} h264_encoder_config_t;

// 编码接口
int h264_encoder_init(h264_encoder_config_t *config);
int h264_encode_frame(uint8_t *yuv_data, uint8_t *h264_data, uint32_t *size);
int h264_encoder_deinit(void);
```

**视频分片管理**：
```c
// 视频分片结构
typedef struct {
    char segment_id[64];     // 分片ID
    uint32_t segment_index;  // 分片索引
    uint32_t duration;       // 分片时长(秒)
    uint32_t file_size;      // 文件大小
    char file_path[256];     // 本地文件路径
    uint8_t upload_status;   // 上传状态
} video_segment_t;

// 分片管理接口
int video_segment_create(video_segment_t *segment);
int video_segment_finalize(video_segment_t *segment);
int video_segment_upload(video_segment_t *segment);
```

#### **3. 音频采集与处理模块**

**麦克风驱动适配**：
```c
// 音频配置结构
typedef struct {
    uint32_t sample_rate;    // 采样率 (16kHz/44.1kHz/48kHz)
    uint32_t channels;       // 声道数 (1/2)
    uint32_t bit_depth;      // 位深度 (16/24)
    uint32_t buffer_size;    // 缓冲区大小
} audio_config_t;

// 音频采集接口
int audio_init(audio_config_t *config);
int audio_start_record(void);
int audio_stop_record(void);
int audio_read_data(int16_t *buffer, uint32_t samples);
```

**音频编码模块**：
```c
// 音频编码器配置
typedef struct {
    uint32_t sample_rate;    // 采样率
    uint32_t channels;       // 声道数
    uint32_t bitrate;        // 比特率 (64-320kbps)
    uint32_t format;         // MP3/AAC/OPUS
} audio_encoder_config_t;

// 编码接口
int audio_encoder_init(audio_encoder_config_t *config);
int audio_encode_frame(int16_t *pcm_data, uint8_t *encoded_data, uint32_t *size);
int audio_encoder_deinit(void);
```

#### **4. L610模组通信模块**

**L610初始化配置**：
```c
// L610模组配置
typedef struct {
    char apn[64];            // APN配置
    char username[32];       // 用户名
    char password[32];       // 密码
    uint32_t timeout;        // 连接超时
} l610_config_t;

// L610控制接口
int l610_init(l610_config_t *config);
int l610_connect_network(void);
int l610_get_signal_strength(int *rssi);
int l610_get_network_status(void);

// AT指令封装
int l610_send_at_command(const char *cmd, char *response, uint32_t timeout);
```

**MQTT连接管理**：
```c
// MQTT配置
typedef struct {
    char broker_url[256];    // 华为云IoT地址
    char device_id[64];      // 设备ID
    char device_secret[128]; // 设备密钥
    uint16_t port;           // 端口 (8883)
    uint32_t keepalive;      // 心跳间隔
} mqtt_config_t;

// MQTT接口
int mqtt_init(mqtt_config_t *config);
int mqtt_connect(void);
int mqtt_publish(const char *topic, const char *payload);
int mqtt_subscribe(const char *topic);
int mqtt_disconnect(void);
```

#### **5. OBS直传模块**

**预签名URL请求**：
```c
// OBS上传请求结构
typedef struct {
    char file_name[256];     // 文件名
    uint32_t file_size;      // 文件大小
    char content_type[64];   // 内容类型
    uint32_t expires;        // 过期时间
} obs_upload_request_t;

// OBS上传响应结构
typedef struct {
    char upload_url[1024];   // 预签名URL
    char object_key[256];    // 对象键
    uint32_t expires_at;     // 过期时间戳
} obs_upload_response_t;

// OBS接口
int obs_request_upload_url(obs_upload_request_t *request, obs_upload_response_t *response);
int obs_upload_file(const char *url, const char *file_path);
int obs_verify_upload(const char *object_key, const char *local_hash);
```

### **（二）华为云平台配置**

#### **1. IoT产品模型定义**

**设备类型定义**：
```json
{
  "deviceType": "LearningSupervisionDevice",
  "manufacturerId": "LearningSupervisionTech001",
  "protocolType": "MQTT",
  "serviceTypeCapabilities": [
    {
      "serviceId": "VideoCapture",
      "serviceType": "VideoCapture",
      "option": "Mandatory"
    },
    {
      "serviceId": "AudioCapture", 
      "serviceType": "AudioCapture",
      "option": "Mandatory"
    },
    {
      "serviceId": "MediaUpload",
      "serviceType": "MediaUpload", 
      "option": "Mandatory"
    }
  ]
}
```

#### **2. OBS桶配置策略**

**存储桶结构**：
```
learning-supervision-obs/
├── video/
│   ├── raw/                 # 原始分片
│   │   ├── {deviceId}/
│   │   │   ├── {date}/
│   │   │   │   ├── {sessionId}/
│   │   │   │   │   ├── segment_001.mp4
│   │   │   │   │   └── segment_002.mp4
│   ├── processed/           # 处理后文件
│   │   ├── {deviceId}/
│   │   │   ├── {date}/
│   │   │   │   └── complete_{sessionId}.mp4
├── audio/
│   ├── raw/                 # 原始分片
│   │   ├── {deviceId}/
│   │   │   ├── {date}/
│   │   │   │   ├── {sessionId}/
│   │   │   │   │   ├── segment_001.mp3
│   │   │   │   │   └── segment_002.mp3
│   ├── processed/           # 处理后文件
│   │   ├── {deviceId}/
│   │   │   ├── {date}/
│   │   │   │   └── complete_{sessionId}.mp3
```

**桶访问策略**：
```json
{
  "Statement": [
    {
      "Sid": "DeviceUploadPolicy",
      "Effect": "Allow",
      "Principal": {"Service": "iotda.myhuaweicloud.com"},
      "Action": ["obs:PutObject"],
      "Resource": "arn:aws:s3:::learning-supervision-obs/*/raw/*"
    },
    {
      "Sid": "AppDownloadPolicy", 
      "Effect": "Allow",
      "Principal": {"AWS": "arn:aws:iam::account:user/app-user"},
      "Action": ["obs:GetObject", "obs:ListBucket"],
      "Resource": [
        "arn:aws:s3:::learning-supervision-obs",
        "arn:aws:s3:::learning-supervision-obs/*/processed/*"
      ]
    }
  ]
}
```

#### **3. MPS媒体处理配置**

**视频处理工作流**：
```json
{
  "workflow_name": "video_processing_workflow",
  "input": {
    "bucket": "learning-supervision-obs",
    "prefix": "video/*/raw/*"
  },
  "tasks": [
    {
      "task_type": "merge",
      "input_pattern": "video/{deviceId}/raw/{date}/{sessionId}/segment_*.mp4",
      "output": "video/{deviceId}/processed/{date}/complete_{sessionId}.mp4",
      "merge_rule": {
        "sort_by": "filename",
        "order": "asc"
      }
    },
    {
      "task_type": "transcode",
      "input": "video/{deviceId}/processed/{date}/complete_{sessionId}.mp4",
      "output": "video/{deviceId}/processed/{date}/optimized_{sessionId}.mp4",
      "template": {
        "codec": "h264",
        "resolution": "1280x720",
        "bitrate": "1500k",
        "fps": 30
      }
    }
  ],
  "trigger": {
    "type": "event",
    "event": "session_complete"
  }
}
```

**音频处理工作流**：
```json
{
  "workflow_name": "audio_processing_workflow", 
  "input": {
    "bucket": "learning-supervision-obs",
    "prefix": "audio/*/raw/*"
  },
  "tasks": [
    {
      "task_type": "merge",
      "input_pattern": "audio/{deviceId}/raw/{date}/{sessionId}/segment_*.mp3",
      "output": "audio/{deviceId}/processed/{date}/complete_{sessionId}.mp3",
      "merge_rule": {
        "sort_by": "filename",
        "order": "asc"
      }
    },
    {
      "task_type": "transcode",
      "input": "audio/{deviceId}/processed/{date}/complete_{sessionId}.mp3",
      "output": "audio/{deviceId}/processed/{date}/optimized_{sessionId}.mp3",
      "template": {
        "codec": "aac",
        "bitrate": "128k",
        "sample_rate": "44100"
      }
    }
  ],
  "trigger": {
    "type": "event", 
    "event": "session_complete"
  }
}
```

### **（三）设备端数据传输流程**

#### **1. 视频数据上传流程**

```c
// 视频录制和上传主流程
int video_record_and_upload_session(uint32_t duration_seconds) {
    char session_id[64];
    video_segment_t segments[MAX_SEGMENTS];
    int segment_count = 0;
    
    // 1. 生成会话ID
    generate_session_id(session_id);
    
    // 2. 开始录制
    camera_start_capture();
    h264_encoder_init(&encoder_config);
    
    uint32_t segment_duration = 10; // 10秒分片
    uint32_t total_segments = duration_seconds / segment_duration;
    
    for(int i = 0; i < total_segments; i++) {
        // 3. 录制分片
        video_segment_t *segment = &segments[segment_count++];
        sprintf(segment->segment_id, "%s_segment_%03d", session_id, i);
        
        if(record_video_segment(segment, segment_duration) != 0) {
            continue; // 跳过失败的分片
        }
        
        // 4. 请求OBS上传URL
        obs_upload_request_t upload_req;
        obs_upload_response_t upload_resp;
        
        sprintf(upload_req.file_name, "video/%s/raw/%s/%s/%s.mp4", 
                device_id, get_current_date(), session_id, segment->segment_id);
        upload_req.file_size = segment->file_size;
        strcpy(upload_req.content_type, "video/mp4");
        upload_req.expires = 3600;
        
        if(obs_request_upload_url(&upload_req, &upload_resp) != 0) {
            segment->upload_status = UPLOAD_FAILED;
            continue;
        }
        
        // 5. 上传分片
        if(obs_upload_file(upload_resp.upload_url, segment->file_path) == 0) {
            segment->upload_status = UPLOAD_SUCCESS;
            
            // 6. 上报上传状态
            report_segment_upload_status(segment);
        } else {
            segment->upload_status = UPLOAD_FAILED;
            // 加入重试队列
            add_to_retry_queue(segment);
        }
        
        // 7. 清理本地文件
        if(segment->upload_status == UPLOAD_SUCCESS) {
            remove(segment->file_path);
        }
    }
    
    // 8. 停止录制
    camera_stop_capture();
    h264_encoder_deinit();
    
    // 9. 上报会话完成
    report_session_complete(session_id, segment_count);
    
    return 0;
}
```

#### **2. 音频数据上传流程**

```c
// 音频录制和上传主流程
int audio_record_and_upload_session(uint32_t duration_seconds) {
    char session_id[64];
    audio_segment_t segments[MAX_SEGMENTS];
    int segment_count = 0;
    
    // 1. 生成会话ID
    generate_session_id(session_id);
    
    // 2. 开始录音
    audio_start_record();
    audio_encoder_init(&encoder_config);
    
    uint32_t segment_duration = 30; // 30秒分片
    uint32_t total_segments = duration_seconds / segment_duration;
    
    for(int i = 0; i < total_segments; i++) {
        // 3. 录制分片
        audio_segment_t *segment = &segments[segment_count++];
        sprintf(segment->segment_id, "%s_segment_%03d", session_id, i);
        
        if(record_audio_segment(segment, segment_duration) != 0) {
            continue;
        }
        
        // 4. 请求OBS上传URL
        obs_upload_request_t upload_req;
        obs_upload_response_t upload_resp;
        
        sprintf(upload_req.file_name, "audio/%s/raw/%s/%s/%s.mp3",
                device_id, get_current_date(), session_id, segment->segment_id);
        upload_req.file_size = segment->file_size;
        strcpy(upload_req.content_type, "audio/mp3");
        upload_req.expires = 3600;
        
        if(obs_request_upload_url(&upload_req, &upload_resp) != 0) {
            segment->upload_status = UPLOAD_FAILED;
            continue;
        }
        
        // 5. 上传分片
        if(obs_upload_file(upload_resp.upload_url, segment->file_path) == 0) {
            segment->upload_status = UPLOAD_SUCCESS;
            report_segment_upload_status(segment);
        } else {
            segment->upload_status = UPLOAD_FAILED;
            add_to_retry_queue(segment);
        }
        
        // 6. 清理本地文件
        if(segment->upload_status == UPLOAD_SUCCESS) {
            remove(segment->file_path);
        }
    }
    
    // 7. 停止录音
    audio_stop_record();
    audio_encoder_deinit();
    
    // 8. 上报会话完成
    report_session_complete(session_id, segment_count);
    
    return 0;
}
```

## 📱 二、应用端到云平台部分重新设计

### **（一）Android项目结构重新设计**

#### **1. 项目依赖配置**

**build.gradle (Module: app)**：
```gradle
android {
    compileSdk 34

    defaultConfig {
        applicationId "com.learningsupervision.iot"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0"

        // OBS配置
        buildConfigField "String", "OBS_ENDPOINT", "\"https://obs.cn-north-4.myhuaweicloud.com\""
        buildConfigField "String", "OBS_BUCKET", "\"learning-supervision-obs\""
    }

    buildTypes {
        debug {
            buildConfigField "String", "OBS_ACCESS_KEY", "\"${obs_access_key_debug}\""
            buildConfigField "String", "OBS_SECRET_KEY", "\"${obs_secret_key_debug}\""
        }
        release {
            buildConfigField "String", "OBS_ACCESS_KEY", "\"${obs_access_key_release}\""
            buildConfigField "String", "OBS_SECRET_KEY", "\"${obs_secret_key_release}\""
        }
    }
}

dependencies {
    // 华为云OBS SDK
    implementation files('libs/esdk-obs-android-3.21.8.jar')
    implementation 'com.squareup.okhttp3:okhttp:4.9.3'
    implementation 'com.squareup.okio:okio:2.10.0'

    // 媒体播放
    implementation 'com.google.android.exoplayer:exoplayer:2.18.7'
    implementation 'com.google.android.exoplayer:exoplayer-ui:2.18.7'

    // 网络请求
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'

    // 协程和生命周期
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.6.2'

    // UI组件
    implementation 'androidx.recyclerview:recyclerview:1.3.1'
    implementation 'com.github.bumptech.glide:glide:4.14.2'
}
```

#### **2. 核心架构组件**

**项目包结构**：
```
com.learningsupervision.iot/
├── data/
│   ├── repository/
│   │   ├── MediaRepository.kt
│   │   └── DeviceRepository.kt
│   ├── remote/
│   │   ├── ObsService.kt
│   │   └── IoTApiService.kt
│   ├── local/
│   │   ├── MediaDatabase.kt
│   │   └── MediaDao.kt
│   └── model/
│       ├── VideoInfo.kt
│       ├── AudioInfo.kt
│       └── DeviceSession.kt
├── domain/
│   ├── usecase/
│   │   ├── GetMediaListUseCase.kt
│   │   ├── PlayVideoUseCase.kt
│   │   └── DownloadMediaUseCase.kt
│   └── repository/
│       └── MediaRepositoryInterface.kt
├── presentation/
│   ├── ui/
│   │   ├── video/
│   │   │   ├── VideoListFragment.kt
│   │   │   ├── VideoPlayerFragment.kt
│   │   │   └── VideoListAdapter.kt
│   │   ├── audio/
│   │   │   ├── AudioListFragment.kt
│   │   │   ├── AudioPlayerFragment.kt
│   │   │   └── AudioListAdapter.kt
│   │   └── main/
│   │       └── MainActivity.kt
│   ├── viewmodel/
│   │   ├── VideoViewModel.kt
│   │   ├── AudioViewModel.kt
│   │   └── MainViewModel.kt
│   └── util/
│       ├── MediaPlayerManager.kt
│       └── DownloadManager.kt
└── di/
    ├── AppModule.kt
    ├── NetworkModule.kt
    └── DatabaseModule.kt
```

### **（二）OBS SDK集成实现**

#### **1. OBS服务管理器**

**ObsManager.kt**：
```kotlin
class ObsManager private constructor() {
    private var obsClient: ObsClient? = null
    private val bucketName = BuildConfig.OBS_BUCKET

    companion object {
        @Volatile
        private var INSTANCE: ObsManager? = null

        fun getInstance(): ObsManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ObsManager().also { INSTANCE = it }
            }
        }
    }

    fun initialize() {
        val config = ObsConfiguration().apply {
            socketTimeout = 30000
            connectionTimeout = 10000
            endPoint = BuildConfig.OBS_ENDPOINT
            pathStyle = false
            maxConnections = 100
        }

        obsClient = ObsClient(
            BuildConfig.OBS_ACCESS_KEY,
            BuildConfig.OBS_SECRET_KEY,
            config
        )
    }

    suspend fun listVideoSessions(deviceId: String, date: String): List<VideoSession> {
        return withContext(Dispatchers.IO) {
            try {
                val prefix = "video/$deviceId/processed/$date/"
                val request = ListObjectsRequest().apply {
                    bucketName = <EMAIL>
                    prefix = prefix
                    maxKeys = 1000
                }

                val result = obsClient?.listObjects(request)
                result?.objects?.map { obj ->
                    VideoSession(
                        sessionId = extractSessionId(obj.objectKey),
                        deviceId = deviceId,
                        date = date,
                        fileName = obj.objectKey.substringAfterLast("/"),
                        fileSize = obj.metadata.contentLength,
                        lastModified = obj.metadata.lastModified,
                        objectKey = obj.objectKey,
                        streamUrl = generatePresignedUrl(obj.objectKey, 3600)
                    )
                } ?: emptyList()
            } catch (e: Exception) {
                Log.e("ObsManager", "Failed to list video sessions", e)
                emptyList()
            }
        }
    }

    suspend fun listAudioSessions(deviceId: String, date: String): List<AudioSession> {
        return withContext(Dispatchers.IO) {
            try {
                val prefix = "audio/$deviceId/processed/$date/"
                val request = ListObjectsRequest().apply {
                    bucketName = <EMAIL>
                    prefix = prefix
                    maxKeys = 1000
                }

                val result = obsClient?.listObjects(request)
                result?.objects?.map { obj ->
                    AudioSession(
                        sessionId = extractSessionId(obj.objectKey),
                        deviceId = deviceId,
                        date = date,
                        fileName = obj.objectKey.substringAfterLast("/"),
                        fileSize = obj.metadata.contentLength,
                        lastModified = obj.metadata.lastModified,
                        objectKey = obj.objectKey,
                        streamUrl = generatePresignedUrl(obj.objectKey, 3600)
                    )
                } ?: emptyList()
            } catch (e: Exception) {
                Log.e("ObsManager", "Failed to list audio sessions", e)
                emptyList()
            }
        }
    }

    suspend fun downloadFile(objectKey: String, localFile: File): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val request = GetObjectRequest(bucketName, objectKey)
                val result = obsClient?.getObject(request)

                result?.objectContent?.use { inputStream ->
                    localFile.outputStream().use { outputStream ->
                        inputStream.copyTo(outputStream)
                    }
                }
                true
            } catch (e: Exception) {
                Log.e("ObsManager", "Failed to download file: $objectKey", e)
                false
            }
        }
    }

    private fun generatePresignedUrl(objectKey: String, expiresInSeconds: Int): String {
        return try {
            val request = CreatePresignedUrlRequest().apply {
                method = HttpMethodEnum.GET
                bucketName = <EMAIL>
                objectKey = objectKey
                expires = expiresInSeconds
            }
            obsClient?.generatePresignedUrl(request) ?: ""
        } catch (e: Exception) {
            Log.e("ObsManager", "Failed to generate presigned URL", e)
            ""
        }
    }

    private fun extractSessionId(objectKey: String): String {
        // 从 "video/device001/processed/20250101/complete_session123.mp4" 提取 "session123"
        val fileName = objectKey.substringAfterLast("/")
        return fileName.substringAfter("complete_").substringBefore(".")
    }

    fun release() {
        try {
            obsClient?.close()
        } catch (e: Exception) {
            Log.e("ObsManager", "Failed to close OBS client", e)
        }
        obsClient = null
    }
}
```

#### **2. 媒体数据模型**

**VideoSession.kt**：
```kotlin
@Entity(tableName = "video_sessions")
data class VideoSession(
    @PrimaryKey val id: String = "${deviceId}_${date}_${sessionId}",
    val sessionId: String,
    val deviceId: String,
    val date: String,
    val fileName: String,
    val fileSize: Long,
    val lastModified: Date,
    val objectKey: String,
    val streamUrl: String,
    val downloadStatus: DownloadStatus = DownloadStatus.NOT_DOWNLOADED,
    val localFilePath: String? = null,
    val createdAt: Date = Date()
)

enum class DownloadStatus {
    NOT_DOWNLOADED,
    DOWNLOADING,
    DOWNLOADED,
    DOWNLOAD_FAILED
}
```

**AudioSession.kt**：
```kotlin
@Entity(tableName = "audio_sessions")
data class AudioSession(
    @PrimaryKey val id: String = "${deviceId}_${date}_${sessionId}",
    val sessionId: String,
    val deviceId: String,
    val date: String,
    val fileName: String,
    val fileSize: Long,
    val lastModified: Date,
    val objectKey: String,
    val streamUrl: String,
    val downloadStatus: DownloadStatus = DownloadStatus.NOT_DOWNLOADED,
    val localFilePath: String? = null,
    val createdAt: Date = Date()
)
```

### **（三）媒体播放器实现**

#### **1. 视频播放器管理器**

**VideoPlayerManager.kt**：
```kotlin
class VideoPlayerManager(private val context: Context) {
    private var exoPlayer: ExoPlayer? = null
    private var currentVideoSession: VideoSession? = null
    private val playerListeners = mutableListOf<PlayerEventListener>()

    interface PlayerEventListener {
        fun onPlayerStateChanged(isPlaying: Boolean, playbackState: Int)
        fun onPlayerError(error: PlaybackException)
        fun onPositionChanged(position: Long, duration: Long)
    }

    fun initializePlayer() {
        if (exoPlayer == null) {
            val loadControl = DefaultLoadControl.Builder()
                .setBufferDurationsMs(
                    15000,  // 最小缓冲时间
                    50000,  // 最大缓冲时间
                    1500,   // 播放缓冲时间
                    5000    // 重新缓冲时间
                )
                .build()

            exoPlayer = ExoPlayer.Builder(context)
                .setLoadControl(loadControl)
                .build()
                .apply {
                    addListener(object : Player.Listener {
                        override fun onPlayerStateChanged(playWhenReady: Boolean, playbackState: Int) {
                            playerListeners.forEach {
                                it.onPlayerStateChanged(playWhenReady, playbackState)
                            }
                        }

                        override fun onPlayerError(error: PlaybackException) {
                            playerListeners.forEach { it.onPlayerError(error) }
                        }

                        override fun onPositionDiscontinuity(
                            oldPosition: Player.PositionInfo,
                            newPosition: Player.PositionInfo,
                            reason: Int
                        ) {
                            playerListeners.forEach {
                                it.onPositionChanged(newPosition.positionMs, duration)
                            }
                        }
                    })
                }
        }
    }

    fun playVideo(videoSession: VideoSession, playerView: PlayerView) {
        currentVideoSession = videoSession

        val mediaItem = MediaItem.Builder()
            .setUri(videoSession.streamUrl)
            .setMediaMetadata(
                MediaMetadata.Builder()
                    .setTitle(videoSession.fileName)
                    .setDescription("设备: ${videoSession.deviceId}, 日期: ${videoSession.date}")
                    .build()
            )
            .build()

        exoPlayer?.let { player ->
            player.setMediaItem(mediaItem)
            player.prepare()
            player.playWhenReady = true
            playerView.player = player
        }
    }

    fun pauseVideo() {
        exoPlayer?.playWhenReady = false
    }

    fun resumeVideo() {
        exoPlayer?.playWhenReady = true
    }

    fun seekTo(positionMs: Long) {
        exoPlayer?.seekTo(positionMs)
    }

    fun getCurrentPosition(): Long {
        return exoPlayer?.currentPosition ?: 0L
    }

    fun getDuration(): Long {
        return exoPlayer?.duration ?: 0L
    }

    fun addPlayerListener(listener: PlayerEventListener) {
        playerListeners.add(listener)
    }

    fun removePlayerListener(listener: PlayerEventListener) {
        playerListeners.remove(listener)
    }

    fun releasePlayer() {
        exoPlayer?.release()
        exoPlayer = null
        currentVideoSession = null
        playerListeners.clear()
    }
}
```

#### **2. 音频播放器管理器**

**AudioPlayerManager.kt**：
```kotlin
class AudioPlayerManager(private val context: Context) {
    private var mediaPlayer: MediaPlayer? = null
    private var currentAudioSession: AudioSession? = null
    private val playerListeners = mutableListOf<AudioPlayerListener>()
    private var isPrepared = false

    interface AudioPlayerListener {
        fun onPlayerStateChanged(isPlaying: Boolean)
        fun onPlayerError(error: String)
        fun onPositionChanged(position: Int, duration: Int)
        fun onPrepared()
        fun onCompletion()
    }

    fun initializePlayer() {
        if (mediaPlayer == null) {
            mediaPlayer = MediaPlayer().apply {
                setOnPreparedListener {
                    isPrepared = true
                    playerListeners.forEach { it.onPrepared() }
                }

                setOnCompletionListener {
                    playerListeners.forEach { it.onCompletion() }
                }

                setOnErrorListener { _, what, extra ->
                    val errorMsg = "MediaPlayer error: what=$what, extra=$extra"
                    playerListeners.forEach { it.onPlayerError(errorMsg) }
                    true
                }
            }
        }
    }

    suspend fun playAudio(audioSession: AudioSession) {
        withContext(Dispatchers.Main) {
            try {
                currentAudioSession = audioSession
                isPrepared = false

                mediaPlayer?.apply {
                    reset()
                    setDataSource(audioSession.streamUrl)
                    prepareAsync()
                }
            } catch (e: Exception) {
                playerListeners.forEach {
                    it.onPlayerError("Failed to play audio: ${e.message}")
                }
            }
        }
    }

    fun startPlayback() {
        if (isPrepared) {
            mediaPlayer?.start()
            playerListeners.forEach { it.onPlayerStateChanged(true) }
            startPositionUpdates()
        }
    }

    fun pausePlayback() {
        mediaPlayer?.pause()
        playerListeners.forEach { it.onPlayerStateChanged(false) }
    }

    fun seekTo(position: Int) {
        if (isPrepared) {
            mediaPlayer?.seekTo(position)
        }
    }

    fun getCurrentPosition(): Int {
        return if (isPrepared) mediaPlayer?.currentPosition ?: 0 else 0
    }

    fun getDuration(): Int {
        return if (isPrepared) mediaPlayer?.duration ?: 0 else 0
    }

    private fun startPositionUpdates() {
        val handler = Handler(Looper.getMainLooper())
        val updateRunnable = object : Runnable {
            override fun run() {
                if (mediaPlayer?.isPlaying == true) {
                    val position = getCurrentPosition()
                    val duration = getDuration()
                    playerListeners.forEach { it.onPositionChanged(position, duration) }
                    handler.postDelayed(this, 1000) // 每秒更新一次
                }
            }
        }
        handler.post(updateRunnable)
    }

    fun addPlayerListener(listener: AudioPlayerListener) {
        playerListeners.add(listener)
    }

    fun removePlayerListener(listener: AudioPlayerListener) {
        playerListeners.remove(listener)
    }

    fun releasePlayer() {
        mediaPlayer?.release()
        mediaPlayer = null
        currentAudioSession = null
        isPrepared = false
        playerListeners.clear()
    }
}
```

## 🧪 三、测试与优化

### **（一）功能测试方案**

#### **1. 设备端功能测试**

**视频采集测试**：
```c
// 视频功能测试用例
typedef struct {
    char test_name[64];
    camera_config_t config;
    uint32_t expected_fps;
    uint32_t expected_bitrate;
    uint32_t test_duration;
} video_test_case_t;

video_test_case_t video_tests[] = {
    {"720P_30FPS_Test", {1280, 720, 30, YUV420, 1500000}, 30, 1500000, 60},
    {"480P_15FPS_Test", {640, 480, 15, YUV420, 800000}, 15, 800000, 60},
    {"1080P_30FPS_Test", {1920, 1080, 30, YUV420, 2000000}, 30, 2000000, 60}
};

int run_video_tests() {
    for(int i = 0; i < sizeof(video_tests)/sizeof(video_test_case_t); i++) {
        video_test_case_t *test = &video_tests[i];
        printf("Running test: %s\n", test->test_name);

        // 初始化摄像头
        if(camera_init(&test->config) != 0) {
            printf("FAIL: Camera init failed\n");
            continue;
        }

        // 测试录制
        uint32_t actual_fps = 0;
        uint32_t actual_bitrate = 0;

        if(test_video_recording(test->test_duration, &actual_fps, &actual_bitrate) == 0) {
            if(abs(actual_fps - test->expected_fps) <= 2 &&
               abs(actual_bitrate - test->expected_bitrate) <= 100000) {
                printf("PASS: %s\n", test->test_name);
            } else {
                printf("FAIL: %s - FPS: %d (expected %d), Bitrate: %d (expected %d)\n",
                       test->test_name, actual_fps, test->expected_fps,
                       actual_bitrate, test->expected_bitrate);
            }
        } else {
            printf("FAIL: %s - Recording failed\n", test->test_name);
        }

        camera_deinit();
    }
    return 0;
}
```

**音频采集测试**：
```c
// 音频功能测试用例
typedef struct {
    char test_name[64];
    audio_config_t config;
    uint32_t expected_sample_rate;
    uint32_t expected_bitrate;
    uint32_t test_duration;
} audio_test_case_t;

audio_test_case_t audio_tests[] = {
    {"44.1kHz_Stereo_Test", {44100, 2, 16, 4096}, 44100, 128000, 30},
    {"16kHz_Mono_Test", {16000, 1, 16, 2048}, 16000, 64000, 30},
    {"48kHz_Stereo_Test", {48000, 2, 16, 4096}, 48000, 256000, 30}
};

int run_audio_tests() {
    for(int i = 0; i < sizeof(audio_tests)/sizeof(audio_test_case_t); i++) {
        audio_test_case_t *test = &audio_tests[i];
        printf("Running test: %s\n", test->test_name);

        // 初始化音频
        if(audio_init(&test->config) != 0) {
            printf("FAIL: Audio init failed\n");
            continue;
        }

        // 测试录音
        uint32_t actual_sample_rate = 0;
        uint32_t actual_bitrate = 0;

        if(test_audio_recording(test->test_duration, &actual_sample_rate, &actual_bitrate) == 0) {
            if(actual_sample_rate == test->expected_sample_rate &&
               abs(actual_bitrate - test->expected_bitrate) <= 10000) {
                printf("PASS: %s\n", test->test_name);
            } else {
                printf("FAIL: %s - Sample Rate: %d (expected %d), Bitrate: %d (expected %d)\n",
                       test->test_name, actual_sample_rate, test->expected_sample_rate,
                       actual_bitrate, test->expected_bitrate);
            }
        } else {
            printf("FAIL: %s - Recording failed\n", test->test_name);
        }

        audio_deinit();
    }
    return 0;
}
```

**OBS上传测试**：
```c
// OBS上传功能测试
int test_obs_upload_functionality() {
    printf("Testing OBS upload functionality...\n");

    // 测试用例1: 小文件上传
    char test_file_small[] = "test_small.mp4";
    create_test_file(test_file_small, 1024 * 1024); // 1MB

    obs_upload_request_t req_small = {
        .file_name = "test/small_file.mp4",
        .file_size = 1024 * 1024,
        .content_type = "video/mp4",
        .expires = 3600
    };

    obs_upload_response_t resp_small;
    if(obs_request_upload_url(&req_small, &resp_small) == 0) {
        if(obs_upload_file(resp_small.upload_url, test_file_small) == 0) {
            printf("PASS: Small file upload\n");
        } else {
            printf("FAIL: Small file upload failed\n");
        }
    } else {
        printf("FAIL: Small file URL request failed\n");
    }

    // 测试用例2: 大文件上传
    char test_file_large[] = "test_large.mp4";
    create_test_file(test_file_large, 50 * 1024 * 1024); // 50MB

    obs_upload_request_t req_large = {
        .file_name = "test/large_file.mp4",
        .file_size = 50 * 1024 * 1024,
        .content_type = "video/mp4",
        .expires = 3600
    };

    obs_upload_response_t resp_large;
    if(obs_request_upload_url(&req_large, &resp_large) == 0) {
        if(obs_upload_file(resp_large.upload_url, test_file_large) == 0) {
            printf("PASS: Large file upload\n");
        } else {
            printf("FAIL: Large file upload failed\n");
        }
    } else {
        printf("FAIL: Large file URL request failed\n");
    }

    // 清理测试文件
    remove(test_file_small);
    remove(test_file_large);

    return 0;
}
```

#### **2. 应用端功能测试**

**Android单元测试**：
```kotlin
@RunWith(AndroidJUnit4::class)
class ObsManagerTest {

    private lateinit var obsManager: ObsManager

    @Before
    fun setup() {
        obsManager = ObsManager.getInstance()
        obsManager.initialize()
    }

    @Test
    fun testListVideoSessions() = runTest {
        // 测试获取视频会话列表
        val deviceId = "test_device_001"
        val date = "20250101"

        val videoSessions = obsManager.listVideoSessions(deviceId, date)

        assertThat(videoSessions).isNotNull()
        // 验证返回的数据格式
        videoSessions.forEach { session ->
            assertThat(session.deviceId).isEqualTo(deviceId)
            assertThat(session.date).isEqualTo(date)
            assertThat(session.streamUrl).isNotEmpty()
            assertThat(session.fileSize).isGreaterThan(0)
        }
    }

    @Test
    fun testListAudioSessions() = runTest {
        // 测试获取音频会话列表
        val deviceId = "test_device_001"
        val date = "20250101"

        val audioSessions = obsManager.listAudioSessions(deviceId, date)

        assertThat(audioSessions).isNotNull()
        audioSessions.forEach { session ->
            assertThat(session.deviceId).isEqualTo(deviceId)
            assertThat(session.date).isEqualTo(date)
            assertThat(session.streamUrl).isNotEmpty()
            assertThat(session.fileSize).isGreaterThan(0)
        }
    }

    @Test
    fun testDownloadFile() = runTest {
        // 测试文件下载功能
        val testObjectKey = "video/test_device_001/processed/20250101/complete_session001.mp4"
        val localFile = File(context.cacheDir, "test_download.mp4")

        val success = obsManager.downloadFile(testObjectKey, localFile)

        assertThat(success).isTrue()
        assertThat(localFile.exists()).isTrue()
        assertThat(localFile.length()).isGreaterThan(0)

        // 清理
        localFile.delete()
    }

    @After
    fun tearDown() {
        obsManager.release()
    }
}
```

**UI测试**：
```kotlin
@RunWith(AndroidJUnit4::class)
class VideoPlayerTest {

    @get:Rule
    val activityRule = ActivityScenarioRule(MainActivity::class.java)

    @Test
    fun testVideoPlayback() {
        // 测试视频播放功能
        onView(withId(R.id.video_list_recycler_view))
            .perform(RecyclerViewActions.actionOnItemAtPosition<VideoListAdapter.ViewHolder>(0, click()))

        // 验证播放器界面显示
        onView(withId(R.id.player_view))
            .check(matches(isDisplayed()))

        // 测试播放控制
        onView(withId(R.id.play_pause_button))
            .perform(click())

        // 等待播放开始
        Thread.sleep(2000)

        // 验证播放状态
        onView(withId(R.id.play_pause_button))
            .check(matches(hasContentDescription("暂停")))
    }

    @Test
    fun testAudioPlayback() {
        // 切换到音频标签
        onView(withText("音频"))
            .perform(click())

        // 点击音频项目
        onView(withId(R.id.audio_list_recycler_view))
            .perform(RecyclerViewActions.actionOnItemAtPosition<AudioListAdapter.ViewHolder>(0, click()))

        // 验证音频播放器界面
        onView(withId(R.id.audio_player_layout))
            .check(matches(isDisplayed()))

        // 测试播放控制
        onView(withId(R.id.audio_play_button))
            .perform(click())

        // 验证播放状态
        Thread.sleep(1000)
        onView(withId(R.id.audio_progress_bar))
            .check(matches(isDisplayed()))
    }
}
```

### **（二）性能测试方案**

#### **1. 设备端性能测试**

**网络性能测试**：
```c
// 网络性能测试结构
typedef struct {
    char network_type[32];   // 4G/WiFi/3G
    uint32_t signal_strength; // 信号强度
    uint32_t upload_speed;   // 上传速度 (KB/s)
    uint32_t success_rate;   // 成功率 (%)
    uint32_t avg_latency;    // 平均延迟 (ms)
} network_performance_t;

int test_network_performance() {
    network_performance_t results[10];
    int test_count = 0;

    // 测试不同网络条件
    char* network_conditions[] = {"4G_STRONG", "4G_WEAK", "WIFI_STRONG", "WIFI_WEAK"};

    for(int i = 0; i < 4; i++) {
        printf("Testing network condition: %s\n", network_conditions[i]);

        // 模拟网络条件
        simulate_network_condition(network_conditions[i]);

        // 执行上传测试
        uint32_t total_uploaded = 0;
        uint32_t successful_uploads = 0;
        uint32_t total_latency = 0;

        for(int j = 0; j < 10; j++) {
            uint32_t start_time = get_timestamp();

            // 创建测试文件
            char test_file[256];
            sprintf(test_file, "test_%d_%d.mp4", i, j);
            create_test_video_segment(test_file, 10); // 10秒视频

            // 上传测试
            if(upload_test_file(test_file) == 0) {
                successful_uploads++;
                uint32_t end_time = get_timestamp();
                total_latency += (end_time - start_time);
            }

            total_uploaded++;
            remove(test_file);
        }

        // 记录结果
        network_performance_t *result = &results[test_count++];
        strcpy(result->network_type, network_conditions[i]);
        result->signal_strength = get_signal_strength();
        result->upload_speed = calculate_upload_speed();
        result->success_rate = (successful_uploads * 100) / total_uploaded;
        result->avg_latency = total_latency / successful_uploads;

        printf("Results - Speed: %d KB/s, Success: %d%%, Latency: %d ms\n",
               result->upload_speed, result->success_rate, result->avg_latency);
    }

    return 0;
}
```

**内存和CPU性能测试**：
```c
// 系统资源监控
typedef struct {
    uint32_t cpu_usage;      // CPU使用率 (%)
    uint32_t memory_usage;   // 内存使用量 (KB)
    uint32_t storage_usage;  // 存储使用量 (KB)
    uint32_t battery_level;  // 电池电量 (%)
} system_resources_t;

int monitor_system_resources(uint32_t duration_seconds) {
    system_resources_t resources[1000];
    int sample_count = 0;

    printf("Starting system resource monitoring for %d seconds...\n", duration_seconds);

    for(uint32_t i = 0; i < duration_seconds; i++) {
        system_resources_t *sample = &resources[sample_count++];

        sample->cpu_usage = get_cpu_usage();
        sample->memory_usage = get_memory_usage();
        sample->storage_usage = get_storage_usage();
        sample->battery_level = get_battery_level();

        // 每10秒打印一次
        if(i % 10 == 0) {
            printf("Time: %ds, CPU: %d%%, Memory: %dKB, Storage: %dKB, Battery: %d%%\n",
                   i, sample->cpu_usage, sample->memory_usage,
                   sample->storage_usage, sample->battery_level);
        }

        sleep(1);
    }

    // 计算平均值
    uint32_t avg_cpu = 0, avg_memory = 0, avg_storage = 0;
    for(int i = 0; i < sample_count; i++) {
        avg_cpu += resources[i].cpu_usage;
        avg_memory += resources[i].memory_usage;
        avg_storage += resources[i].storage_usage;
    }

    avg_cpu /= sample_count;
    avg_memory /= sample_count;
    avg_storage /= sample_count;

    printf("Average - CPU: %d%%, Memory: %dKB, Storage: %dKB\n",
           avg_cpu, avg_memory, avg_storage);

    return 0;
}
```

#### **2. 应用端性能测试**

**Android性能测试**：
```kotlin
@RunWith(AndroidJUnit4::class)
class PerformanceTest {

    @Test
    fun testVideoPlaybackPerformance() {
        val performanceMetrics = mutableListOf<PlaybackMetrics>()

        // 测试不同分辨率视频的播放性能
        val testVideos = listOf(
            "video/test_device/processed/20250101/complete_720p.mp4",
            "video/test_device/processed/20250101/complete_1080p.mp4"
        )

        testVideos.forEach { videoPath ->
            val startTime = System.currentTimeMillis()
            var bufferingEvents = 0
            var frameDrops = 0

            // 模拟播放
            val videoPlayerManager = VideoPlayerManager(context)
            videoPlayerManager.initializePlayer()

            // 添加性能监听器
            videoPlayerManager.addPlayerListener(object : VideoPlayerManager.PlayerEventListener {
                override fun onPlayerStateChanged(isPlaying: Boolean, playbackState: Int) {
                    if (playbackState == Player.STATE_BUFFERING) {
                        bufferingEvents++
                    }
                }

                override fun onPlayerError(error: PlaybackException) {
                    // 记录错误
                }

                override fun onPositionChanged(position: Long, duration: Long) {
                    // 检测帧丢失
                    // 实际实现需要更复杂的逻辑
                }
            })

            // 播放30秒
            Thread.sleep(30000)

            val endTime = System.currentTimeMillis()
            val metrics = PlaybackMetrics(
                videoPath = videoPath,
                playbackDuration = endTime - startTime,
                bufferingEvents = bufferingEvents,
                frameDrops = frameDrops,
                memoryUsage = getMemoryUsage()
            )

            performanceMetrics.add(metrics)
            videoPlayerManager.releasePlayer()
        }

        // 验证性能指标
        performanceMetrics.forEach { metrics ->
            assertThat(metrics.bufferingEvents).isLessThan(5) // 缓冲事件少于5次
            assertThat(metrics.frameDrops).isLessThan(10)     // 丢帧少于10次
            assertThat(metrics.memoryUsage).isLessThan(100 * 1024 * 1024) // 内存使用少于100MB
        }
    }

    @Test
    fun testDownloadPerformance() {
        val downloadMetrics = mutableListOf<DownloadMetrics>()

        // 测试不同大小文件的下载性能
        val testFiles = listOf(
            "video/test_device/processed/20250101/small_file.mp4",   // ~5MB
            "video/test_device/processed/20250101/medium_file.mp4",  // ~20MB
            "video/test_device/processed/20250101/large_file.mp4"    // ~50MB
        )

        testFiles.forEach { filePath ->
            val startTime = System.currentTimeMillis()
            val localFile = File(context.cacheDir, filePath.substringAfterLast("/"))

            runBlocking {
                val success = ObsManager.getInstance().downloadFile(filePath, localFile)
                val endTime = System.currentTimeMillis()

                if (success) {
                    val metrics = DownloadMetrics(
                        filePath = filePath,
                        fileSize = localFile.length(),
                        downloadTime = endTime - startTime,
                        downloadSpeed = (localFile.length() * 1000) / (endTime - startTime) // bytes/sec
                    )
                    downloadMetrics.add(metrics)
                }

                localFile.delete()
            }
        }

        // 验证下载性能
        downloadMetrics.forEach { metrics ->
            val speedMBps = metrics.downloadSpeed / (1024 * 1024) // MB/s
            assertThat(speedMBps).isGreaterThan(1) // 下载速度大于1MB/s
        }
    }

    private fun getMemoryUsage(): Long {
        val runtime = Runtime.getRuntime()
        return runtime.totalMemory() - runtime.freeMemory()
    }
}

data class PlaybackMetrics(
    val videoPath: String,
    val playbackDuration: Long,
    val bufferingEvents: Int,
    val frameDrops: Int,
    val memoryUsage: Long
)

data class DownloadMetrics(
    val filePath: String,
    val fileSize: Long,
    val downloadTime: Long,
    val downloadSpeed: Long
)
```

## 🚀 四、项目上线与维护

### **（一）设备端部署与维护**

#### **1. 生产环境部署**

**固件打包配置**：
```makefile
# Makefile for production build
PRODUCTION_FLAGS = -DPRODUCTION_BUILD -O3 -DNDEBUG
DEVICE_CONFIG = -DDEVICE_TYPE=LEARNING_SUPERVISION -DHARDWARE_VERSION=3.0

# OBS配置
OBS_CONFIG = -DOBS_ENDPOINT=\"obs.cn-north-4.myhuaweicloud.com\" \
             -DOBS_BUCKET=\"learning-supervision-obs\" \
             -DOBS_REGION=\"cn-north-4\"

# MQTT配置
MQTT_CONFIG = -DMQTT_BROKER=\"iot-mqtts.cn-north-4.myhuaweicloud.com\" \
              -DMQTT_PORT=8883 \
              -DMQTT_KEEPALIVE=60

# 编译目标
production: clean
	$(CC) $(CFLAGS) $(PRODUCTION_FLAGS) $(DEVICE_CONFIG) $(OBS_CONFIG) $(MQTT_CONFIG) \
	      -o learning_supervision_device src/*.c

# 固件打包
firmware_package: production
	mkdir -p firmware_package
	cp learning_supervision_device firmware_package/
	cp config/production_config.json firmware_package/
	cp scripts/install.sh firmware_package/
	tar -czf learning_supervision_firmware_v3.0.tar.gz firmware_package/
```

**设备配置管理**：
```c
// 生产环境配置结构
typedef struct {
    char device_id[64];
    char device_secret[128];
    char mqtt_broker[256];
    uint16_t mqtt_port;
    char obs_endpoint[256];
    char obs_bucket[64];
    uint32_t video_bitrate;
    uint32_t audio_bitrate;
    uint32_t upload_retry_count;
    uint32_t heartbeat_interval;
} production_config_t;

// 配置文件加载
int load_production_config(production_config_t *config) {
    FILE *config_file = fopen("/etc/learning_supervision/config.json", "r");
    if (!config_file) {
        printf("Error: Cannot open production config file\n");
        return -1;
    }

    // 解析JSON配置
    char buffer[4096];
    fread(buffer, 1, sizeof(buffer), config_file);
    fclose(config_file);

    // 使用cJSON解析配置
    cJSON *json = cJSON_Parse(buffer);
    if (!json) {
        printf("Error: Invalid JSON config\n");
        return -1;
    }

    // 提取配置项
    cJSON *device_id = cJSON_GetObjectItem(json, "device_id");
    cJSON *device_secret = cJSON_GetObjectItem(json, "device_secret");
    cJSON *mqtt_broker = cJSON_GetObjectItem(json, "mqtt_broker");

    if (device_id && device_secret && mqtt_broker) {
        strcpy(config->device_id, device_id->valuestring);
        strcpy(config->device_secret, device_secret->valuestring);
        strcpy(config->mqtt_broker, mqtt_broker->valuestring);

        // 设置默认值
        config->mqtt_port = 8883;
        strcpy(config->obs_endpoint, "obs.cn-north-4.myhuaweicloud.com");
        strcpy(config->obs_bucket, "learning-supervision-obs");
        config->video_bitrate = 1500000;
        config->audio_bitrate = 128000;
        config->upload_retry_count = 3;
        config->heartbeat_interval = 60;
    } else {
        printf("Error: Missing required config fields\n");
        cJSON_Delete(json);
        return -1;
    }

    cJSON_Delete(json);
    return 0;
}
```

#### **2. 监控与日志系统**

**设备状态监控**：
```c
// 设备监控数据结构
typedef struct {
    uint32_t timestamp;
    uint32_t cpu_usage;
    uint32_t memory_usage;
    uint32_t storage_usage;
    uint32_t battery_level;
    uint32_t signal_strength;
    uint32_t upload_success_rate;
    uint32_t video_segments_uploaded;
    uint32_t audio_segments_uploaded;
    uint32_t error_count;
} device_status_t;

// 状态上报
int report_device_status() {
    device_status_t status;

    // 收集系统状态
    status.timestamp = get_timestamp();
    status.cpu_usage = get_cpu_usage();
    status.memory_usage = get_memory_usage();
    status.storage_usage = get_storage_usage();
    status.battery_level = get_battery_level();
    status.signal_strength = get_signal_strength();

    // 收集业务状态
    status.upload_success_rate = calculate_upload_success_rate();
    status.video_segments_uploaded = get_video_upload_count();
    status.audio_segments_uploaded = get_audio_upload_count();
    status.error_count = get_error_count();

    // 构建MQTT消息
    cJSON *json = cJSON_CreateObject();
    cJSON_AddNumberToObject(json, "timestamp", status.timestamp);
    cJSON_AddNumberToObject(json, "cpu_usage", status.cpu_usage);
    cJSON_AddNumberToObject(json, "memory_usage", status.memory_usage);
    cJSON_AddNumberToObject(json, "storage_usage", status.storage_usage);
    cJSON_AddNumberToObject(json, "battery_level", status.battery_level);
    cJSON_AddNumberToObject(json, "signal_strength", status.signal_strength);
    cJSON_AddNumberToObject(json, "upload_success_rate", status.upload_success_rate);
    cJSON_AddNumberToObject(json, "video_segments_uploaded", status.video_segments_uploaded);
    cJSON_AddNumberToObject(json, "audio_segments_uploaded", status.audio_segments_uploaded);
    cJSON_AddNumberToObject(json, "error_count", status.error_count);

    char *json_string = cJSON_Print(json);

    // 发送状态报告
    char topic[256];
    sprintf(topic, "$oc/devices/%s/sys/status/up", get_device_id());
    int result = mqtt_publish(topic, json_string);

    free(json_string);
    cJSON_Delete(json);

    return result;
}
```

**日志管理系统**：
```c
// 日志级别定义
typedef enum {
    LOG_LEVEL_DEBUG = 0,
    LOG_LEVEL_INFO = 1,
    LOG_LEVEL_WARN = 2,
    LOG_LEVEL_ERROR = 3,
    LOG_LEVEL_FATAL = 4
} log_level_t;

// 日志配置
typedef struct {
    log_level_t min_level;
    uint32_t max_file_size;
    uint32_t max_file_count;
    char log_directory[256];
    bool enable_remote_log;
} log_config_t;

// 日志系统初始化
int log_system_init(log_config_t *config) {
    // 创建日志目录
    mkdir(config->log_directory, 0755);

    // 设置日志配置
    set_log_level(config->min_level);
    set_log_rotation(config->max_file_size, config->max_file_count);

    // 启用远程日志（如果配置）
    if (config->enable_remote_log) {
        enable_remote_logging();
    }

    LOG_INFO("Log system initialized");
    return 0;
}

// 日志记录宏
#define LOG_DEBUG(fmt, ...) log_write(LOG_LEVEL_DEBUG, __FILE__, __LINE__, fmt, ##__VA_ARGS__)
#define LOG_INFO(fmt, ...)  log_write(LOG_LEVEL_INFO, __FILE__, __LINE__, fmt, ##__VA_ARGS__)
#define LOG_WARN(fmt, ...)  log_write(LOG_LEVEL_WARN, __FILE__, __LINE__, fmt, ##__VA_ARGS__)
#define LOG_ERROR(fmt, ...) log_write(LOG_LEVEL_ERROR, __FILE__, __LINE__, fmt, ##__VA_ARGS__)
#define LOG_FATAL(fmt, ...) log_write(LOG_LEVEL_FATAL, __FILE__, __LINE__, fmt, ##__VA_ARGS__)

// 日志写入实现
void log_write(log_level_t level, const char *file, int line, const char *fmt, ...) {
    if (level < get_current_log_level()) {
        return;
    }

    char timestamp[32];
    get_timestamp_string(timestamp, sizeof(timestamp));

    char level_str[16];
    get_log_level_string(level, level_str, sizeof(level_str));

    va_list args;
    va_start(args, fmt);

    // 写入本地日志文件
    FILE *log_file = get_current_log_file();
    fprintf(log_file, "[%s] [%s] [%s:%d] ", timestamp, level_str, file, line);
    vfprintf(log_file, fmt, args);
    fprintf(log_file, "\n");
    fflush(log_file);

    // 如果是错误级别，同时发送远程日志
    if (level >= LOG_LEVEL_ERROR && is_remote_logging_enabled()) {
        char log_message[1024];
        vsnprintf(log_message, sizeof(log_message), fmt, args);
        send_remote_log(level, file, line, log_message);
    }

    va_end(args);
}
```

#### **3. 远程升级系统**

**OTA升级实现**：
```c
// OTA升级配置
typedef struct {
    char firmware_url[512];
    char firmware_version[32];
    char firmware_hash[64];
    uint32_t firmware_size;
    bool force_upgrade;
} ota_config_t;

// OTA升级流程
int perform_ota_upgrade(ota_config_t *ota_config) {
    LOG_INFO("Starting OTA upgrade to version %s", ota_config->firmware_version);

    // 1. 检查存储空间
    uint32_t available_space = get_available_storage();
    if (available_space < ota_config->firmware_size * 2) {
        LOG_ERROR("Insufficient storage space for OTA upgrade");
        return -1;
    }

    // 2. 下载固件
    char firmware_path[] = "/tmp/firmware_update.bin";
    if (download_firmware(ota_config->firmware_url, firmware_path) != 0) {
        LOG_ERROR("Failed to download firmware");
        return -1;
    }

    // 3. 验证固件
    char downloaded_hash[64];
    if (calculate_file_hash(firmware_path, downloaded_hash) != 0) {
        LOG_ERROR("Failed to calculate firmware hash");
        remove(firmware_path);
        return -1;
    }

    if (strcmp(downloaded_hash, ota_config->firmware_hash) != 0) {
        LOG_ERROR("Firmware hash mismatch");
        remove(firmware_path);
        return -1;
    }

    // 4. 备份当前固件
    if (backup_current_firmware() != 0) {
        LOG_WARN("Failed to backup current firmware");
    }

    // 5. 安装新固件
    if (install_firmware(firmware_path) != 0) {
        LOG_ERROR("Failed to install firmware");
        restore_firmware_backup();
        remove(firmware_path);
        return -1;
    }

    // 6. 清理临时文件
    remove(firmware_path);

    // 7. 上报升级状态
    report_ota_status("SUCCESS", ota_config->firmware_version);

    LOG_INFO("OTA upgrade completed successfully");

    // 8. 重启设备
    system_reboot(5); // 5秒后重启

    return 0;
}
```

### **（二）应用端部署与维护**

#### **1. Android应用发布**

**发布配置**：
```gradle
android {
    signingConfigs {
        release {
            storeFile file('../keystore/learning_supervision.jks')
            storePassword project.hasProperty('KEYSTORE_PASSWORD') ? KEYSTORE_PASSWORD : ''
            keyAlias 'learning_supervision_key'
            keyPassword project.hasProperty('KEY_PASSWORD') ? KEY_PASSWORD : ''
        }
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release

            // 生产环境配置
            buildConfigField "String", "OBS_ENDPOINT", "\"https://obs.cn-north-4.myhuaweicloud.com\""
            buildConfigField "String", "OBS_BUCKET", "\"learning-supervision-obs\""
            buildConfigField "boolean", "DEBUG_MODE", "false"

            // 性能优化
            manifestPlaceholders = [
                app_name: "学习监督助手",
                app_icon: "@mipmap/ic_launcher"
            ]
        }
    }
}
```

**ProGuard混淆配置**：
```proguard
# learning_supervision_proguard_rules.pro

# 保留OBS SDK
-keep class com.obs.services.** { *; }
-keep class com.obs.services.model.** { *; }

# 保留ExoPlayer
-keep class com.google.android.exoplayer2.** { *; }

# 保留数据模型
-keep class com.learningsupervision.iot.data.model.** { *; }

# 保留Retrofit接口
-keep interface com.learningsupervision.iot.data.remote.** { *; }

# 保留Room数据库
-keep class * extends androidx.room.RoomDatabase
-keep @androidx.room.Entity class *
-keep @androidx.room.Dao class *

# 网络安全配置
-keep class okhttp3.** { *; }
-keep class retrofit2.** { *; }
```

#### **2. 应用监控系统**

**性能监控**：
```kotlin
class AppPerformanceMonitor {
    private val performanceMetrics = mutableListOf<PerformanceMetric>()

    fun startMonitoring() {
        // 内存监控
        startMemoryMonitoring()

        // 网络监控
        startNetworkMonitoring()

        // 崩溃监控
        startCrashMonitoring()

        // 用户行为监控
        startUserBehaviorMonitoring()
    }

    private fun startMemoryMonitoring() {
        val handler = Handler(Looper.getMainLooper())
        val memoryRunnable = object : Runnable {
            override fun run() {
                val runtime = Runtime.getRuntime()
                val usedMemory = runtime.totalMemory() - runtime.freeMemory()
                val maxMemory = runtime.maxMemory()
                val memoryUsagePercent = (usedMemory * 100) / maxMemory

                if (memoryUsagePercent > 80) {
                    Log.w("PerformanceMonitor", "High memory usage: $memoryUsagePercent%")
                    reportPerformanceIssue("HIGH_MEMORY_USAGE", memoryUsagePercent.toString())
                }

                handler.postDelayed(this, 30000) // 每30秒检查一次
            }
        }
        handler.post(memoryRunnable)
    }

    private fun startNetworkMonitoring() {
        // 监控网络请求性能
        val okHttpClient = OkHttpClient.Builder()
            .addInterceptor { chain ->
                val request = chain.request()
                val startTime = System.currentTimeMillis()

                val response = chain.proceed(request)

                val endTime = System.currentTimeMillis()
                val duration = endTime - startTime

                // 记录慢请求
                if (duration > 5000) { // 超过5秒
                    Log.w("PerformanceMonitor", "Slow network request: ${request.url} took ${duration}ms")
                    reportPerformanceIssue("SLOW_NETWORK_REQUEST", "${request.url}:$duration")
                }

                response
            }
            .build()
    }

    private fun startCrashMonitoring() {
        Thread.setDefaultUncaughtExceptionHandler { thread, exception ->
            Log.e("CrashMonitor", "Uncaught exception in thread ${thread.name}", exception)

            // 收集崩溃信息
            val crashInfo = CrashInfo(
                timestamp = System.currentTimeMillis(),
                threadName = thread.name,
                exceptionType = exception.javaClass.simpleName,
                exceptionMessage = exception.message ?: "",
                stackTrace = exception.stackTraceToString(),
                deviceInfo = getDeviceInfo(),
                appVersion = BuildConfig.VERSION_NAME
            )

            // 保存崩溃日志
            saveCrashLog(crashInfo)

            // 上报崩溃信息
            reportCrash(crashInfo)

            // 重启应用
            restartApp()
        }
    }

    private fun reportPerformanceIssue(type: String, details: String) {
        // 上报性能问题到服务器
        val performanceIssue = PerformanceIssue(
            type = type,
            details = details,
            timestamp = System.currentTimeMillis(),
            deviceInfo = getDeviceInfo(),
            appVersion = BuildConfig.VERSION_NAME
        )

        // 异步上报
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 发送到监控服务器
                sendPerformanceReport(performanceIssue)
            } catch (e: Exception) {
                Log.e("PerformanceMonitor", "Failed to report performance issue", e)
            }
        }
    }
}
```

#### **3. 用户反馈系统**

**反馈收集**：
```kotlin
class FeedbackManager {

    fun collectUserFeedback(
        feedbackType: FeedbackType,
        description: String,
        attachments: List<File> = emptyList()
    ) {
        val feedback = UserFeedback(
            id = UUID.randomUUID().toString(),
            type = feedbackType,
            description = description,
            timestamp = System.currentTimeMillis(),
            deviceInfo = getDeviceInfo(),
            appVersion = BuildConfig.VERSION_NAME,
            userId = getCurrentUserId(),
            attachments = attachments.map { it.name }
        )

        // 保存到本地数据库
        saveFeedbackToLocal(feedback)

        // 上传反馈和附件
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 上传附件到OBS
                val attachmentUrls = uploadAttachments(attachments)
                feedback.attachmentUrls = attachmentUrls

                // 提交反馈
                submitFeedback(feedback)

                // 标记为已提交
                markFeedbackAsSubmitted(feedback.id)

            } catch (e: Exception) {
                Log.e("FeedbackManager", "Failed to submit feedback", e)
                // 标记为提交失败，稍后重试
                markFeedbackAsFailed(feedback.id)
            }
        }
    }

    private suspend fun uploadAttachments(attachments: List<File>): List<String> {
        val urls = mutableListOf<String>()

        attachments.forEach { file ->
            try {
                val objectKey = "feedback/attachments/${UUID.randomUUID()}_${file.name}"
                val success = ObsManager.getInstance().uploadFile(objectKey, file)

                if (success) {
                    val url = ObsManager.getInstance().generatePresignedUrl(objectKey, 86400) // 24小时有效
                    urls.add(url)
                }
            } catch (e: Exception) {
                Log.e("FeedbackManager", "Failed to upload attachment: ${file.name}", e)
            }
        }

        return urls
    }
}

enum class FeedbackType {
    BUG_REPORT,
    FEATURE_REQUEST,
    PERFORMANCE_ISSUE,
    USER_EXPERIENCE,
    OTHER
}

data class UserFeedback(
    val id: String,
    val type: FeedbackType,
    val description: String,
    val timestamp: Long,
    val deviceInfo: DeviceInfo,
    val appVersion: String,
    val userId: String,
    val attachments: List<String>,
    var attachmentUrls: List<String> = emptyList()
)
```

### **（三）运维监控平台**

#### **1. 设备监控仪表板**

**监控指标定义**：
```json
{
  "device_metrics": {
    "system_metrics": [
      {
        "name": "cpu_usage",
        "unit": "percent",
        "threshold": {"warning": 70, "critical": 90}
      },
      {
        "name": "memory_usage",
        "unit": "percent",
        "threshold": {"warning": 80, "critical": 95}
      },
      {
        "name": "storage_usage",
        "unit": "percent",
        "threshold": {"warning": 85, "critical": 95}
      },
      {
        "name": "battery_level",
        "unit": "percent",
        "threshold": {"warning": 20, "critical": 10}
      }
    ],
    "business_metrics": [
      {
        "name": "upload_success_rate",
        "unit": "percent",
        "threshold": {"warning": 90, "critical": 80}
      },
      {
        "name": "video_quality_score",
        "unit": "score",
        "threshold": {"warning": 7, "critical": 5}
      },
      {
        "name": "audio_quality_score",
        "unit": "score",
        "threshold": {"warning": 7, "critical": 5}
      }
    ]
  },
  "app_metrics": {
    "performance_metrics": [
      {
        "name": "app_startup_time",
        "unit": "milliseconds",
        "threshold": {"warning": 3000, "critical": 5000}
      },
      {
        "name": "video_playback_latency",
        "unit": "milliseconds",
        "threshold": {"warning": 2000, "critical": 5000}
      },
      {
        "name": "crash_rate",
        "unit": "percent",
        "threshold": {"warning": 1, "critical": 5}
      }
    ]
  }
}
```

---

**总结**: 完整的音视频模块重新设计方案已完成，涵盖了从设备端到应用端的全栈解决方案，包括华为云OBS集成、性能优化、测试方案和运维监控，为学习监督项目提供了完整的技术架构和实施指南。
