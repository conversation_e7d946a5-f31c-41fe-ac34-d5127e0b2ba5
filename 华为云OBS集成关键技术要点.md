# 华为云OBS集成关键技术要点

## 🎯 文档目标

本文档专门描述华为云OBS（对象存储服务）在IoT Android应用中的集成关键技术要点，为开发者提供深度的技术实现指南。

## 🔑 OBS集成核心要点

### **1. 虚拟主机域名要求（关键）**

华为云OBS要求使用虚拟主机域名格式访问，这是与标准S3协议的重要差异：

#### **A. URL格式对比**
```kotlin
// ❌ 错误：路径格式（会返回VirtualHostDomainRequired错误）
"https://obs.cn-north-4.myhuaweicloud.com/iotdavideo/voice/file.3gp"

// ✅ 正确：虚拟主机域名格式
"https://iotdavideo.obs.cn-north-4.myhuaweicloud.com/voice/file.3gp"
```

#### **B. 实现代码**
```kotlin
// 构建虚拟主机域名URL
val virtualHostEndpoint = "https://$BUCKET_NAME.obs.$REGION.myhuaweicloud.com"
val url = "$virtualHostEndpoint/$objectKey"

// 设置正确的Host头
.header("Host", "$BUCKET_NAME.obs.$REGION.myhuaweicloud.com")
```

### **2. 签名算法特殊性（核心）**

华为云OBS的签名算法有特殊要求，即使使用虚拟主机域名，签名计算中仍需包含bucket名称：

#### **A. CanonicalizedResource构建**
```kotlin
// 华为云OBS特殊要求：即使使用虚拟主机域名，签名中仍需包含bucket名称
val canonicalizedResource = "/$BUCKET_NAME/$objectKey"
// 例如：/iotdavideo/voice/parent/1751733548670_voice_message.3gp
```

#### **B. StringToSign格式**
```kotlin
val stringToSign = buildString {
    append(method).append("\n")           // PUT
    append(contentMD5).append("\n")       // 6r6kxufrPWUBD3jCrseodw==
    append(contentType).append("\n")      // audio/3gpp
    append(date).append("\n")             // Sat, 05 Jul 2025 16:39:08 GMT
    append(canonicalizedResource)         // /iotdavideo/voice/parent/file.3gp
}
```

#### **C. 完整签名实现**
```kotlin
private fun generateAuthorization(
    method: String,
    objectKey: String,
    contentType: String,
    date: String,
    contentMD5: String
): String {
    // 1. 构建CanonicalizedResource（包含bucket名称）
    val canonicalizedResource = "/$BUCKET_NAME/$objectKey"
    
    // 2. 构建StringToSign
    val stringToSign = "$method\n$contentMD5\n$contentType\n$date\n$canonicalizedResource"
    
    // 3. UTF-8编码
    val stringToSignBytes = stringToSign.toByteArray(Charsets.UTF_8)
    val secretKeyBytes = SECRET_KEY.toByteArray(Charsets.UTF_8)
    
    // 4. HMAC-SHA1签名
    val mac = Mac.getInstance("HmacSHA1")
    val secretKeySpec = SecretKeySpec(secretKeyBytes, "HmacSHA1")
    mac.init(secretKeySpec)
    val signature = mac.doFinal(stringToSignBytes)
    
    // 5. Base64编码（无换行符）
    val signatureBase64 = android.util.Base64.encodeToString(signature, android.util.Base64.NO_WRAP)
    
    // 6. 构建Authorization头
    return "OBS $ACCESS_KEY:$signatureBase64"
}
```

### **3. HTTP请求头配置**

#### **A. 必需的请求头**
```kotlin
val request = Request.Builder()
    .url(url)
    .put(requestBody)
    .header("Host", "$BUCKET_NAME.obs.$REGION.myhuaweicloud.com")  // 虚拟主机Host
    .header("Date", date)                                          // GMT时间
    .header("Authorization", authorization)                        // OBS签名
    .header("Content-Type", contentType)                          // MIME类型
    .header("Content-MD5", md5Hash)                               // MD5校验
    .header("Content-Length", file.length().toString())          // 文件大小
    .build()
```

#### **B. Content-Type映射**
```kotlin
private fun getContentType(fileExtension: String): String {
    return when (fileExtension.lowercase()) {
        "3gp" -> "audio/3gpp"
        "aac" -> "audio/aac"
        "mp4" -> "audio/mp4"
        "wav" -> "audio/wav"
        "m4a" -> "audio/mp4"
        else -> "audio/mpeg"
    }
}
```

### **4. MD5计算与验证**

#### **A. 文件MD5计算**
```kotlin
private fun calculateMD5(file: File): String {
    return try {
        val md = MessageDigest.getInstance("MD5")
        file.inputStream().use { input ->
            val buffer = ByteArray(8192)
            var bytesRead: Int
            while (input.read(buffer).also { bytesRead = it } != -1) {
                md.update(buffer, 0, bytesRead)
            }
        }
        android.util.Base64.encodeToString(md.digest(), android.util.Base64.NO_WRAP)
    } catch (e: Exception) {
        Logger.e("计算MD5失败: ${e.message}")
        ""
    }
}
```

#### **B. MD5验证重要性**
- **数据完整性** - 确保上传过程中文件未损坏
- **华为云要求** - OBS API要求提供Content-MD5头
- **错误检测** - MD5不匹配时服务器返回BadDigest错误

### **5. 时间格式处理**

#### **A. GMT时间生成**
```kotlin
private fun getGMTDate(): String {
    val sdf = SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss 'GMT'", Locale.US)
    sdf.timeZone = TimeZone.getTimeZone("GMT")
    return sdf.format(Date())
}
```

#### **B. 时间同步要求**
- **时间差限制** - 客户端时间与服务器时间差不能超过15分钟
- **时区处理** - 必须使用GMT时区
- **格式严格** - 必须符合RFC 1123格式

### **6. 错误处理与诊断**

#### **A. 常见错误码处理**
```kotlin
private fun handleObsError(response: Response): String {
    val errorBody = response.body?.string() ?: ""
    
    return when (response.code) {
        403 -> when {
            errorBody.contains("SignatureDoesNotMatch") -> {
                Logger.e("签名不匹配错误详情: $errorBody")
                "签名计算错误，请检查AK/SK和签名算法"
            }
            errorBody.contains("VirtualHostDomainRequired") -> {
                "必须使用虚拟主机域名格式访问"
            }
            errorBody.contains("RequestTimeTooSkewed") -> {
                "客户端时间与服务器时间差异过大"
            }
            else -> "访问被拒绝: $errorBody"
        }
        400 -> when {
            errorBody.contains("BadDigest") -> "文件MD5校验失败"
            errorBody.contains("InvalidArgument") -> "请求参数无效"
            else -> "请求格式错误: $errorBody"
        }
        404 -> "存储桶或对象不存在"
        else -> "HTTP错误 ${response.code}: $errorBody"
    }
}
```

#### **B. 调试信息输出**
```kotlin
private fun logRequestDetails(request: Request, objectKey: String) {
    Logger.d("🔗 [OBS请求] URL: ${request.url}")
    Logger.d("🔗 [OBS请求] ObjectKey: $objectKey")
    Logger.d("🔗 [OBS请求] Host: ${request.header("Host")}")
    Logger.d("🔗 [OBS请求] Date: ${request.header("Date")}")
    Logger.d("🔗 [OBS请求] Authorization: ${request.header("Authorization")}")
    Logger.d("🔗 [OBS请求] Content-Type: ${request.header("Content-Type")}")
    Logger.d("🔗 [OBS请求] Content-MD5: ${request.header("Content-MD5")}")
}
```

### **7. 性能优化策略**

#### **A. 连接池配置**
```kotlin
private val httpClient = OkHttpClient.Builder()
    .connectTimeout(30, TimeUnit.SECONDS)
    .writeTimeout(60, TimeUnit.SECONDS)
    .readTimeout(60, TimeUnit.SECONDS)
    .connectionPool(ConnectionPool(5, 5, TimeUnit.MINUTES))
    .retryOnConnectionFailure(true)
    .build()
```

#### **B. 分片上传（大文件）**
```kotlin
private suspend fun uploadLargeFile(objectKey: String, file: File): ObsApiResult<String> {
    if (file.length() > MULTIPART_THRESHOLD) {
        return uploadFileInParts(objectKey, file)
    } else {
        return putObject(objectKey, file, getContentType(file.extension))
    }
}

private suspend fun uploadFileInParts(objectKey: String, file: File): ObsApiResult<String> {
    // 1. 初始化分片上传
    val uploadId = initiateMultipartUpload(objectKey)
    
    // 2. 分片上传
    val partSize = 5 * 1024 * 1024 // 5MB per part
    val parts = mutableListOf<PartETag>()
    
    file.inputStream().use { input ->
        var partNumber = 1
        val buffer = ByteArray(partSize)
        
        while (true) {
            val bytesRead = input.read(buffer)
            if (bytesRead == -1) break
            
            val partData = buffer.copyOf(bytesRead)
            val etag = uploadPart(objectKey, uploadId, partNumber, partData)
            parts.add(PartETag(partNumber, etag))
            partNumber++
        }
    }
    
    // 3. 完成分片上传
    return completeMultipartUpload(objectKey, uploadId, parts)
}
```

### **8. 安全性考虑**

#### **A. AK/SK安全存储**
```kotlin
// ❌ 不安全：硬编码在代码中
const val ACCESS_KEY = "HPUAFQCTHCE7ZQ854RXI"

// ✅ 安全：从安全存储中获取
private fun getAccessKey(): String {
    return EncryptedSharedPreferences.create(
        "obs_credentials",
        MasterKeys.getOrCreate(MasterKeys.AES256_GCM_SPEC),
        context,
        EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
        EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
    ).getString("access_key", "") ?: ""
}
```

#### **B. 临时凭证支持**
```kotlin
private fun addSecurityTokenIfPresent(requestBuilder: Request.Builder) {
    val securityToken = getSecurityToken()
    if (securityToken.isNotEmpty()) {
        requestBuilder.header("x-obs-security-token", securityToken)
    }
}
```

### **9. 监控与日志**

#### **A. 上传性能监控**
```kotlin
class ObsPerformanceMonitor {
    private val uploadMetrics = ConcurrentHashMap<String, UploadMetric>()
    
    fun recordUploadStart(objectKey: String, fileSize: Long): String {
        val monitorId = UUID.randomUUID().toString()
        uploadMetrics[monitorId] = UploadMetric(
            objectKey = objectKey,
            fileSize = fileSize,
            startTime = System.currentTimeMillis()
        )
        return monitorId
    }
    
    fun recordUploadComplete(monitorId: String, success: Boolean) {
        uploadMetrics[monitorId]?.let { metric ->
            metric.endTime = System.currentTimeMillis()
            metric.success = success
            
            val duration = metric.endTime - metric.startTime
            val speed = if (duration > 0) metric.fileSize * 1000 / duration else 0
            
            Logger.i("OBS上传完成: ${metric.objectKey}, 耗时: ${duration}ms, 速度: ${speed}B/s, 成功: $success")
        }
    }
}
```

#### **B. 错误统计与报告**
```kotlin
class ObsErrorReporter {
    private val errorStats = mutableMapOf<String, Int>()
    
    fun reportError(errorType: String, errorMessage: String) {
        errorStats[errorType] = errorStats.getOrDefault(errorType, 0) + 1
        
        Logger.e("OBS错误: $errorType - $errorMessage")
        
        // 发送错误统计到监控系统
        if (errorStats[errorType]!! > ERROR_THRESHOLD) {
            sendAlertToMonitoring(errorType, errorStats[errorType]!!)
        }
    }
    
    fun getErrorSummary(): Map<String, Int> = errorStats.toMap()
}
```

## 🎯 验证检查清单

### **开发者验证要点**

#### **1. 签名验证**
- [ ] StringToSign格式与服务器期望一致
- [ ] CanonicalizedResource包含bucket名称
- [ ] HMAC-SHA1算法实现正确
- [ ] Base64编码无换行符

#### **2. 请求格式验证**
- [ ] 使用虚拟主机域名URL
- [ ] Host头设置正确
- [ ] Content-MD5计算准确
- [ ] GMT时间格式正确

#### **3. 错误处理验证**
- [ ] 403错误详细分析
- [ ] 网络异常重试机制
- [ ] 文件完整性校验
- [ ] 资源清理机制

#### **4. 性能验证**
- [ ] 上传速度合理
- [ ] 内存使用稳定
- [ ] 并发上传支持
- [ ] 大文件分片上传

## 🚀 最佳实践建议

### **1. 开发阶段**
- 使用详细的调试日志
- 实现完整的错误处理
- 添加性能监控
- 编写单元测试

### **2. 测试阶段**
- 测试各种文件格式
- 验证网络异常情况
- 压力测试并发上传
- 验证签名算法正确性

### **3. 生产部署**
- 使用安全的凭证存储
- 配置合理的超时时间
- 实现监控和告警
- 定期检查错误日志

---

**文档版本**: v1.0  
**最后更新**: 2025-07-06  
**专注领域**: 华为云OBS集成技术要点
