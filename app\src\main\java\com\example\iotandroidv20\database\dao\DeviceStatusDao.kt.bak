package com.example.iotandroidv20.database.dao

import androidx.room.*
import com.example.iotandroidv20.database.entity.DeviceStatusHistoryEntity
import kotlinx.coroutines.flow.Flow

/**
 * 设备状态历史DAO
 */
@Dao
interface DeviceStatusDao {
    
    /**
     * 插入设备状态记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertDeviceStatus(deviceStatus: DeviceStatusHistoryEntity): Long
    
    /**
     * 批量插入设备状态记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertDeviceStatusList(deviceStatusList: List<DeviceStatusHistoryEntity>)
    
    /**
     * 更新设备状态记录
     */
    @Update
    suspend fun updateDeviceStatus(deviceStatus: DeviceStatusHistoryEntity)
    
    /**
     * 删除设备状态记录
     */
    @Delete
    suspend fun deleteDeviceStatus(deviceStatus: DeviceStatusHistoryEntity)
    
    /**
     * 根据ID删除设备状态记录
     */
    @Query("DELETE FROM device_status_history WHERE id = :id")
    suspend fun deleteDeviceStatusById(id: Long)
    
    /**
     * 删除指定时间之前的记录
     */
    @Query("DELETE FROM device_status_history WHERE timestamp < :timestamp")
    suspend fun deleteDeviceStatusBefore(timestamp: Long)
    
    /**
     * 获取所有设备状态记录（按时间倒序）
     */
    @Query("SELECT * FROM device_status_history ORDER BY timestamp DESC")
    fun getAllDeviceStatus(): Flow<List<DeviceStatusHistoryEntity>>
    
    /**
     * 获取最近N条设备状态记录
     */
    @Query("SELECT * FROM device_status_history ORDER BY timestamp DESC LIMIT :limit")
    fun getRecentDeviceStatus(limit: Int): Flow<List<DeviceStatusHistoryEntity>>
    
    /**
     * 获取指定设备的状态记录
     */
    @Query("SELECT * FROM device_status_history WHERE device_id = :deviceId ORDER BY timestamp DESC")
    fun getDeviceStatusByDevice(deviceId: String): Flow<List<DeviceStatusHistoryEntity>>
    
    /**
     * 获取指定时间范围内的设备状态记录
     */
    @Query("SELECT * FROM device_status_history WHERE timestamp BETWEEN :startTime AND :endTime ORDER BY timestamp DESC")
    fun getDeviceStatusByTimeRange(startTime: Long, endTime: Long): Flow<List<DeviceStatusHistoryEntity>>
    
    /**
     * 获取指定状态的记录
     */
    @Query("SELECT * FROM device_status_history WHERE status = :status ORDER BY timestamp DESC")
    fun getDeviceStatusByStatus(status: String): Flow<List<DeviceStatusHistoryEntity>>
    
    /**
     * 获取最新的设备状态记录
     */
    @Query("SELECT * FROM device_status_history ORDER BY timestamp DESC LIMIT 1")
    suspend fun getLatestDeviceStatus(): DeviceStatusHistoryEntity?
    
    /**
     * 获取指定设备的最新状态记录
     */
    @Query("SELECT * FROM device_status_history WHERE device_id = :deviceId ORDER BY timestamp DESC LIMIT 1")
    suspend fun getLatestDeviceStatusByDevice(deviceId: String): DeviceStatusHistoryEntity?
    
    /**
     * 获取记录总数
     */
    @Query("SELECT COUNT(*) FROM device_status_history")
    suspend fun getDeviceStatusCount(): Int
    
    /**
     * 获取指定时间范围内的设备在线时长统计
     */
    @Query("""
        SELECT 
            status,
            COUNT(*) as count,
            AVG(battery_level) as avg_battery,
            AVG(signal_strength) as avg_signal
        FROM device_status_history 
        WHERE timestamp BETWEEN :startTime AND :endTime
        GROUP BY status
    """)
    suspend fun getDeviceStatusStats(startTime: Long, endTime: Long): List<DeviceStatusStat>
    
    /**
     * 获取设备电池电量历史
     */
    @Query("SELECT timestamp, battery_level FROM device_status_history WHERE device_id = :deviceId ORDER BY timestamp DESC LIMIT :limit")
    fun getBatteryHistory(deviceId: String, limit: Int): Flow<List<BatteryRecord>>
    
    /**
     * 获取设备信号强度历史
     */
    @Query("SELECT timestamp, signal_strength FROM device_status_history WHERE device_id = :deviceId ORDER BY timestamp DESC LIMIT :limit")
    fun getSignalHistory(deviceId: String, limit: Int): Flow<List<SignalRecord>>
    
    /**
     * 获取设备状态变化记录
     */
    @Query("""
        SELECT * FROM device_status_history 
        WHERE device_id = :deviceId 
        AND id IN (
            SELECT MIN(id) FROM device_status_history 
            WHERE device_id = :deviceId 
            GROUP BY status, DATE(timestamp/1000, 'unixepoch')
        )
        ORDER BY timestamp DESC
    """)
    fun getDeviceStatusChanges(deviceId: String): Flow<List<DeviceStatusHistoryEntity>>
}

/**
 * 设备状态统计结果
 */
data class DeviceStatusStat(
    val status: String,
    val count: Int,
    val avg_battery: Float,
    val avg_signal: Float
)

/**
 * 电池记录
 */
data class BatteryRecord(
    val timestamp: Long,
    val battery_level: Int
)

/**
 * 信号记录
 */
data class SignalRecord(
    val timestamp: Long,
    val signal_strength: Int
)
