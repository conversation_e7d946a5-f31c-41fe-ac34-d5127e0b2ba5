{"logs": [{"outputFile": "com.example.iotandroidv20.app-mergeDebugResources-62:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5dca3f3744b4fa46ea145e6d4fc9225c\\transformed\\media3-exoplayer-1.2.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,189,251,320,398,468,561,652", "endColumns": "72,60,61,68,77,69,92,90,64", "endOffsets": "123,184,246,315,393,463,556,647,712"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4397,4470,4531,4593,4662,4740,4810,4903,4994", "endColumns": "72,60,61,68,77,69,92,90,64", "endOffsets": "4465,4526,4588,4657,4735,4805,4898,4989,5054"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\835843a1fca13b839a80c71cfb075e12\\transformed\\media3-session-1.2.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,133,211,283,352,434,521,621", "endColumns": "77,77,71,68,81,86,99,105", "endOffsets": "128,206,278,347,429,516,616,722"}, "to": {"startLines": "23,34,147,148,149,150,151,152", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "994,2098,12658,12730,12799,12881,12968,13068", "endColumns": "77,77,71,68,81,86,99,105", "endOffsets": "1067,2171,12725,12794,12876,12963,13063,13169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\31c73cb6dfb12faac49eb1bae4282a74\\transformed\\core-1.16.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "24,25,26,27,28,29,30,161", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1072,1169,1271,1369,1468,1582,1687,13819", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "1164,1266,1364,1463,1577,1682,1804,13915"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\752167a3e67da85263f648420d161268\\transformed\\ui-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,285,394,499,576,653,746,836,919,1002,1089,1161,1237,1315,1391,1473,1541", "endColumns": "94,84,108,104,76,76,92,89,82,82,86,71,75,77,75,81,67,119", "endOffsets": "195,280,389,494,571,648,741,831,914,997,1084,1156,1232,1310,1386,1468,1536,1656"}, "to": {"startLines": "31,32,33,35,36,88,89,153,154,155,156,157,158,159,160,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1809,1904,1989,2176,2281,6237,6314,13174,13264,13347,13430,13517,13589,13665,13743,13920,14002,14070", "endColumns": "94,84,108,104,76,76,92,89,82,82,86,71,75,77,75,81,67,119", "endOffsets": "1899,1984,2093,2276,2353,6309,6402,13259,13342,13425,13512,13584,13660,13738,13814,13997,14065,14185"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\01b6e8b8fea8bb45b55852d4590f3437\\transformed\\foundation-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,87", "endOffsets": "138,226"}, "to": {"startLines": "165,166", "startColumns": "4,4", "startOffsets": "14190,14278", "endColumns": "87,87", "endOffsets": "14273,14361"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2c60f935d2795008c652ca71073c0e75\\transformed\\media3-ui-1.2.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,622,944,1022,1100,1183,1272,1361,1444,1511,1605,1699,1768,1834,1899,1971,2098,2221,2344,2420,2501,2574,2657,2754,2851,2919,2983,3036,3094,3142,3203,3276,3342,3406,3483,3550,3608,3675,3740,3805,3857,3924,4015,4106", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,77,77,82,88,88,82,66,93,93,68,65,64,71,126,122,122,75,80,72,82,96,96,67,63,52,57,47,60,72,65,63,76,66,57,66,64,64,51,66,90,90,54", "endOffsets": "282,617,939,1017,1095,1178,1267,1356,1439,1506,1600,1694,1763,1829,1894,1966,2093,2216,2339,2415,2496,2569,2652,2749,2846,2914,2978,3031,3089,3137,3198,3271,3337,3401,3478,3545,3603,3670,3735,3800,3852,3919,4010,4101,4156"}, "to": {"startLines": "2,11,17,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,672,2358,2436,2514,2597,2686,2775,2858,2925,3019,3113,3182,3248,3313,3385,3512,3635,3758,3834,3915,3988,4071,4168,4265,4333,5059,5112,5170,5218,5279,5352,5418,5482,5559,5626,5684,5751,5816,5881,5933,6000,6091,6182", "endLines": "10,16,22,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "endColumns": "17,12,12,77,77,82,88,88,82,66,93,93,68,65,64,71,126,122,122,75,80,72,82,96,96,67,63,52,57,47,60,72,65,63,76,66,57,66,64,64,51,66,90,90,54", "endOffsets": "332,667,989,2431,2509,2592,2681,2770,2853,2920,3014,3108,3177,3243,3308,3380,3507,3630,3753,3829,3910,3983,4066,4163,4260,4328,4392,5107,5165,5213,5274,5347,5413,5477,5554,5621,5679,5746,5811,5876,5928,5995,6086,6177,6232"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\398f61ea23c76807315a7831064b1215\\transformed\\material3-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,287,409,524,624,723,839,977,1099,1241,1325,1424,1516,1612,1729,1853,1957,2097,2233,2377,2538,2670,2791,2916,3037,3130,3230,3350,3474,3573,3677,3783,3924,4071,4182,4281,4355,4450,4546,4650,4737,4824,4936,5016,5103,5198,5303,5394,5503,5591,5697,5798,5908,6026,6106,6209", "endColumns": "114,116,121,114,99,98,115,137,121,141,83,98,91,95,116,123,103,139,135,143,160,131,120,124,120,92,99,119,123,98,103,105,140,146,110,98,73,94,95,103,86,86,111,79,86,94,104,90,108,87,105,100,109,117,79,102,96", "endOffsets": "165,282,404,519,619,718,834,972,1094,1236,1320,1419,1511,1607,1724,1848,1952,2092,2228,2372,2533,2665,2786,2911,3032,3125,3225,3345,3469,3568,3672,3778,3919,4066,4177,4276,4350,4445,4541,4645,4732,4819,4931,5011,5098,5193,5298,5389,5498,5586,5692,5793,5903,6021,6101,6204,6301"}, "to": {"startLines": "90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6407,6522,6639,6761,6876,6976,7075,7191,7329,7451,7593,7677,7776,7868,7964,8081,8205,8309,8449,8585,8729,8890,9022,9143,9268,9389,9482,9582,9702,9826,9925,10029,10135,10276,10423,10534,10633,10707,10802,10898,11002,11089,11176,11288,11368,11455,11550,11655,11746,11855,11943,12049,12150,12260,12378,12458,12561", "endColumns": "114,116,121,114,99,98,115,137,121,141,83,98,91,95,116,123,103,139,135,143,160,131,120,124,120,92,99,119,123,98,103,105,140,146,110,98,73,94,95,103,86,86,111,79,86,94,104,90,108,87,105,100,109,117,79,102,96", "endOffsets": "6517,6634,6756,6871,6971,7070,7186,7324,7446,7588,7672,7771,7863,7959,8076,8200,8304,8444,8580,8724,8885,9017,9138,9263,9384,9477,9577,9697,9821,9920,10024,10130,10271,10418,10529,10628,10702,10797,10893,10997,11084,11171,11283,11363,11450,11545,11650,11741,11850,11938,12044,12145,12255,12373,12453,12556,12653"}}]}]}