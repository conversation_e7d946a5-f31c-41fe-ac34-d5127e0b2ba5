package com.example.iotandroidv20.navigation

import androidx.compose.runtime.*
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.iotandroidv20.ui.screen.MainScreen
import com.example.iotandroidv20.viewmodel.MainViewModel

/**
 * 应用导航路由
 */
object AppRoutes {
    const val MAIN = "main"
    const val MEDIA_CENTER = "media_center"
}

/**
 * 应用主导航
 */
@Composable
fun AppNavigation(
    navController: NavHostController = rememberNavController()
) {
    NavHost(
        navController = navController,
        startDestination = AppRoutes.MAIN
    ) {
        // 主界面
        composable(AppRoutes.MAIN) {
            val viewModel: MainViewModel = viewModel()
            MainScreen(
                viewModel = viewModel,
                onOpenMediaCenter = {
                    navController.navigate(AppRoutes.MEDIA_CENTER)
                },
                onOpenObsConfig = {
                    navController.navigate(AppRoutes.MEDIA_CENTER)
                }
            )
        }
        
        // 媒体中心
        composable(AppRoutes.MEDIA_CENTER) {
            MediaNavHost(
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }
    }
}
