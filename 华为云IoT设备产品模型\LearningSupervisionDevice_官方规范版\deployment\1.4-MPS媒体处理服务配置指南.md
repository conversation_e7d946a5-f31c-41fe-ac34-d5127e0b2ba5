# MPS媒体处理服务配置指南

## 🎯 配置目标

配置华为云媒体处理服务(MPS)，创建视频和音频处理工作流，实现设备端上传的分片文件自动合并、转码和优化处理。

## 📋 处理需求分析

### **视频处理需求**
- **分片合并**: 将10秒分片合并为完整视频
- **格式转码**: H.264编码，支持多分辨率输出
- **质量优化**: 码率控制、帧率调整
- **缩略图生成**: 自动生成视频预览图

### **音频处理需求**
- **分片合并**: 将30秒分片合并为完整音频
- **格式转码**: AAC编码，支持多比特率输出
- **音质优化**: 降噪、音量标准化
- **波形生成**: 生成音频波形图

### **处理流程设计**
```
设备端分片上传 → OBS存储 → MPS触发处理 → 合并转码 → 输出到processed目录
```

## 🔧 实施步骤

### **步骤1：创建视频处理模板**

#### **视频合并模板**
```json
{
  "template_name": "video_merge_template",
  "template_type": "merge",
  "description": "学习监督视频分片合并模板",
  "input_config": {
    "source_type": "obs",
    "bucket": "learning-supervision-obs",
    "input_pattern": "video/{deviceId}/raw/{date}/{sessionId}/segment_*.mp4",
    "sort_rule": {
      "sort_by": "filename",
      "order": "ascending"
    }
  },
  "output_config": {
    "target_type": "obs",
    "bucket": "learning-supervision-obs",
    "output_path": "video/{deviceId}/processed/{date}/complete_{sessionId}.mp4"
  },
  "merge_config": {
    "video_codec": "h264",
    "audio_codec": "aac",
    "container_format": "mp4",
    "merge_mode": "concat"
  }
}
```

#### **视频转码模板**
```json
{
  "template_name": "video_transcode_template",
  "template_type": "transcode",
  "description": "学习监督视频转码优化模板",
  "video_config": {
    "codec": "h264",
    "profile": "main",
    "level": "3.1",
    "bitrate_control": "vbr",
    "quality_presets": [
      {
        "name": "high_quality",
        "resolution": "1280x720",
        "bitrate": "1500k",
        "fps": 30,
        "gop_size": 60
      },
      {
        "name": "medium_quality", 
        "resolution": "854x480",
        "bitrate": "800k",
        "fps": 25,
        "gop_size": 50
      },
      {
        "name": "low_quality",
        "resolution": "640x360",
        "bitrate": "400k", 
        "fps": 20,
        "gop_size": 40
      }
    ]
  },
  "audio_config": {
    "codec": "aac",
    "bitrate": "128k",
    "sample_rate": "44100",
    "channels": 2
  }
}
```

#### **缩略图生成模板**
```json
{
  "template_name": "video_thumbnail_template",
  "template_type": "thumbnail",
  "description": "视频缩略图生成模板",
  "thumbnail_config": {
    "output_format": "jpg",
    "quality": 85,
    "sizes": [
      {"width": 320, "height": 240, "suffix": "_small"},
      {"width": 640, "height": 480, "suffix": "_medium"},
      {"width": 1280, "height": 720, "suffix": "_large"}
    ],
    "capture_points": [
      {"type": "time", "value": "00:00:05"},
      {"type": "time", "value": "00:01:00"},
      {"type": "percentage", "value": 50}
    ]
  }
}
```

### **步骤2：创建音频处理模板**

#### **音频合并模板**
```json
{
  "template_name": "audio_merge_template",
  "template_type": "merge",
  "description": "学习监督音频分片合并模板",
  "input_config": {
    "source_type": "obs",
    "bucket": "learning-supervision-obs",
    "input_pattern": "audio/{deviceId}/raw/{date}/{sessionId}/segment_*.mp3",
    "sort_rule": {
      "sort_by": "filename",
      "order": "ascending"
    }
  },
  "output_config": {
    "target_type": "obs",
    "bucket": "learning-supervision-obs",
    "output_path": "audio/{deviceId}/processed/{date}/complete_{sessionId}.mp3"
  },
  "merge_config": {
    "audio_codec": "mp3",
    "container_format": "mp3",
    "merge_mode": "concat"
  }
}
```

#### **音频转码模板**
```json
{
  "template_name": "audio_transcode_template",
  "template_type": "transcode",
  "description": "学习监督音频转码优化模板",
  "audio_config": {
    "codec": "aac",
    "quality_presets": [
      {
        "name": "high_quality",
        "bitrate": "256k",
        "sample_rate": "48000",
        "channels": 2
      },
      {
        "name": "medium_quality",
        "bitrate": "128k", 
        "sample_rate": "44100",
        "channels": 2
      },
      {
        "name": "low_quality",
        "bitrate": "64k",
        "sample_rate": "22050",
        "channels": 1
      }
    ],
    "audio_enhancement": {
      "noise_reduction": true,
      "volume_normalization": true,
      "dynamic_range_compression": true
    }
  }
}
```

#### **音频波形生成模板**
```json
{
  "template_name": "audio_waveform_template",
  "template_type": "analysis",
  "description": "音频波形分析模板",
  "waveform_config": {
    "output_format": "png",
    "width": 800,
    "height": 200,
    "background_color": "#ffffff",
    "waveform_color": "#3498db",
    "sample_points": 1000,
    "time_markers": true
  }
}
```

### **步骤3：创建处理工作流**

#### **视频处理工作流**
```json
{
  "workflow_name": "video_processing_workflow",
  "description": "学习监督视频完整处理工作流",
  "trigger": {
    "type": "obs_event",
    "event_type": "ObjectCreated",
    "filter": {
      "bucket": "learning-supervision-obs",
      "prefix": "video/",
      "suffix": ".mp4"
    },
    "condition": "session_complete"
  },
  "tasks": [
    {
      "task_id": "merge_video_segments",
      "task_type": "merge",
      "template_id": "video_merge_template",
      "input": {
        "source": "obs://learning-supervision-obs/video/{deviceId}/raw/{date}/{sessionId}/",
        "pattern": "segment_*.mp4"
      },
      "output": {
        "target": "obs://learning-supervision-obs/video/{deviceId}/processed/{date}/complete_{sessionId}.mp4"
      },
      "retry_config": {
        "max_retries": 3,
        "retry_interval": 60
      }
    },
    {
      "task_id": "transcode_video",
      "task_type": "transcode",
      "template_id": "video_transcode_template",
      "depends_on": ["merge_video_segments"],
      "input": {
        "source": "obs://learning-supervision-obs/video/{deviceId}/processed/{date}/complete_{sessionId}.mp4"
      },
      "output": {
        "targets": [
          "obs://learning-supervision-obs/video/{deviceId}/processed/{date}/high_quality_{sessionId}.mp4",
          "obs://learning-supervision-obs/video/{deviceId}/processed/{date}/medium_quality_{sessionId}.mp4",
          "obs://learning-supervision-obs/video/{deviceId}/processed/{date}/low_quality_{sessionId}.mp4"
        ]
      }
    },
    {
      "task_id": "generate_thumbnails",
      "task_type": "thumbnail",
      "template_id": "video_thumbnail_template",
      "depends_on": ["merge_video_segments"],
      "input": {
        "source": "obs://learning-supervision-obs/video/{deviceId}/processed/{date}/complete_{sessionId}.mp4"
      },
      "output": {
        "target": "obs://learning-supervision-obs/video/{deviceId}/processed/{date}/thumbnails/"
      }
    }
  ],
  "notification": {
    "success": {
      "type": "webhook",
      "url": "https://api.learningsupervision.com/mps/video/success",
      "headers": {
        "Authorization": "Bearer {api_token}",
        "Content-Type": "application/json"
      }
    },
    "failure": {
      "type": "webhook", 
      "url": "https://api.learningsupervision.com/mps/video/failure",
      "headers": {
        "Authorization": "Bearer {api_token}",
        "Content-Type": "application/json"
      }
    }
  }
}
```

#### **音频处理工作流**
```json
{
  "workflow_name": "audio_processing_workflow",
  "description": "学习监督音频完整处理工作流",
  "trigger": {
    "type": "obs_event",
    "event_type": "ObjectCreated",
    "filter": {
      "bucket": "learning-supervision-obs",
      "prefix": "audio/",
      "suffix": ".mp3"
    },
    "condition": "session_complete"
  },
  "tasks": [
    {
      "task_id": "merge_audio_segments",
      "task_type": "merge",
      "template_id": "audio_merge_template",
      "input": {
        "source": "obs://learning-supervision-obs/audio/{deviceId}/raw/{date}/{sessionId}/",
        "pattern": "segment_*.mp3"
      },
      "output": {
        "target": "obs://learning-supervision-obs/audio/{deviceId}/processed/{date}/complete_{sessionId}.mp3"
      },
      "retry_config": {
        "max_retries": 3,
        "retry_interval": 60
      }
    },
    {
      "task_id": "transcode_audio",
      "task_type": "transcode", 
      "template_id": "audio_transcode_template",
      "depends_on": ["merge_audio_segments"],
      "input": {
        "source": "obs://learning-supervision-obs/audio/{deviceId}/processed/{date}/complete_{sessionId}.mp3"
      },
      "output": {
        "targets": [
          "obs://learning-supervision-obs/audio/{deviceId}/processed/{date}/high_quality_{sessionId}.aac",
          "obs://learning-supervision-obs/audio/{deviceId}/processed/{date}/medium_quality_{sessionId}.aac",
          "obs://learning-supervision-obs/audio/{deviceId}/processed/{date}/low_quality_{sessionId}.aac"
        ]
      }
    },
    {
      "task_id": "generate_waveform",
      "task_type": "analysis",
      "template_id": "audio_waveform_template",
      "depends_on": ["merge_audio_segments"],
      "input": {
        "source": "obs://learning-supervision-obs/audio/{deviceId}/processed/{date}/complete_{sessionId}.mp3"
      },
      "output": {
        "target": "obs://learning-supervision-obs/audio/{deviceId}/processed/{date}/waveform_{sessionId}.png"
      }
    }
  ],
  "notification": {
    "success": {
      "type": "webhook",
      "url": "https://api.learningsupervision.com/mps/audio/success",
      "headers": {
        "Authorization": "Bearer {api_token}",
        "Content-Type": "application/json"
      }
    },
    "failure": {
      "type": "webhook",
      "url": "https://api.learningsupervision.com/mps/audio/failure",
      "headers": {
        "Authorization": "Bearer {api_token}",
        "Content-Type": "application/json"
      }
    }
  }
}
```

### **步骤4：配置触发条件**

#### **会话完成检测**
```json
{
  "session_detection_rules": [
    {
      "rule_name": "video_session_complete",
      "condition": {
        "type": "iot_event",
        "event_source": "iotda",
        "event_type": "recording_session_complete",
        "filter": {
          "service_id": "VideoMonitoring",
          "event_data": {
            "sessionId": "{sessionId}",
            "totalSegments": "{totalSegments}"
          }
        }
      },
      "action": {
        "type": "trigger_workflow",
        "workflow_id": "video_processing_workflow",
        "parameters": {
          "deviceId": "{deviceId}",
          "sessionId": "{sessionId}",
          "date": "{date}",
          "expectedSegments": "{totalSegments}"
        }
      }
    },
    {
      "rule_name": "audio_session_complete",
      "condition": {
        "type": "iot_event",
        "event_source": "iotda", 
        "event_type": "recording_session_complete",
        "filter": {
          "service_id": "VoiceInteraction",
          "event_data": {
            "sessionId": "{sessionId}",
            "totalSegments": "{totalSegments}"
          }
        }
      },
      "action": {
        "type": "trigger_workflow",
        "workflow_id": "audio_processing_workflow",
        "parameters": {
          "deviceId": "{deviceId}",
          "sessionId": "{sessionId}",
          "date": "{date}",
          "expectedSegments": "{totalSegments}"
        }
      }
    }
  ]
}
```

## 📊 监控和告警配置

### **处理状态监控**
```json
{
  "monitoring_config": {
    "metrics": [
      {
        "metric_name": "workflow_success_rate",
        "description": "工作流成功率",
        "threshold": {
          "warning": 95,
          "critical": 90
        }
      },
      {
        "metric_name": "processing_duration",
        "description": "处理耗时",
        "threshold": {
          "warning": 300,
          "critical": 600
        }
      },
      {
        "metric_name": "queue_length",
        "description": "处理队列长度",
        "threshold": {
          "warning": 10,
          "critical": 20
        }
      }
    ],
    "alerts": [
      {
        "alert_name": "workflow_failure_alert",
        "condition": "workflow_success_rate < 90",
        "notification": {
          "type": "email",
          "recipients": ["<EMAIL>"]
        }
      },
      {
        "alert_name": "processing_delay_alert",
        "condition": "processing_duration > 600",
        "notification": {
          "type": "sms",
          "recipients": ["+86138xxxxxxxx"]
        }
      }
    ]
  }
}
```

## ✅ 完成检查清单

- [ ] 创建视频处理模板
- [ ] 创建音频处理模板
- [ ] 配置视频处理工作流
- [ ] 配置音频处理工作流
- [ ] 设置触发条件
- [ ] 配置监控告警
- [ ] 测试工作流执行
- [ ] 验证输出质量

---

**注意**: MPS服务配置完成后，需要与IoT设备端协调，确保会话完成事件能正确触发处理工作流。
