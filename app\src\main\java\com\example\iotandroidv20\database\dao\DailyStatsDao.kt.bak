package com.example.iotandroidv20.database.dao

import androidx.room.*
import com.example.iotandroidv20.database.entity.DailyStatsEntity
import kotlinx.coroutines.flow.Flow

/**
 * 每日统计数据DAO
 */
@Dao
interface DailyStatsDao {
    
    /**
     * 插入或更新每日统计数据
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertDailyStats(dailyStats: DailyStatsEntity)
    
    /**
     * 批量插入每日统计数据
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertDailyStatsList(dailyStatsList: List<DailyStatsEntity>)
    
    /**
     * 更新每日统计数据
     */
    @Update
    suspend fun updateDailyStats(dailyStats: DailyStatsEntity)
    
    /**
     * 删除每日统计数据
     */
    @Delete
    suspend fun deleteDailyStats(dailyStats: DailyStatsEntity)
    
    /**
     * 根据日期删除统计数据
     */
    @Query("DELETE FROM daily_stats WHERE date = :date")
    suspend fun deleteDailyStatsByDate(date: String)
    
    /**
     * 删除指定日期之前的统计数据
     */
    @Query("DELETE FROM daily_stats WHERE date < :date")
    suspend fun deleteDailyStatsBefore(date: String)
    
    /**
     * 获取所有每日统计数据（按日期倒序）
     */
    @Query("SELECT * FROM daily_stats ORDER BY date DESC")
    fun getAllDailyStats(): Flow<List<DailyStatsEntity>>
    
    /**
     * 获取最近N天的统计数据
     */
    @Query("SELECT * FROM daily_stats ORDER BY date DESC LIMIT :limit")
    fun getRecentDailyStats(limit: Int): Flow<List<DailyStatsEntity>>
    
    /**
     * 获取指定日期的统计数据
     */
    @Query("SELECT * FROM daily_stats WHERE date = :date")
    suspend fun getDailyStatsByDate(date: String): DailyStatsEntity?
    
    /**
     * 获取指定日期范围内的统计数据
     */
    @Query("SELECT * FROM daily_stats WHERE date BETWEEN :startDate AND :endDate ORDER BY date DESC")
    fun getDailyStatsByDateRange(startDate: String, endDate: String): Flow<List<DailyStatsEntity>>
    
    /**
     * 获取最新的统计数据
     */
    @Query("SELECT * FROM daily_stats ORDER BY date DESC LIMIT 1")
    suspend fun getLatestDailyStats(): DailyStatsEntity?
    
    /**
     * 获取统计数据总数
     */
    @Query("SELECT COUNT(*) FROM daily_stats")
    suspend fun getDailyStatsCount(): Int
    
    /**
     * 获取平均坐姿评分
     */
    @Query("SELECT AVG(posture_score) FROM daily_stats")
    suspend fun getAveragePostureScore(): Float?
    
    /**
     * 获取最高坐姿评分
     */
    @Query("SELECT MAX(posture_score) FROM daily_stats")
    suspend fun getMaxPostureScore(): Float?
    
    /**
     * 获取最低坐姿评分
     */
    @Query("SELECT MIN(posture_score) FROM daily_stats")
    suspend fun getMinPostureScore(): Float?
    
    /**
     * 获取总监控时间
     */
    @Query("SELECT SUM(total_monitoring_time) FROM daily_stats")
    suspend fun getTotalMonitoringTime(): Long?
    
    /**
     * 获取总良好坐姿时间
     */
    @Query("SELECT SUM(good_posture_time) FROM daily_stats")
    suspend fun getTotalGoodPostureTime(): Long?
    
    /**
     * 获取总提醒次数
     */
    @Query("SELECT SUM(reminder_count) FROM daily_stats")
    suspend fun getTotalReminderCount(): Int?
    
    /**
     * 获取指定日期范围内的汇总统计
     */
    @Query("""
        SELECT 
            COUNT(*) as day_count,
            AVG(posture_score) as avg_score,
            SUM(total_monitoring_time) as total_time,
            SUM(good_posture_time) as total_good_time,
            SUM(bad_posture_time) as total_bad_time,
            SUM(reminder_count) as total_reminders
        FROM daily_stats 
        WHERE date BETWEEN :startDate AND :endDate
    """)
    suspend fun getDailyStatsSummary(startDate: String, endDate: String): DailyStatsSummary?
    
    /**
     * 获取最近7天的趋势数据
     */
    @Query("SELECT * FROM daily_stats ORDER BY date DESC LIMIT 7")
    fun getWeeklyTrend(): Flow<List<DailyStatsEntity>>
    
    /**
     * 获取最近30天的趋势数据
     */
    @Query("SELECT * FROM daily_stats ORDER BY date DESC LIMIT 30")
    fun getMonthlyTrend(): Flow<List<DailyStatsEntity>>
}

/**
 * 每日统计汇总结果
 */
data class DailyStatsSummary(
    val day_count: Int,
    val avg_score: Float,
    val total_time: Long,
    val total_good_time: Long,
    val total_bad_time: Long,
    val total_reminders: Int
)
