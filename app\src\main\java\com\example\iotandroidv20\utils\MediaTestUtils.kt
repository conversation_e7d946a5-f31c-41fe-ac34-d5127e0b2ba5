package com.example.iotandroidv20.utils

import android.content.Context
import android.util.Log
import com.example.iotandroidv20.obs.*
import com.example.iotandroidv20.repository.MediaRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.*

/**
 * 媒体功能测试工具类
 * 用于测试OBS音视频模块的各项功能
 */
object MediaTestUtils {
    
    private const val TAG = "MediaTestUtils"
    
    /**
     * 测试OBS配置
     */
    fun testObsConfiguration(context: Context): Boolean {
        return try {
            val obsConfig = ObsConfig.getInstance()
            obsConfig.initialize(context)
            
            val isConfigured = obsConfig.isConfigured()
            Log.d(TAG, "OBS配置状态: $isConfigured")
            
            if (isConfigured) {
                Log.d(TAG, obsConfig.getConfigSummary())
            }
            
            isConfigured
        } catch (e: Exception) {
            Log.e(TAG, "测试OBS配置失败", e)
            false
        }
    }
    
    /**
     * 测试OBS管理器初始化
     */
    fun testObsManagerInitialization(context: Context): Boolean {
        return try {
            val obsConfig = ObsConfig.getInstance()
            obsConfig.initialize(context)
            
            if (!obsConfig.isConfigured()) {
                Log.w(TAG, "OBS未配置，跳过管理器测试")
                return false
            }
            
            val obsManager = ObsManager.getInstance()
            obsManager.initialize(obsConfig.getAccessKey(), obsConfig.getSecretKey())
            
            Log.d(TAG, "OBS管理器初始化成功")
            true
        } catch (e: Exception) {
            Log.e(TAG, "测试OBS管理器初始化失败", e)
            false
        }
    }
    
    /**
     * 测试媒体仓库功能
     */
    fun testMediaRepository(context: Context, scope: CoroutineScope) {
        scope.launch(Dispatchers.IO) {
            try {
                Log.d(TAG, "开始测试媒体仓库功能")
                
                val mediaRepository = MediaRepository.getInstance()
                
                // 测试获取可用日期
                val dates = mediaRepository.getAvailableDates("TEST_DEVICE")
                Log.d(TAG, "可用日期: $dates")
                
                // 测试获取视频会话
                if (dates.isNotEmpty()) {
                    val videoSessions = mediaRepository.getVideoSessions("TEST_DEVICE", dates.first())
                    Log.d(TAG, "视频会话数量: ${videoSessions.size}")
                    
                    val audioSessions = mediaRepository.getAudioSessions("TEST_DEVICE", dates.first())
                    Log.d(TAG, "音频会话数量: ${audioSessions.size}")
                }
                
                // 测试缓存大小
                val cacheSize = mediaRepository.getCacheSize(context)
                Log.d(TAG, "缓存大小: ${formatFileSize(cacheSize)}")
                
                Log.d(TAG, "媒体仓库功能测试完成")
                
            } catch (e: Exception) {
                Log.e(TAG, "测试媒体仓库功能失败", e)
            }
        }
    }
    
    /**
     * 创建测试数据
     */
    fun createTestData(): Pair<List<VideoSession>, List<AudioSession>> {
        val testVideos = mutableListOf<VideoSession>()
        val testAudios = mutableListOf<AudioSession>()
        
        // 创建测试视频会话
        repeat(5) { index ->
            testVideos.add(
                VideoSession(
                    sessionId = "test_video_${String.format("%03d", index + 1)}",
                    deviceId = "TEST_DEVICE_001",
                    date = "20250102",
                    fileName = "test_video_${String.format("%03d", index + 1)}.mp4",
                    fileSize = (10 + index * 2) * 1024 * 1024L, // 10-18MB
                    lastModified = Date(System.currentTimeMillis() - index * 3600000L), // 每小时间隔
                    objectKey = "video/TEST_DEVICE_001/processed/20250102/test_video_${String.format("%03d", index + 1)}.mp4",
                    streamUrl = "https://test.example.com/video_${index + 1}.mp4",
                    downloadStatus = when (index % 3) {
                        0 -> DownloadStatus.DOWNLOADED
                        1 -> DownloadStatus.NOT_DOWNLOADED
                        else -> DownloadStatus.DOWNLOADING
                    },
                    duration = (30 + index * 15).toLong(), // 30-90秒
                    resolution = if (index % 2 == 0) "1280x720" else "1920x1080",
                    bitrate = if (index % 2 == 0) 1500 else 2500
                )
            )
        }
        
        // 创建测试音频会话
        repeat(8) { index ->
            testAudios.add(
                AudioSession(
                    sessionId = "test_audio_${String.format("%03d", index + 1)}",
                    deviceId = "TEST_DEVICE_001",
                    date = "20250102",
                    fileName = "test_audio_${String.format("%03d", index + 1)}.mp3",
                    fileSize = (2 + index) * 1024 * 1024L, // 2-9MB
                    lastModified = Date(System.currentTimeMillis() - index * 1800000L), // 每30分钟间隔
                    objectKey = "audio/TEST_DEVICE_001/processed/20250102/test_audio_${String.format("%03d", index + 1)}.mp3",
                    streamUrl = "https://test.example.com/audio_${index + 1}.mp3",
                    downloadStatus = when (index % 4) {
                        0 -> DownloadStatus.DOWNLOADED
                        1 -> DownloadStatus.NOT_DOWNLOADED
                        2 -> DownloadStatus.DOWNLOADING
                        else -> DownloadStatus.DOWNLOAD_FAILED
                    },
                    duration = (60 + index * 30).toLong(), // 60-270秒
                    sampleRate = if (index % 2 == 0) 44100 else 48000,
                    bitrate = when (index % 3) {
                        0 -> 128
                        1 -> 192
                        else -> 256
                    },
                    channels = if (index % 2 == 0) 2 else 1
                )
            )
        }
        
        Log.d(TAG, "创建测试数据: ${testVideos.size}个视频, ${testAudios.size}个音频")
        return testVideos to testAudios
    }
    
    /**
     * 测试播放器功能
     */
    fun testPlayerFunctionality(context: Context) {
        try {
            Log.d(TAG, "开始测试播放器功能")
            
            // 测试视频播放器管理器
            val videoPlayerManager = com.example.iotandroidv20.player.VideoPlayerManager(context)
            videoPlayerManager.initializePlayer()
            Log.d(TAG, "视频播放器初始化成功")
            
            // 测试音频播放器管理器
            val audioPlayerManager = com.example.iotandroidv20.player.AudioPlayerManager(context)
            audioPlayerManager.initializePlayer()
            Log.d(TAG, "音频播放器初始化成功")
            
            // 清理资源
            videoPlayerManager.releasePlayer()
            audioPlayerManager.releasePlayer()
            
            Log.d(TAG, "播放器功能测试完成")
            
        } catch (e: Exception) {
            Log.e(TAG, "测试播放器功能失败", e)
        }
    }
    
    /**
     * 性能测试
     */
    fun performanceTest(context: Context, scope: CoroutineScope) {
        scope.launch(Dispatchers.IO) {
            try {
                Log.d(TAG, "开始性能测试")
                
                val startTime = System.currentTimeMillis()
                
                // 测试大量数据加载
                val mediaRepository = MediaRepository.getInstance()
                repeat(10) { deviceIndex ->
                    repeat(7) { dayIndex ->
                        val deviceId = "PERF_TEST_DEVICE_${String.format("%03d", deviceIndex)}"
                        val date = "2025010${dayIndex + 1}"
                        
                        mediaRepository.getVideoSessions(deviceId, date)
                        mediaRepository.getAudioSessions(deviceId, date)
                    }
                }
                
                val endTime = System.currentTimeMillis()
                val duration = endTime - startTime
                
                Log.d(TAG, "性能测试完成，耗时: ${duration}ms")
                
                // 内存使用情况
                val runtime = Runtime.getRuntime()
                val usedMemory = runtime.totalMemory() - runtime.freeMemory()
                Log.d(TAG, "内存使用: ${formatFileSize(usedMemory)}")
                
            } catch (e: Exception) {
                Log.e(TAG, "性能测试失败", e)
            }
        }
    }
    
    /**
     * 格式化文件大小
     */
    private fun formatFileSize(bytes: Long): String {
        return when {
            bytes < 1024 -> "${bytes}B"
            bytes < 1024 * 1024 -> "${bytes / 1024}KB"
            bytes < 1024 * 1024 * 1024 -> "${bytes / (1024 * 1024)}MB"
            else -> "${bytes / (1024 * 1024 * 1024)}GB"
        }
    }
    
    /**
     * 运行完整测试套件
     */
    fun runFullTestSuite(context: Context, scope: CoroutineScope) {
        Log.d(TAG, "=== 开始完整测试套件 ===")
        
        // 基础配置测试
        val configOk = testObsConfiguration(context)
        val managerOk = testObsManagerInitialization(context)
        
        if (configOk && managerOk) {
            // 功能测试
            testMediaRepository(context, scope)
            testPlayerFunctionality(context)
            
            // 性能测试
            performanceTest(context, scope)
        } else {
            Log.w(TAG, "基础配置测试失败，跳过其他测试")
        }
        
        Log.d(TAG, "=== 完整测试套件结束 ===")
    }
}
