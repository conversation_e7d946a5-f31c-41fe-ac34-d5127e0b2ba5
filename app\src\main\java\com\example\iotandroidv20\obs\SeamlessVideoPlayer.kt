package com.example.iotandroidv20.obs

import android.content.Context
import android.net.Uri
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.exoplayer.upstream.DefaultLoadErrorHandlingPolicy
import com.example.iotandroidv20.utils.Logger
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import java.io.File
import kotlin.time.Duration.Companion.milliseconds

/**
 * 无缝视频播放器
 * 实现3秒片段的无缝切换播放
 */
class SeamlessVideoPlayer(private val context: Context) {
    
    companion object {
        private const val TAG = "SeamlessVideoPlayer"
        private const val SWITCH_DELAY_MS = 50L // 切换延迟
        private const val PLAYER_READY_TIMEOUT = 3000L // 播放器准备超时
    }
    
    // 双播放器实现无缝切换
    private var primaryPlayer: ExoPlayer? = null
    private var secondaryPlayer: ExoPlayer? = null
    private var isUsingPrimary = true
    
    // 状态管理
    private val _playbackState = MutableStateFlow(PlaybackState.IDLE)
    val playbackState: StateFlow<PlaybackState> = _playbackState.asStateFlow()
    
    private val _currentSegment = MutableStateFlow<VideoSegment?>(null)
    val currentSegment: StateFlow<VideoSegment?> = _currentSegment.asStateFlow()
    
    private val _playerError = MutableSharedFlow<String>()
    val playerError: SharedFlow<String> = _playerError.asSharedFlow()
    
    // 播放控制
    private var playbackJob: Job? = null
    private val segmentQueue = SegmentQueueManager(context)
    
    /**
     * 初始化双播放器
     */
    fun initializePlayers() {
        try {
            primaryPlayer = createExoPlayer("Primary")
            secondaryPlayer = createExoPlayer("Secondary")
            
            Logger.d("双播放器初始化完成", tag = TAG)
        } catch (e: Exception) {
            Logger.e("播放器初始化失败: ${e.message}", tag = TAG)
            _playbackState.value = PlaybackState.ERROR
        }
    }
    
    /**
     * 创建ExoPlayer实例
     */
    private fun createExoPlayer(name: String): ExoPlayer {
        return ExoPlayer.Builder(context)
            .setMediaSourceFactory(
                DefaultMediaSourceFactory(context)
                    .setLoadErrorHandlingPolicy(
                        DefaultLoadErrorHandlingPolicy(3) // 3次重试
                    )
            )
            .build().apply {
                // 配置播放器
                playWhenReady = true
                repeatMode = Player.REPEAT_MODE_OFF
                volume = 1.0f
                
                // 添加监听器
                addListener(object : Player.Listener {
                    override fun onPlaybackStateChanged(playbackState: Int) {
                        handlePlaybackStateChange(name, playbackState)
                    }
                    
                    override fun onPlayerError(error: PlaybackException) {
                        Logger.e("播放器错误 [$name]: ${error.message}", tag = TAG)
                        handlePlaybackError(name, error)
                    }
                    
                    override fun onIsPlayingChanged(isPlaying: Boolean) {
                        if (isPlaying) {
                            Logger.d("播放器开始播放 [$name]", tag = TAG)
                        }
                    }
                })
            }
    }
    
    /**
     * 开始无缝播放
     */
    fun startSeamlessPlayback() {
        if (playbackJob?.isActive == true) {
            Logger.w("播放任务已在运行", tag = TAG)
            return
        }
        
        _playbackState.value = PlaybackState.BUFFERING
        
        playbackJob = CoroutineScope(Dispatchers.Main).launch {
            try {
                while (isActive) {
                    val nextSegment = segmentQueue.getNextSegment()
                    
                    if (nextSegment != null) {
                        playSegmentSeamlessly(nextSegment)
                    } else {
                        // 等待新片段
                        delay(100)
                    }
                }
            } catch (e: Exception) {
                Logger.e("无缝播放错误: ${e.message}", tag = TAG)
                _playbackState.value = PlaybackState.ERROR
                _playerError.emit("播放错误: ${e.message}")
            }
        }
    }
    
    /**
     * 无缝播放片段
     */
    private suspend fun playSegmentSeamlessly(segment: VideoSegment) {
        try {
            Logger.d("开始播放片段: ${segment.objectKey.substringAfterLast("/")}", tag = TAG)
            
            // 获取预加载的文件或下载
            val localFile = segmentQueue.getPreloadedSegment(segment.objectKey)
                ?: downloadSegmentSync(segment)
            
            if (!localFile.exists()) {
                Logger.e("片段文件不存在: ${localFile.path}", tag = TAG)
                return
            }
            
            // 准备下一个播放器
            val nextPlayer = if (isUsingPrimary) secondaryPlayer else primaryPlayer
            val currentPlayer = if (isUsingPrimary) primaryPlayer else secondaryPlayer
            
            // 设置媒体源
            val mediaItem = MediaItem.fromUri(Uri.fromFile(localFile))
            nextPlayer?.setMediaItem(mediaItem)
            nextPlayer?.prepare()
            
            // 等待播放器准备完成
            waitForPlayerReady(nextPlayer!!)
            
            // 无缝切换
            withContext(Dispatchers.Main) {
                nextPlayer.play()
                
                // 短暂延迟后停止当前播放器
                delay(SWITCH_DELAY_MS)
                currentPlayer?.pause()
                
                // 切换播放器
                isUsingPrimary = !isUsingPrimary
                
                // 更新状态
                _currentSegment.value = segment
                _playbackState.value = PlaybackState.PLAYING
            }
            
            Logger.d("无缝切换完成: ${segment.objectKey.substringAfterLast("/")}", tag = TAG)
            
            // 等待当前片段播放完成
            delay((segment.duration * 1000).toLong())
            
        } catch (e: Exception) {
            Logger.e("播放片段失败: ${e.message}", tag = TAG)
            _playerError.emit("播放片段失败: ${e.message}")
        }
    }
    
    /**
     * 等待播放器准备就绪
     */
    private suspend fun waitForPlayerReady(player: ExoPlayer) {
        val startTime = System.currentTimeMillis()
        
        while (player.playbackState != Player.STATE_READY) {
            if (System.currentTimeMillis() - startTime > PLAYER_READY_TIMEOUT) {
                throw Exception("播放器准备超时")
            }
            delay(50)
        }
    }
    
    /**
     * 同步下载片段
     */
    private suspend fun downloadSegmentSync(segment: VideoSegment): File {
        return withContext(Dispatchers.IO) {
            segmentQueue.preloadSegment(segment)
            segmentQueue.getPreloadedSegment(segment.objectKey)
                ?: throw Exception("下载片段失败")
        }
    }
    
    /**
     * 添加片段到播放队列
     */
    fun addSegment(segment: VideoSegment) {
        segmentQueue.addSegment(segment)
    }
    
    /**
     * 添加多个片段
     */
    fun addSegments(segments: List<VideoSegment>) {
        segments.forEach { segment ->
            segmentQueue.addSegment(segment)
        }
    }
    
    /**
     * 停止播放
     */
    fun stopPlayback() {
        playbackJob?.cancel()
        playbackJob = null
        
        primaryPlayer?.pause()
        secondaryPlayer?.pause()
        
        segmentQueue.clearQueue()
        
        _playbackState.value = PlaybackState.IDLE
        _currentSegment.value = null
        
        Logger.d("播放已停止", tag = TAG)
    }
    
    /**
     * 暂停播放
     */
    fun pausePlayback() {
        getCurrentPlayer()?.pause()
        _playbackState.value = PlaybackState.PAUSED
    }
    
    /**
     * 恢复播放
     */
    fun resumePlayback() {
        getCurrentPlayer()?.play()
        _playbackState.value = PlaybackState.PLAYING
    }
    
    /**
     * 获取当前活跃的播放器
     */
    fun getCurrentPlayer(): ExoPlayer? {
        return if (isUsingPrimary) primaryPlayer else secondaryPlayer
    }
    
    /**
     * 处理播放状态变化
     */
    private fun handlePlaybackStateChange(playerName: String, state: Int) {
        when (state) {
            Player.STATE_BUFFERING -> {
                Logger.d("播放器缓冲中 [$playerName]", tag = TAG)
            }
            Player.STATE_READY -> {
                Logger.d("播放器准备就绪 [$playerName]", tag = TAG)
            }
            Player.STATE_ENDED -> {
                Logger.d("播放器播放结束 [$playerName]", tag = TAG)
            }
            Player.STATE_IDLE -> {
                Logger.d("播放器空闲 [$playerName]", tag = TAG)
            }
        }
    }
    
    /**
     * 处理播放错误
     */
    private fun handlePlaybackError(playerName: String, error: PlaybackException) {
        CoroutineScope(Dispatchers.Main).launch {
            _playbackState.value = PlaybackState.ERROR
            _playerError.emit("播放器错误 [$playerName]: ${error.message}")
        }
    }
    
    /**
     * 获取播放统计信息
     */
    fun getPlaybackStatistics(): PlaybackStatistics {
        val currentPlayer = getCurrentPlayer()
        return PlaybackStatistics(
            isPlaying = currentPlayer?.isPlaying ?: false,
            currentPosition = currentPlayer?.currentPosition ?: 0L,
            duration = currentPlayer?.duration ?: 0L,
            bufferedPercentage = currentPlayer?.bufferedPercentage ?: 0,
            queueSize = segmentQueue.getQueueSize(),
            cacheSize = segmentQueue.getCacheSize()
        )
    }
    
    /**
     * 释放资源
     */
    fun release() {
        stopPlayback()
        
        primaryPlayer?.release()
        secondaryPlayer?.release()
        
        primaryPlayer = null
        secondaryPlayer = null
        
        Logger.d("播放器资源已释放", tag = TAG)
    }
}

/**
 * 播放统计信息
 */
data class PlaybackStatistics(
    val isPlaying: Boolean,
    val currentPosition: Long,
    val duration: Long,
    val bufferedPercentage: Int,
    val queueSize: Int,
    val cacheSize: Long
)
