package com.example.iotandroidv20.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp

/**
 * 语音模块测试卡片
 * 用于在开发阶段测试语音功能
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VoiceModuleTestCard(
    onRunCompleteTest: () -> Unit,
    onRunQuickTest: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFF3E5F5) // 淡紫色背景表示测试功能
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "🧪 语音模块测试",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF7B1FA2) // 深紫色
                )
                
                Icon(
                    imageVector = Icons.Default.BugReport,
                    contentDescription = "测试功能",
                    tint = Color(0xFF7B1FA2),
                    modifier = Modifier.size(24.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 说明文字
            Text(
                text = "开发测试功能，用于验证语音交互模块的各项功能",
                style = MaterialTheme.typography.bodyMedium,
                color = Color(0xFF4A148C)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 测试按钮区域
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // 快速测试按钮
                OutlinedButton(
                    onClick = onRunQuickTest,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = Color(0xFF7B1FA2)
                    )
                ) {
                    Icon(
                        imageVector = Icons.Default.Speed,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("快速测试")
                }
                
                // 完整测试按钮
                Button(
                    onClick = onRunCompleteTest,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF7B1FA2)
                    )
                ) {
                    Icon(
                        imageVector = Icons.Default.PlaylistAddCheck,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("完整测试")
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 测试说明
            TestInstructions()
        }
    }
}

/**
 * 测试说明组件
 */
@Composable
private fun TestInstructions() {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFE1BEE7).copy(alpha = 0.5f)
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Text(
                text = "📋 测试说明",
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF4A148C)
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            val instructions = listOf(
                "快速测试: 验证录音启动和停止的基本功能",
                "完整测试: 全面检查所有语音模块功能",
                "测试结果会在日志中显示详细信息",
                "请确保已授予录音和存储权限"
            )
            
            instructions.forEach { instruction ->
                Text(
                    text = "• $instruction",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF6A1B9A),
                    modifier = Modifier.padding(vertical = 1.dp)
                )
            }
        }
    }
}

/**
 * 语音模块状态显示卡片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VoiceModuleStatusCard(
    isRecording: Boolean,
    interactionStatus: String,
    statusDescription: String,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 4.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(
            containerColor = when {
                isRecording -> Color(0xFFFFEBEE) // 录音中 - 红色背景
                interactionStatus.contains("成功") -> Color(0xFFE8F5E8) // 成功 - 绿色背景
                interactionStatus.contains("失败") -> Color(0xFFFFEBEE) // 失败 - 红色背景
                else -> MaterialTheme.colorScheme.surface
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "🔊 语音模块状态",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium
                )
                
                // 状态指示器
                StatusIndicator(isRecording = isRecording)
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 状态信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "录音状态",
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = if (isRecording) "🎙️ 录音中" else "⏹️ 已停止",
                        style = MaterialTheme.typography.bodySmall,
                        color = if (isRecording) Color.Red else Color.Gray
                    )
                }
                
                Column {
                    Text(
                        text = "交互状态",
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = interactionStatus,
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 状态描述
            Text(
                text = "状态描述: $statusDescription",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 状态指示器
 */
@Composable
private fun StatusIndicator(
    isRecording: Boolean,
    modifier: Modifier = Modifier
) {
    if (isRecording) {
        // 录音中的动画指示器
        var isVisible by remember { mutableStateOf(true) }
        
        LaunchedEffect(Unit) {
            while (true) {
                kotlinx.coroutines.delay(500)
                isVisible = !isVisible
            }
        }
        
        if (isVisible) {
            Icon(
                imageVector = Icons.Default.FiberManualRecord,
                contentDescription = "录音中",
                tint = Color.Red,
                modifier = modifier.size(20.dp)
            )
        } else {
            Spacer(modifier = modifier.size(20.dp))
        }
    } else {
        Icon(
            imageVector = Icons.Default.Stop,
            contentDescription = "已停止",
            tint = Color.Gray,
            modifier = modifier.size(20.dp)
        )
    }
}

/**
 * 测试结果显示卡片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TestResultCard(
    testResult: String,
    modifier: Modifier = Modifier
) {
    if (testResult.isNotEmpty()) {
        Card(
            modifier = modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 4.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
            colors = CardDefaults.cardColors(
                containerColor = when {
                    testResult.contains("✅") -> Color(0xFFE8F5E8) // 成功 - 绿色
                    testResult.contains("❌") -> Color(0xFFFFEBEE) // 失败 - 红色
                    testResult.contains("🧪") -> Color(0xFFF3E5F5) // 测试中 - 紫色
                    else -> MaterialTheme.colorScheme.surfaceVariant
                }
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "📊 测试结果",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = testResult,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}
