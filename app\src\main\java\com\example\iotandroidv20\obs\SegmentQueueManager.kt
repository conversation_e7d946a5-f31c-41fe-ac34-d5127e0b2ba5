package com.example.iotandroidv20.obs

import android.content.Context
import com.example.iotandroidv20.utils.Logger
import kotlinx.coroutines.*
import okhttp3.*
import java.io.File
import java.io.IOException
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import kotlin.time.Duration.Companion.seconds

/**
 * 3秒片段队列管理器
 * 负责管理视频片段的下载、缓存和播放队列
 */
class SegmentQueueManager(private val context: Context) {
    
    companion object {
        private const val TAG = "SegmentQueueManager"
        private const val MAX_QUEUE_SIZE = 10
        private const val MAX_CACHE_SIZE = 20 * 1024 * 1024L // 20MB缓存
        private const val PRELOAD_COUNT = 3 // 预加载3个片段
    }
    
    // 播放队列
    private val segmentQueue = LinkedBlockingQueue<VideoSegment>(MAX_QUEUE_SIZE)
    
    // 预加载缓存
    private val preloadedSegments = ConcurrentHashMap<String, File>()
    
    // 下载管理
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(10, java.util.concurrent.TimeUnit.SECONDS)
        .readTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
        .writeTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
        .build()
    
    // 缓存目录
    private val cacheDir = File(context.cacheDir, "video_segments").apply {
        if (!exists()) mkdirs()
    }
    
    // 统计信息
    private val totalDownloadBytes = AtomicLong(0)
    private val downloadCount = AtomicLong(0)
    
    /**
     * 添加片段到播放队列
     */
    fun addSegment(segment: VideoSegment): Boolean {
        return try {
            // 如果队列满了，移除最老的片段
            if (segmentQueue.size >= MAX_QUEUE_SIZE) {
                val oldSegment = segmentQueue.poll()
                oldSegment?.let { cleanupSegment(it) }
            }
            
            val added = segmentQueue.offer(segment)
            if (added) {
                Logger.d("片段添加到队列: ${segment.objectKey.substringAfterLast("/")}", tag = TAG)
                
                // 异步预加载
                CoroutineScope(Dispatchers.IO).launch {
                    preloadSegment(segment)
                }
            }
            added
        } catch (e: Exception) {
            Logger.e("添加片段到队列失败: ${e.message}", tag = TAG)
            false
        }
    }
    
    /**
     * 获取下一个播放片段
     */
    fun getNextSegment(): VideoSegment? {
        return segmentQueue.poll()
    }
    
    /**
     * 预加载片段
     */
    suspend fun preloadSegment(segment: VideoSegment) {
        if (preloadedSegments.containsKey(segment.objectKey)) {
            return // 已经预加载
        }
        
        try {
            val localFile = downloadSegment(segment)
            preloadedSegments[segment.objectKey] = localFile
            
            Logger.d("预加载完成: ${segment.objectKey.substringAfterLast("/")}", tag = TAG)
            
            // 管理缓存大小
            manageCacheSize()
            
        } catch (e: Exception) {
            Logger.e("预加载失败: ${e.message}", tag = TAG)
        }
    }
    
    /**
     * 获取预加载的片段文件
     */
    fun getPreloadedSegment(objectKey: String): File? {
        return preloadedSegments[objectKey]
    }
    
    /**
     * 下载片段文件
     */
    private suspend fun downloadSegment(segment: VideoSegment): File = withContext(Dispatchers.IO) {
        val fileName = segment.objectKey.substringAfterLast("/")
        val localFile = File(cacheDir, fileName)
        
        // 如果文件已存在且大小匹配，直接返回
        if (localFile.exists() && localFile.length() == segment.size) {
            return@withContext localFile
        }
        
        val request = Request.Builder()
            .url(segment.url)
            .build()
        
        val response = httpClient.newCall(request).execute()
        
        if (!response.isSuccessful) {
            throw IOException("下载失败: HTTP ${response.code}")
        }
        
        response.body?.let { body ->
            localFile.outputStream().use { output ->
                body.byteStream().use { input ->
                    input.copyTo(output)
                }
            }
            
            // 更新统计信息
            totalDownloadBytes.addAndGet(localFile.length())
            downloadCount.incrementAndGet()
            
            Logger.d("下载完成: $fileName, 大小: ${localFile.length()}B", tag = TAG)
            
        } ?: throw IOException("响应体为空")
        
        localFile
    }
    
    /**
     * 管理缓存大小
     */
    private fun manageCacheSize() {
        val totalSize = preloadedSegments.values.sumOf { it.length() }
        
        if (totalSize > MAX_CACHE_SIZE) {
            Logger.d("缓存超限，开始清理: ${totalSize}B > ${MAX_CACHE_SIZE}B", tag = TAG)
            
            // 按文件修改时间排序，删除最老的文件
            val sortedEntries = preloadedSegments.entries.sortedBy { 
                it.value.lastModified() 
            }
            
            var deletedSize = 0L
            val iterator = sortedEntries.iterator()
            
            while (iterator.hasNext() && totalSize - deletedSize > MAX_CACHE_SIZE * 0.8) {
                val entry = iterator.next()
                deletedSize += entry.value.length()
                
                if (entry.value.delete()) {
                    preloadedSegments.remove(entry.key)
                    Logger.d("清理缓存文件: ${entry.key.substringAfterLast("/")}", tag = TAG)
                }
            }
            
            Logger.d("缓存清理完成，释放: ${deletedSize}B", tag = TAG)
        }
    }
    
    /**
     * 清理片段相关文件
     */
    private fun cleanupSegment(segment: VideoSegment) {
        preloadedSegments[segment.objectKey]?.let { file ->
            if (file.delete()) {
                preloadedSegments.remove(segment.objectKey)
                Logger.d("清理片段文件: ${segment.objectKey.substringAfterLast("/")}", tag = TAG)
            }
        }
    }
    
    /**
     * 清空队列和缓存
     */
    fun clearQueue() {
        // 清空播放队列
        segmentQueue.clear()
        
        // 清空预加载缓存
        preloadedSegments.values.forEach { file ->
            file.delete()
        }
        preloadedSegments.clear()
        
        Logger.d("队列和缓存已清空", tag = TAG)
    }
    
    /**
     * 获取队列大小
     */
    fun getQueueSize(): Int = segmentQueue.size
    
    /**
     * 获取缓存大小
     */
    fun getCacheSize(): Long = preloadedSegments.values.sumOf { it.length() }
    
    /**
     * 获取统计信息
     */
    fun getStatistics(): SegmentStatistics {
        return SegmentStatistics(
            queueSize = segmentQueue.size,
            cacheSize = getCacheSize(),
            preloadedCount = preloadedSegments.size,
            totalDownloadBytes = totalDownloadBytes.get(),
            downloadCount = downloadCount.get(),
            averageSegmentSize = if (downloadCount.get() > 0) {
                totalDownloadBytes.get() / downloadCount.get()
            } else 0L
        )
    }
    
    /**
     * 预加载接下来的几个片段
     */
    suspend fun preloadUpcomingSegments(segments: List<VideoSegment>) {
        val toPreload = segments.take(PRELOAD_COUNT)
        
        toPreload.forEach { segment ->
            if (!preloadedSegments.containsKey(segment.objectKey)) {
                launch {
                    preloadSegment(segment)
                }
            }
        }
    }
}

/**
 * 片段统计信息
 */
data class SegmentStatistics(
    val queueSize: Int,
    val cacheSize: Long,
    val preloadedCount: Int,
    val totalDownloadBytes: Long,
    val downloadCount: Long,
    val averageSegmentSize: Long
)
