{"logs": [{"outputFile": "com.example.iotandroidv20.app-mergeReleaseResources-58:/values-lv/values-lv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\31c73cb6dfb12faac49eb1bae4282a74\\transformed\\core-1.16.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "22,23,24,25,26,27,28,159", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "962,1060,1162,1262,1363,1470,1578,13780", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "1055,1157,1257,1358,1465,1573,1688,13876"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5dca3f3744b4fa46ea145e6d4fc9225c\\transformed\\media3-exoplayer-1.2.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,196,268,338,418,495,596,694", "endColumns": "74,65,71,69,79,76,100,97,78", "endOffsets": "125,191,263,333,413,490,591,689,768"}, "to": {"startLines": "59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4243,4318,4384,4456,4526,4606,4683,4784,4882", "endColumns": "74,65,71,69,79,76,100,97,78", "endOffsets": "4313,4379,4451,4521,4601,4678,4779,4877,4956"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\398f61ea23c76807315a7831064b1215\\transformed\\material3-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,309,424,549,658,758,875,1013,1131,1278,1364,1462,1556,1657,1776,1900,2003,2141,2272,2410,2593,2725,2844,2971,3091,3186,3285,3406,3541,3643,3757,3863,3998,4143,4252,4355,4438,4533,4627,4737,4827,4914,5025,5105,5191,5286,5390,5481,5579,5668,5775,5877,5977,6130,6210,6315", "endColumns": "126,126,114,124,108,99,116,137,117,146,85,97,93,100,118,123,102,137,130,137,182,131,118,126,119,94,98,120,134,101,113,105,134,144,108,102,82,94,93,109,89,86,110,79,85,94,103,90,97,88,106,101,99,152,79,104,98", "endOffsets": "177,304,419,544,653,753,870,1008,1126,1273,1359,1457,1551,1652,1771,1895,1998,2136,2267,2405,2588,2720,2839,2966,3086,3181,3280,3401,3536,3638,3752,3858,3993,4138,4247,4350,4433,4528,4622,4732,4822,4909,5020,5100,5186,5281,5385,5476,5574,5663,5770,5872,5972,6125,6205,6310,6409"}, "to": {"startLines": "88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6265,6392,6519,6634,6759,6868,6968,7085,7223,7341,7488,7574,7672,7766,7867,7986,8110,8213,8351,8482,8620,8803,8935,9054,9181,9301,9396,9495,9616,9751,9853,9967,10073,10208,10353,10462,10565,10648,10743,10837,10947,11037,11124,11235,11315,11401,11496,11600,11691,11789,11878,11985,12087,12187,12340,12420,12525", "endColumns": "126,126,114,124,108,99,116,137,117,146,85,97,93,100,118,123,102,137,130,137,182,131,118,126,119,94,98,120,134,101,113,105,134,144,108,102,82,94,93,109,89,86,110,79,85,94,103,90,97,88,106,101,99,152,79,104,98", "endOffsets": "6387,6514,6629,6754,6863,6963,7080,7218,7336,7483,7569,7667,7761,7862,7981,8105,8208,8346,8477,8615,8798,8930,9049,9176,9296,9391,9490,9611,9746,9848,9962,10068,10203,10348,10457,10560,10643,10738,10832,10942,11032,11119,11230,11310,11396,11491,11595,11686,11784,11873,11980,12082,12182,12335,12415,12520,12619"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\752167a3e67da85263f648420d161268\\transformed\\ui-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,292,388,491,581,667,755,848,932,1017,1104,1177,1253,1330,1406,1484,1552", "endColumns": "97,88,95,102,89,85,87,92,83,84,86,72,75,76,75,77,67,121", "endOffsets": "198,287,383,486,576,662,750,843,927,1012,1099,1172,1248,1325,1401,1479,1547,1669"}, "to": {"startLines": "29,30,31,33,34,86,87,151,152,153,154,155,156,157,158,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1693,1791,1880,2052,2155,6091,6177,13129,13222,13306,13391,13478,13551,13627,13704,13881,13959,14027", "endColumns": "97,88,95,102,89,85,87,92,83,84,86,72,75,76,75,77,67,121", "endOffsets": "1786,1875,1971,2150,2240,6172,6260,13217,13301,13386,13473,13546,13622,13699,13775,13954,14022,14144"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2c60f935d2795008c652ca71073c0e75\\transformed\\media3-ui-1.2.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,567,831,915,998,1081,1176,1271,1344,1411,1505,1599,1665,1732,1795,1871,1977,2088,2195,2269,2351,2425,2498,2598,2697,2763,2829,2882,2940,2988,3049,3107,3183,3247,3312,3377,3434,3500,3566,3632,3684,3748,3826,3904", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,83,82,82,94,94,72,66,93,93,65,66,62,75,105,110,106,73,81,73,72,99,98,65,65,52,57,47,60,57,75,63,64,64,56,65,65,65,51,63,77,77,54", "endOffsets": "281,562,826,910,993,1076,1171,1266,1339,1406,1500,1594,1660,1727,1790,1866,1972,2083,2190,2264,2346,2420,2493,2593,2692,2758,2824,2877,2935,2983,3044,3102,3178,3242,3307,3372,3429,3495,3561,3627,3679,3743,3821,3899,3954"}, "to": {"startLines": "2,11,16,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,617,2245,2329,2412,2495,2590,2685,2758,2825,2919,3013,3079,3146,3209,3285,3391,3502,3609,3683,3765,3839,3912,4012,4111,4177,4961,5014,5072,5120,5181,5239,5315,5379,5444,5509,5566,5632,5698,5764,5816,5880,5958,6036", "endLines": "10,15,20,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "17,12,12,83,82,82,94,94,72,66,93,93,65,66,62,75,105,110,106,73,81,73,72,99,98,65,65,52,57,47,60,57,75,63,64,64,56,65,65,65,51,63,77,77,54", "endOffsets": "331,612,876,2324,2407,2490,2585,2680,2753,2820,2914,3008,3074,3141,3204,3280,3386,3497,3604,3678,3760,3834,3907,4007,4106,4172,4238,5009,5067,5115,5176,5234,5310,5374,5439,5504,5561,5627,5693,5759,5811,5875,5953,6031,6086"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\835843a1fca13b839a80c71cfb075e12\\transformed\\media3-session-1.2.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,136,212,282,352,433,520,614", "endColumns": "80,75,69,69,80,86,93,102", "endOffsets": "131,207,277,347,428,515,609,712"}, "to": {"startLines": "21,32,145,146,147,148,149,150", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "881,1976,12624,12694,12764,12845,12932,13026", "endColumns": "80,75,69,69,80,86,93,102", "endOffsets": "957,2047,12689,12759,12840,12927,13021,13124"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\01b6e8b8fea8bb45b55852d4590f3437\\transformed\\foundation-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,89", "endOffsets": "140,230"}, "to": {"startLines": "163,164", "startColumns": "4,4", "startOffsets": "14149,14239", "endColumns": "89,89", "endOffsets": "14234,14324"}}]}]}