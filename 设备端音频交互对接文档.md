# 设备端音频交互对接文档

## 📋 概述

本文档说明设备端如何与Android应用端进行音频交互，实现家长语音消息的接收、解析和播放功能。

## 🔗 系统架构

```
[Android应用端] → [华为云OBS] → [华为云IoT平台] → [设备端]
     ↓              ↓              ↓              ↓
  录制语音      上传音频文件    发送播放命令    下载并播放
```

## 📡 华为云IoT命令接口

### **PLAY_PARENT_VOICE 命令**

#### **命令参数**
```json
{
  "commandName": "PLAY_PARENT_VOICE",
  "paras": {
    "voiceUrl": "https://obs.cn-north-4.myhuaweicloud.com/iotdavideo/voice/parent_voice_1704369783000.aac",
    "playMode": "immediate",
    "volume": 80,
    "priority": "high",
    "timestamp": 1704369783000
  }
}
```

#### **参数说明**
| 参数名 | 类型 | 必填 | 说明 | 取值范围 |
|--------|------|------|------|----------|
| voiceUrl | string | 是 | 语音文件的OBS下载URL | 最大512字符 |
| playMode | string | 否 | 播放模式 | immediate/queue/interrupt |
| volume | int | 否 | 播放音量 | 0-100 |
| priority | string | 否 | 播放优先级 | low/normal/high/urgent |
| timestamp | long | 否 | 命令时间戳 | Unix时间戳(毫秒) |

#### **响应格式**
```json
{
  "responseName": "PLAY_PARENT_VOICE_RSP",
  "paras": {
    "result": "success",
    "playbackId": "pb_1704369783001",
    "errorCode": null
  }
}
```

## 🔧 设备端实现指南

### **1. 命令接收处理**

```python
def handle_play_parent_voice_command(command_data):
    """
    处理家长语音播放命令
    """
    try:
        # 解析命令参数
        voice_url = command_data.get('voiceUrl')
        play_mode = command_data.get('playMode', 'immediate')
        volume = command_data.get('volume', 80)
        priority = command_data.get('priority', 'normal')
        timestamp = command_data.get('timestamp', int(time.time() * 1000))
        
        # 验证参数
        if not voice_url:
            return create_error_response("INVALID_VOICE_URL", "语音URL不能为空")
        
        # 生成播放ID
        playback_id = f"pb_{timestamp}"
        
        # 根据播放模式处理
        if play_mode == "immediate":
            # 立即播放，中断当前播放
            stop_current_playback()
            result = download_and_play_voice(voice_url, volume, playback_id)
        elif play_mode == "queue":
            # 加入播放队列
            result = add_to_playback_queue(voice_url, volume, playback_id)
        elif play_mode == "interrupt":
            # 高优先级中断播放
            interrupt_and_play_voice(voice_url, volume, playback_id)
            result = True
        else:
            return create_error_response("INVALID_PLAY_MODE", f"不支持的播放模式: {play_mode}")
        
        # 返回响应
        if result:
            return create_success_response(playback_id)
        else:
            return create_error_response("PLAYBACK_FAILED", "语音播放失败")
            
    except Exception as e:
        logger.error(f"处理家长语音播放命令异常: {e}")
        return create_error_response("INTERNAL_ERROR", str(e))

def create_success_response(playback_id):
    """创建成功响应"""
    return {
        "responseName": "PLAY_PARENT_VOICE_RSP",
        "paras": {
            "result": "success",
            "playbackId": playback_id,
            "errorCode": None
        }
    }

def create_error_response(error_code, error_message):
    """创建错误响应"""
    return {
        "responseName": "PLAY_PARENT_VOICE_RSP",
        "paras": {
            "result": "failed",
            "playbackId": None,
            "errorCode": error_code
        }
    }
```

### **2. 语音文件下载**

```python
import requests
import os
from urllib.parse import urlparse

def download_and_play_voice(voice_url, volume, playback_id):
    """
    下载并播放语音文件
    """
    try:
        # 解析文件名
        parsed_url = urlparse(voice_url)
        filename = os.path.basename(parsed_url.path)
        local_path = f"/tmp/voice/{filename}"
        
        # 确保目录存在
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        
        # 下载文件
        logger.info(f"开始下载语音文件: {voice_url}")
        response = requests.get(voice_url, timeout=30)
        response.raise_for_status()
        
        # 保存文件
        with open(local_path, 'wb') as f:
            f.write(response.content)
        
        logger.info(f"语音文件下载完成: {local_path}")
        
        # 播放语音
        return play_audio_file(local_path, volume, playback_id)
        
    except requests.RequestException as e:
        logger.error(f"下载语音文件失败: {e}")
        return False
    except Exception as e:
        logger.error(f"处理语音文件异常: {e}")
        return False

def play_audio_file(file_path, volume, playback_id):
    """
    播放音频文件
    """
    try:
        # 设置音量
        set_system_volume(volume)
        
        # 使用系统播放器播放（示例使用aplay）
        import subprocess
        
        # 记录播放开始
        logger.info(f"开始播放语音: {file_path}, 播放ID: {playback_id}")
        
        # 播放音频
        result = subprocess.run(['aplay', file_path], 
                              capture_output=True, 
                              text=True, 
                              timeout=60)
        
        if result.returncode == 0:
            logger.info(f"语音播放完成: {playback_id}")
            # 清理临时文件
            os.remove(file_path)
            return True
        else:
            logger.error(f"语音播放失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("语音播放超时")
        return False
    except Exception as e:
        logger.error(f"播放音频文件异常: {e}")
        return False

def set_system_volume(volume_percent):
    """
    设置系统音量
    """
    try:
        # 使用amixer设置音量（Linux系统）
        import subprocess
        subprocess.run(['amixer', 'set', 'Master', f'{volume_percent}%'], 
                      check=True, capture_output=True)
        logger.info(f"系统音量设置为: {volume_percent}%")
    except Exception as e:
        logger.warning(f"设置音量失败: {e}")
```

### **3. 播放队列管理**

```python
import queue
import threading
from dataclasses import dataclass
from typing import Optional

@dataclass
class VoicePlaybackTask:
    voice_url: str
    volume: int
    playback_id: str
    priority: str
    timestamp: int

class VoicePlaybackManager:
    """语音播放管理器"""
    
    def __init__(self):
        self.playback_queue = queue.PriorityQueue()
        self.current_playback = None
        self.is_playing = False
        self.worker_thread = None
        self.stop_event = threading.Event()
        
    def start(self):
        """启动播放管理器"""
        self.worker_thread = threading.Thread(target=self._playback_worker)
        self.worker_thread.daemon = True
        self.worker_thread.start()
        
    def add_to_queue(self, task: VoicePlaybackTask):
        """添加到播放队列"""
        priority_map = {'urgent': 1, 'high': 2, 'normal': 3, 'low': 4}
        priority = priority_map.get(task.priority, 3)
        
        self.playback_queue.put((priority, task.timestamp, task))
        logger.info(f"语音任务已加入队列: {task.playback_id}")
        
    def interrupt_current_playback(self):
        """中断当前播放"""
        if self.is_playing and self.current_playback:
            logger.info(f"中断当前播放: {self.current_playback.playback_id}")
            # 这里需要实现具体的中断逻辑
            self.stop_current_playback()
            
    def _playback_worker(self):
        """播放工作线程"""
        while not self.stop_event.is_set():
            try:
                # 从队列获取任务（阻塞等待）
                priority, timestamp, task = self.playback_queue.get(timeout=1)
                
                # 执行播放
                self.current_playback = task
                self.is_playing = True
                
                success = download_and_play_voice(
                    task.voice_url, 
                    task.volume, 
                    task.playback_id
                )
                
                if success:
                    logger.info(f"语音播放成功: {task.playback_id}")
                else:
                    logger.error(f"语音播放失败: {task.playback_id}")
                
                self.current_playback = None
                self.is_playing = False
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"播放工作线程异常: {e}")
                self.is_playing = False
                self.current_playback = None

# 全局播放管理器实例
playback_manager = VoicePlaybackManager()
```

## 🔊 音频格式支持

### **支持的音频格式**
- **AAC** (.aac) - 推荐格式，压缩率高，质量好
- **MP3** (.mp3) - 通用格式，兼容性好
- **WAV** (.wav) - 无损格式，文件较大

### **音频参数建议**
- **采样率**: 16kHz 或 44.1kHz
- **比特率**: 64kbps - 128kbps
- **声道**: 单声道或立体声
- **编码**: AAC-LC 或 MP3

## 🔧 错误处理

### **常见错误码**
| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| INVALID_VOICE_URL | 语音URL无效 | 检查URL格式和可访问性 |
| DOWNLOAD_FAILED | 下载失败 | 检查网络连接和OBS权限 |
| PLAYBACK_FAILED | 播放失败 | 检查音频格式和系统音频设备 |
| INVALID_PLAY_MODE | 播放模式无效 | 使用支持的播放模式 |
| INTERNAL_ERROR | 内部错误 | 查看详细日志信息 |

### **重试机制**
```python
def download_with_retry(voice_url, max_retries=3):
    """带重试的下载"""
    for attempt in range(max_retries):
        try:
            response = requests.get(voice_url, timeout=30)
            response.raise_for_status()
            return response.content
        except Exception as e:
            logger.warning(f"下载尝试 {attempt + 1} 失败: {e}")
            if attempt == max_retries - 1:
                raise
            time.sleep(2 ** attempt)  # 指数退避
```

## 📊 状态上报

### **VoiceInteraction服务属性上报**
```python
def report_voice_status():
    """上报语音交互状态"""
    status_data = {
        "voiceStatus": "ACTIVE" if playback_manager.is_playing else "INACTIVE",
        "speakerStatus": "ACTIVE",
        "microphoneStatus": "ACTIVE",
        "volumeLevel": get_current_volume(),
        "lastAudioEvent": get_last_audio_event()
    }
    
    # 通过华为云IoT SDK上报
    iot_client.report_properties("VoiceInteraction", status_data)
```

## 🔍 调试和日志

### **关键日志点**
1. 命令接收和解析
2. 语音文件下载进度
3. 播放开始和结束
4. 错误和异常情况

### **日志格式示例**
```
[2025-01-04 15:30:25] INFO: 收到家长语音播放命令, 播放ID: pb_1704369783001
[2025-01-04 15:30:26] INFO: 开始下载语音文件: https://obs.../parent_voice_1704369783000.aac
[2025-01-04 15:30:27] INFO: 语音文件下载完成: /tmp/voice/parent_voice_1704369783000.aac
[2025-01-04 15:30:27] INFO: 开始播放语音: pb_1704369783001
[2025-01-04 15:30:32] INFO: 语音播放完成: pb_1704369783001
```

## 🚀 部署建议

1. **确保网络连接** - 设备能访问华为云OBS服务
2. **音频设备检查** - 确认扬声器正常工作
3. **存储空间** - 预留足够空间存储临时音频文件
4. **权限配置** - 确保进程有音频播放权限
5. **监控告警** - 设置播放失败的告警机制

## 📞 技术支持

如有技术问题，请提供：
- 设备ID和时间戳
- 完整的错误日志
- 网络连接状态
- 音频设备状态

---

**注意**: 本文档基于华为云IoT平台和OBS服务，请确保设备端已正确配置相关服务的访问权限。
