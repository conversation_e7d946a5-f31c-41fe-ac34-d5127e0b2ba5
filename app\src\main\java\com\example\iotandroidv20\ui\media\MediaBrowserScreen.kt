package com.example.iotandroidv20.ui.media

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.iotandroidv20.obs.AudioSession
import com.example.iotandroidv20.obs.VideoSession

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MediaBrowserScreen(
    onNavigateBack: () -> Unit,
    onVideoClick: (VideoSession) -> Unit,
    onAudioClick: (AudioSession) -> Unit,
    onObsConfigClick: () -> Unit,
    viewModel: MediaBrowserViewModel = viewModel()
) {
    val context = LocalContext.current
    val uiState by viewModel.uiState.collectAsState()
    
    // 初始化
    LaunchedEffect(Unit) {
        viewModel.initialize(context)
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("媒体中心") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    // 刷新按钮
                    IconButton(
                        onClick = { viewModel.refreshData() },
                        enabled = !uiState.isLoading
                    ) {
                        Icon(Icons.Default.Refresh, contentDescription = "刷新")
                    }
                    
                    // 设置按钮
                    IconButton(onClick = onObsConfigClick) {
                        Icon(Icons.Default.Settings, contentDescription = "OBS配置")
                    }
                }
            )
        },
        floatingActionButton = {
            if (uiState.isConfigured) {
                FloatingActionButton(
                    onClick = { viewModel.showMediaList() }
                ) {
                    Icon(Icons.Default.List, contentDescription = "媒体列表")
                }
            }
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                !uiState.isConfigured -> {
                    // 未配置状态
                    MediaBrowserEmptyState(
                        title = "OBS未配置",
                        message = "请先配置华为云OBS访问凭据以使用媒体功能",
                        actionText = "去配置",
                        onAction = onObsConfigClick,
                        icon = Icons.Default.CloudOff
                    )
                }
                
                uiState.isLoading -> {
                    // 加载状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            CircularProgressIndicator()
                            Spacer(modifier = Modifier.height(16.dp))
                            Text("加载媒体数据...")
                        }
                    }
                }
                
                uiState.error.isNotEmpty() -> {
                    // 错误状态
                    MediaBrowserEmptyState(
                        title = "加载失败",
                        message = uiState.error,
                        actionText = "重试",
                        onAction = { viewModel.refreshData() },
                        icon = Icons.Default.Error
                    )
                }
                
                uiState.recentVideos.isEmpty() && uiState.recentAudios.isEmpty() -> {
                    // 空状态
                    MediaBrowserEmptyState(
                        title = "暂无媒体文件",
                        message = "当前没有可用的音视频文件，请检查设备是否正常上传数据",
                        actionText = "刷新",
                        onAction = { viewModel.refreshData() },
                        icon = Icons.Default.VideoLibrary
                    )
                }
                
                else -> {
                    // 正常状态 - 显示媒体概览
                    MediaBrowserContent(
                        uiState = uiState,
                        onVideoClick = onVideoClick,
                        onAudioClick = onAudioClick,
                        onViewAllVideos = { viewModel.showVideoList() },
                        onViewAllAudios = { viewModel.showAudioList() },
                        onDeviceClick = { viewModel.selectDevice(it) }
                    )
                }
            }
        }
    }
}

@Composable
private fun MediaBrowserContent(
    uiState: MediaBrowserUiState,
    onVideoClick: (VideoSession) -> Unit,
    onAudioClick: (AudioSession) -> Unit,
    onViewAllVideos: () -> Unit,
    onViewAllAudios: () -> Unit,
    onDeviceClick: (String) -> Unit
) {
    LazyVerticalGrid(
        columns = GridCells.Fixed(1),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 统计卡片
        item {
            MediaStatisticsCard(
                totalVideos = uiState.totalVideos,
                totalAudios = uiState.totalAudios,
                totalSize = uiState.totalSize,
                availableDevices = uiState.availableDevices,
                onDeviceClick = onDeviceClick
            )
        }
        
        // 最近视频
        if (uiState.recentVideos.isNotEmpty()) {
            item {
                MediaSectionCard(
                    title = "最近视频",
                    subtitle = "共${uiState.totalVideos}个视频文件",
                    onViewAll = onViewAllVideos
                ) {
                    LazyVerticalGrid(
                        columns = GridCells.Fixed(2),
                        verticalArrangement = Arrangement.spacedBy(8.dp),
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        modifier = Modifier.height(200.dp)
                    ) {
                        items(uiState.recentVideos.take(4)) { video ->
                            VideoThumbnailCard(
                                videoSession = video,
                                onClick = { onVideoClick(video) }
                            )
                        }
                    }
                }
            }
        }
        
        // 最近音频
        if (uiState.recentAudios.isNotEmpty()) {
            item {
                MediaSectionCard(
                    title = "最近音频",
                    subtitle = "共${uiState.totalAudios}个音频文件",
                    onViewAll = onViewAllAudios
                ) {
                    Column(
                        verticalArrangement = Arrangement.spacedBy(4.dp),
                        modifier = Modifier.height(200.dp)
                    ) {
                        uiState.recentAudios.take(4).forEach { audio ->
                            AudioListItem(
                                audioSession = audio,
                                onClick = { onAudioClick(audio) },
                                compact = true
                            )
                        }
                    }
                }
            }
        }
        
        // 快速操作
        item {
            QuickActionsCard(
                onDownloadManager = { /* TODO: 实现下载管理 */ },
                onCacheManager = { /* TODO: 实现缓存管理 */ },
                onSettings = { /* TODO: 实现设置 */ }
            )
        }
    }
}

@Composable
private fun MediaStatisticsCard(
    totalVideos: Int,
    totalAudios: Int,
    totalSize: Long,
    availableDevices: List<String>,
    onDeviceClick: (String) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "媒体统计",
                style = MaterialTheme.typography.titleMedium
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatisticItem(
                    icon = Icons.Default.VideoLibrary,
                    value = totalVideos.toString(),
                    label = "视频"
                )
                
                StatisticItem(
                    icon = Icons.Default.AudioFile,
                    value = totalAudios.toString(),
                    label = "音频"
                )
                
                StatisticItem(
                    icon = Icons.Default.Storage,
                    value = formatFileSize(totalSize),
                    label = "总大小"
                )
                
                StatisticItem(
                    icon = Icons.Default.Devices,
                    value = availableDevices.size.toString(),
                    label = "设备"
                )
            }
            
            if (availableDevices.isNotEmpty()) {
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "可用设备",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    availableDevices.take(3).forEach { device ->
                        AssistChip(
                            onClick = { onDeviceClick(device) },
                            label = { Text(device) }
                        )
                    }
                    
                    if (availableDevices.size > 3) {
                        AssistChip(
                            onClick = { },
                            label = { Text("+${availableDevices.size - 3}") }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun StatisticItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    value: String,
    label: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            icon,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(24.dp)
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = value,
            style = MaterialTheme.typography.titleMedium
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
private fun MediaSectionCard(
    title: String,
    subtitle: String,
    onViewAll: () -> Unit,
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = subtitle,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                TextButton(onClick = onViewAll) {
                    Text("查看全部")
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            content()
        }
    }
}

@Composable
private fun QuickActionsCard(
    onDownloadManager: () -> Unit,
    onCacheManager: () -> Unit,
    onSettings: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "快速操作",
                style = MaterialTheme.typography.titleMedium
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                QuickActionButton(
                    icon = Icons.Default.Download,
                    text = "下载管理",
                    onClick = onDownloadManager
                )
                
                QuickActionButton(
                    icon = Icons.Default.Storage,
                    text = "缓存管理",
                    onClick = onCacheManager
                )
                
                QuickActionButton(
                    icon = Icons.Default.Settings,
                    text = "设置",
                    onClick = onSettings
                )
            }
        }
    }
}

@Composable
private fun QuickActionButton(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    text: String,
    onClick: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        IconButton(
            onClick = onClick,
            modifier = Modifier.size(48.dp)
        ) {
            Icon(
                icon,
                contentDescription = text,
                modifier = Modifier.size(24.dp)
            )
        }
        Text(
            text = text,
            style = MaterialTheme.typography.bodySmall
        )
    }
}

@Composable
private fun MediaBrowserEmptyState(
    title: String,
    message: String,
    actionText: String,
    onAction: () -> Unit,
    icon: androidx.compose.ui.graphics.vector.ImageVector
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(32.dp)
        ) {
            Icon(
                icon,
                contentDescription = null,
                modifier = Modifier.size(64.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = title,
                style = MaterialTheme.typography.titleLarge,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = message,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = androidx.compose.ui.text.style.TextAlign.Center
            )
            Spacer(modifier = Modifier.height(24.dp))
            Button(onClick = onAction) {
                Text(actionText)
            }
        }
    }
}

private fun formatFileSize(bytes: Long): String {
    return when {
        bytes < 1024 -> "${bytes}B"
        bytes < 1024 * 1024 -> "${bytes / 1024}KB"
        bytes < 1024 * 1024 * 1024 -> "${bytes / (1024 * 1024)}MB"
        else -> "${bytes / (1024 * 1024 * 1024)}GB"
    }
}
