{"services": [{"serviceId": "VoiceInteraction", "serviceType": "VoiceInteraction", "properties": [{"propertyName": "voiceStatus", "dataType": "string", "required": true, "enumList": ["ACTIVE", "INACTIVE", "RECORDING", "PLAYING", "ERROR"], "min": null, "max": null, "maxLength": 15, "step": 0, "unit": null, "method": "R", "description": "语音系统状态", "defaultValue": null}, {"propertyName": "recordingStatus", "dataType": "string", "required": false, "enumList": ["RECORDING", "STOPPED", "PAUSED", "ERROR", "UPLOADING"], "min": null, "max": null, "maxLength": 15, "step": 0, "unit": null, "method": "R", "description": "录音状态", "defaultValue": null}, {"propertyName": "currentSegmentId", "dataType": "string", "required": false, "enumList": null, "min": null, "max": null, "maxLength": 100, "step": 0, "unit": null, "method": "R", "description": "当前录音分片ID", "defaultValue": null}, {"propertyName": "segmentDuration", "dataType": "int", "required": false, "enumList": null, "min": "10", "max": "60", "maxLength": 0, "step": 0, "unit": "seconds", "method": "RW", "description": "音频分片时长", "defaultValue": null}, {"propertyName": "audioLevel", "dataType": "decimal", "required": false, "enumList": null, "min": "0.0", "max": "100.0", "maxLength": 0, "step": 0.1, "unit": "dB", "method": "R", "description": "当前音频电平", "defaultValue": null}, {"propertyName": "ambientNoiseLevel", "dataType": "decimal", "required": false, "enumList": null, "min": "0.0", "max": "120.0", "maxLength": 0, "step": 0.1, "unit": "dB", "method": "R", "description": "环境噪音水平", "defaultValue": null}, {"propertyName": "microphoneStatus", "dataType": "string", "required": true, "enumList": ["ACTIVE", "MUTED", "ERROR", "DISCONNECTED"], "min": null, "max": null, "maxLength": 15, "step": 0, "unit": null, "method": "R", "description": "麦克风状态", "defaultValue": null}, {"propertyName": "speaker<PERSON><PERSON>us", "dataType": "string", "required": true, "enumList": ["ACTIVE", "MUTED", "ERROR", "DISCONNECTED"], "min": null, "max": null, "maxLength": 15, "step": 0, "unit": null, "method": "R", "description": "扬声器状态", "defaultValue": null}, {"propertyName": "audioQuality", "dataType": "decimal", "required": false, "enumList": null, "min": "0.0", "max": "1.0", "maxLength": 0, "step": 0.01, "unit": null, "method": "R", "description": "音频质量指标", "defaultValue": null}, {"propertyName": "voiceActivityDetected", "dataType": "boolean", "required": false, "enumList": null, "min": null, "max": null, "maxLength": 0, "step": 0, "unit": null, "method": "R", "description": "是否检测到语音活动", "defaultValue": null}, {"propertyName": "audioSampleRate", "dataType": "int", "required": false, "enumList": null, "min": "16000", "max": "48000", "maxLength": 0, "step": 0, "unit": "Hz", "method": "RW", "description": "音频采样率", "defaultValue": null}, {"propertyName": "audioBitrate", "dataType": "int", "required": false, "enumList": null, "min": "64", "max": "320", "maxLength": 0, "step": 0, "unit": "kbps", "method": "RW", "description": "音频比特率", "defaultValue": null}, {"propertyName": "encodingFormat", "dataType": "string", "required": false, "enumList": ["MP3", "AAC", "OPUS"], "min": null, "max": null, "maxLength": 10, "step": 0, "unit": null, "method": "RW", "description": "音频编码格式", "defaultValue": null}, {"propertyName": "uploadProgress", "dataType": "decimal", "required": false, "enumList": null, "min": "0.0", "max": "100.0", "maxLength": 0, "step": 0.1, "unit": "%", "method": "R", "description": "当前分片上传进度", "defaultValue": null}, {"propertyName": "volumeLevel", "dataType": "int", "required": false, "enumList": null, "min": "0", "max": "100", "maxLength": 0, "step": 0, "unit": "%", "method": "RW", "description": "音量等级", "defaultValue": null}, {"propertyName": "lastUploadTime", "dataType": "DateTime", "required": false, "enumList": null, "min": null, "max": null, "maxLength": 20, "step": 0, "unit": null, "method": "R", "description": "最后上传时间", "defaultValue": null}], "commands": [{"commandName": "START_VOICE_RECORDING", "paras": [{"paraName": "duration", "dataType": "int", "required": false, "enumList": null, "min": "30", "max": "1800", "maxLength": 0, "step": 0, "unit": "seconds", "description": "录音持续时间"}, {"paraName": "segmentDuration", "dataType": "int", "required": false, "enumList": null, "min": "10", "max": "60", "maxLength": 0, "step": 0, "unit": "seconds", "description": "分片时长"}, {"paraName": "sampleRate", "dataType": "int", "required": false, "enumList": null, "min": "16000", "max": "48000", "maxLength": 0, "step": 0, "unit": "Hz", "description": "采样率设置"}, {"paraName": "quality", "dataType": "string", "required": false, "enumList": ["HIGH", "MEDIUM", "LOW"], "min": null, "max": null, "maxLength": 10, "step": 0, "unit": null, "description": "录音质量"}], "responses": [{"responseName": "cmdResponses", "paras": [{"paraName": "result", "dataType": "string", "required": true, "enumList": ["success", "failed"], "min": null, "max": null, "maxLength": 10, "step": 0, "unit": null, "description": "录音启动结果"}, {"paraName": "sessionId", "dataType": "string", "required": false, "enumList": null, "min": null, "max": null, "maxLength": 50, "step": 0, "unit": null, "description": "录音会话ID"}]}]}, {"commandName": "STOP_VOICE_RECORDING", "paras": [], "responses": [{"responseName": "cmdResponses", "paras": [{"paraName": "result", "dataType": "string", "required": true, "enumList": ["success", "failed"], "min": null, "max": null, "maxLength": 10, "step": 0, "unit": null, "description": "录音停止结果"}]}]}, {"commandName": "PLAY_VOICE_MESSAGE", "paras": [{"paraName": "messageType", "dataType": "string", "required": true, "enumList": ["REMINDER", "WARNING", "ENCOURAGEMENT", "INSTRUCTION"], "min": null, "max": null, "maxLength": 15, "step": 0, "unit": null, "description": "消息类型"}, {"paraName": "volume", "dataType": "int", "required": false, "enumList": null, "min": "0", "max": "100", "maxLength": 0, "step": 0, "unit": "%", "description": "播放音量"}, {"paraName": "audioUrl", "dataType": "string", "required": false, "enumList": null, "min": null, "max": null, "maxLength": 500, "step": 0, "unit": null, "description": "音频文件URL"}], "responses": [{"responseName": "cmdResponses", "paras": [{"paraName": "result", "dataType": "string", "required": true, "enumList": ["success", "failed"], "min": null, "max": null, "maxLength": 10, "step": 0, "unit": null, "description": "播放结果"}]}]}, {"commandName": "REQUEST_OBS_UPLOAD_URL", "paras": [{"paraName": "fileName", "dataType": "string", "required": true, "enumList": null, "min": null, "max": null, "maxLength": 200, "step": 0, "unit": null, "description": "上传文件名"}, {"paraName": "fileSize", "dataType": "int", "required": true, "enumList": null, "min": "1", "max": "52428800", "maxLength": 0, "step": 0, "unit": "bytes", "description": "文件大小"}], "responses": [{"responseName": "cmdResponses", "paras": [{"paraName": "result", "dataType": "string", "required": true, "enumList": ["success", "failed"], "min": null, "max": null, "maxLength": 10, "step": 0, "unit": null, "description": "URL请求结果"}, {"paraName": "uploadUrl", "dataType": "string", "required": false, "enumList": null, "min": null, "max": null, "maxLength": 1000, "step": 0, "unit": null, "description": "OBS预签名上传URL"}, {"paraName": "expireTime", "dataType": "DateTime", "required": false, "enumList": null, "min": null, "max": null, "maxLength": 20, "step": 0, "unit": null, "description": "URL过期时间"}]}]}], "events": [{"eventType": "audio_segment_uploaded", "paras": [{"paraName": "segmentId", "dataType": "string", "required": true, "enumList": null, "min": null, "max": null, "maxLength": 100, "step": 0, "unit": null, "description": "音频分片ID"}, {"paraName": "uploadStatus", "dataType": "string", "required": true, "enumList": ["SUCCESS", "FAILED", "RETRY"], "min": null, "max": null, "maxLength": 10, "step": 0, "unit": null, "description": "上传状态"}, {"paraName": "fileSize", "dataType": "int", "required": false, "enumList": null, "min": "0", "max": "52428800", "maxLength": 0, "step": 0, "unit": "bytes", "description": "文件大小"}]}, {"eventType": "voice_activity_detected", "paras": [{"paraName": "activityType", "dataType": "string", "required": true, "enumList": ["SPEECH_START", "SPEECH_END", "NOISE_DETECTED"], "min": null, "max": null, "maxLength": 15, "step": 0, "unit": null, "description": "语音活动类型"}, {"paraName": "confidence", "dataType": "decimal", "required": false, "enumList": null, "min": "0.0", "max": "1.0", "maxLength": 0, "step": 0.01, "unit": null, "description": "检测置信度"}]}, {"eventType": "recording_session_complete", "paras": [{"paraName": "sessionId", "dataType": "string", "required": true, "enumList": null, "min": null, "max": null, "maxLength": 50, "step": 0, "unit": null, "description": "录音会话ID"}, {"paraName": "totalSegments", "dataType": "int", "required": true, "enumList": null, "min": "1", "max": "1000", "maxLength": 0, "step": 0, "unit": "count", "description": "总分片数量"}, {"paraName": "totalDuration", "dataType": "int", "required": false, "enumList": null, "min": "1", "max": "1800", "maxLength": 0, "step": 0, "unit": "seconds", "description": "总录音时长"}]}], "description": "语音交互服务 - 支持音频录制和OBS分片上传，设备端负责采集编码，应用端负责语音识别和分析", "option": "Optional"}]}