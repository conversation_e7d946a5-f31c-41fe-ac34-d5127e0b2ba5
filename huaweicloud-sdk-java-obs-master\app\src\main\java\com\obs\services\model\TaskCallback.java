/**
 * Copyright 2019 Huawei Technologies Co.,Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License.  You may obtain a copy of the
 * License at
 * 
 * http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software distributed
 * under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
 * CONDITIONS OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

package com.obs.services.model;

import com.obs.services.exception.ObsException;

/**
 * Task execution callback
 */
public interface TaskCallback<K, V> {

    /**
     * Callback when the task is executed successfully.
     *
     * @param result
     *            Callback parameter. Generally, the return type of a specific
     *            operation is used.
     */
    void onSuccess(K result);

    /**
     * Callback when an exception is thrown during task execution.
     *
     * @param exception
     *            Exception information
     * @param singleRequest
     *            The request that causes an exception
     * 
     */
    void onException(ObsException exception, V singleRequest);
}
