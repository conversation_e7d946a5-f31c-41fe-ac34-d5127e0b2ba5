package com.example.iotandroidv20.ui.player

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.media3.ui.PlayerView
import com.example.iotandroidv20.obs.PlaybackState
import com.example.iotandroidv20.obs.VideoSession

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VideoPlayerScreen(
    videoSession: VideoSession,
    onNavigateBack: () -> Unit,
    viewModel: VideoPlayerViewModel = viewModel()
) {
    val context = LocalContext.current
    val uiState by viewModel.uiState.collectAsState()
    
    // 初始化播放器
    LaunchedEffect(videoSession) {
        viewModel.initialize(context, videoSession)
    }
    
    // 清理资源
    DisposableEffect(Unit) {
        onDispose {
            viewModel.releasePlayer()
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { 
                    Text(
                        text = videoSession.sessionId,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color.Black.copy(alpha = 0.7f),
                    titleContentColor = Color.White,
                    navigationIconContentColor = Color.White
                )
            )
        },
        containerColor = Color.Black
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 视频播放器视图
            AndroidView(
                factory = { context ->
                    PlayerView(context).apply {
                        player = viewModel.getExoPlayer()
                        useController = false // 使用自定义控制器
                        setShowBuffering(PlayerView.SHOW_BUFFERING_WHEN_PLAYING)
                    }
                },
                modifier = Modifier.fillMaxSize(),
                update = { playerView ->
                    playerView.player = viewModel.getExoPlayer()
                }
            )
            
            // 加载状态覆盖层
            if (uiState.playerInfo.state == PlaybackState.PREPARING) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.Black.copy(alpha = 0.7f)),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        CircularProgressIndicator(color = Color.White)
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "加载中...",
                            color = Color.White
                        )
                    }
                }
            }
            
            // 错误状态覆盖层
            if (uiState.error.isNotEmpty()) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.Black.copy(alpha = 0.8f)),
                    contentAlignment = Alignment.Center
                ) {
                    Card(
                        modifier = Modifier.padding(32.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.errorContainer
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(24.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Icon(
                                Icons.Default.Error,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.onErrorContainer,
                                modifier = Modifier.size(48.dp)
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = "播放出错",
                                style = MaterialTheme.typography.titleMedium,
                                color = MaterialTheme.colorScheme.onErrorContainer
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = uiState.error,
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onErrorContainer
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            Button(
                                onClick = { viewModel.retry() },
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = MaterialTheme.colorScheme.error
                                )
                            ) {
                                Text("重试")
                            }
                        }
                    }
                }
            }
            
            // 自定义播放控制器
            VideoPlayerControls(
                modifier = Modifier.align(Alignment.BottomCenter),
                playerInfo = uiState.playerInfo,
                isControlsVisible = uiState.isControlsVisible,
                onPlayPauseClick = viewModel::togglePlayPause,
                onSeekTo = viewModel::seekTo,
                onSpeedChange = viewModel::setPlaybackSpeed,
                onVolumeChange = viewModel::setVolume,
                onToggleControls = viewModel::toggleControls
            )
            
            // 点击区域（用于显示/隐藏控制器）
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .clickable { viewModel.toggleControls() }
            )
        }
    }
}

@Composable
private fun VideoPlayerControls(
    modifier: Modifier = Modifier,
    playerInfo: com.example.iotandroidv20.obs.PlayerInfo,
    isControlsVisible: Boolean,
    onPlayPauseClick: () -> Unit,
    onSeekTo: (Long) -> Unit,
    onSpeedChange: (Float) -> Unit,
    onVolumeChange: (Float) -> Unit,
    onToggleControls: () -> Unit
) {
    AnimatedVisibility(
        visible = isControlsVisible,
        modifier = modifier
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    Color.Black.copy(alpha = 0.7f),
                    RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
                )
                .padding(16.dp)
        ) {
            // 进度条
            VideoProgressBar(
                currentPosition = playerInfo.currentPosition,
                duration = playerInfo.duration,
                bufferedPosition = playerInfo.bufferedPosition,
                onSeekTo = onSeekTo
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 播放控制按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 播放/暂停按钮
                IconButton(
                    onClick = onPlayPauseClick,
                    modifier = Modifier.size(56.dp)
                ) {
                    Icon(
                        imageVector = if (playerInfo.state == PlaybackState.PLAYING) {
                            Icons.Default.Pause
                        } else {
                            Icons.Default.PlayArrow
                        },
                        contentDescription = if (playerInfo.state == PlaybackState.PLAYING) "暂停" else "播放",
                        tint = Color.White,
                        modifier = Modifier.size(32.dp)
                    )
                }
                
                // 播放速度控制
                SpeedControlButton(
                    currentSpeed = playerInfo.playbackSpeed,
                    onSpeedChange = onSpeedChange
                )
                
                // 音量控制
                VolumeControlButton(
                    currentVolume = playerInfo.volume,
                    onVolumeChange = onVolumeChange
                )
            }
        }
    }
}

@Composable
private fun VideoProgressBar(
    currentPosition: Long,
    duration: Long,
    bufferedPosition: Long,
    onSeekTo: (Long) -> Unit
) {
    Column {
        // 时间显示
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = formatTime(currentPosition),
                color = Color.White,
                style = MaterialTheme.typography.bodySmall
            )
            Text(
                text = formatTime(duration),
                color = Color.White,
                style = MaterialTheme.typography.bodySmall
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 进度条
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(4.dp)
                .clip(RoundedCornerShape(2.dp))
                .background(Color.White.copy(alpha = 0.3f))
        ) {
            // 缓冲进度
            if (duration > 0) {
                Box(
                    modifier = Modifier
                        .fillMaxHeight()
                        .fillMaxWidth(bufferedPosition.toFloat() / duration.toFloat())
                        .background(Color.White.copy(alpha = 0.5f))
                )
            }
            
            // 播放进度
            if (duration > 0) {
                Box(
                    modifier = Modifier
                        .fillMaxHeight()
                        .fillMaxWidth(currentPosition.toFloat() / duration.toFloat())
                        .background(Color.White)
                )
            }
        }
        
        // 可拖拽的进度条
        if (duration > 0) {
            var isDragging by remember { mutableStateOf(false) }
            var dragPosition by remember { mutableStateOf(currentPosition) }
            
            Slider(
                value = if (isDragging) dragPosition.toFloat() else currentPosition.toFloat(),
                onValueChange = { value ->
                    isDragging = true
                    dragPosition = value.toLong()
                },
                onValueChangeFinished = {
                    onSeekTo(dragPosition)
                    isDragging = false
                },
                valueRange = 0f..duration.toFloat(),
                modifier = Modifier.fillMaxWidth(),
                colors = SliderDefaults.colors(
                    thumbColor = Color.White,
                    activeTrackColor = Color.White,
                    inactiveTrackColor = Color.White.copy(alpha = 0.3f)
                )
            )
        }
    }
}

@Composable
private fun SpeedControlButton(
    currentSpeed: Float,
    onSpeedChange: (Float) -> Unit
) {
    var showSpeedMenu by remember { mutableStateOf(false) }
    
    Box {
        TextButton(
            onClick = { showSpeedMenu = true },
            colors = ButtonDefaults.textButtonColors(contentColor = Color.White)
        ) {
            Text("${currentSpeed}x")
        }
        
        DropdownMenu(
            expanded = showSpeedMenu,
            onDismissRequest = { showSpeedMenu = false }
        ) {
            listOf(0.5f, 0.75f, 1.0f, 1.25f, 1.5f, 2.0f).forEach { speed ->
                DropdownMenuItem(
                    text = { Text("${speed}x") },
                    onClick = {
                        onSpeedChange(speed)
                        showSpeedMenu = false
                    }
                )
            }
        }
    }
}

@Composable
private fun VolumeControlButton(
    currentVolume: Float,
    onVolumeChange: (Float) -> Unit
) {
    var showVolumeSlider by remember { mutableStateOf(false) }
    
    Box {
        IconButton(onClick = { showVolumeSlider = !showVolumeSlider }) {
            Icon(
                imageVector = when {
                    currentVolume == 0f -> Icons.Default.VolumeOff
                    currentVolume < 0.5f -> Icons.Default.VolumeDown
                    else -> Icons.Default.VolumeUp
                },
                contentDescription = "音量",
                tint = Color.White
            )
        }
        
        if (showVolumeSlider) {
            Card(
                modifier = Modifier
                    .width(200.dp)
                    .offset(x = (-100).dp, y = (-60).dp)
            ) {
                Slider(
                    value = currentVolume,
                    onValueChange = onVolumeChange,
                    valueRange = 0f..1f,
                    modifier = Modifier.padding(16.dp)
                )
            }
        }
    }
}

private fun formatTime(timeMs: Long): String {
    val totalSeconds = timeMs / 1000
    val hours = totalSeconds / 3600
    val minutes = (totalSeconds % 3600) / 60
    val seconds = totalSeconds % 60
    
    return if (hours > 0) {
        String.format("%02d:%02d:%02d", hours, minutes, seconds)
    } else {
        String.format("%02d:%02d", minutes, seconds)
    }
}
