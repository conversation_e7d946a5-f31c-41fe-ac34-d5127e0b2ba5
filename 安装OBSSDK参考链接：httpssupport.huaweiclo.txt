安装OBSSDK参考链接：https://support.huaweicloud.com/sdk-android-devg-obs/obs_26_0105.html
使用预签名URL直传参考链接：OBShttps://support.huaweicloud.com/bestpractice-obs/obs_05_1203.html
现成的obs-sdk文件：huaweicloud-sdk-java-obs-master
参考并借鉴以下文档，重新设计一下视频和音频模块部分
一、设备端到云平台部分​
（一）嵌入式设备端开发​
采集硬件驱动与数据获取​
视频：适配摄像头硬件，通过 USB、MIPI 等接口连接主控单元，采集原始视频数据（如 YUV 格式）。​
音频：适配麦克风硬件，支持 I2S、PCM 等常见音频输入接口，采集原始音频数据（如 PCM 格式）。​
两者均需对原始数据进行初步处理，视频可进行降噪等操作，音频包括降噪、音量调节等。​
数据编码​
视频：选用 H.264/H.265 等编码算法，兼顾带宽与画质，码率控制在 1-2Mbps（720P@30fps），并可动态调整分辨率、帧率。​
音频：选用 MP3、AAC、OPUS 等编码算法，比特率在 64kbps-320kbps，采样率如 44.1kHz，根据网络和设备性能动态调整参数。​
L610 模组配置与 MQTT 连接​
无论是视频还是音频设备，均通过 UART/SPI 接口将 L610 模组与主控单元连接，发送 AT 指令配置网络（如 APN），建立 TCP/UDP 连接或利用 MQTT AT 指令接入华为云 IoT 平台，使用设备证书完成认证，确保 L610 模组成功注册到 LTE 网络并获取 IP 地址。​
OBS 直传准备​
视频和音频设备端均通过 MQTT 向华为云 IoT 平台请求 OBS 预签名 URL，请求主题为$oc/devices/${deviceId}/sys/obs/url/request。​
视频：按 10 秒 / 片分割为 MP4 等文件进行分片传输。​
音频：按 30 秒 / 片分割为 MP3、AAC 等文件，封装成适合 HTTP PUT/POST 上传的格式。​
（二）华为云平台配置（与设备端交互相关）​
IoT 产品与设备创建​
分别创建 “视频设备” 和 “音频设备” 类型的产品，定义对应的数据模型（视频的 “video_data” 字段、音频的 “audio_data” 字段）。​
在各自产品下注册设备，获取设备证书（Device ID、Secret），确保设备端成功接入。​
OBS 桶配置（设备端上传相关）​
为视频和音频分别创建或共用 OBS 桶（建议分开存储便于管理），选择与设备端网络同区域，配置桶策略授予华为云 IoT 平台写入权限，设置合适的存储类别，并配置预签名 URL 功能，为设备端提供临时上传 URL 实现直传。​
媒体处理服务（MPS）配置（针对设备上传数据）​
进入华为云媒体处理服务控制台，分别为视频和音频创建处理任务，关联对应的 OBS 桶和处理模板。​
视频：配置转码模板，根据应用端需求设置输出参数，设置合并规则将视频分片按顺序合并。​
音频：配置音频转码模板，根据应用端播放需求设置输出参数，设置合并规则将音频分片按顺序合并。​
（三）设备端到云平台的数据传输与处理流程​
设备端数据上报​
视频和音频设备端均通过 L610 模组，利用获取到的 OBS 预签名 URL，将分片数据通过 HTTP PUT/POST 请求直接上传至 OBS 桶。​
上传完成后，均需对比本地文件哈希值与 OBS 桶中文件哈希值，确保数据准确，记录上传日志（时间、分片编号、状态、耗时等）。​
都要实现重试机制，上传超时或失败时自动重试（如 3 次），并记录相关信息。​
华为云对设备上传数据的处理​
MPS 定期扫描 OBS 桶中符合各自命名规则（视频：设备 ID + 时间戳 + 分片编号；音频：同理）的分片文件。​
按照配置的合并规则，分别将视频和音频分片按顺序合并成完整文件。​
依据各自的转码模板，对合并后的文件进行转码处理，生成适应不同终端播放的格式。​
二、应用端到云平台部分​
（一）华为云平台配置（与应用端交互相关）​
OBS 桶配置（应用端访问相关）​
确保 OBS 桶已正确存储经 MPS 处理后的完整视频和音频文件。​
配置 OBS 桶的访问权限，保证应用端能合法访问，可通过设置访问策略、生成访问密钥等方式实现，视频和音频在此配置上一致。​
媒体处理服务（MPS）配置（与应用端对接相关）​
确保 MPS 处理后的视频和音频文件按预期存储在 OBS 桶的指定路径，方便应用端查找和访问，两者通用此配置原则。​
（二）应用端开发（与云平台交互）​
OBS SDK/API 集成​
无论是视频还是音频应用端，均根据开发语言（如 Java、Python 等）选择对应的 OBS SDK 进行集成，或熟悉 OBS API 的调用方式。​
都需配置访问 OBS 的认证信息（Access Key ID 和 Secret Access Key），确保能成功访问 OBS 服务。​
与云平台的数据交互功能实现​
下载功能：视频和音频应用端均通过 SDK 的下载接口，指定桶名和文件路径进行下载；或使用 API 构建 HTTP 请求获取。​
播放功能：​
视频：若使用 SDK，利用其提供的流播放相关接口；若使用 API，构建合适的 HTTP 请求获取视频流地址，在应用端播放器中进行播放，考虑缓存、进度控制等功能。​
音频：利用 SDK 的流播放接口或通过 API 获取音频流地址，在应用端播放器中播放，考虑播放缓存、进度控制等功能。​
（三）应用端到云平台的交互流程​
应用端获取文件​
视频和音频应用端均通过集成的 OBS SDK 或调用 OBS API，向华为云 OBS 服务发送请求，获取所需文件的信息（路径、访问地址等）。​
根据获取到的信息，执行下载或在线播放操作，与 OBS 服务建立数据传输连接，获取对应的数据。​
应用端与云平台的交互保障​
两者都要处理交互过程中的异常情况（如网络中断、请求超时等），实现重试机制或给用户相应的提示。​
均通过 HTTPS 等加密方式保证应用端与云平台之间的通信安全。​
三、测试与优化​
功能测试​
分别在视频和音频嵌入式设备端模拟不同场景的采集，验证数据是否能成功上报至 OBS 桶，检查 MPS 是否能正确合并、转码文件，在应用端测试下载和播放功能。​
性能测试​
视频：测试不同网络条件下视频数据的上传速度、丢包率等，评估 MPS 处理视频的性能，测试应用端播放视频的卡顿情况、加载时间等。​
音频：测试不同网络条件下音频数据的上传速度、丢包率等，评估 MPS 处理音频的性能，测试应用端播放音频的流畅度等。​
整体优化​
根据视频设备端测试结果，调整视频编码参数；根据音频设备端测试结果，调整音频编码参数和上传策略等。​
依据视频和音频应用端测试情况，分别优化各自的交互逻辑、缓存策略等，提升用户体验。​
四、项目上线与维护​
设备端到云平台部分上线与维护​
将视频和音频嵌入式设备端的程序分别部署到实际设备中，确保能稳定向云平台上传数据。​
分别监控视频和音频设备的运行状态、数据上传情况及云平台中对应 OBS 桶存储状态、MPS 处理状态，及时解决问题。​
应用端到云平台部分上线与维护​
分别发布视频和音频应用端版本，确保能正常与云平台交互，实现各自的下载和播放功能。​
分别监控视频和音频应用端与云平台的交互情况，收集用户反馈，进行优化升级，适配云平台服务更新。​
