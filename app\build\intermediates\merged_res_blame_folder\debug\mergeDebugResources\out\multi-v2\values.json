{"logs": [{"outputFile": "com.example.iotandroidv20.app-mergeDebugResources-2:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\0d3e1235aadef9afdb3676245f6331de\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "309,330", "startColumns": "4,4", "startOffsets": "18251,19352", "endColumns": "41,59", "endOffsets": "18288,19407"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\bb18151aaf917c25a26fedc6cf286b2d\\transformed\\navigation-common-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "902,915,921,927,936", "startColumns": "4,4,4,4,4", "startOffsets": "50245,50884,51128,51375,51738", "endLines": "914,920,926,929,940", "endColumns": "24,24,24,24,24", "endOffsets": "50879,51123,51370,51503,51915"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\fe681a066bb9f16f82c1d9151f660d84\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "299", "startColumns": "4", "startOffsets": "17707", "endColumns": "65", "endOffsets": "17768"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\01b6e8b8fea8bb45b55852d4590f3437\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "497,498", "startColumns": "4,4", "startOffsets": "31407,31463", "endColumns": "55,54", "endOffsets": "31458,31513"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\ac6cf4e4cf3046c8796a2a18ffa7c217\\transformed\\recyclerview-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,170,218,274,349,425,497,563", "endLines": "2,3,4,5,6,7,8,9,30", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "106,165,213,269,344,420,492,558,1398"}, "to": {"startLines": "36,146,147,148,149,150,151,303,1010", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "1519,7497,7556,7604,7660,7735,7811,17933,54328", "endLines": "36,146,147,148,149,150,151,303,1030", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "1570,7551,7599,7655,7730,7806,7878,17994,55163"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidV20\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "54", "endOffsets": "66"}, "to": {"startLines": "348", "startColumns": "4", "startOffsets": "20578", "endColumns": "54", "endOffsets": "20628"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidV20\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "82,97,98,99,101,102,103", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3632,4584,4631,4678,4798,4843,4888", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "3669,4626,4673,4720,4838,4883,4925"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidV20\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "90", "endOffsets": "142"}, "to": {"startLines": "704", "startColumns": "4", "startOffsets": "43356", "endColumns": "89", "endOffsets": "43441"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\de19e379d14d1f39ee96e7327b472e4c\\transformed\\media-1.6.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,350,410,476,598,659,725", "endColumns": "88,70,72,61,59,65,121,60,65,66", "endOffsets": "139,210,283,345,405,471,593,654,720,787"}, "to": {"startLines": "95,96,100,306,334,693,695,696,701,703", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "4424,4513,4725,18069,19569,42534,42710,42832,43094,43289", "endColumns": "88,70,72,61,59,65,121,60,65,66", "endOffsets": "4508,4579,4793,18126,19624,42595,42827,42888,43155,43351"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\476832c20a3d9dd64162d4134700dd9c\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "302,308", "startColumns": "4,4", "startOffsets": "17879,18184", "endColumns": "53,66", "endOffsets": "17928,18246"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\b3b7fa97e364d9ad8b2814831030b81d\\transformed\\navigation-runtime-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "307,707,930,933", "startColumns": "4,4,4,4", "startOffsets": "18131,43585,51508,51623", "endLines": "307,713,932,935", "endColumns": "52,24,24,24", "endOffsets": "18179,43884,51618,51733"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\398f61ea23c76807315a7831064b1215\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "337,418,419,420,421,422,423,424,425,426,427,430,431,432,433,434,435,436,437,438,439,440,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,502,512", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19785,25854,25942,26028,26109,26193,26262,26327,26410,26516,26602,26722,26776,26845,26906,26975,27064,27159,27233,27330,27423,27521,27670,27761,27849,27945,28043,28107,28175,28262,28356,28423,28495,28567,28668,28777,28853,28922,28970,29036,29100,29174,29231,29288,29360,29410,29464,29535,29606,29676,29745,29803,29879,29950,30024,30110,30160,30230,31630,32345", "endLines": "337,418,419,420,421,422,423,424,425,426,429,430,431,432,433,434,435,436,437,438,439,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,511,514", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "19853,25937,26023,26104,26188,26257,26322,26405,26511,26597,26717,26771,26840,26901,26970,27059,27154,27228,27325,27418,27516,27665,27756,27844,27940,28038,28102,28170,28257,28351,28418,28490,28562,28663,28772,28848,28917,28965,29031,29095,29169,29226,29283,29355,29405,29459,29530,29601,29671,29740,29798,29874,29945,30019,30105,30155,30225,30290,32340,32493"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\6a1e544bfd28dfa7114f03c4611100d0\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "331", "startColumns": "4", "startOffsets": "19412", "endColumns": "53", "endOffsets": "19461"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\6a7b738fe73569af25e9727733e76bdb\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "332", "startColumns": "4", "startOffsets": "19466", "endColumns": "49", "endOffsets": "19511"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\7235d196f56494969715e85d3652ab7a\\transformed\\coil-base-2.5.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "254", "startColumns": "4", "startOffsets": "15665", "endColumns": "49", "endOffsets": "15710"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\f2d994ae1eb1d6872e0205116c65da83\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "347", "startColumns": "4", "startOffsets": "20495", "endColumns": "82", "endOffsets": "20573"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\5dca3f3744b4fa46ea145e6d4fc9225c\\transformed\\media3-exoplayer-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "389,390,391,392,393,394,395,396,397", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "23770,23840,23902,23967,24031,24108,24173,24263,24347", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "23835,23897,23962,24026,24103,24168,24258,24342,24411"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\8bd6821415c472a8ee2b4482086533df\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "328", "startColumns": "4", "startOffsets": "19266", "endColumns": "42", "endOffsets": "19304"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\2c60f935d2795008c652ca71073c0e75\\transformed\\media3-ui-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,29,30,31,32,33,34,35,40,47,48,49,50,51,52,57,58,59,60,61,62,63,64,65,66,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,213,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,266,270,274,278,282,286,290,294,295,301,312,316,320,324,328,332,336,340,344,348,352,356,369,374,379,384,397,405,415,419,423,427,430,446,472,501", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,385,439,493,656,702,751,877,926,975,1034,1088,1140,1190,1255,1312,1359,1414,1562,1800,1849,1910,1970,2026,2086,2256,2316,2369,2426,2481,2537,2594,2643,2694,2753,3040,3105,3163,3212,3260,3311,3368,3425,3487,3554,3625,3697,3741,3798,3854,3917,3990,4060,4119,4176,4223,4278,4323,4372,4427,4481,4531,4582,4636,4695,4745,4803,4859,4912,4975,5040,5103,5155,5215,5279,5345,5403,5475,5536,5606,5676,5741,5806,5877,5972,6077,6180,6261,6344,6425,6514,6607,6700,6793,6878,6973,7066,7143,7235,7313,7393,7471,7557,7639,7732,7810,7901,7982,8071,8174,8275,8359,8455,8552,8647,8740,8832,8925,9018,9111,9194,9281,9376,9469,9550,9645,9738,9815,9859,9900,9945,9993,10037,10080,10129,10176,10220,10276,10329,10371,10418,10466,10526,10564,10614,10658,10708,10760,10798,10845,10892,10933,10972,11010,11054,11102,11144,11182,11224,11278,11325,11362,11411,11453,11494,11535,11577,11620,11658,11694,11772,11850,12147,12417,12499,12581,12723,12801,12888,12973,13040,13103,13195,13287,13352,13415,13477,13548,13658,13769,13879,13946,14026,14097,14164,14249,14334,14397,14485,14549,14691,14791,14839,14982,15045,15107,15172,15243,15301,15359,15425,15489,15555,15607,15669,15745,15821,15875,16154,16385,16595,16808,17018,17240,17456,17660,17698,18052,18839,19080,19320,19577,19830,20083,20318,20565,20804,21048,21269,21464,22136,22427,22723,23026,23692,24226,24700,24911,25111,25287,25395,25971,26916,27966", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,29,30,31,32,33,34,39,46,47,48,49,50,51,56,57,58,59,60,61,62,63,64,65,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,212,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,265,269,273,277,281,285,289,293,294,300,311,315,319,323,327,331,335,339,343,347,351,355,368,373,378,383,396,404,414,418,422,426,429,445,471,500,540", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,51,49,64,56,46,54,9,9,48,60,59,55,59,9,59,52,56,54,55,56,48,50,58,9,64,57,48,47,50,56,56,61,66,70,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,63,65,51,61,75,75,53,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22", "endOffsets": "330,380,434,488,651,697,746,872,921,970,1029,1083,1135,1185,1250,1307,1354,1409,1557,1795,1844,1905,1965,2021,2081,2251,2311,2364,2421,2476,2532,2589,2638,2689,2748,3035,3100,3158,3207,3255,3306,3363,3420,3482,3549,3620,3692,3736,3793,3849,3912,3985,4055,4114,4171,4218,4273,4318,4367,4422,4476,4526,4577,4631,4690,4740,4798,4854,4907,4970,5035,5098,5150,5210,5274,5340,5398,5470,5531,5601,5671,5736,5801,5872,5967,6072,6175,6256,6339,6420,6509,6602,6695,6788,6873,6968,7061,7138,7230,7308,7388,7466,7552,7634,7727,7805,7896,7977,8066,8169,8270,8354,8450,8547,8642,8735,8827,8920,9013,9106,9189,9276,9371,9464,9545,9640,9733,9810,9854,9895,9940,9988,10032,10075,10124,10171,10215,10271,10324,10366,10413,10461,10521,10559,10609,10653,10703,10755,10793,10840,10887,10928,10967,11005,11049,11097,11139,11177,11219,11273,11320,11357,11406,11448,11489,11530,11572,11615,11653,11689,11767,11845,12142,12412,12494,12576,12718,12796,12883,12968,13035,13098,13190,13282,13347,13410,13472,13543,13653,13764,13874,13941,14021,14092,14159,14244,14329,14392,14480,14544,14686,14786,14834,14977,15040,15102,15167,15238,15296,15354,15420,15484,15550,15602,15664,15740,15816,15870,16149,16380,16590,16803,17013,17235,17451,17655,17693,18047,18834,19075,19315,19572,19825,20078,20313,20560,20799,21043,21264,21459,22131,22422,22718,23021,23687,24221,24695,24906,25106,25282,25390,25966,26911,27961,29319"}, "to": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,29,30,31,33,34,35,37,42,49,50,51,52,53,54,59,60,61,62,63,64,65,66,67,68,75,76,77,78,79,85,86,87,88,89,90,91,92,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,335,336,339,343,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,515,520,524,528,532,536,540,544,548,549,555,566,570,574,578,582,586,590,594,598,602,606,610,623,628,633,638,651,659,669,673,677,746,765,876,941,970", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,430,484,538,701,747,796,922,971,1020,1079,1133,1185,1235,1360,1417,1464,1575,1723,1961,2010,2071,2131,2187,2247,2417,2477,2530,2587,2642,2698,2755,2804,2855,2914,3201,3266,3324,3373,3421,3805,3862,3919,3981,4048,4119,4191,4235,5418,5474,5537,5610,5680,5739,5796,5843,5898,5943,5992,6047,6101,6151,6202,6256,6315,6365,6423,6479,6532,6595,6660,6723,6775,6835,6899,6965,7023,7095,7156,7226,7296,7361,7426,8861,8956,9061,9164,9245,9328,9409,9498,9591,9684,9777,9862,9957,10050,10127,10219,10297,10377,10455,10541,10623,10716,10794,10885,10966,11055,11158,11259,11343,11439,11536,11631,11724,11816,11909,12002,12095,12178,12265,12360,12453,12534,12629,12722,15828,15872,15913,15958,16006,16050,16093,16142,16189,16233,16289,16342,16384,16431,16479,16539,16577,16627,16671,16721,16773,16811,16858,16905,16946,16985,17023,17067,17115,17157,17195,17237,17291,17338,17375,17424,17466,17507,17548,17590,17633,17671,19629,19707,19928,20225,21638,21720,21802,21944,22022,22109,22194,22261,22324,22416,22508,22573,22636,22698,22769,22879,22990,23100,23167,23247,23318,23385,23470,23555,23618,23706,24416,24558,24658,24706,24849,24912,24974,25039,25110,25168,25226,25292,25356,25422,25474,25536,25612,25688,32498,32777,33008,33218,33431,33641,33863,34079,34283,34321,34675,35462,35703,35943,36200,36453,36706,36941,37188,37427,37671,37892,38087,38759,39050,39346,39649,40315,40849,41323,41534,41734,44776,45388,49300,51920,52970", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,29,30,31,33,34,35,41,48,49,50,51,52,53,58,59,60,61,62,63,64,65,66,67,74,75,76,77,78,79,85,86,87,88,89,90,91,92,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,335,336,342,346,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,519,523,527,531,535,539,543,547,548,554,565,569,573,577,581,585,589,593,597,601,605,609,622,627,632,637,650,658,668,672,676,680,748,780,901,969,1009", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,51,49,64,56,46,54,9,9,48,60,59,55,59,9,59,52,56,54,55,56,48,50,58,9,64,57,48,47,50,56,56,61,66,70,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,63,65,51,61,75,75,53,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22", "endOffsets": "375,425,479,533,696,742,791,917,966,1015,1074,1128,1180,1230,1295,1412,1459,1514,1718,1956,2005,2066,2126,2182,2242,2412,2472,2525,2582,2637,2693,2750,2799,2850,2909,3196,3261,3319,3368,3416,3467,3857,3914,3976,4043,4114,4186,4230,4287,5469,5532,5605,5675,5734,5791,5838,5893,5938,5987,6042,6096,6146,6197,6251,6310,6360,6418,6474,6527,6590,6655,6718,6770,6830,6894,6960,7018,7090,7151,7221,7291,7356,7421,7492,8951,9056,9159,9240,9323,9404,9493,9586,9679,9772,9857,9952,10045,10122,10214,10292,10372,10450,10536,10618,10711,10789,10880,10961,11050,11153,11254,11338,11434,11531,11626,11719,11811,11904,11997,12090,12173,12260,12355,12448,12529,12624,12717,12794,15867,15908,15953,16001,16045,16088,16137,16184,16228,16284,16337,16379,16426,16474,16534,16572,16622,16666,16716,16768,16806,16853,16900,16941,16980,17018,17062,17110,17152,17190,17232,17286,17333,17370,17419,17461,17502,17543,17585,17628,17666,17702,19702,19780,20220,20490,21715,21797,21939,22017,22104,22189,22256,22319,22411,22503,22568,22631,22693,22764,22874,22985,23095,23162,23242,23313,23380,23465,23550,23613,23701,23765,24553,24653,24701,24844,24907,24969,25034,25105,25163,25221,25287,25351,25417,25469,25531,25607,25683,25737,32772,33003,33213,33426,33636,33858,34074,34278,34316,34670,35457,35698,35938,36195,36448,36701,36936,37183,37422,37666,37887,38082,38754,39045,39341,39644,40310,40844,41318,41529,41729,41905,44879,45959,50240,52965,54323"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\835843a1fca13b839a80c71cfb075e12\\transformed\\media3-session-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,235,328,423,520,625,724,800,874,942,1008,1084,1166,1253", "endColumns": "90,88,92,94,96,104,98,75,73,67,65,75,81,86,94", "endOffsets": "141,230,323,418,515,620,719,795,869,937,1003,1079,1161,1248,1343"}, "to": {"startLines": "211,212,213,214,215,216,217,349,360,479,480,481,482,483,484", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12799,12890,12979,13072,13167,13264,13369,20633,21405,30295,30363,30429,30505,30587,30674", "endColumns": "90,88,92,94,96,104,98,75,73,67,65,75,81,86,94", "endOffsets": "12885,12974,13067,13162,13259,13364,13463,20704,21474,30358,30424,30500,30582,30669,30764"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\e09d0326515e514bb831e61ed2892f09\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "329", "startColumns": "4", "startOffsets": "19309", "endColumns": "42", "endOffsets": "19347"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\31c73cb6dfb12faac49eb1bae4282a74\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "32,80,81,83,84,93,94,104,105,106,107,108,109,110,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,304,305,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,338,350,351,352,353,354,355,356,493,688,689,694,697,702,705,706,749,755,781,816,837,870", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1300,3472,3544,3674,3739,4292,4361,4930,5000,5068,5140,5210,5271,5345,7883,7944,8005,8067,8131,8193,8254,8322,8422,8482,8548,8621,8690,8747,8799,13468,13540,13616,13681,13740,13799,13859,13919,13979,14039,14099,14159,14219,14279,14339,14399,14458,14518,14578,14638,14698,14758,14818,14878,14938,14998,15058,15117,15177,15237,15296,15355,15414,15473,15532,17999,18034,18293,18348,18411,18466,18524,18580,18638,18699,18762,18819,18870,18928,18978,19039,19096,19162,19196,19231,19858,20709,20776,20848,20917,20986,21060,21132,31163,42216,42333,42600,42893,43160,43446,43518,44884,45087,45964,47770,48451,49133", "endLines": "32,80,81,83,84,93,94,104,105,106,107,108,109,110,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,304,305,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,338,350,351,352,353,354,355,356,493,688,692,694,700,702,705,706,754,764,815,836,869,875", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1355,3539,3627,3734,3800,4356,4419,4995,5063,5135,5205,5266,5340,5413,7939,8000,8062,8126,8188,8249,8317,8417,8477,8543,8616,8685,8742,8794,8856,13535,13611,13676,13735,13794,13854,13914,13974,14034,14094,14154,14214,14274,14334,14394,14453,14513,14573,14633,14693,14753,14813,14873,14933,14993,15053,15112,15172,15232,15291,15350,15409,15468,15527,15586,18029,18064,18343,18406,18461,18519,18575,18633,18694,18757,18814,18865,18923,18973,19034,19091,19157,19191,19226,19261,19923,20771,20843,20912,20981,21055,21127,21215,31229,42328,42529,42705,43089,43284,43513,43580,45082,45383,47765,48446,49128,49295"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\752167a3e67da85263f648420d161268\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "253,255,256,300,301,333,357,358,359,361,362,416,417,485,486,487,488,489,490,491,492,494,495,496,499,681,684", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15591,15715,15773,17773,17824,19516,21220,21285,21339,21479,21580,25742,25794,30769,30831,30885,30935,30989,31035,31081,31123,31234,31281,31317,31518,41910,42021", "endLines": "253,255,256,300,301,333,357,358,359,361,362,416,417,485,486,487,488,489,490,491,492,494,495,496,501,683,687", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "15660,15768,15823,17819,17874,19564,21280,21334,21400,21575,21633,25789,25849,30826,30880,30930,30984,31030,31076,31118,31158,31276,31312,31402,31625,42016,42211"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\120aba5530967f823682dff5e31591c9\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "714,730,736,1031,1047", "startColumns": "4,4,4,4,4", "startOffsets": "43889,44314,44492,55168,55579", "endLines": "729,735,745,1046,1050", "endColumns": "24,24,24,24,24", "endOffsets": "44309,44487,44771,55574,55701"}}]}]}