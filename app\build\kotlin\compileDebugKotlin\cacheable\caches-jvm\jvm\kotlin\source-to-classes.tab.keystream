I$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\MainActivity.kt_$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\analysis\IntelligentAnalysisEngine.ktN$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\auth\TokenManager.ktQ$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\camera\CameraManager.ktT$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\camera\StreamConnection.ktU$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\config\HuaweiCloudConfig.ktX$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\eeg\AdvancedFatigueDetector.ktX$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\eeg\AdvancedFocusCalculator.ktQ$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\eeg\DeviceEEGManager.ktT$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\eeg\EEGAssessmentEngine.ktK$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\eeg\EEGManager.ktS$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\eeg\EEGSignalProcessor.ktU$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.ktU$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\eeg\FocusCalculationDemo.kt\$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\fusion\RealTimeDataFusionEngine.ktZ$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\health\HealthAssessmentSystem.kt`$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\intelligence\LearningGuidanceEngine.ktX$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\iot\HuaweiCloudOkHttpClient.ktQ$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\iot\HuaweiIoTManager.ktN$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\iot\MqttIoTClient.ktP$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\iot\TokenIoTManager.ktS$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\live\HuaweiLiveManager.ktN$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\model\AppSettings.ktJ$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\model\EEGData.ktX$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\model\LearningGuidanceModel.kt[$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\model\LearningSupervisionModel.ktV$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\model\MultiModalDataModel.ktN$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\model\PostureData.ktO$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\model\UserSettings.ktJ$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\model\VISData.ktL$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\model\VideoData.ktL$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\model\VoiceData.ktU$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\navigation\AppNavigation.ktW$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\navigation\MediaNavigation.ktL$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\obs\MediaModels.ktJ$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\obs\ObsConfig.ktK$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\obs\ObsManager.ktV$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\player\AudioPlayerManager.ktV$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\player\VideoPlayerManager.kt\$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\recording\VideoRecordingManager.ktW$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\repository\MediaRepository.kt`$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\repository\MultiModalDataRepository.kt]$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\repository\PostureDataRepository.ktY$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\settings\UserSettingsManager.ktc$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\supervision\LearningSupervisionManager.kta$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\AdvancedFatigueDisplay.kt_$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\AdvancedFocusDisplay.ktg$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\ComprehensiveHealthDashboard.kt\$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\EEGBrainwaveChart.ktX$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\EEGFocusGauge.kt\$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\EEGMonitoringCard.kt^$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\EEGSpectrumAnalyzer.kt]$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\EEGWaveformDisplay.ktX$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\ErrorHandling.kt]$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\PostureDisplayCard.kt^$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\PostureHistoryChart.kt[$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\PostureScoreCard.kta$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\PostureVisualization3D.ktT$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\StatsCard.kt]$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\VISVideoPlayerCard.kt_$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\VideoPlayerComponent.kt[$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\VideoSurfaceView.kt_$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\VoiceInteractionCard.ktX$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\media\MediaBrowserScreen.kt[$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\media\MediaBrowserViewModel.ktY$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\media\MediaItemComponents.ktU$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\media\MediaListScreen.ktX$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\media\MediaListViewModel.kt^$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\media\MediaThumbnailComponents.ktS$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\obs\ObsConfigScreen.ktV$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\obs\ObsConfigViewModel.ktX$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\player\AudioPlayerScreen.kt[$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\player\AudioPlayerViewModel.ktX$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\player\VideoPlayerScreen.kt[$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\player\VideoPlayerViewModel.kt]$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\screen\AdvancedSettingsScreen.kt`$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\screen\LearningSupervisionScreen.ktQ$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\screen\MainScreen.ktU$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\screen\SettingsScreen.ktK$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\theme\Color.ktK$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\theme\Theme.ktJ$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\theme\Type.ktI$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\utils\Logger.ktQ$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\utils\MediaTestUtils.ktY$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\utils\PostureScoreCalculator.ktT$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\viewmodel\MainViewModel.ktQ$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\vis\HuaweiVISManager.ktV$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\vis\VoiceRecordingManager.kt[$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\vision\PostureDetectionManager.ktU$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\voice\DeviceVoiceManager.kt\$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\voice\SmartVoiceReminderManager.ktY$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\voice\VoiceCommandController.ktO$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\voice\VoiceManager.ktU$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\obs\ObsAutoConfigManager.kt^$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\obs\ParentVoiceInteractionManager.ktU$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\obs\RealTimeVideoManager.ktS$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\obs\HuaweiObsApiClient.ktQ$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\obs\ObsApiTestHelper.ktV$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\obs\RealTimeVideoVerifier.kth$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\RealTimeVideoVerificationCard.kt[$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\obs\VideoMonitoringDiagnostics.kte$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\ParentVoiceInteractionCard.ktW$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\test\VoiceModuleTestHelper.kt^$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\VoiceModuleTestCard.kt`$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\PermissionRequestCard.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                