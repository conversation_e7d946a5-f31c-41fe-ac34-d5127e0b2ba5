package com.example.iotandroidv20.ui.components

import androidx.compose.animation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.iotandroidv20.obs.InteractionStatus

/**
 * 家长语音交互卡片
 * 集成在实时视频模块下方，提供语音录制和发送功能
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ParentVoiceInteractionCard(
    isRecording: Boolean,
    interactionStatus: InteractionStatus,
    statusDescription: String,
    onStartRecording: () -> Unit,
    onStopRecording: () -> Unit,
    onCancelRecording: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = when (interactionStatus) {
                InteractionStatus.RECORDING -> Color(0xFFE3F2FD)
                InteractionStatus.SENDING -> Color(0xFFFFF3E0)
                InteractionStatus.SENT -> Color(0xFFE8F5E8)
                InteractionStatus.ERROR -> Color(0xFFFFEBEE)
                else -> MaterialTheme.colorScheme.surface
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "家长语音交互",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                // 状态指示器
                StatusIndicator(status = interactionStatus)
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 状态描述
            Text(
                text = statusDescription,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 录音控制区域
            RecordingControlArea(
                isRecording = isRecording,
                interactionStatus = interactionStatus,
                onStartRecording = onStartRecording,
                onStopRecording = onStopRecording,
                onCancelRecording = onCancelRecording
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 使用说明
            UsageInstructions()
        }
    }
}

/**
 * 状态指示器
 */
@Composable
private fun StatusIndicator(
    status: InteractionStatus,
    modifier: Modifier = Modifier
) {
    val (icon, color) = when (status) {
        InteractionStatus.IDLE -> Icons.Default.Mic to MaterialTheme.colorScheme.onSurface
        InteractionStatus.RECORDING -> Icons.Default.FiberManualRecord to Color.Red
        InteractionStatus.SENDING -> Icons.Default.CloudUpload to Color.Blue
        InteractionStatus.SENT -> Icons.Default.CheckCircle to Color.Green
        InteractionStatus.ERROR -> Icons.Default.Error to Color.Red
    }
    
    Icon(
        imageVector = icon,
        contentDescription = null,
        tint = color,
        modifier = modifier.size(24.dp)
    )
}

/**
 * 录音控制区域
 */
@Composable
private fun RecordingControlArea(
    isRecording: Boolean,
    interactionStatus: InteractionStatus,
    onStartRecording: () -> Unit,
    onStopRecording: () -> Unit,
    onCancelRecording: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (!isRecording && interactionStatus == InteractionStatus.IDLE) {
            // 开始录音按钮
            FloatingActionButton(
                onClick = onStartRecording,
                containerColor = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(64.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Mic,
                    contentDescription = "开始录音",
                    modifier = Modifier.size(32.dp)
                )
            }
        } else if (isRecording) {
            // 录音中的控制按钮
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 取消录音
                OutlinedButton(
                    onClick = onCancelRecording,
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = Color.Red
                    )
                ) {
                    Icon(
                        imageVector = Icons.Default.Cancel,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("取消")
                }
                
                // 录音动画指示器
                RecordingAnimationIndicator()
                
                // 完成录音
                Button(
                    onClick = onStopRecording,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.Green
                    )
                ) {
                    Icon(
                        imageVector = Icons.Default.Stop,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("完成")
                }
            }
        } else {
            // 其他状态（发送中、已发送、错误）
            Box(
                modifier = Modifier.size(64.dp),
                contentAlignment = Alignment.Center
            ) {
                when (interactionStatus) {
                    InteractionStatus.SENDING -> {
                        CircularProgressIndicator(
                            modifier = Modifier.size(32.dp),
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                    InteractionStatus.SENT -> {
                        Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = "发送成功",
                            tint = Color.Green,
                            modifier = Modifier.size(32.dp)
                        )
                    }
                    InteractionStatus.ERROR -> {
                        Icon(
                            imageVector = Icons.Default.Error,
                            contentDescription = "发送失败",
                            tint = Color.Red,
                            modifier = Modifier.size(32.dp)
                        )
                    }
                    else -> {
                        // 空状态
                    }
                }
            }
        }
    }
}

/**
 * 录音动画指示器
 */
@Composable
private fun RecordingAnimationIndicator() {
    var isVisible by remember { mutableStateOf(true) }
    
    LaunchedEffect(Unit) {
        while (true) {
            kotlinx.coroutines.delay(500)
            isVisible = !isVisible
        }
    }
    
    AnimatedVisibility(
        visible = isVisible,
        enter = fadeIn(),
        exit = fadeOut()
    ) {
        Box(
            modifier = Modifier
                .size(48.dp)
                .clip(CircleShape),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Default.FiberManualRecord,
                contentDescription = "录音中",
                tint = Color.Red,
                modifier = Modifier.size(24.dp)
            )
        }
    }
}

/**
 * 使用说明
 */
@Composable
private fun UsageInstructions() {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Text(
                text = "💡 使用说明",
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            val instructions = listOf(
                "点击麦克风按钮开始录制语音消息",
                "录制完成后点击"完成"按钮发送给设备",
                "语音消息将通过OBS云存储传输到设备端",
                "设备收到后会自动播放您的语音消息"
            )
            
            instructions.forEach { instruction ->
                Text(
                    text = "• $instruction",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(vertical = 1.dp)
                )
            }
        }
    }
}

/**
 * 语音交互状态卡片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VoiceInteractionStatusCard(
    lastSentTime: String,
    totalMessagesSent: Int,
    deviceResponseStatus: String,
    onRefreshStatus: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 4.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "语音交互状态",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium
                )
                
                IconButton(onClick = onRefreshStatus) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "刷新状态"
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "最后发送",
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = lastSentTime,
                        style = MaterialTheme.typography.bodySmall
                    )
                }
                
                Column {
                    Text(
                        text = "总消息数",
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = totalMessagesSent.toString(),
                        style = MaterialTheme.typography.bodySmall
                    )
                }
                
                Column {
                    Text(
                        text = "设备响应",
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = deviceResponseStatus,
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
        }
    }
}
