package com.example.iotandroidv20.database

import android.content.Context
import androidx.room.*
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.example.iotandroidv20.database.dao.DailyStatsDao
import com.example.iotandroidv20.database.dao.DeviceStatusDao
import com.example.iotandroidv20.database.dao.PostureDataDao
import com.example.iotandroidv20.database.entity.DailyStatsEntity
import com.example.iotandroidv20.database.entity.DeviceStatusHistoryEntity
import com.example.iotandroidv20.database.entity.PostureDataEntity
import com.example.iotandroidv20.model.MultiModalDataSnapshot

/**
 * 坐姿监控应用数据库
 */
@Database(
    entities = [
        PostureDataEntity::class,
        DailyStatsEntity::class,
        DeviceStatusHistoryEntity::class
        // MultiModalDataSnapshot::class // 暂时注释掉以避免迁移问题
    ],
    version = 1, // 暂时回退到版本1
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class PostureDatabase : RoomDatabase() {
    
    abstract fun postureDataDao(): PostureDataDao
    abstract fun dailyStatsDao(): DailyStatsDao
    abstract fun deviceStatusDao(): DeviceStatusDao
    // abstract fun multiModalDataDao(): MultiModalDataDao // 暂时注释掉
    
    companion object {
        @Volatile
        private var INSTANCE: PostureDatabase? = null
        
        private const val DATABASE_NAME = "posture_database"
        
        fun getDatabase(context: Context): PostureDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    PostureDatabase::class.java,
                    DATABASE_NAME
                )
                    .addCallback(DatabaseCallback())
                    // .addMigrations(MIGRATION_1_2) // 暂时注释掉
                    .fallbackToDestructiveMigration() // 开发阶段使用，生产环境应该移除
                    .build()
                INSTANCE = instance
                instance
            }
        }
        
        /**
         * 数据库创建回调
         */
        private class DatabaseCallback : RoomDatabase.Callback() {
            override fun onCreate(db: SupportSQLiteDatabase) {
                super.onCreate(db)
                // 可以在这里执行初始化数据插入
            }
            
            override fun onOpen(db: SupportSQLiteDatabase) {
                super.onOpen(db)
                // 数据库打开时的操作
            }
        }
        
        /**
         * 数据库迁移（从版本1到版本2）- 添加多模态数据表
         */
        private val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 创建多模态数据表
                database.execSQL("""
                    CREATE TABLE IF NOT EXISTS `multimodal_data` (
                        `id` TEXT NOT NULL,
                        `timestamp` INTEGER NOT NULL,
                        `sessionId` TEXT NOT NULL,
                        `postureData` TEXT,
                        `postureScore` REAL NOT NULL,
                        `postureRisk` TEXT NOT NULL,
                        `eegData` TEXT,
                        `focusScore` REAL NOT NULL,
                        `fatigueScore` REAL NOT NULL,
                        `alertnessLevel` REAL NOT NULL,
                        `videoData` TEXT,
                        `engagementScore` REAL NOT NULL,
                        `visualAttentionScore` REAL NOT NULL,
                        `voiceData` TEXT,
                        `voiceActivityScore` REAL NOT NULL,
                        `emotionalState` TEXT NOT NULL,
                        `environmentData` TEXT,
                        `environmentScore` REAL NOT NULL,
                        `fusionResult` TEXT NOT NULL,
                        `overallHealthScore` REAL NOT NULL,
                        `riskLevel` TEXT NOT NULL,
                        `recommendations` TEXT NOT NULL,
                        `dataQuality` TEXT NOT NULL,
                        `confidence` REAL NOT NULL,
                        `processingTime` INTEGER NOT NULL,
                        PRIMARY KEY(`id`)
                    )
                """.trimIndent())
            }
        }
        
        /**
         * 清空数据库实例（用于测试）
         */
        fun clearInstance() {
            INSTANCE = null
        }
    }
}

/**
 * 类型转换器
 */
class Converters {
    
    /**
     * 将Map转换为JSON字符串
     */
    @TypeConverter
    fun fromMap(value: Map<String, Any>?): String {
        return if (value == null) {
            "{}"
        } else {
            try {
                val gson = com.google.gson.Gson()
                gson.toJson(value)
            } catch (e: Exception) {
                "{}"
            }
        }
    }
    
    /**
     * 将JSON字符串转换为Map
     */
    @TypeConverter
    fun toMap(value: String): Map<String, Any> {
        return try {
            val gson = com.google.gson.Gson()
            gson.fromJson(value, Map::class.java) as? Map<String, Any> ?: emptyMap()
        } catch (e: Exception) {
            emptyMap()
        }
    }
    
    /**
     * 将List转换为JSON字符串
     */
    @TypeConverter
    fun fromList(value: List<String>?): String {
        return if (value == null) {
            "[]"
        } else {
            try {
                val gson = com.google.gson.Gson()
                gson.toJson(value)
            } catch (e: Exception) {
                "[]"
            }
        }
    }
    
    /**
     * 将JSON字符串转换为List
     */
    @TypeConverter
    fun toList(value: String): List<String> {
        return try {
            val gson = com.google.gson.Gson()
            gson.fromJson(value, Array<String>::class.java)?.toList() ?: emptyList()
        } catch (e: Exception) {
            emptyList()
        }
    }
}
