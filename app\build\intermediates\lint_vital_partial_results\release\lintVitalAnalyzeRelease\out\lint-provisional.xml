<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.0" type="conditional_incidents">

    <incident
        id="DuplicatePlatformClasses"
        severity="fatal"
        message="`httpclient` defines classes that conflict with classes now provided by Android. Solutions include finding newer versions or alternative libraries that don&apos;t have the same problem (for example, for `httpclient` use `HttpUrlConnection` or `okhttp` instead), or repackaging the library using something like `jarjar`.">
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="76"
            column="21"
            startOffset="2182"
            endLine="76"
            endColumn="64"
            endOffset="2225"/>
    </incident>

    <incident
        id="DuplicatePlatformClasses"
        severity="fatal"
        message="`commons-logging` defines classes that conflict with classes now provided by Android. Solutions include finding newer versions or alternative libraries that don&apos;t have the same problem (for example, for `httpclient` use `HttpUrlConnection` or `okhttp` instead), or repackaging the library using something like `jarjar`.">
        <fix-replace
            description="Delete dependency"
            replacement=""
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="84"
            column="5"
            startOffset="2519"
            endLine="84"
            endColumn="58"
            endOffset="2572"/>
    </incident>

</incidents>
