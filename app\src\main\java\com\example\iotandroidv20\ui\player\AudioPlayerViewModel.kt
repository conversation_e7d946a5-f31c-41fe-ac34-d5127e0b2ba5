package com.example.iotandroidv20.ui.player

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.media3.exoplayer.ExoPlayer
import com.example.iotandroidv20.obs.AudioSession
import com.example.iotandroidv20.obs.PlayerInfo
import com.example.iotandroidv20.player.AudioPlayerManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 音频播放器界面的ViewModel
 */
class AudioPlayerViewModel : ViewModel() {
    
    private var audioPlayerManager: AudioPlayerManager? = null
    
    private val _uiState = MutableStateFlow(AudioPlayerUiState())
    val uiState: StateFlow<AudioPlayerUiState> = _uiState.asStateFlow()
    
    /**
     * 初始化播放器
     */
    fun initialize(context: Context, audioSession: AudioSession) {
        try {
            // 创建播放器管理器
            audioPlayerManager = AudioPlayerManager(context).apply {
                // 添加播放器事件监听器
                addPlayerListener(object : AudioPlayerManager.AudioPlayerEventListener {
                    override fun onPlayerError(error: Exception) {
                        _uiState.value = _uiState.value.copy(
                            error = error.message ?: "播放出错"
                        )
                    }
                    
                    override fun onTrackChanged(audioSession: AudioSession) {
                        _uiState.value = _uiState.value.copy(
                            currentAudioSession = audioSession
                        )
                    }
                    
                    override fun onPlaylistChanged(playlist: List<AudioSession>) {
                        _uiState.value = _uiState.value.copy(
                            playlist = playlist
                        )
                    }
                })
                
                // 初始化播放器
                initializePlayer()
                
                // 开始播放音频
                playAudioSession(audioSession)
            }
            
            // 监听播放器信息变化
            viewModelScope.launch {
                audioPlayerManager?.playerInfo?.collect { playerInfo ->
                    _uiState.value = _uiState.value.copy(playerInfo = playerInfo)
                }
            }
            
            // 监听播放列表变化
            viewModelScope.launch {
                audioPlayerManager?.playlist?.collect { playlist ->
                    _uiState.value = _uiState.value.copy(playlist = playlist)
                }
            }
            
            // 监听当前索引变化
            viewModelScope.launch {
                audioPlayerManager?.currentIndex?.collect { index ->
                    _uiState.value = _uiState.value.copy(currentIndex = index)
                }
            }
            
            // 监听重复模式变化
            viewModelScope.launch {
                audioPlayerManager?.repeatMode?.collect { mode ->
                    _uiState.value = _uiState.value.copy(repeatMode = mode)
                }
            }
            
            // 监听随机模式变化
            viewModelScope.launch {
                audioPlayerManager?.shuffleMode?.collect { shuffle ->
                    _uiState.value = _uiState.value.copy(shuffleMode = shuffle)
                }
            }
            
            // 设置当前音频会话
            _uiState.value = _uiState.value.copy(currentAudioSession = audioSession)
            
        } catch (e: Exception) {
            _uiState.value = _uiState.value.copy(
                error = "初始化播放器失败: ${e.message}"
            )
        }
    }
    
    /**
     * 设置播放列表
     */
    fun setPlaylist(audioSessions: List<AudioSession>, startIndex: Int = 0) {
        audioPlayerManager?.setPlaylist(audioSessions, startIndex)
    }
    
    /**
     * 获取ExoPlayer实例
     */
    fun getExoPlayer(): ExoPlayer? {
        return audioPlayerManager?.getExoPlayer()
    }
    
    /**
     * 播放/暂停切换
     */
    fun togglePlayPause() {
        audioPlayerManager?.togglePlayPause()
    }
    
    /**
     * 上一首
     */
    fun skipToPrevious() {
        audioPlayerManager?.skipToPrevious()
    }
    
    /**
     * 下一首
     */
    fun skipToNext() {
        audioPlayerManager?.skipToNext()
    }
    
    /**
     * 跳转到指定位置
     */
    fun seekTo(positionMs: Long) {
        audioPlayerManager?.seekTo(positionMs)
    }
    
    /**
     * 跳转到指定曲目
     */
    fun seekToTrack(index: Int) {
        audioPlayerManager?.seekToTrack(index)
    }
    
    /**
     * 设置重复模式
     */
    fun setRepeatMode(mode: AudioPlayerManager.RepeatMode) {
        audioPlayerManager?.setRepeatMode(mode)
    }
    
    /**
     * 设置随机播放
     */
    fun setShuffleMode(enabled: Boolean) {
        audioPlayerManager?.setShuffleMode(enabled)
    }
    
    /**
     * 设置播放速度
     */
    fun setPlaybackSpeed(speed: Float) {
        audioPlayerManager?.setPlaybackSpeed(speed)
    }
    
    /**
     * 设置音量
     */
    fun setVolume(volume: Float) {
        audioPlayerManager?.setVolume(volume)
    }
    
    /**
     * 重试播放
     */
    fun retry() {
        _uiState.value = _uiState.value.copy(error = "")
        _uiState.value.currentAudioSession?.let { audioSession ->
            audioPlayerManager?.playAudioSession(audioSession)
        }
    }
    
    /**
     * 释放播放器资源
     */
    fun releasePlayer() {
        try {
            audioPlayerManager?.releasePlayer()
            audioPlayerManager = null
            
            _uiState.value = AudioPlayerUiState()
            
        } catch (e: Exception) {
            // 忽略释放时的错误
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        releasePlayer()
    }
}

/**
 * 音频播放器界面的UI状态
 */
data class AudioPlayerUiState(
    val playerInfo: PlayerInfo = PlayerInfo(
        state = com.example.iotandroidv20.obs.PlaybackState.IDLE,
        currentPosition = 0L,
        duration = 0L,
        bufferedPosition = 0L
    ),
    val currentAudioSession: AudioSession? = null,
    val playlist: List<AudioSession> = emptyList(),
    val currentIndex: Int = 0,
    val repeatMode: AudioPlayerManager.RepeatMode = AudioPlayerManager.RepeatMode.OFF,
    val shuffleMode: Boolean = false,
    val error: String = ""
)
