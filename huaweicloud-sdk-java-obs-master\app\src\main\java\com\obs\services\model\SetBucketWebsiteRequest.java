/**
 * Copyright 2019 Huawei Technologies Co.,Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License.  You may obtain a copy of the
 * License at
 * 
 * http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software distributed
 * under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
 * CONDITIONS OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

package com.obs.services.model;

/**
 * Configure the static website hosting of a bucket.
 * 
 * @since 3.20.3
 *
 */
public class SetBucketWebsiteRequest extends BaseBucketRequest {

    {
        httpMethod = HttpMethodEnum.PUT;
    }

    private WebsiteConfiguration websiteConfig;

    public SetBucketWebsiteRequest(String bucketName, WebsiteConfiguration websiteConfig) {
        this.bucketName = bucketName;
        this.websiteConfig = websiteConfig;
    }

    public WebsiteConfiguration getWebsiteConfig() {
        return websiteConfig;
    }

    public void setWebsiteConfig(WebsiteConfiguration websiteConfig) {
        this.websiteConfig = websiteConfig;
    }

    @Override
    public String toString() {
        return "SetBucketWebsiteRequest [websiteConfig=" + websiteConfig + ", getBucketName()=" + getBucketName()
                + ", isRequesterPays()=" + isRequesterPays() + "]";
    }
}
