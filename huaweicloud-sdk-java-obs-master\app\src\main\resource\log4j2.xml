<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <Appenders>
    	<!-- console appender -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout charset="UTF-8" pattern="%d{yyyy-MM-dd HH:mm:ss SSS}|%t|%-5p|%m%n" />
        </Console>
		
		<!-- north interface log -->
        <RollingFile name="NorthInterfaceLogAppender" fileName="${sys:user.dir}/logs/OBS-SDK.interface_north.log"
                     filePattern="${sys:user.dir}/logs/OBS-SDK.interface_north-%i.log" filePermissions="rw-------">
            <PatternLayout charset="UTF-8" pattern="%d{yyyy-MM-dd HH:mm:ss SSS}|%t|%-5p|%m%n" />
            <SizeBasedTriggeringPolicy size="20M" />
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>
        
        <!-- south interface log -->
        <RollingFile name="SouthInterfaceLogAppender" fileName="${sys:user.dir}/logs/OBS-SDK.interface_south.log"
                     filePattern="${sys:user.dir}/logs/OBS-SDK.interface_south-%i.log" filePermissions="rw-------">
             <PatternLayout charset="UTF-8" pattern="%d{yyyy-MM-dd HH:mm:ss SSS}|%t|%-5p|%m%n" />
            <SizeBasedTriggeringPolicy size="20M" />
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <!-- access log -->
        <RollingFile name="AccessLogAppender" fileName="${sys:user.dir}/logs/OBS-SDK.access.log"
                     filePattern="${sys:user.dir}/logs/OBS-SDK.access-%i.log" filePermissions="rw-------">
            <PatternLayout charset="UTF-8" pattern="%t|%-5p|%m%n" />
            <SizeBasedTriggeringPolicy size="20M" />
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>
        
    </Appenders>

    <Loggers>
    	<!-- north log -->
        <Logger name="com.obs.services.AbstractClient" level="WARN"
                additivity="false">
            <AppenderRef ref="NorthInterfaceLogAppender" />
        </Logger>
        
        <!-- south log -->
        <Logger name="com.obs.services.internal.RestStorageService" level="WARN"
                additivity="false">
            <AppenderRef ref="SouthInterfaceLogAppender" />
        </Logger>

        <!-- access log -->
        <Logger name="com.obs.log.AccessLogger" level="WARN"
                additivity="false">
            <AppenderRef ref="AccessLogAppender" />
        </Logger>
        
        <!-- console log -->
        <Root level="WARN">
            <AppenderRef ref="Console" />
        </Root>
    </Loggers>
</Configuration>