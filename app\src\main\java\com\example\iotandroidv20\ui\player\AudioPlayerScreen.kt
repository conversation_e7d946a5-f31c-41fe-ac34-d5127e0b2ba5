package com.example.iotandroidv20.ui.player

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.iotandroidv20.obs.AudioSession
import com.example.iotandroidv20.obs.PlaybackState
import com.example.iotandroidv20.player.AudioPlayerManager

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AudioPlayerScreen(
    audioSession: AudioSession,
    onNavigateBack: () -> Unit,
    viewModel: AudioPlayerViewModel = viewModel()
) {
    val context = LocalContext.current
    val uiState by viewModel.uiState.collectAsState()
    
    // 初始化播放器
    LaunchedEffect(audioSession) {
        viewModel.initialize(context, audioSession)
    }
    
    // 清理资源
    DisposableEffect(Unit) {
        onDispose {
            viewModel.releasePlayer()
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("音频播放器") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(MaterialTheme.colorScheme.background)
        ) {
            // 主播放器区域
            AudioPlayerMainArea(
                modifier = Modifier.weight(1f),
                currentAudioSession = uiState.currentAudioSession,
                playerInfo = uiState.playerInfo,
                error = uiState.error,
                onRetry = viewModel::retry
            )
            
            // 播放控制器
            AudioPlayerControls(
                playerInfo = uiState.playerInfo,
                repeatMode = uiState.repeatMode,
                shuffleMode = uiState.shuffleMode,
                onPlayPauseClick = viewModel::togglePlayPause,
                onPreviousClick = viewModel::skipToPrevious,
                onNextClick = viewModel::skipToNext,
                onSeekTo = viewModel::seekTo,
                onRepeatModeChange = viewModel::setRepeatMode,
                onShuffleModeChange = viewModel::setShuffleMode,
                onSpeedChange = viewModel::setPlaybackSpeed,
                onVolumeChange = viewModel::setVolume
            )
            
            // 播放列表（如果有多首）
            if (uiState.playlist.size > 1) {
                AudioPlaylist(
                    playlist = uiState.playlist,
                    currentIndex = uiState.currentIndex,
                    onTrackClick = viewModel::seekToTrack,
                    modifier = Modifier.height(200.dp)
                )
            }
        }
    }
}

@Composable
private fun AudioPlayerMainArea(
    modifier: Modifier = Modifier,
    currentAudioSession: AudioSession?,
    playerInfo: com.example.iotandroidv20.obs.PlayerInfo,
    error: String,
    onRetry: () -> Unit
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        when {
            error.isNotEmpty() -> {
                // 错误状态
                Card(
                    modifier = Modifier.padding(32.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(24.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            Icons.Default.Error,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.onErrorContainer,
                            modifier = Modifier.size(48.dp)
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "播放出错",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = error,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onErrorContainer,
                            textAlign = TextAlign.Center
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(
                            onClick = onRetry,
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.colorScheme.error
                            )
                        ) {
                            Text("重试")
                        }
                    }
                }
            }
            
            playerInfo.state == PlaybackState.PREPARING -> {
                // 加载状态
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    CircularProgressIndicator()
                    Spacer(modifier = Modifier.height(16.dp))
                    Text("加载中...")
                }
            }
            
            currentAudioSession != null -> {
                // 正常播放状态
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.padding(32.dp)
                ) {
                    // 音频图标
                    Card(
                        modifier = Modifier.size(200.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.primaryContainer
                        )
                    ) {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                Icons.Default.AudioFile,
                                contentDescription = null,
                                modifier = Modifier.size(80.dp),
                                tint = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(24.dp))
                    
                    // 音频信息
                    Text(
                        text = currentAudioSession.sessionId,
                        style = MaterialTheme.typography.headlineSmall,
                        textAlign = TextAlign.Center,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = "${currentAudioSession.getQualityDescription()} • ${currentAudioSession.getChannelDescription()}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        textAlign = TextAlign.Center
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 音频属性
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        AudioPropertyChip(
                            icon = Icons.Default.Storage,
                            text = currentAudioSession.getFormattedFileSize()
                        )
                        
                        AudioPropertyChip(
                            icon = Icons.Default.GraphicEq,
                            text = "${currentAudioSession.sampleRate}Hz"
                        )
                        
                        if (currentAudioSession.duration > 0) {
                            AudioPropertyChip(
                                icon = Icons.Default.Schedule,
                                text = currentAudioSession.getFormattedDuration()
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun AudioPropertyChip(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    text: String
) {
    AssistChip(
        onClick = { },
        label = { Text(text) },
        leadingIcon = {
            Icon(
                icon,
                contentDescription = null,
                modifier = Modifier.size(16.dp)
            )
        }
    )
}

@Composable
private fun AudioPlayerControls(
    playerInfo: com.example.iotandroidv20.obs.PlayerInfo,
    repeatMode: AudioPlayerManager.RepeatMode,
    shuffleMode: Boolean,
    onPlayPauseClick: () -> Unit,
    onPreviousClick: () -> Unit,
    onNextClick: () -> Unit,
    onSeekTo: (Long) -> Unit,
    onRepeatModeChange: (AudioPlayerManager.RepeatMode) -> Unit,
    onShuffleModeChange: (Boolean) -> Unit,
    onSpeedChange: (Float) -> Unit,
    onVolumeChange: (Float) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 进度条
            AudioProgressBar(
                currentPosition = playerInfo.currentPosition,
                duration = playerInfo.duration,
                onSeekTo = onSeekTo
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 主要控制按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 上一首
                IconButton(onClick = onPreviousClick) {
                    Icon(Icons.Default.SkipPrevious, contentDescription = "上一首")
                }
                
                // 播放/暂停
                IconButton(
                    onClick = onPlayPauseClick,
                    modifier = Modifier.size(64.dp)
                ) {
                    Icon(
                        imageVector = if (playerInfo.state == PlaybackState.PLAYING) {
                            Icons.Default.Pause
                        } else {
                            Icons.Default.PlayArrow
                        },
                        contentDescription = if (playerInfo.state == PlaybackState.PLAYING) "暂停" else "播放",
                        modifier = Modifier.size(32.dp)
                    )
                }
                
                // 下一首
                IconButton(onClick = onNextClick) {
                    Icon(Icons.Default.SkipNext, contentDescription = "下一首")
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 辅助控制按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 随机播放
                IconButton(
                    onClick = { onShuffleModeChange(!shuffleMode) }
                ) {
                    Icon(
                        Icons.Default.Shuffle,
                        contentDescription = "随机播放",
                        tint = if (shuffleMode) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                // 重复模式
                IconButton(
                    onClick = {
                        val nextMode = when (repeatMode) {
                            AudioPlayerManager.RepeatMode.OFF -> AudioPlayerManager.RepeatMode.ALL
                            AudioPlayerManager.RepeatMode.ALL -> AudioPlayerManager.RepeatMode.ONE
                            AudioPlayerManager.RepeatMode.ONE -> AudioPlayerManager.RepeatMode.OFF
                        }
                        onRepeatModeChange(nextMode)
                    }
                ) {
                    val (icon, tint) = when (repeatMode) {
                        AudioPlayerManager.RepeatMode.OFF -> Icons.Default.Repeat to MaterialTheme.colorScheme.onSurfaceVariant
                        AudioPlayerManager.RepeatMode.ALL -> Icons.Default.Repeat to MaterialTheme.colorScheme.primary
                        AudioPlayerManager.RepeatMode.ONE -> Icons.Default.RepeatOne to MaterialTheme.colorScheme.primary
                    }
                    Icon(icon, contentDescription = "重复模式", tint = tint)
                }
                
                // 播放速度 - 暂时注释掉
                /*
                SpeedControlButton(
                    currentSpeed = playerInfo.playbackSpeed,
                    onSpeedChange = onSpeedChange
                )
                */

                // 音量控制 - 暂时注释掉
                /*
                VolumeControlButton(
                    currentVolume = playerInfo.volume,
                    onVolumeChange = onVolumeChange
                )
                */
            }
        }
    }
}

@Composable
private fun AudioProgressBar(
    currentPosition: Long,
    duration: Long,
    onSeekTo: (Long) -> Unit
) {
    Column {
        // 时间显示
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = formatTime(currentPosition),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = formatTime(duration),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 进度条
        if (duration > 0) {
            var isDragging by remember { mutableStateOf(false) }
            var dragPosition by remember { mutableStateOf(currentPosition) }
            
            Slider(
                value = if (isDragging) dragPosition.toFloat() else currentPosition.toFloat(),
                onValueChange = { value ->
                    isDragging = true
                    dragPosition = value.toLong()
                },
                onValueChangeFinished = {
                    onSeekTo(dragPosition)
                    isDragging = false
                },
                valueRange = 0f..duration.toFloat(),
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Composable
private fun AudioPlaylist(
    playlist: List<AudioSession>,
    currentIndex: Int,
    onTrackClick: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
    ) {
        Column {
            Text(
                text = "播放列表",
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.padding(16.dp)
            )
            
            LazyColumn {
                itemsIndexed(playlist) { index, audioSession ->
                    AudioPlaylistItem(
                        audioSession = audioSession,
                        isCurrentTrack = index == currentIndex,
                        onClick = { onTrackClick(index) }
                    )
                }
            }
        }
    }
}

@Composable
private fun AudioPlaylistItem(
    audioSession: AudioSession,
    isCurrentTrack: Boolean,
    onClick: () -> Unit
) {
    ListItem(
        headlineContent = {
            Text(
                text = audioSession.sessionId,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                color = if (isCurrentTrack) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurface
            )
        },
        supportingContent = {
            Text(
                text = "${audioSession.getFormattedDuration()} • ${audioSession.getFormattedFileSize()}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        },
        leadingContent = {
            Icon(
                if (isCurrentTrack) Icons.Default.PlayArrow else Icons.Default.AudioFile,
                contentDescription = null,
                tint = if (isCurrentTrack) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurfaceVariant
            )
        },
        modifier = Modifier.clickable { onClick() }
    )
}

private fun formatTime(timeMs: Long): String {
    val totalSeconds = timeMs / 1000
    val hours = totalSeconds / 3600
    val minutes = (totalSeconds % 3600) / 60
    val seconds = totalSeconds % 60
    
    return if (hours > 0) {
        String.format("%02d:%02d:%02d", hours, minutes, seconds)
    } else {
        String.format("%02d:%02d", minutes, seconds)
    }
}
