package com.example.iotandroidv20.ui.media

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.iotandroidv20.obs.AudioSession
import com.example.iotandroidv20.obs.DownloadStatus
import com.example.iotandroidv20.obs.VideoSession
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MediaListScreen(
    onNavigateBack: () -> Unit,
    onVideoClick: (VideoSession) -> Unit,
    onAudioClick: (AudioSession) -> Unit,
    viewModel: MediaListViewModel = viewModel()
) {
    val context = LocalContext.current
    val uiState by viewModel.uiState.collectAsState()
    
    // 初始化
    LaunchedEffect(Unit) {
        viewModel.initialize(context)
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("媒体文件") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    // 刷新按钮
                    IconButton(
                        onClick = { viewModel.refreshData() },
                        enabled = !uiState.isLoading
                    ) {
                        Icon(Icons.Default.Refresh, contentDescription = "刷新")
                    }
                    
                    // 设置按钮
                    IconButton(onClick = { viewModel.showSettings() }) {
                        Icon(Icons.Default.Settings, contentDescription = "设置")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 设备和日期选择器
            DeviceDateSelector(
                selectedDevice = uiState.selectedDevice,
                selectedDate = uiState.selectedDate,
                availableDevices = uiState.availableDevices,
                availableDates = uiState.availableDates,
                onDeviceSelected = viewModel::selectDevice,
                onDateSelected = viewModel::selectDate,
                isLoading = uiState.isLoading
            )
            
            // 媒体类型标签页
            MediaTypeTabs(
                selectedTab = uiState.selectedMediaType,
                onTabSelected = viewModel::selectMediaType
            )
            
            // 内容区域
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .weight(1f)
            ) {
                when {
                    uiState.isLoading -> {
                        // 加载状态
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                CircularProgressIndicator()
                                Spacer(modifier = Modifier.height(16.dp))
                                Text("加载中...")
                            }
                        }
                    }
                    
                    uiState.error.isNotEmpty() -> {
                        // 错误状态
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Icon(
                                    Icons.Default.Error,
                                    contentDescription = null,
                                    tint = MaterialTheme.colorScheme.error,
                                    modifier = Modifier.size(48.dp)
                                )
                                Spacer(modifier = Modifier.height(16.dp))
                                Text(
                                    text = uiState.error,
                                    color = MaterialTheme.colorScheme.error
                                )
                                Spacer(modifier = Modifier.height(16.dp))
                                Button(onClick = { viewModel.refreshData() }) {
                                    Text("重试")
                                }
                            }
                        }
                    }
                    
                    uiState.selectedMediaType == MediaType.VIDEO -> {
                        // 视频列表
                        if (uiState.videoSessions.isEmpty()) {
                            EmptyStateView(
                                icon = Icons.Default.VideoLibrary,
                                message = "暂无视频文件",
                                actionText = "刷新",
                                onAction = { viewModel.refreshData() }
                            )
                        } else {
                            VideoSessionList(
                                sessions = uiState.videoSessions,
                                onSessionClick = onVideoClick,
                                onDownloadClick = { viewModel.downloadVideo(context, it) },
                                downloadProgress = uiState.downloadProgress
                            )
                        }
                    }
                    
                    uiState.selectedMediaType == MediaType.AUDIO -> {
                        // 音频列表
                        if (uiState.audioSessions.isEmpty()) {
                            EmptyStateView(
                                icon = Icons.Default.AudioFile,
                                message = "暂无音频文件",
                                actionText = "刷新",
                                onAction = { viewModel.refreshData() }
                            )
                        } else {
                            AudioSessionList(
                                sessions = uiState.audioSessions,
                                onSessionClick = onAudioClick,
                                onDownloadClick = { viewModel.downloadAudio(context, it) },
                                downloadProgress = uiState.downloadProgress
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun DeviceDateSelector(
    selectedDevice: String,
    selectedDate: String,
    availableDevices: List<String>,
    availableDates: List<String>,
    onDeviceSelected: (String) -> Unit,
    onDateSelected: (String) -> Unit,
    isLoading: Boolean
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "选择设备和日期",
                style = MaterialTheme.typography.titleSmall
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 设备选择器
                var deviceExpanded by remember { mutableStateOf(false) }
                ExposedDropdownMenuBox(
                    expanded = deviceExpanded,
                    onExpandedChange = { deviceExpanded = !deviceExpanded },
                    modifier = Modifier.weight(1f)
                ) {
                    OutlinedTextField(
                        value = selectedDevice,
                        onValueChange = {},
                        readOnly = true,
                        label = { Text("设备") },
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = deviceExpanded) },
                        modifier = Modifier
                            .menuAnchor()
                            .fillMaxWidth(),
                        enabled = !isLoading
                    )
                    
                    ExposedDropdownMenu(
                        expanded = deviceExpanded,
                        onDismissRequest = { deviceExpanded = false }
                    ) {
                        availableDevices.forEach { device ->
                            DropdownMenuItem(
                                text = { Text(device) },
                                onClick = {
                                    onDeviceSelected(device)
                                    deviceExpanded = false
                                }
                            )
                        }
                    }
                }
                
                // 日期选择器
                var dateExpanded by remember { mutableStateOf(false) }
                ExposedDropdownMenuBox(
                    expanded = dateExpanded,
                    onExpandedChange = { dateExpanded = !dateExpanded },
                    modifier = Modifier.weight(1f)
                ) {
                    OutlinedTextField(
                        value = formatDate(selectedDate),
                        onValueChange = {},
                        readOnly = true,
                        label = { Text("日期") },
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = dateExpanded) },
                        modifier = Modifier
                            .menuAnchor()
                            .fillMaxWidth(),
                        enabled = !isLoading
                    )
                    
                    ExposedDropdownMenu(
                        expanded = dateExpanded,
                        onDismissRequest = { dateExpanded = false }
                    ) {
                        availableDates.forEach { date ->
                            DropdownMenuItem(
                                text = { Text(formatDate(date)) },
                                onClick = {
                                    onDateSelected(date)
                                    dateExpanded = false
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun MediaTypeTabs(
    selectedTab: MediaType,
    onTabSelected: (MediaType) -> Unit
) {
    TabRow(
        selectedTabIndex = selectedTab.ordinal,
        modifier = Modifier.fillMaxWidth()
    ) {
        Tab(
            selected = selectedTab == MediaType.VIDEO,
            onClick = { onTabSelected(MediaType.VIDEO) },
            text = { Text("视频") },
            icon = { Icon(Icons.Default.VideoLibrary, contentDescription = null) }
        )
        
        Tab(
            selected = selectedTab == MediaType.AUDIO,
            onClick = { onTabSelected(MediaType.AUDIO) },
            text = { Text("音频") },
            icon = { Icon(Icons.Default.AudioFile, contentDescription = null) }
        )
    }
}

@Composable
private fun VideoSessionList(
    sessions: List<VideoSession>,
    onSessionClick: (VideoSession) -> Unit,
    onDownloadClick: (VideoSession) -> Unit,
    downloadProgress: Map<String, com.example.iotandroidv20.obs.DownloadProgress>
) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(sessions) { session ->
            VideoSessionItem(
                session = session,
                onClick = { onSessionClick(session) },
                onDownloadClick = { onDownloadClick(session) },
                downloadProgress = downloadProgress[session.objectKey]
            )
        }
    }
}

@Composable
private fun AudioSessionList(
    sessions: List<AudioSession>,
    onSessionClick: (AudioSession) -> Unit,
    onDownloadClick: (AudioSession) -> Unit,
    downloadProgress: Map<String, com.example.iotandroidv20.obs.DownloadProgress>
) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(sessions) { session ->
            AudioSessionItem(
                session = session,
                onClick = { onSessionClick(session) },
                onDownloadClick = { onDownloadClick(session) },
                downloadProgress = downloadProgress[session.objectKey]
            )
        }
    }
}

@Composable
private fun EmptyStateView(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    message: String,
    actionText: String,
    onAction: () -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                icon,
                contentDescription = null,
                modifier = Modifier.size(64.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = message,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Spacer(modifier = Modifier.height(16.dp))
            Button(onClick = onAction) {
                Text(actionText)
            }
        }
    }
}

private fun formatDate(dateString: String): String {
    return try {
        val inputFormat = SimpleDateFormat("yyyyMMdd", Locale.getDefault())
        val outputFormat = SimpleDateFormat("yyyy年MM月dd日", Locale.getDefault())
        val date = inputFormat.parse(dateString)
        outputFormat.format(date ?: Date())
    } catch (e: Exception) {
        dateString
    }
}
