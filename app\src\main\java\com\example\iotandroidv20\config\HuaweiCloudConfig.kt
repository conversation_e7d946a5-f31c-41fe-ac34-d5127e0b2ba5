package com.example.iotandroidv20.config

import android.content.Context

/**
 * 华为云IoT配置类 - v2.0 Token认证版本
 * 使用IAM Token认证方式，解决权限问题
 */
object HuaweiCloudConfig {

    // 华为云IoT平台区域
    const val REGION = "cn-north-4"  // 华北-北京四
    const val PROJECT_ID = "17da5bacd5dd47589ca000607ae61ccc"  // 项目ID
    const val ACCOUNT_ID = "945cda04010d48e0902f7ff7923c0ab5"  // 账号ID

    // 设备认证信息（您提供的设备信息）
    const val DEVICE_ID = "682d4e2e84adf27cda5ad878_L610_TEST"
    const val DEVICE_NAME = "智能桌宠"
    const val PRODUCT_ID = "l610"
    const val RESOURCE_SPACE = "DefaultApp_6814wz0r"

    // MQTT设备认证参数（备用方式）
    const val MQTT_USERNAME = "682d4e2e84adf27cda5ad878_L610_TEST"
    const val MQTT_PASSWORD = "a5df262339aea854d4b1c24de222d2e3bd4dcd862b373ca7fa931bbc20cc1f12"
    const val MQTT_CLIENT_ID = "682d4e2e84adf27cda5ad878_L610_TEST_0_0_2025061913"
    const val MQTT_HOSTNAME = "517b9a9dad.st1.iotda-device.cn-north-4.myhuaweicloud.com"
    const val MQTT_PORT = 8883
    const val MQTT_PROTOCOL = "MQTTS"

    // v2.0 Token认证配置
    // IAM用户认证信息
    const val IAM_USERNAME = "mkloopjnb"
    const val IAM_PASSWORD = "jzzaurezz337"
    const val IAM_DOMAIN_NAME = "mkloopjnb.com"  // 新注册的自定义域名

    // 备用：AK/SK认证（可用于获取Token）
    const val ACCESS_KEY = "HPUAFQCTHCE7ZQ854RXI"
    const val SECRET_KEY = "OtPzqzS5FOA3spl75ISujwv7OIfjQHCee38cAgsH"

    // API端点配置
    const val IAM_ENDPOINT = "https://iam.cn-north-4.myhuaweicloud.com"
    const val IOT_API_ENDPOINT = "https://517b9a9dad.st1.iotda-app.cn-north-4.myhuaweicloud.com"  // 您的IoT实例专用端点

    // API路径
    const val TOKEN_API_PATH = "/v3/auth/tokens"
    const val DEVICE_API_PATH = "/v5/iot/{project_id}/devices"
    const val DEVICE_DETAIL_PATH = "/v5/iot/{project_id}/devices/{device_id}"
    const val DEVICE_PROPERTIES_PATH = "/v5/iot/{project_id}/devices/{device_id}/properties"
    const val DEVICE_COMMANDS_PATH = "/v5/iot/{project_id}/devices/{device_id}/commands"

    // IoT实例配置
    const val IOT_INSTANCE_ID = "f85ac2cf-1de3-4fd5-9b39-962723077be9"  // IoT实例ID

    // 认证方式配置 - v2.0使用Token认证
    const val USE_TOKEN_AUTH = true  // v2.0版本使用Token认证
    const val USE_DEVICE_AUTH = false  // 不使用设备认证

    // 服务ID和命令定义（根据产品模型）
    const val SERVICE_ID_POSTURE = "PostureMonitor"
    const val SERVICE_ID_REMINDER = "ReminderService"

    // 命令名称
    const val COMMAND_TEST = "test_command"
    const val COMMAND_POSTURE_REMINDER = "posture_reminder"
    const val COMMAND_QUERY_STATUS = "query_status"

    // 新增：家长语音互动命令
    const val COMMAND_PLAY_PARENT_VOICE = "PLAY_PARENT_VOICE"  // 播放家长语音
    const val COMMAND_TTS_SPEAK = "TTS_SPEAK"  // 文本转语音播放
    const val COMMAND_AUDIO_CONTROL = "AUDIO_CONTROL"  // 音频控制（音量、暂停等）

    // 属性名称
    const val PROPERTY_POSTURE_STATUS = "posture_status"
    const val PROPERTY_ANGLE_X = "angle_x"
    const val PROPERTY_ANGLE_Y = "angle_y"
    const val PROPERTY_ANGLE_Z = "angle_z"
    const val PROPERTY_BATTERY_LEVEL = "battery_level"
    const val PROPERTY_SIGNAL_STRENGTH = "signal_strength"

    // 数据轮询间隔（毫秒）
    const val DEVICE_STATUS_POLL_INTERVAL = 10000L  // 10秒
    const val DEVICE_PROPERTIES_POLL_INTERVAL = 5000L  // 5秒

    // 超时配置
    const val CONNECTION_TIMEOUT = 30000  // 30秒
    const val READ_TIMEOUT = 60000  // 60秒

    // Token缓存配置
    const val TOKEN_CACHE_DURATION = 23 * 60 * 60 * 1000L  // 23小时（Token有效期24小时）

    // ==================== OBS 对象存储配置 ====================
    // OBS 基础配置（您确认的正确配置）
    const val OBS_ENDPOINT = "https://obs.cn-north-4.myhuaweicloud.com"  // 华北-北京四
    const val OBS_BUCKET_NAME = "iotdavideo"  // 您提供的存储桶名称
    const val OBS_REGION = "cn-north-4"  // 华北-北京四

    // OBS 使用与IoT相同的 AK/SK（您确认的配置）
    const val OBS_ACCESS_KEY = ACCESS_KEY  // 与IoT使用相同的AK
    const val OBS_SECRET_KEY = "OtPzqzS5FOA3spl75ISujwv7OIfjQHCee38cAgsH"  // 您提供的正确SK

    // OBS 存储路径配置
    const val OBS_VIDEO_PATH = "videos/"  // 视频文件路径
    const val OBS_VOICE_PATH = "voice/"   // 语音文件路径
    const val OBS_LIVE_PATH = "live/"     // 实时流路径

    // OBS 文件命名规则
    const val VIDEO_FILE_PREFIX = "learning_session_"
    const val VOICE_FILE_PREFIX = "parent_voice_"
    const val LIVE_STREAM_PREFIX = "live_stream_"

    /**
     * 验证设备认证配置是否完整
     */
    fun isDeviceAuthConfigured(): Boolean {
        return DEVICE_ID.isNotBlank() &&
               MQTT_USERNAME.isNotBlank() &&
               MQTT_PASSWORD.isNotBlank() &&
               MQTT_CLIENT_ID.isNotBlank() &&
               MQTT_HOSTNAME.isNotBlank()
    }

    /**
     * 验证华为云AK/SK配置是否完整
     */
    fun isAkSkConfigured(): Boolean {
        return ACCESS_KEY != "your_access_key_here" &&
               SECRET_KEY != "your_secret_key_here" &&
               ACCESS_KEY.isNotBlank() &&
               SECRET_KEY.isNotBlank() &&
               REGION.isNotBlank() &&
               PROJECT_ID != "your_project_id_here" &&
               PROJECT_ID.isNotBlank()
    }

    /**
     * 验证Token认证配置是否完整
     */
    fun isTokenAuthConfigured(): Boolean {
        return IAM_USERNAME.isNotBlank() &&
               IAM_PASSWORD.isNotBlank() &&
               IAM_DOMAIN_NAME.isNotBlank() &&
               REGION.isNotBlank() &&
               PROJECT_ID.isNotBlank()
    }

    /**
     * 验证配置是否完整 - v2.0版本
     */
    fun isConfigurationValid(context: Context): Boolean {
        return if (USE_TOKEN_AUTH) {
            isTokenAuthConfigured()  // v2.0使用Token认证
        } else if (USE_DEVICE_AUTH) {
            isDeviceAuthConfigured()
        } else {
            isAkSkConfigured()  // 备用AK/SK方式
        }
    }

    /**
     * 获取配置状态描述 - v2.0版本
     */
    fun getConfigurationStatus(context: Context): String {
        return if (isConfigurationValid(context)) {
            if (USE_TOKEN_AUTH) {
                "华为云IoT Token认证配置已完成 (v2.0)"
            } else {
                "华为云IoT设备认证配置已完成"
            }
        } else {
            if (USE_TOKEN_AUTH) {
                "Token认证配置检查中..."
            } else {
                "设备认证配置检查中..."
            }
        }
    }

    /**
     * 获取设备完整信息
     */
    fun getDeviceInfo(): Map<String, String> {
        return mapOf(
            "device_id" to DEVICE_ID,
            "device_name" to DEVICE_NAME,
            "product_id" to PRODUCT_ID,
            "resource_space" to RESOURCE_SPACE,
            "region" to REGION
        )
    }

    /**
     * 获取MQTT连接信息
     */
    fun getMqttConnectionInfo(): Map<String, Any> {
        return mapOf(
            "username" to MQTT_USERNAME,
            "password" to MQTT_PASSWORD,
            "clientId" to MQTT_CLIENT_ID,
            "hostname" to MQTT_HOSTNAME,
            "port" to MQTT_PORT,
            "protocol" to MQTT_PROTOCOL
        )
    }
}
