package com.example.iotandroidv20.navigation

import androidx.compose.runtime.*
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.iotandroidv20.obs.AudioSession
import com.example.iotandroidv20.obs.VideoSession
import com.example.iotandroidv20.ui.media.MediaBrowserScreen
import com.example.iotandroidv20.ui.media.MediaListScreen
import com.example.iotandroidv20.ui.obs.ObsConfigScreen
import com.example.iotandroidv20.ui.player.AudioPlayerScreen
import com.example.iotandroidv20.ui.player.VideoPlayerScreen

/**
 * 媒体导航路由
 */
object MediaRoutes {
    const val MEDIA_BROWSER = "media_browser"
    const val MEDIA_LIST = "media_list"
    const val VIDEO_PLAYER = "video_player"
    const val AUDIO_PLAYER = "audio_player"
    const val OBS_CONFIG = "obs_config"
}

/**
 * 媒体导航主机
 */
@Composable
fun MediaNavHost(
    navController: NavHostController = rememberNavController(),
    onNavigateBack: () -> Unit
) {
    // 当前选择的媒体项
    var selectedVideoSession by remember { mutableStateOf<VideoSession?>(null) }
    var selectedAudioSession by remember { mutableStateOf<AudioSession?>(null) }
    
    NavHost(
        navController = navController,
        startDestination = MediaRoutes.MEDIA_BROWSER
    ) {
        // 媒体浏览器主界面
        composable(MediaRoutes.MEDIA_BROWSER) {
            MediaBrowserScreen(
                onNavigateBack = onNavigateBack,
                onVideoClick = { videoSession ->
                    selectedVideoSession = videoSession
                    navController.navigate(MediaRoutes.VIDEO_PLAYER)
                },
                onAudioClick = { audioSession ->
                    selectedAudioSession = audioSession
                    navController.navigate(MediaRoutes.AUDIO_PLAYER)
                },
                onObsConfigClick = {
                    navController.navigate(MediaRoutes.OBS_CONFIG)
                }
            )
        }
        
        // 媒体列表界面
        composable(MediaRoutes.MEDIA_LIST) {
            MediaListScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                onVideoClick = { videoSession ->
                    selectedVideoSession = videoSession
                    navController.navigate(MediaRoutes.VIDEO_PLAYER)
                },
                onAudioClick = { audioSession ->
                    selectedAudioSession = audioSession
                    navController.navigate(MediaRoutes.AUDIO_PLAYER)
                }
            )
        }
        
        // 视频播放器界面
        composable(MediaRoutes.VIDEO_PLAYER) {
            selectedVideoSession?.let { videoSession ->
                VideoPlayerScreen(
                    videoSession = videoSession,
                    onNavigateBack = {
                        navController.popBackStack()
                    }
                )
            }
        }
        
        // 音频播放器界面
        composable(MediaRoutes.AUDIO_PLAYER) {
            selectedAudioSession?.let { audioSession ->
                AudioPlayerScreen(
                    audioSession = audioSession,
                    onNavigateBack = {
                        navController.popBackStack()
                    }
                )
            }
        }
        
        // OBS配置界面
        composable(MediaRoutes.OBS_CONFIG) {
            ObsConfigScreen(
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }
    }
}

/**
 * 媒体导航管理器
 */
class MediaNavigationManager {
    
    companion object {
        @Volatile
        private var INSTANCE: MediaNavigationManager? = null
        
        fun getInstance(): MediaNavigationManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: MediaNavigationManager().also { INSTANCE = it }
            }
        }
    }
    
    private var navController: NavHostController? = null
    
    /**
     * 设置导航控制器
     */
    fun setNavController(controller: NavHostController) {
        navController = controller
    }
    
    /**
     * 导航到媒体浏览器
     */
    fun navigateToMediaBrowser() {
        navController?.navigate(MediaRoutes.MEDIA_BROWSER) {
            popUpTo(MediaRoutes.MEDIA_BROWSER) { inclusive = true }
        }
    }
    
    /**
     * 导航到媒体列表
     */
    fun navigateToMediaList() {
        navController?.navigate(MediaRoutes.MEDIA_LIST)
    }
    
    /**
     * 导航到视频播放器
     */
    fun navigateToVideoPlayer() {
        navController?.navigate(MediaRoutes.VIDEO_PLAYER)
    }
    
    /**
     * 导航到音频播放器
     */
    fun navigateToAudioPlayer() {
        navController?.navigate(MediaRoutes.AUDIO_PLAYER)
    }
    
    /**
     * 导航到OBS配置
     */
    fun navigateToObsConfig() {
        navController?.navigate(MediaRoutes.OBS_CONFIG)
    }
    
    /**
     * 返回上一页
     */
    fun navigateBack() {
        navController?.popBackStack()
    }
    
    /**
     * 清除导航栈并返回主界面
     */
    fun navigateToMain() {
        navController?.popBackStack(MediaRoutes.MEDIA_BROWSER, inclusive = true)
    }
}
