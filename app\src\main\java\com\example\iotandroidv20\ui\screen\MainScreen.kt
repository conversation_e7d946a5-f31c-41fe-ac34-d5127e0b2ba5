package com.example.iotandroidv20.ui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.draw.clip
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Send
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.iotandroidv20.model.PostureData
import com.example.iotandroidv20.viewmodel.MainViewModel
import com.example.iotandroidv20.ui.components.EnhancedPostureCard
import com.example.iotandroidv20.ui.components.Advanced3DPostureVisualization
import com.example.iotandroidv20.ui.components.PostureHistoryChart
import com.example.iotandroidv20.ui.components.PostureScoreCard
import com.example.iotandroidv20.ui.components.DailyStatsCard
// import com.example.iotandroidv20.ui.components.VISVideoPlayerCard // 已移除，统一使用OBS视频服务
import com.example.iotandroidv20.ui.components.EEGMonitoringCard
import com.example.iotandroidv20.ui.components.RealTimeVideoVerificationCard
import com.example.iotandroidv20.ui.components.RealTimeVideoStatusCard
import com.example.iotandroidv20.ui.components.ParentVoiceInteractionCard
import com.example.iotandroidv20.ui.components.VoiceInteractionStatusCard
import com.example.iotandroidv20.ui.components.VoiceModuleTestCard
import com.example.iotandroidv20.ui.components.VoiceModuleStatusCard
import com.example.iotandroidv20.ui.components.TestResultCard
import com.example.iotandroidv20.ui.components.PermissionRequestCard
import com.example.iotandroidv20.ui.components.PermissionStatusCard
// import com.example.iotandroidv20.ui.components.ComprehensiveHealthCard // 暂时注释掉
import com.example.iotandroidv20.ui.screen.AdvancedSettingsScreen
import com.example.iotandroidv20.ui.screen.LearningSupervisionScreen
import com.example.iotandroidv20.ui.components.AdvancedFocusDisplay
import com.example.iotandroidv20.ui.components.AdvancedFatigueDisplay
import com.example.iotandroidv20.ui.components.EEGWaveformDisplay
import com.example.iotandroidv20.ui.components.EEGSpectrumAnalyzer
// import com.example.iotandroidv20.ui.components.ComprehensiveHealthDashboard // 暂时注释掉

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    viewModel: MainViewModel,
    onOpenMediaCenter: () -> Unit = {},
    onOpenObsConfig: () -> Unit = {}
) {
    val appState by viewModel.appState.collectAsState()
    val connectionState by viewModel.connectionState.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()
    val tokenStatus by viewModel.tokenStatus.collectAsState()
    val appSettings by viewModel.appSettings.collectAsState()

    // VIS视频状态
    val visStatus by viewModel.visStatus.collectAsState()
    val playbackUrl by viewModel.playbackUrl.collectAsState()
    val userSettings by viewModel.userSettings.collectAsState()

    // 学习监督相关状态
    val currentLearningSession by viewModel.currentLearningSession.collectAsState()
    val realTimeLearningStatus by viewModel.realTimeLearningStatus.collectAsState()
    val parentDashboard by viewModel.parentDashboard.collectAsState()

    var showSettings by remember { mutableStateOf(false) }
    var showVideoFullScreen by remember { mutableStateOf(false) }
    var showLearningSupervision by remember { mutableStateOf(false) }

    // 实时视频验证相关状态
    val realTimeVideoVerifier = remember { viewModel.realTimeVideoVerifier }
    val verificationStatus by realTimeVideoVerifier.verificationStatus.collectAsState()
    val verificationProgress by realTimeVideoVerifier.verificationProgress.collectAsState()
    val verificationMessage by realTimeVideoVerifier.verificationMessage.collectAsState()
    val verificationResults by realTimeVideoVerifier.verificationResults.collectAsState()

    // 家长语音交互相关状态
    val parentVoiceManager = remember { viewModel.parentVoiceInteractionManager }
    val isVoiceRecording by parentVoiceManager.isRecording.collectAsState()
    val voiceInteractionStatus by parentVoiceManager.interactionStatus.collectAsState()
    val voiceStatusDescription = remember(voiceInteractionStatus) {
        parentVoiceManager.getStatusDescription()
    }
    
    // 显示错误消息的Snackbar
    if (errorMessage.isNotEmpty()) {
        LaunchedEffect(errorMessage) {
            // 这里可以显示Snackbar或Toast
        }
    }

    // 高级设置状态
    var showAdvancedSettings by remember { mutableStateOf(false) }

    // 初始化学习监督系统
    LaunchedEffect(Unit) {
        viewModel.initializeLearningSupervision()
    }

    // 设置界面
    if (showSettings) {
        SettingsScreen(
            settings = appSettings,
            onSettingsChange = { viewModel.updateSettings(it) },
            onBack = { showSettings = false },
            onAdvancedSettings = { showAdvancedSettings = true }
        )
        return
    }

    // 高级设置界面
    if (showAdvancedSettings) {
        AdvancedSettingsScreen(
            userSettings = userSettings,
            onSettingsUpdate = { settings ->
                viewModel.updateUserSettings(settings)
                showAdvancedSettings = false
                showSettings = false
            },
            onBack = { showAdvancedSettings = false }
        )
        return
    }

    // 学习监督界面
    if (showLearningSupervision) {
        LearningSupervisionScreen(
            currentSession = currentLearningSession,
            realTimeStatus = realTimeLearningStatus,
            currentGuidance = null, // TODO: 从智能指导引擎获取
            onStartSession = { sessionType, duration ->
                viewModel.startLearningSession(sessionType, duration)
            },
            onPauseSession = {
                viewModel.pauseLearningSession()
            },
            onResumeSession = {
                viewModel.resumeLearningSession()
            },
            onEndSession = {
                viewModel.endLearningSession()
            },
            onBack = { showLearningSupervision = false }
        )
        return
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        "儿童坐姿监控",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                actions = {
                    IconButton(onClick = { showSettings = true }) {
                        Icon(Icons.Default.Settings, contentDescription = "设置")
                    }
                },

                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 学习监督卡片 - 新的主要功能
            item {
                LearningSupervisionCard(
                    currentSession = currentLearningSession,
                    realTimeStatus = realTimeLearningStatus,
                    onStartLearning = { showLearningSupervision = true },
                    onViewSession = { showLearningSupervision = true }
                )
            }

            // 媒体中心卡片 - OBS音视频功能
            item {
                MediaCenterCard(
                    onOpenMediaCenter = onOpenMediaCenter,
                    onOpenObsConfig = onOpenObsConfig
                )
            }
            // 连接状态卡片 - v2.0 Token认证版本
            item {
                TokenConnectionStatusCard(
                    connectionState = connectionState,
                    deviceStatus = appState.deviceStatus,
                    tokenStatus = tokenStatus,
                    onConnect = { viewModel.connectToDevice() },
                    onDisconnect = { viewModel.disconnectFromDevice() },
                    onRefreshToken = { viewModel.refreshToken() }
                )
            }
            
            // 当前坐姿状态卡片（增强版）
            item {
                EnhancedPostureCard(
                    postureData = appState.currentPosture,
                    isMonitoring = appState.isMonitoring,
                    onToggleMonitoring = { viewModel.toggleMonitoring() }
                )
            }

            // 坐姿评分卡片
            item {
                PostureScoreCard(
                    currentPosture = appState.currentPosture,
                    postureHistory = appState.postureHistory
                )
            }

            // 3D坐姿可视化
            item {
                Advanced3DPostureVisualization(
                    postureData = appState.currentPosture,
                    showDetails = true
                )
            }

            // 实时视频监控验证 - 使用OBS统一服务
            item {
                RealTimeVideoVerificationCard(
                    verificationStatus = verificationStatus,
                    verificationProgress = verificationProgress,
                    verificationMessage = verificationMessage,
                    verificationResults = verificationResults,
                    onStartVerification = {
                        viewModel.verifyRealTimeVideoFunction()
                    },
                    onStartRealTimeVideo = {
                        viewModel.startRealTimeVideoView()
                    }
                )
            }

            // 实时视频状态详情
            item {
                RealTimeVideoStatusCard(
                    statusDetails = viewModel.getRealTimeVideoStatusDetails(),
                    onRefreshStatus = {
                        // 刷新状态
                    }
                )
            }

            // 权限请求（如果需要）
            item {
                PermissionRequestCard(
                    onPermissionsGranted = {
                        // 权限授予后的回调
                    }
                )
            }

            // 家长语音交互模块
            item {
                ParentVoiceInteractionCard(
                    isRecording = isVoiceRecording,
                    interactionStatus = voiceInteractionStatus,
                    statusDescription = voiceStatusDescription,
                    onStartRecording = {
                        viewModel.startParentVoiceRecording()
                    },
                    onStopRecording = {
                        viewModel.stopParentVoiceRecording()
                    },
                    onCancelRecording = {
                        viewModel.cancelParentVoiceRecording()
                    }
                )
            }

            // 语音交互状态详情
            item {
                VoiceInteractionStatusCard(
                    lastSentTime = viewModel.getLastVoiceSentTime(),
                    totalMessagesSent = viewModel.getTotalVoiceMessagesSent(),
                    deviceResponseStatus = viewModel.getDeviceVoiceResponseStatus(),
                    onRefreshStatus = {
                        viewModel.refreshVoiceInteractionStatus()
                    }
                )
            }

            // 语音模块测试工具（开发阶段）
            item {
                VoiceModuleTestCard(
                    onRunCompleteTest = {
                        viewModel.runVoiceModuleTest()
                    },
                    onRunQuickTest = {
                        viewModel.quickTestVoiceRecording()
                    }
                )
            }

            // 语音模块状态监控
            item {
                VoiceModuleStatusCard(
                    isRecording = isVoiceRecording,
                    interactionStatus = voiceInteractionStatus.toString(),
                    statusDescription = voiceStatusDescription
                )
            }

            // 测试结果显示
            item {
                TestResultCard(
                    testResult = errorMessage
                )
            }

            // EEG脑电波监控组件
            item {
                val eegDeviceStatus by viewModel.eegDeviceStatus.collectAsState()
                val eegFrequencyAnalysis by viewModel.eegFrequencyAnalysis.collectAsState()
                val eegFocusAssessment by viewModel.eegFocusAssessment.collectAsState()
                val eegFatigueAssessment by viewModel.eegFatigueAssessment.collectAsState()
                val eegIsRecording by viewModel.eegIsRecording.collectAsState()

                EEGMonitoringCard(
                    deviceStatus = eegDeviceStatus,
                    frequencyAnalysis = eegFrequencyAnalysis,
                    focusAssessment = eegFocusAssessment,
                    fatigueAssessment = eegFatigueAssessment,
                    isRecording = eegIsRecording,
                    onConnectDevice = { viewModel.connectEEGDevice() },
                    onDisconnectDevice = { viewModel.disconnectEEGDevice() },
                    onStartRecording = { viewModel.startEEGRecording() },
                    onStopRecording = { viewModel.stopEEGRecording() },
                    onResetHistory = { viewModel.resetEEGHistory() }
                )
            }

            // 综合健康评估卡片 - 暂时注释掉
            /*
            item {
                val comprehensiveAssessment by viewModel.comprehensiveHealthAssessment.collectAsState()

                ComprehensiveHealthCard(
                    assessment = comprehensiveAssessment,
                    onRefresh = { viewModel.performComprehensiveHealthAnalysis() }
                )
            }
            */

            // 高级专注度分析卡片
            item {
                val focusResult = viewModel.getAdvancedFocusResult()
                val focusStatistics = viewModel.getFocusStatistics()

                AdvancedFocusDisplay(
                    focusResult = focusResult,
                    focusStatistics = focusStatistics,
                    isAnimated = true
                )
            }

            // 高级疲劳状态分析卡片
            item {
                val fatigueResult = viewModel.getAdvancedFatigueResult()
                val fatigueStatistics = viewModel.getFatigueStatistics()

                AdvancedFatigueDisplay(
                    fatigueResult = fatigueResult,
                    fatigueStatistics = fatigueStatistics,
                    isAnimated = true
                )
            }

            // EEG实时波形显示
            item {
                val eegFrequencyAnalysis by viewModel.eegFrequencyAnalysis.collectAsState()
                val eegHistory = listOf(eegFrequencyAnalysis).filterNotNull()

                EEGWaveformDisplay(
                    eegData = eegHistory,
                    isRealTime = true,
                    showGrid = true
                )
            }

            // EEG频谱分析器
            item {
                val eegFrequencyAnalysis by viewModel.eegFrequencyAnalysis.collectAsState()
                val eegHistory = listOf(eegFrequencyAnalysis).filterNotNull()

                EEGSpectrumAnalyzer(
                    frequencyAnalysis = eegFrequencyAnalysis,
                    analysisHistory = eegHistory,
                    showHistory = true,
                    isAnimated = true
                )
            }

            // 控制按钮卡片 - v2.1版本
            item {
                TokenControlButtonsCard(
                    reminderEnabled = appState.reminderEnabled,
                    onToggleReminder = { viewModel.toggleReminder() },
                    onQueryDeviceInfo = { viewModel.queryDeviceInfo() },
                    onQueryDeviceProperties = { viewModel.queryDeviceProperties() },
                    onRefreshToken = { viewModel.refreshToken() },
                    onTestDataParsing = { viewModel.testDataParsing() },
                    onSendPostureReminder = { viewModel.sendPostureReminder() },
                    onSendTestCommand = { viewModel.sendTestCommand() },
                    onQueryDeviceStatus = { viewModel.sendQueryStatusCommand() },
                    onGetDatabaseStats = { viewModel.getDatabaseStatistics() },
                    isConnected = connectionState == "已连接 (Token认证)"
                )
            }
            
            // 每日统计数据
            item {
                DailyStatsCard(
                    dailyStats = appState.dailyStats
                )
            }

            // 坐姿历史图表
            item {
                PostureHistoryChart(
                    postureHistory = appState.postureHistory
                )
            }

            // Token状态显示卡片（调试用）
            if (tokenStatus.isNotEmpty()) {
                item {
                    TokenDebugCard(
                        tokenStatus = tokenStatus,
                        postureHistory = appState.postureHistory
                    )
                }
            }
            
            // 错误消息
            if (errorMessage.isNotEmpty()) {
                item {
                    ErrorMessageCard(
                        errorMessage = errorMessage,
                        onDismiss = { viewModel.clearErrorMessage() }
                    )
                }
            }
        }
    }
}

/**
 * 学习监督卡片 - 主界面的核心功能入口
 */
@Composable
fun LearningSupervisionCard(
    currentSession: com.example.iotandroidv20.model.LearningSupervisionSession?,
    realTimeStatus: com.example.iotandroidv20.model.RealTimeLearningStatus?,
    onStartLearning: () -> Unit,
    onViewSession: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (currentSession != null)
                MaterialTheme.colorScheme.primaryContainer
            else
                MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.School,
                        contentDescription = "学习监督",
                        modifier = Modifier.size(32.dp),
                        tint = if (currentSession != null)
                            MaterialTheme.colorScheme.primary
                        else
                            MaterialTheme.colorScheme.onSurface
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Column {
                        Text(
                            text = "智能学习监督",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold,
                            color = if (currentSession != null)
                                MaterialTheme.colorScheme.primary
                            else
                                MaterialTheme.colorScheme.onSurface
                        )
                        Text(
                            text = if (currentSession != null)
                                "监督进行中"
                            else
                                "坐姿 + 专注度 + 疲劳度综合监控",
                            fontSize = 14.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                if (currentSession != null) {
                    // 显示会话状态指示器
                    Surface(
                        color = when (currentSession.status) {
                            com.example.iotandroidv20.model.SessionStatus.ACTIVE -> Color(0xFF4CAF50)
                            com.example.iotandroidv20.model.SessionStatus.PAUSED -> Color(0xFFFF9800)
                            else -> Color(0xFF9E9E9E)
                        }.copy(alpha = 0.1f),
                        shape = MaterialTheme.shapes.small
                    ) {
                        Text(
                            text = currentSession.status.displayName,
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                            fontSize = 12.sp,
                            color = when (currentSession.status) {
                                com.example.iotandroidv20.model.SessionStatus.ACTIVE -> Color(0xFF4CAF50)
                                com.example.iotandroidv20.model.SessionStatus.PAUSED -> Color(0xFFFF9800)
                                else -> Color(0xFF9E9E9E)
                            }
                        )
                    }
                }
            }

            if (currentSession != null && realTimeStatus != null) {
                Spacer(modifier = Modifier.height(16.dp))

                // 显示当前会话信息
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Column {
                        Text(
                            text = currentSession.sessionType.displayName,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = "进度: ${(realTimeStatus.sessionProgress * 100).toInt()}%",
                            fontSize = 14.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }

                    Column(
                        horizontalAlignment = Alignment.End
                    ) {
                        Text(
                            text = "剩余时间",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = "${realTimeStatus.timeRemaining}分钟",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }

                Spacer(modifier = Modifier.height(12.dp))

                // 进度条
                LinearProgressIndicator(
                    progress = realTimeStatus.sessionProgress,
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 实时状态指标
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    QuickStatusItem(
                        icon = Icons.Default.Psychology,
                        label = "专注度",
                        value = realTimeStatus.currentFocusLevel.displayName,
                        color = getFocusColor(realTimeStatus.currentFocusLevel)
                    )

                    QuickStatusItem(
                        icon = Icons.Default.Star,
                        label = "疲劳度",
                        value = realTimeStatus.currentFatigueLevel.displayName,
                        color = getFatigueColor(realTimeStatus.currentFatigueLevel)
                    )

                    QuickStatusItem(
                        icon = Icons.Default.Chair,
                        label = "坐姿",
                        value = realTimeStatus.currentPostureQuality.displayName,
                        color = getPostureColor(realTimeStatus.currentPostureQuality)
                    )
                }

                // 当前建议
                if (realTimeStatus.currentRecommendation.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(16.dp))

                    Card(
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.secondaryContainer
                        )
                    ) {
                        Row(
                            modifier = Modifier.padding(12.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Lightbulb,
                                contentDescription = "建议",
                                tint = MaterialTheme.colorScheme.secondary,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = realTimeStatus.currentRecommendation,
                                fontSize = 14.sp,
                                color = MaterialTheme.colorScheme.onSecondaryContainer
                            )
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 操作按钮
            Button(
                onClick = if (currentSession != null) onViewSession else onStartLearning,
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    imageVector = if (currentSession != null) Icons.Default.Visibility else Icons.Default.PlayArrow,
                    contentDescription = null
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = if (currentSession != null) "查看详情" else "开始学习"
                )
            }
        }
    }
}

/**
 * 快速状态指标项
 */
@Composable
private fun QuickStatusItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    label: String,
    value: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            tint = color,
            modifier = Modifier.size(20.dp)
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = value,
            fontSize = 12.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            text = label,
            fontSize = 10.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

// 辅助函数
private fun getFocusColor(level: com.example.iotandroidv20.model.FocusLevel) = when (level) {
    com.example.iotandroidv20.model.FocusLevel.VERY_HIGH,
    com.example.iotandroidv20.model.FocusLevel.HIGH -> Color(0xFF4CAF50)
    com.example.iotandroidv20.model.FocusLevel.MODERATE -> Color(0xFFFF9800)
    com.example.iotandroidv20.model.FocusLevel.LOW,
    com.example.iotandroidv20.model.FocusLevel.VERY_LOW -> Color(0xFFF44336)
}

private fun getFatigueColor(level: com.example.iotandroidv20.model.FatigueLevel) = when (level) {
    com.example.iotandroidv20.model.FatigueLevel.NONE,
    com.example.iotandroidv20.model.FatigueLevel.LOW,
    com.example.iotandroidv20.model.FatigueLevel.MILD -> Color(0xFF4CAF50)
    com.example.iotandroidv20.model.FatigueLevel.MODERATE -> Color(0xFFFF9800)
    com.example.iotandroidv20.model.FatigueLevel.HIGH,
    com.example.iotandroidv20.model.FatigueLevel.SEVERE,
    com.example.iotandroidv20.model.FatigueLevel.EXTREME -> Color(0xFFF44336)
    com.example.iotandroidv20.model.FatigueLevel.MENTAL,
    com.example.iotandroidv20.model.FatigueLevel.PHYSICAL -> Color(0xFFFF5722)
}

private fun getPostureColor(quality: com.example.iotandroidv20.model.PostureQuality) = when (quality) {
    com.example.iotandroidv20.model.PostureQuality.EXCELLENT,
    com.example.iotandroidv20.model.PostureQuality.GOOD -> Color(0xFF4CAF50)
    com.example.iotandroidv20.model.PostureQuality.FAIR -> Color(0xFFFF9800)
    com.example.iotandroidv20.model.PostureQuality.POOR,
    com.example.iotandroidv20.model.PostureQuality.VERY_POOR -> Color(0xFFF44336)
}

/**
 * 媒体中心卡片 - OBS音视频功能入口
 */
@Composable
fun MediaCenterCard(
    onOpenMediaCenter: () -> Unit,
    onOpenObsConfig: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.secondaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.VideoLibrary,
                        contentDescription = "媒体中心",
                        modifier = Modifier.size(32.dp),
                        tint = MaterialTheme.colorScheme.secondary
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Column {
                        Text(
                            text = "媒体中心",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.secondary
                        )
                        Text(
                            text = "查看和播放设备录制的音视频文件",
                            fontSize = 14.sp,
                            color = MaterialTheme.colorScheme.onSecondaryContainer
                        )
                    }
                }

                Icon(
                    imageVector = Icons.Default.CloudDownload,
                    contentDescription = "云存储",
                    modifier = Modifier.size(24.dp),
                    tint = MaterialTheme.colorScheme.secondary
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 功能说明
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                MediaFeatureItem(
                    icon = Icons.Default.VideoFile,
                    text = "视频播放"
                )

                MediaFeatureItem(
                    icon = Icons.Default.AudioFile,
                    text = "音频播放"
                )

                MediaFeatureItem(
                    icon = Icons.Default.Download,
                    text = "离线下载"
                )

                MediaFeatureItem(
                    icon = Icons.Default.Storage,
                    text = "云端存储"
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 操作按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = onOpenMediaCenter,
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(Icons.Default.PlayArrow, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("打开媒体中心")
                }

                OutlinedButton(
                    onClick = onOpenObsConfig,
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(Icons.Default.Settings, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("OBS配置")
                }
            }
        }
    }
}

/**
 * 媒体功能项
 */
@Composable
private fun MediaFeatureItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    text: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = text,
            modifier = Modifier.size(20.dp),
            tint = MaterialTheme.colorScheme.secondary
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = text,
            fontSize = 12.sp,
            color = MaterialTheme.colorScheme.onSecondaryContainer,
            textAlign = TextAlign.Center
        )
    }
}

// 旧的ConnectionStatusCard已删除，使用TokenConnectionStatusCard替代

// 旧的StatusIndicator已删除，使用TokenStatusIndicator替代

@Composable
fun CurrentPostureCard(
    postureData: PostureData,
    isMonitoring: Boolean,
    onToggleMonitoring: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "当前坐姿状态",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                
                Switch(
                    checked = isMonitoring,
                    onCheckedChange = { onToggleMonitoring() }
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = postureData.getPostureDescription(),
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = if (postureData.isGoodPosture()) Color.Green else Color.Red
                    )
                    Text(
                        text = "更新时间: ${postureData.getFormattedTime()}",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                Icon(
                    imageVector = if (postureData.isGoodPosture()) Icons.Default.CheckCircle else Icons.Default.Warning,
                    contentDescription = null,
                    tint = if (postureData.isGoodPosture()) Color.Green else Color.Red,
                    modifier = Modifier.size(32.dp)
                )
            }
            
            if (postureData.angleX != 0f || postureData.angleY != 0f || postureData.angleZ != 0f) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "角度: X=${postureData.angleX}° Y=${postureData.angleY}° Z=${postureData.angleZ}°",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
fun ControlButtonsCard(
    reminderEnabled: Boolean,
    onToggleReminder: () -> Unit,
    onSendTestData: () -> Unit,
    onSendManualReminder: () -> Unit,
    onQueryDeviceInfo: () -> Unit,
    onQueryDeviceProperties: () -> Unit,
    isConnected: Boolean,
    simulationEnabled: Boolean,
    onToggleSimulation: (Boolean) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "控制功能",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(12.dp))

            // 提醒开关
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text("自动提醒")
                Switch(
                    checked = reminderEnabled,
                    onCheckedChange = { onToggleReminder() }
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 模拟数据开关
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text("模拟数据")
                Switch(
                    checked = simulationEnabled,
                    onCheckedChange = onToggleSimulation,
                    colors = SwitchDefaults.colors(
                        checkedThumbColor = MaterialTheme.colorScheme.secondary,
                        checkedTrackColor = MaterialTheme.colorScheme.secondaryContainer
                    )
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 控制按钮 - 第一行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = onSendTestData,
                    modifier = Modifier.weight(1f),
                    enabled = isConnected
                ) {
                    Icon(Icons.AutoMirrored.Filled.Send, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("测试数据")
                }

                Button(
                    onClick = onSendManualReminder,
                    modifier = Modifier.weight(1f),
                    enabled = isConnected,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.secondary
                    )
                ) {
                    Icon(Icons.Default.Notifications, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("手动提醒")
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 控制按钮 - 第二行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = onQueryDeviceInfo,
                    modifier = Modifier.weight(1f),
                    enabled = isConnected,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.tertiary
                    )
                ) {
                    Icon(Icons.Default.Info, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("设备信息")
                }

                Button(
                    onClick = onQueryDeviceProperties,
                    modifier = Modifier.weight(1f),
                    enabled = isConnected,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.tertiary
                    )
                ) {
                    Icon(Icons.Default.Search, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("查询数据")
                }
            }
        }
    }
}

@Composable
fun DataDisplayCard(
    rawData: String,
    postureHistory: List<PostureData>
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "数据显示",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(12.dp))

            // 原始数据
            if (rawData.isNotEmpty()) {
                Text(
                    text = "最新数据:",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium
                )
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Text(
                        text = rawData,
                        modifier = Modifier.padding(8.dp),
                        fontSize = 12.sp,
                        fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                    )
                }
            }

            // 历史记录
            if (postureHistory.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "坐姿历史 (最近${postureHistory.size}条):",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium
                )

                postureHistory.takeLast(5).forEach { posture ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 2.dp),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = posture.getFormattedTime(),
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = posture.getPostureDescription(),
                            fontSize = 12.sp,
                            color = if (posture.isGoodPosture()) Color.Green else Color.Red
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun ErrorMessageCard(
    errorMessage: String,
    onDismiss: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "错误信息",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onErrorContainer
                )
                Text(
                    text = errorMessage,
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onErrorContainer
                )
            }

            IconButton(onClick = onDismiss) {
                Icon(
                    Icons.Default.Close,
                    contentDescription = "关闭",
                    tint = MaterialTheme.colorScheme.onErrorContainer
                )
            }
        }
    }
}

@Composable
fun TokenConnectionStatusCard(
    connectionState: String,
    deviceStatus: com.example.iotandroidv20.model.DeviceStatus,
    tokenStatus: Map<String, Any>,
    onConnect: () -> Unit,
    onDisconnect: () -> Unit,
    onRefreshToken: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "设备连接状态 (v2.0 Token)",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )

                TokenStatusIndicator(connectionState)
            }

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = when (connectionState) {
                    "未连接" -> "未连接到华为云IoT平台"
                    "初始化中..." -> "正在初始化Token认证..."
                    "连接中..." -> "正在连接华为云IoT平台..."
                    "已连接 (Token认证)" -> "已通过Token认证连接到华为云IoT平台"
                    "Token获取失败" -> "Token认证失败"
                    "连接失败" -> "连接华为云IoT平台失败"
                    else -> connectionState
                },
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            // Token状态显示
            if (tokenStatus.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                val hasToken = tokenStatus["hasToken"] as? Boolean ?: false
                val isValid = tokenStatus["isValid"] as? Boolean ?: false
                val remainingTime = tokenStatus["remainingTime"] as? Long ?: 0

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column {
                        Text(
                            text = when {
                                !hasToken -> "无Token"
                                !isValid -> "Token已过期"
                                else -> "Token有效"
                            },
                            fontSize = 14.sp,
                            color = when {
                                !hasToken -> Color.Gray
                                !isValid -> Color.Red
                                else -> Color.Green
                            },
                            fontWeight = FontWeight.Medium
                        )
                        if (hasToken && isValid) {
                            Text(
                                text = "剩余: ${remainingTime / 1000 / 60}分钟",
                                fontSize = 12.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }

                    OutlinedButton(
                        onClick = onRefreshToken,
                        modifier = Modifier.height(32.dp)
                    ) {
                        Icon(Icons.Default.Refresh, contentDescription = null, modifier = Modifier.size(16.dp))
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("刷新Token", fontSize = 12.sp)
                    }
                }
            }

            if (deviceStatus.isConnected) {
                Spacer(modifier = Modifier.height(8.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Column {
                        Text(
                            text = "设备在线",
                            fontSize = 14.sp,
                            color = Color.Green,
                            fontWeight = FontWeight.Medium
                        )
                        if (deviceStatus.batteryLevel > 0) {
                            Text(
                                text = "电量: ${deviceStatus.batteryLevel}%",
                                fontSize = 12.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        if (deviceStatus.signalStrength > 0) {
                            Text(
                                text = "信号: ${deviceStatus.signalStrength}%",
                                fontSize = 12.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                if (connectionState == "已连接 (Token认证)") {
                    Button(
                        onClick = onDisconnect,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.error
                        )
                    ) {
                        Icon(Icons.Default.Close, contentDescription = null)
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("断开连接")
                    }
                } else {
                    Button(
                        onClick = onConnect,
                        modifier = Modifier.weight(1f),
                        enabled = !connectionState.contains("中...")
                    ) {
                        Icon(Icons.Default.Refresh, contentDescription = null)
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(if (connectionState.contains("中...")) "连接中..." else "连接API")
                    }
                }
            }
        }
    }
}

@Composable
fun TokenStatusIndicator(connectionState: String) {
    val colorAndIcon = when (connectionState) {
        "未连接" -> Pair(Color.Gray, Icons.Default.Close)
        "初始化中...", "连接中..." -> Pair(Color(0xFFFFA500), Icons.Default.Refresh)
        "已连接 (Token认证)" -> Pair(Color.Green, Icons.Default.CheckCircle)
        "Token获取失败", "连接失败" -> Pair(Color.Red, Icons.Default.Warning)
        else -> Pair(Color.Gray, Icons.Default.Info)
    }
    val color = colorAndIcon.first
    val icon = colorAndIcon.second

    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = color,
            modifier = Modifier.size(20.dp)
        )
        Spacer(modifier = Modifier.width(4.dp))
        Box(
            modifier = Modifier
                .size(12.dp)
                .background(color, RoundedCornerShape(6.dp))
        )
    }
}

@Composable
fun TokenControlButtonsCard(
    reminderEnabled: Boolean,
    onToggleReminder: () -> Unit,
    onQueryDeviceInfo: () -> Unit,
    onQueryDeviceProperties: () -> Unit,
    onRefreshToken: () -> Unit,
    onTestDataParsing: () -> Unit,
    onSendPostureReminder: () -> Unit,
    onSendTestCommand: () -> Unit,
    onQueryDeviceStatus: () -> Unit,
    onGetDatabaseStats: () -> Unit,
    isConnected: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "设备控制 (v2.0)",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(12.dp))

            // 提醒功能开关
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "自动提醒",
                    fontSize = 16.sp
                )
                Switch(
                    checked = reminderEnabled,
                    onCheckedChange = { onToggleReminder() }
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 控制按钮
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Button(
                        onClick = onQueryDeviceInfo,
                        modifier = Modifier.weight(1f),
                        enabled = isConnected
                    ) {
                        Icon(Icons.Default.Info, contentDescription = null)
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("设备信息")
                    }

                    Button(
                        onClick = onQueryDeviceProperties,
                        modifier = Modifier.weight(1f),
                        enabled = isConnected
                    ) {
                        Icon(Icons.Default.Settings, contentDescription = null)
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("设备属性")
                    }
                }

                Button(
                    onClick = onRefreshToken,
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.secondary
                    )
                ) {
                    Icon(Icons.Default.Refresh, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("刷新Token")
                }

                Spacer(modifier = Modifier.height(8.dp))

                // 测试数据解析按钮
                Button(
                    onClick = onTestDataParsing,
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.tertiary
                    )
                ) {
                    Icon(Icons.Default.Build, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("测试数据解析")
                }

                Spacer(modifier = Modifier.height(16.dp))

                // 设备控制命令区域
                Text(
                    text = "设备控制命令",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface
                )

                Spacer(modifier = Modifier.height(8.dp))

                // 坐姿提醒按钮
                Button(
                    onClick = onSendPostureReminder,
                    modifier = Modifier.fillMaxWidth(),
                    enabled = isConnected,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.primary
                    )
                ) {
                    Icon(Icons.Default.Notifications, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("发送坐姿提醒")
                }

                Spacer(modifier = Modifier.height(8.dp))

                // 设备状态查询按钮
                Button(
                    onClick = onQueryDeviceStatus,
                    modifier = Modifier.fillMaxWidth(),
                    enabled = isConnected,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.secondary
                    )
                ) {
                    Icon(Icons.Default.Search, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("查询设备状态")
                }

                Spacer(modifier = Modifier.height(8.dp))

                // 测试命令按钮
                Button(
                    onClick = onSendTestCommand,
                    modifier = Modifier.fillMaxWidth(),
                    enabled = isConnected,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.tertiary
                    )
                ) {
                    Icon(Icons.AutoMirrored.Filled.Send, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("发送测试命令")
                }

                Spacer(modifier = Modifier.height(16.dp))

                // 数据库管理区域
                Text(
                    text = "数据库管理",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface
                )

                Spacer(modifier = Modifier.height(8.dp))

                // 数据库统计按钮
                Button(
                    onClick = onGetDatabaseStats,
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.outline
                    )
                ) {
                    Icon(Icons.Default.Info, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("查看数据库统计")
                }
            }
        }
    }
}

@Composable
fun TokenDebugCard(
    tokenStatus: Map<String, Any>,
    postureHistory: List<PostureData>
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Token状态调试信息",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(12.dp))

            val hasToken = tokenStatus["hasToken"] as? Boolean ?: false
            val isValid = tokenStatus["isValid"] as? Boolean ?: false
            val tokenLength = tokenStatus["tokenLength"] as? Int ?: 0
            val remainingTime = tokenStatus["remainingTime"] as? Long ?: 0
            val currentTime = tokenStatus["currentTime"] as? Long ?: 0

            Text(
                text = "Token状态: ${if (hasToken) "存在" else "不存在"}",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Text(
                text = "Token有效性: ${if (isValid) "有效" else "无效"}",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            if (hasToken) {
                Text(
                    text = "Token长度: $tokenLength",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                Text(
                    text = "剩余时间: ${remainingTime / 1000 / 60}分钟",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Text(
                text = "姿态数据记录: ${postureHistory.size}条",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Text(
                text = "当前时间: ${java.text.DateFormat.getTimeInstance().format(java.util.Date(currentTime))}",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}




/**
 * 格式化时长显示
 */
fun formatDuration(milliseconds: Long): String {
    val seconds = milliseconds / 1000
    val minutes = seconds / 60
    val hours = minutes / 60

    return when {
        hours > 0 -> String.format("%02d:%02d:%02d", hours, minutes % 60, seconds % 60)
        minutes > 0 -> String.format("%02d:%02d", minutes, seconds % 60)
        else -> String.format("00:%02d", seconds)
    }
}
