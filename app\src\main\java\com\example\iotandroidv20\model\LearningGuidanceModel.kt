package com.example.iotandroidv20.model

/**
 * 学习指导模型 - 基于脑波状态的智能建议系统
 */
data class LearningGuidance(
    val timestamp: Long = System.currentTimeMillis(),
    val sessionId: String,
    val childProfile: ChildProfile,
    val currentState: LearningState,
    val environmentRecommendations: EnvironmentRecommendations,
    val scheduleRecommendations: ScheduleRecommendations,
    val immediateActions: List<ImmediateAction>,
    val longTermSuggestions: List<LongTermSuggestion>,
    val confidence: Float // 建议的可信度 0-1
)

/**
 * 儿童档案
 */
data class ChildProfile(
    val age: Int,
    val gender: Gender,
    val learningStyle: LearningStyle = LearningStyle.UNKNOWN,
    val attentionSpanBaseline: Int = 0, // 分钟，系统学习得出
    val optimalLearningTimes: List<TimeRange> = emptyList(), // 最佳学习时段
    val environmentPreferences: EnvironmentPreferences = EnvironmentPreferences(),
    val personalizedFactors: PersonalizedFactors = PersonalizedFactors()
)

/**
 * 学习风格
 */
enum class LearningStyle(val displayName: String) {
    VISUAL("视觉型"),
    AUDITORY("听觉型"),
    KINESTHETIC("动觉型"),
    MIXED("混合型"),
    UNKNOWN("未知")
}

/**
 * 当前学习状态
 */
data class LearningState(
    val focusLevel: FocusLevel,
    val fatigueLevel: FatigueLevel,
    val postureQuality: PostureQuality,
    val sessionDuration: Long, // 毫秒
    val currentEnvironment: DetectedEnvironment,
    val brainwavePattern: BrainwavePattern,
    val trendAnalysis: TrendAnalysis
)

/**
 * 检测到的环境状态
 */
data class DetectedEnvironment(
    val estimatedNoiseLevel: NoiseLevel,
    val estimatedLighting: LightingCondition,
    val timeOfDay: TimeOfDay,
    val dayOfWeek: DayOfWeek,
    val seasonalFactor: Float = 1.0f
)

/**
 * 脑波模式分析
 */
data class BrainwavePattern(
    val dominantPattern: BrainWaveType,
    val stability: Float, // 0-1，稳定性
    val coherence: Float, // 0-1，一致性
    val adaptability: Float, // 0-1，适应性
    val optimalFrequency: Float // Hz，个人最佳频率
)

// 注意：BrainwaveType已移除，统一使用EEGData.kt中的BrainWaveType

/**
 * 趋势分析
 */
data class TrendAnalysis(
    val focusTrend: Trend,
    val fatigueTrend: Trend,
    val postureTrend: Trend,
    val overallTrend: Trend,
    val predictedDuration: Int // 预测还能专注多久（分钟）
)

/**
 * 趋势方向
 */
enum class Trend(val displayName: String) {
    IMPROVING("改善中"),
    STABLE("稳定"),
    DECLINING("下降中"),
    FLUCTUATING("波动中")
}

/**
 * 环境建议
 */
data class EnvironmentRecommendations(
    val lighting: LightingRecommendation,
    val noise: NoiseRecommendation,
    val temperature: TemperatureRecommendation,
    val workspace: WorkspaceRecommendation,
    val priority: RecommendationPriority
)

/**
 * 光照建议
 */
data class LightingRecommendation(
    val action: LightingAction,
    val reason: String,
    val expectedImprovement: Float // 预期改善百分比
)

enum class LightingAction(val displayName: String) {
    INCREASE_BRIGHTNESS("增加亮度"),
    DECREASE_BRIGHTNESS("降低亮度"),
    ADJUST_COLOR_TEMPERATURE("调整色温"),
    ADD_TASK_LIGHTING("增加台灯"),
    REDUCE_GLARE("减少眩光"),
    MAINTAIN_CURRENT("保持当前")
}

/**
 * 噪音建议
 */
data class NoiseRecommendation(
    val action: NoiseAction,
    val reason: String,
    val expectedImprovement: Float
)

enum class NoiseAction(val displayName: String) {
    REDUCE_NOISE("降低噪音"),
    ADD_WHITE_NOISE("添加白噪音"),
    PLAY_FOCUS_MUSIC("播放专注音乐"),
    USE_NOISE_CANCELING("使用降噪设备"),
    CHANGE_LOCATION("更换学习地点"),
    MAINTAIN_CURRENT("保持当前")
}

/**
 * 温度建议
 */
data class TemperatureRecommendation(
    val action: TemperatureAction,
    val reason: String,
    val expectedImprovement: Float
)

enum class TemperatureAction(val displayName: String) {
    INCREASE_TEMPERATURE("提高温度"),
    DECREASE_TEMPERATURE("降低温度"),
    IMPROVE_VENTILATION("改善通风"),
    ADJUST_CLOTHING("调整衣物"),
    MAINTAIN_CURRENT("保持当前")
}

/**
 * 工作空间建议
 */
data class WorkspaceRecommendation(
    val action: WorkspaceAction,
    val reason: String,
    val expectedImprovement: Float
)

enum class WorkspaceAction(val displayName: String) {
    ADJUST_CHAIR_HEIGHT("调整椅子高度"),
    ADJUST_DESK_HEIGHT("调整桌子高度"),
    IMPROVE_ERGONOMICS("改善人体工学"),
    REDUCE_CLUTTER("减少杂物"),
    ADD_PLANTS("添加绿植"),
    CHANGE_SEATING("更换座椅"),
    MAINTAIN_CURRENT("保持当前")
}

/**
 * 时间安排建议
 */
data class ScheduleRecommendations(
    val currentSessionAdvice: SessionAdvice,
    val breakRecommendation: BreakRecommendation,
    val nextSessionPlanning: NextSessionPlanning,
    val dailyScheduleOptimization: DailyScheduleOptimization
)

/**
 * 当前会话建议
 */
data class SessionAdvice(
    val action: SessionAction,
    val reason: String,
    val timeframe: Int // 分钟
)

enum class SessionAction(val displayName: String) {
    CONTINUE_CURRENT("继续当前任务"),
    TAKE_SHORT_BREAK("短暂休息"),
    TAKE_LONG_BREAK("长时间休息"),
    SWITCH_TASK_TYPE("切换任务类型"),
    END_SESSION("结束学习"),
    ADJUST_DIFFICULTY("调整难度")
}

/**
 * 休息建议
 */
data class BreakRecommendation(
    val type: BreakType,
    val duration: Int, // 分钟
    val activities: List<BreakActivity>,
    val reason: String
)

enum class BreakType(val displayName: String) {
    MICRO_BREAK("微休息"), // 1-2分钟
    SHORT_BREAK("短休息"), // 5-10分钟
    LONG_BREAK("长休息"), // 15-30分钟
    MEAL_BREAK("用餐休息"), // 30-60分钟
    END_OF_DAY("结束学习") // 停止学习
}

enum class BreakActivity(val displayName: String) {
    EYE_EXERCISES("眼部运动"),
    NECK_STRETCHES("颈部拉伸"),
    DEEP_BREATHING("深呼吸"),
    LIGHT_WALKING("轻松走动"),
    HYDRATION("补充水分"),
    SNACK_TIME("健康零食"),
    OUTDOOR_TIME("户外活动"),
    CREATIVE_PLAY("创意游戏")
}

/**
 * 下次学习规划
 */
data class NextSessionPlanning(
    val recommendedStartTime: Long, // 时间戳
    val estimatedOptimalDuration: Int, // 分钟
    val recommendedTaskType: TaskType,
    val environmentPreparation: List<String>
)

enum class TaskType(val displayName: String) {
    HIGH_CONCENTRATION("高专注任务"), // 数学、编程等
    MEDIUM_CONCENTRATION("中等专注任务"), // 阅读、写作等
    LOW_CONCENTRATION("低专注任务"), // 复习、整理等
    CREATIVE_TASKS("创意任务"), // 绘画、音乐等
    PHYSICAL_TASKS("体感任务") // 实验、手工等
}

/**
 * 每日时间安排优化
 */
data class DailyScheduleOptimization(
    val optimalLearningWindows: List<TimeWindow>,
    val recommendedBreakPattern: BreakPattern,
    val totalRecommendedStudyTime: Int, // 分钟
    val energyManagementTips: List<String>
)

data class TimeWindow(
    val startTime: String, // "09:00"
    val endTime: String,   // "10:30"
    val suitableTaskTypes: List<TaskType>,
    val expectedPerformance: Float // 0-1
)

data class BreakPattern(
    val shortBreakInterval: Int, // 分钟
    val longBreakInterval: Int,  // 分钟
    val microBreakInterval: Int  // 分钟
)

/**
 * 即时行动建议
 */
data class ImmediateAction(
    val action: String,
    val reason: String,
    val urgency: ActionUrgency,
    val expectedEffect: String,
    val timeToImplement: Int // 秒
)

enum class ActionUrgency(val displayName: String) {
    LOW("建议"),
    MEDIUM("推荐"),
    HIGH("强烈建议"),
    CRITICAL("立即执行")
}

/**
 * 长期建议
 */
data class LongTermSuggestion(
    val category: SuggestionCategory,
    val suggestion: String,
    val implementationSteps: List<String>,
    val expectedTimeframe: String, // "1-2周"
    val measurableGoals: List<String>
)

enum class SuggestionCategory(val displayName: String) {
    ENVIRONMENT_SETUP("环境设置"),
    SCHEDULE_OPTIMIZATION("时间安排"),
    HABIT_FORMATION("习惯养成"),
    SKILL_DEVELOPMENT("能力发展"),
    HEALTH_WELLNESS("健康保健")
}

/**
 * 建议优先级
 */
enum class RecommendationPriority(val displayName: String) {
    LOW("低优先级"),
    MEDIUM("中等优先级"),
    HIGH("高优先级"),
    URGENT("紧急"),
    CRITICAL("严重紧急")
}

/**
 * 时间范围
 */
data class TimeRange(
    val startHour: Int, // 0-23
    val startMinute: Int, // 0-59
    val endHour: Int,
    val endMinute: Int,
    val dayOfWeek: List<DayOfWeek> = DayOfWeek.values().toList()
)

enum class DayOfWeek(val displayName: String) {
    MONDAY("周一"),
    TUESDAY("周二"),
    WEDNESDAY("周三"),
    THURSDAY("周四"),
    FRIDAY("周五"),
    SATURDAY("周六"),
    SUNDAY("周日")
}

enum class TimeOfDay(val displayName: String) {
    EARLY_MORNING("清晨"), // 6-8点
    MORNING("上午"),       // 8-12点
    AFTERNOON("下午"),     // 12-18点
    EVENING("傍晚"),       // 18-20点
    NIGHT("夜晚")          // 20-22点
}

/**
 * 环境偏好
 */
data class EnvironmentPreferences(
    val preferredNoiseLevel: NoiseLevel = NoiseLevel.NORMAL,
    val preferredLighting: LightingCondition = LightingCondition.NORMAL,
    val preferredTemperature: TemperatureComfort = TemperatureComfort.COMFORTABLE,
    val learningSpaceType: String = "书房"
)

/**
 * 个性化因子
 */
data class PersonalizedFactors(
    val focusPatternType: String = "标准型", // 从数据中学习得出
    val fatigueResistance: Float = 1.0f,    // 抗疲劳能力
    val environmentSensitivity: Float = 1.0f, // 环境敏感度
    val adaptabilityScore: Float = 1.0f     // 适应性评分
)
