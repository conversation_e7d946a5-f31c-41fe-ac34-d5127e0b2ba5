package com.example.iotandroidv20.database.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.example.iotandroidv20.model.PostureData

/**
 * 坐姿数据实体 - Room数据库表
 */
@Entity(tableName = "posture_data")
data class PostureDataEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    @ColumnInfo(name = "timestamp")
    val timestamp: Long,
    
    @ColumnInfo(name = "device_id")
    val deviceId: String,
    
    @ColumnInfo(name = "posture_status")
    val postureStatus: String,
    
    @ColumnInfo(name = "angle_x")
    val angleX: Float,
    
    @ColumnInfo(name = "angle_y")
    val angleY: Float,
    
    @ColumnInfo(name = "angle_z")
    val angleZ: Float,
    
    @ColumnInfo(name = "confidence")
    val confidence: Float,
    
    @ColumnInfo(name = "duration")
    val duration: Long,
    
    @ColumnInfo(name = "battery_level")
    val batteryLevel: Int,
    
    @ColumnInfo(name = "signal_strength")
    val signalStrength: Int,
    
    @ColumnInfo(name = "sensor_data_json")
    val sensorDataJson: String = "{}", // 存储为JSON字符串
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long = System.currentTimeMillis()
) {
    /**
     * 转换为PostureData模型
     */
    fun toPostureData(): PostureData {
        // 解析sensor_data JSON
        val sensorData = try {
            val gson = com.google.gson.Gson()
            gson.fromJson(sensorDataJson, Map::class.java) as? Map<String, Any> ?: emptyMap()
        } catch (e: Exception) {
            emptyMap<String, Any>()
        }
        
        return PostureData(
            timestamp = timestamp,
            deviceId = deviceId,
            postureStatus = postureStatus,
            angleX = angleX,
            angleY = angleY,
            angleZ = angleZ,
            confidence = confidence,
            duration = duration,
            sensorData = sensorData,
            batteryLevel = batteryLevel,
            signalStrength = signalStrength
        )
    }
    
    companion object {
        /**
         * 从PostureData模型创建实体
         */
        fun fromPostureData(postureData: PostureData): PostureDataEntity {
            // 将sensor_data转换为JSON字符串
            val sensorDataJson = try {
                val gson = com.google.gson.Gson()
                gson.toJson(postureData.sensorData)
            } catch (e: Exception) {
                "{}"
            }
            
            return PostureDataEntity(
                timestamp = postureData.timestamp,
                deviceId = postureData.deviceId,
                postureStatus = postureData.postureStatus,
                angleX = postureData.angleX,
                angleY = postureData.angleY,
                angleZ = postureData.angleZ,
                confidence = postureData.confidence,
                duration = postureData.duration,
                batteryLevel = postureData.batteryLevel,
                signalStrength = postureData.signalStrength,
                sensorDataJson = sensorDataJson
            )
        }
    }
}

/**
 * 每日统计数据实体
 */
@Entity(tableName = "daily_stats")
data class DailyStatsEntity(
    @PrimaryKey
    @ColumnInfo(name = "date")
    val date: String, // 格式: yyyy-MM-dd
    
    @ColumnInfo(name = "total_monitoring_time")
    val totalMonitoringTime: Long,
    
    @ColumnInfo(name = "good_posture_time")
    val goodPostureTime: Long,
    
    @ColumnInfo(name = "bad_posture_time")
    val badPostureTime: Long,
    
    @ColumnInfo(name = "reminder_count")
    val reminderCount: Int,
    
    @ColumnInfo(name = "posture_score")
    val postureScore: Float,
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long = System.currentTimeMillis(),
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long = System.currentTimeMillis()
) {
    /**
     * 转换为DailyStats模型
     */
    fun toDailyStats(): com.example.iotandroidv20.model.DailyStats {
        return com.example.iotandroidv20.model.DailyStats(
            date = date,
            totalMonitoringTime = totalMonitoringTime,
            goodPostureTime = goodPostureTime,
            badPostureTime = badPostureTime,
            reminderCount = reminderCount,
            postureScore = postureScore
        )
    }
    
    companion object {
        /**
         * 从DailyStats模型创建实体
         */
        fun fromDailyStats(dailyStats: com.example.iotandroidv20.model.DailyStats): DailyStatsEntity {
            return DailyStatsEntity(
                date = dailyStats.date,
                totalMonitoringTime = dailyStats.totalMonitoringTime,
                goodPostureTime = dailyStats.goodPostureTime,
                badPostureTime = dailyStats.badPostureTime,
                reminderCount = dailyStats.reminderCount,
                postureScore = dailyStats.postureScore
            )
        }
    }
}

/**
 * 设备状态历史实体
 */
@Entity(tableName = "device_status_history")
data class DeviceStatusHistoryEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    @ColumnInfo(name = "device_id")
    val deviceId: String,
    
    @ColumnInfo(name = "status")
    val status: String, // ONLINE, OFFLINE, UNKNOWN
    
    @ColumnInfo(name = "battery_level")
    val batteryLevel: Int,
    
    @ColumnInfo(name = "signal_strength")
    val signalStrength: Int,
    
    @ColumnInfo(name = "timestamp")
    val timestamp: Long,
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long = System.currentTimeMillis()
)
