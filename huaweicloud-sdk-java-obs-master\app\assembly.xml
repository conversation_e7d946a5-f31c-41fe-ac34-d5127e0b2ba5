<?xml version="1.0" encoding="UTF-8"?>
<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.0.0 http://maven.apache.org/xsd/assembly-2.0.0.xsd">
  <id>assembly_jar_with_third_party</id>
  <formats>
    <format>zip</format>
  </formats>
  <!-- 无需wrapp -->
  <includeBaseDirectory>false</includeBaseDirectory>
  <files>
    <file>
      <source>${project.build.directory}/${project.artifactId}-${project.version}.jar</source>
      <outputDirectory>/</outputDirectory>
    </file>
    <file>
      <source>${project.build.directory}/${project.artifactId}-${project.version}-javadoc.jar</source>
      <outputDirectory>/</outputDirectory>
    </file>
    <file>
      <source>${project.basedir}/README.txt</source>
      <outputDirectory>/</outputDirectory>
    </file>
    <file>
      <source>${project.basedir}/pom-dependencies.xml</source>
      <outputDirectory>/</outputDirectory>
      <destName>pom.xml</destName>
    </file>
    <file>
      <source>${project.basedir}/src/main/resources/log4j2.xml</source>
      <outputDirectory>/</outputDirectory>
    </file>
  </files>
      <fileSets>
        <fileSet>
            <directory>${project.basedir}/src/test/java/samples</directory>
            <outputDirectory>/samples</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}/doc</directory>
            <outputDirectory>/doc</outputDirectory>
        </fileSet>
    </fileSets>
  <dependencySets>
    <dependencySet>
      <!-- 不包含项目文件 -->
      <useProjectArtifact>false</useProjectArtifact>
      <unpack>false</unpack>
      <scope>runtime</scope>
      <outputDirectory>third_party</outputDirectory>
    </dependencySet>
  </dependencySets>
</assembly>