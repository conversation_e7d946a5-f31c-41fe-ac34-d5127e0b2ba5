package com.example.iotandroidv20.repository

import android.content.Context
import android.util.Log
import com.example.iotandroidv20.obs.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

/**
 * 媒体文件仓库
 * 负责管理音视频文件的获取、下载、缓存等操作
 */
class MediaRepository private constructor() {
    
    companion object {
        private const val TAG = "MediaRepository"
        private const val CACHE_DIR_NAME = "obs_media_cache"
        private const val VIDEO_CACHE_DIR = "video"
        private const val AUDIO_CACHE_DIR = "audio"
        
        @Volatile
        private var INSTANCE: MediaRepository? = null
        
        fun getInstance(): MediaRepository {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: MediaRepository().also { INSTANCE = it }
            }
        }
    }
    
    private val obsManager = ObsManager.getInstance()
    private val obsConfig = ObsConfig.getInstance()
    
    // 下载进度状态
    private val _downloadProgress = MutableStateFlow<Map<String, DownloadProgress>>(emptyMap())
    val downloadProgress: StateFlow<Map<String, DownloadProgress>> = _downloadProgress.asStateFlow()
    
    // 缓存的视频会话
    private val _cachedVideoSessions = MutableStateFlow<Map<String, List<VideoSession>>>(emptyMap())
    val cachedVideoSessions: StateFlow<Map<String, List<VideoSession>>> = _cachedVideoSessions.asStateFlow()
    
    // 缓存的音频会话
    private val _cachedAudioSessions = MutableStateFlow<Map<String, List<AudioSession>>>(emptyMap())
    val cachedAudioSessions: StateFlow<Map<String, List<AudioSession>>> = _cachedAudioSessions.asStateFlow()
    
    /**
     * 获取指定设备和日期的视频会话列表
     */
    suspend fun getVideoSessions(deviceId: String, date: String, forceRefresh: Boolean = false): List<VideoSession> {
        return withContext(Dispatchers.IO) {
            try {
                val cacheKey = "${deviceId}_$date"
                
                // 检查缓存
                if (!forceRefresh) {
                    val cached = _cachedVideoSessions.value[cacheKey]
                    if (cached != null) {
                        Log.d(TAG, "Returning cached video sessions for $cacheKey")
                        return@withContext cached
                    }
                }
                
                Log.d(TAG, "Fetching video sessions for device: $deviceId, date: $date")
                val sessions = obsManager.getVideoSessions(deviceId, date)
                
                // 更新本地文件状态
                val updatedSessions = sessions.map { session ->
                    val localFile = getLocalVideoFile(session)
                    session.copy(
                        downloadStatus = if (localFile.exists()) DownloadStatus.DOWNLOADED else DownloadStatus.NOT_DOWNLOADED,
                        localFilePath = if (localFile.exists()) localFile.absolutePath else null
                    )
                }
                
                // 更新缓存
                val currentCache = _cachedVideoSessions.value.toMutableMap()
                currentCache[cacheKey] = updatedSessions
                _cachedVideoSessions.value = currentCache
                
                Log.d(TAG, "Found ${updatedSessions.size} video sessions")
                updatedSessions
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to get video sessions", e)
                emptyList()
            }
        }
    }
    
    /**
     * 获取指定设备和日期的音频会话列表
     */
    suspend fun getAudioSessions(deviceId: String, date: String, forceRefresh: Boolean = false): List<AudioSession> {
        return withContext(Dispatchers.IO) {
            try {
                val cacheKey = "${deviceId}_$date"
                
                // 检查缓存
                if (!forceRefresh) {
                    val cached = _cachedAudioSessions.value[cacheKey]
                    if (cached != null) {
                        Log.d(TAG, "Returning cached audio sessions for $cacheKey")
                        return@withContext cached
                    }
                }
                
                Log.d(TAG, "Fetching audio sessions for device: $deviceId, date: $date")
                val sessions = obsManager.getAudioSessions(deviceId, date)
                
                // 更新本地文件状态
                val updatedSessions = sessions.map { session ->
                    val localFile = getLocalAudioFile(session)
                    session.copy(
                        downloadStatus = if (localFile.exists()) DownloadStatus.DOWNLOADED else DownloadStatus.NOT_DOWNLOADED,
                        localFilePath = if (localFile.exists()) localFile.absolutePath else null
                    )
                }
                
                // 更新缓存
                val currentCache = _cachedAudioSessions.value.toMutableMap()
                currentCache[cacheKey] = updatedSessions
                _cachedAudioSessions.value = currentCache
                
                Log.d(TAG, "Found ${updatedSessions.size} audio sessions")
                updatedSessions
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to get audio sessions", e)
                emptyList()
            }
        }
    }
    
    /**
     * 下载视频文件
     */
    suspend fun downloadVideoFile(context: Context, videoSession: VideoSession): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting download for video: ${videoSession.sessionId}")
                
                val localFile = getLocalVideoFile(videoSession)
                
                // 更新下载状态
                updateVideoSessionStatus(videoSession, DownloadStatus.DOWNLOADING)
                updateDownloadProgress(videoSession.objectKey, 0f, 0L)
                
                // 执行下载
                val success = obsManager.downloadFile(videoSession.objectKey, localFile)
                
                if (success) {
                    // 更新下载状态
                    updateVideoSessionStatus(videoSession, DownloadStatus.DOWNLOADED, localFile.absolutePath)
                    removeDownloadProgress(videoSession.objectKey)
                    Log.d(TAG, "Video download completed: ${videoSession.sessionId}")
                } else {
                    updateVideoSessionStatus(videoSession, DownloadStatus.DOWNLOAD_FAILED)
                    removeDownloadProgress(videoSession.objectKey)
                    Log.e(TAG, "Video download failed: ${videoSession.sessionId}")
                }
                
                success
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to download video file", e)
                updateVideoSessionStatus(videoSession, DownloadStatus.DOWNLOAD_FAILED)
                removeDownloadProgress(videoSession.objectKey)
                false
            }
        }
    }
    
    /**
     * 下载音频文件
     */
    suspend fun downloadAudioFile(context: Context, audioSession: AudioSession): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting download for audio: ${audioSession.sessionId}")
                
                val localFile = getLocalAudioFile(audioSession)
                
                // 更新下载状态
                updateAudioSessionStatus(audioSession, DownloadStatus.DOWNLOADING)
                updateDownloadProgress(audioSession.objectKey, 0f, 0L)
                
                // 执行下载
                val success = obsManager.downloadFile(audioSession.objectKey, localFile)
                
                if (success) {
                    // 更新下载状态
                    updateAudioSessionStatus(audioSession, DownloadStatus.DOWNLOADED, localFile.absolutePath)
                    removeDownloadProgress(audioSession.objectKey)
                    Log.d(TAG, "Audio download completed: ${audioSession.sessionId}")
                } else {
                    updateAudioSessionStatus(audioSession, DownloadStatus.DOWNLOAD_FAILED)
                    removeDownloadProgress(audioSession.objectKey)
                    Log.e(TAG, "Audio download failed: ${audioSession.sessionId}")
                }
                
                success
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to download audio file", e)
                updateAudioSessionStatus(audioSession, DownloadStatus.DOWNLOAD_FAILED)
                removeDownloadProgress(audioSession.objectKey)
                false
            }
        }
    }
    
    /**
     * 获取可用的日期列表
     */
    suspend fun getAvailableDates(deviceId: String): List<String> {
        return withContext(Dispatchers.IO) {
            try {
                obsManager.getAvailableDates(deviceId)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to get available dates", e)
                emptyList()
            }
        }
    }
    
    /**
     * 清理缓存
     */
    suspend fun clearCache(context: Context) {
        withContext(Dispatchers.IO) {
            try {
                val cacheDir = getCacheDir(context)
                if (cacheDir.exists()) {
                    cacheDir.deleteRecursively()
                    Log.d(TAG, "Cache cleared successfully")
                }
                
                // 清理内存缓存
                _cachedVideoSessions.value = emptyMap()
                _cachedAudioSessions.value = emptyMap()
                _downloadProgress.value = emptyMap()
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to clear cache", e)
            }
        }
    }
    
    /**
     * 获取缓存大小
     */
    fun getCacheSize(context: Context): Long {
        return try {
            val cacheDir = getCacheDir(context)
            if (cacheDir.exists()) {
                cacheDir.walkTopDown().filter { it.isFile }.map { it.length() }.sum()
            } else {
                0L
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get cache size", e)
            0L
        }
    }
    
    /**
     * 获取本地视频文件
     */
    private fun getLocalVideoFile(videoSession: VideoSession): File {
        val cacheDir = File(getCacheDir(), VIDEO_CACHE_DIR)
        val deviceDir = File(cacheDir, videoSession.deviceId)
        val dateDir = File(deviceDir, videoSession.date)
        dateDir.mkdirs()
        return File(dateDir, videoSession.fileName)
    }
    
    /**
     * 获取本地音频文件
     */
    private fun getLocalAudioFile(audioSession: AudioSession): File {
        val cacheDir = File(getCacheDir(), AUDIO_CACHE_DIR)
        val deviceDir = File(cacheDir, audioSession.deviceId)
        val dateDir = File(deviceDir, audioSession.date)
        dateDir.mkdirs()
        return File(dateDir, audioSession.fileName)
    }
    
    /**
     * 获取缓存目录
     */
    private fun getCacheDir(context: Context? = null): File {
        return if (context != null) {
            File(context.cacheDir, CACHE_DIR_NAME)
        } else {
            // 使用默认路径（仅用于路径计算）
            File(CACHE_DIR_NAME)
        }
    }
    
    /**
     * 更新视频会话状态
     */
    private fun updateVideoSessionStatus(videoSession: VideoSession, status: DownloadStatus, localPath: String? = null) {
        val currentCache = _cachedVideoSessions.value.toMutableMap()
        val cacheKey = "${videoSession.deviceId}_${videoSession.date}"
        val sessions = currentCache[cacheKey]?.toMutableList() ?: return
        
        val index = sessions.indexOfFirst { it.sessionId == videoSession.sessionId }
        if (index >= 0) {
            sessions[index] = sessions[index].copy(
                downloadStatus = status,
                localFilePath = localPath
            )
            currentCache[cacheKey] = sessions
            _cachedVideoSessions.value = currentCache
        }
    }
    
    /**
     * 更新音频会话状态
     */
    private fun updateAudioSessionStatus(audioSession: AudioSession, status: DownloadStatus, localPath: String? = null) {
        val currentCache = _cachedAudioSessions.value.toMutableMap()
        val cacheKey = "${audioSession.deviceId}_${audioSession.date}"
        val sessions = currentCache[cacheKey]?.toMutableList() ?: return
        
        val index = sessions.indexOfFirst { it.sessionId == audioSession.sessionId }
        if (index >= 0) {
            sessions[index] = sessions[index].copy(
                downloadStatus = status,
                localFilePath = localPath
            )
            currentCache[cacheKey] = sessions
            _cachedAudioSessions.value = currentCache
        }
    }
    
    /**
     * 更新下载进度
     */
    private fun updateDownloadProgress(objectKey: String, progress: Float, speed: Long) {
        val currentProgress = _downloadProgress.value.toMutableMap()
        currentProgress[objectKey] = DownloadProgress(
            objectKey = objectKey,
            totalBytes = 0L, // 实际实现中需要获取文件大小
            downloadedBytes = 0L,
            progress = progress,
            speed = speed,
            remainingTime = 0L
        )
        _downloadProgress.value = currentProgress
    }
    
    /**
     * 移除下载进度
     */
    private fun removeDownloadProgress(objectKey: String) {
        val currentProgress = _downloadProgress.value.toMutableMap()
        currentProgress.remove(objectKey)
        _downloadProgress.value = currentProgress
    }
}
