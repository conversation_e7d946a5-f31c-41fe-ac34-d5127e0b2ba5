{"logs": [{"outputFile": "com.example.iotandroidv20.app-mergeDebugResources-62:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2c60f935d2795008c652ca71073c0e75\\transformed\\media3-ui-1.2.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,500,689,776,864,944,1031,1117,1188,1255,1353,1446,1516,1580,1642,1711,1826,1940,2053,2125,2207,2281,2347,2434,2522,2585,2650,2703,2761,2809,2870,2935,3007,3072,3140,3198,3256,3322,3386,3452,3504,3563,3636,3709", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,86,87,79,86,85,70,66,97,92,69,63,61,68,114,113,112,71,81,73,65,86,87,62,64,52,57,47,60,64,71,64,67,57,57,65,63,65,51,58,72,72,54", "endOffsets": "281,495,684,771,859,939,1026,1112,1183,1250,1348,1441,1511,1575,1637,1706,1821,1935,2048,2120,2202,2276,2342,2429,2517,2580,2645,2698,2756,2804,2865,2930,3002,3067,3135,3193,3251,3317,3381,3447,3499,3558,3631,3704,3759"}, "to": {"startLines": "2,11,15,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,550,2083,2170,2258,2338,2425,2511,2582,2649,2747,2840,2910,2974,3036,3105,3220,3334,3447,3519,3601,3675,3741,3828,3916,3979,4696,4749,4807,4855,4916,4981,5053,5118,5186,5244,5302,5368,5432,5498,5550,5609,5682,5755", "endLines": "10,14,18,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83", "endColumns": "17,12,12,86,87,79,86,85,70,66,97,92,69,63,61,68,114,113,112,71,81,73,65,86,87,62,64,52,57,47,60,64,71,64,67,57,57,65,63,65,51,58,72,72,54", "endOffsets": "331,545,734,2165,2253,2333,2420,2506,2577,2644,2742,2835,2905,2969,3031,3100,3215,3329,3442,3514,3596,3670,3736,3823,3911,3974,4039,4744,4802,4850,4911,4976,5048,5113,5181,5239,5297,5363,5427,5493,5545,5604,5677,5750,5805"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\31c73cb6dfb12faac49eb1bae4282a74\\transformed\\core-1.16.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "20,21,22,23,24,25,26,157", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "811,909,1011,1109,1207,1314,1423,13271", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "904,1006,1104,1202,1309,1418,1538,13367"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\752167a3e67da85263f648420d161268\\transformed\\ui-release\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,288,385,486,572,648,739,829,915,993,1074,1145,1220,1291,1362,1443,1513", "endColumns": "95,86,96,100,85,75,90,89,85,77,80,70,74,70,70,80,69,119", "endOffsets": "196,283,380,481,567,643,734,824,910,988,1069,1140,1215,1286,1357,1438,1508,1628"}, "to": {"startLines": "27,28,29,31,32,84,85,149,150,151,152,153,154,155,156,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1543,1639,1726,1896,1997,5810,5886,12648,12738,12824,12902,12983,13054,13129,13200,13372,13453,13523", "endColumns": "95,86,96,100,85,75,90,89,85,77,80,70,74,70,70,80,69,119", "endOffsets": "1634,1721,1818,1992,2078,5881,5972,12733,12819,12897,12978,13049,13124,13195,13266,13448,13518,13638"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5dca3f3744b4fa46ea145e6d4fc9225c\\transformed\\media3-exoplayer-1.2.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,184,250,317,392,462,551,635", "endColumns": "68,59,65,66,74,69,88,83,71", "endOffsets": "119,179,245,312,387,457,546,630,702"}, "to": {"startLines": "57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4044,4113,4173,4239,4306,4381,4451,4540,4624", "endColumns": "68,59,65,66,74,69,88,83,71", "endOffsets": "4108,4168,4234,4301,4376,4446,4535,4619,4691"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\01b6e8b8fea8bb45b55852d4590f3437\\transformed\\foundation-release\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,84", "endOffsets": "137,222"}, "to": {"startLines": "161,162", "startColumns": "4,4", "startOffsets": "13643,13730", "endColumns": "86,84", "endOffsets": "13725,13810"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\398f61ea23c76807315a7831064b1215\\transformed\\material3-release\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,290,405,521,621,725,846,987,1115,1257,1342,1441,1531,1627,1742,1863,1967,2095,2220,2352,2518,2643,2765,2888,3017,3108,3207,3323,3449,3549,3659,3762,3899,4039,4145,4243,4320,4414,4508,4612,4697,4785,4890,4971,5054,5153,5251,5346,5444,5532,5635,5735,5838,5954,6035,6135", "endColumns": "116,117,114,115,99,103,120,140,127,141,84,98,89,95,114,120,103,127,124,131,165,124,121,122,128,90,98,115,125,99,109,102,136,139,105,97,76,93,93,103,84,87,104,80,82,98,97,94,97,87,102,99,102,115,80,99,95", "endOffsets": "167,285,400,516,616,720,841,982,1110,1252,1337,1436,1526,1622,1737,1858,1962,2090,2215,2347,2513,2638,2760,2883,3012,3103,3202,3318,3444,3544,3654,3757,3894,4034,4140,4238,4315,4409,4503,4607,4692,4780,4885,4966,5049,5148,5246,5341,5439,5527,5630,5730,5833,5949,6030,6130,6226"}, "to": {"startLines": "86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5977,6094,6212,6327,6443,6543,6647,6768,6909,7037,7179,7264,7363,7453,7549,7664,7785,7889,8017,8142,8274,8440,8565,8687,8810,8939,9030,9129,9245,9371,9471,9581,9684,9821,9961,10067,10165,10242,10336,10430,10534,10619,10707,10812,10893,10976,11075,11173,11268,11366,11454,11557,11657,11760,11876,11957,12057", "endColumns": "116,117,114,115,99,103,120,140,127,141,84,98,89,95,114,120,103,127,124,131,165,124,121,122,128,90,98,115,125,99,109,102,136,139,105,97,76,93,93,103,84,87,104,80,82,98,97,94,97,87,102,99,102,115,80,99,95", "endOffsets": "6089,6207,6322,6438,6538,6642,6763,6904,7032,7174,7259,7358,7448,7544,7659,7780,7884,8012,8137,8269,8435,8560,8682,8805,8934,9025,9124,9240,9366,9466,9576,9679,9816,9956,10062,10160,10237,10331,10425,10529,10614,10702,10807,10888,10971,11070,11168,11263,11361,11449,11552,11652,11755,11871,11952,12052,12148"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\835843a1fca13b839a80c71cfb075e12\\transformed\\media3-session-1.2.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,127,200,273,340,420,503,598", "endColumns": "71,72,72,66,79,82,94,96", "endOffsets": "122,195,268,335,415,498,593,690"}, "to": {"startLines": "19,30,143,144,145,146,147,148", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "739,1823,12153,12226,12293,12373,12456,12551", "endColumns": "71,72,72,66,79,82,94,96", "endOffsets": "806,1891,12221,12288,12368,12451,12546,12643"}}]}]}