/**
 * Copyright 2019 Huawei Technologies Co.,Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License.  You may obtain a copy of the
 * License at
 * 
 * http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software distributed
 * under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
 * CONDITIONS OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */


package com.obs.test.internal.util;

public class ReflectUtilsTest {
//    @Test
//    public void test_set_ObsFSBucket_innerClient() {
//        ObsClient obsClient = TestTools.getExternalEnvironment();
//        
//        ObsFSBucket fsBucket = new ObsFSBucket("bucketName", "location");
//        ReflectUtils.setInnerClient(fsBucket, obsClient);
//    }
//    
//    @Test
//    public void test_set_ObsFSFile_innerClient() {
//        ObsClient obsClient = TestTools.getExternalEnvironment();
//        
//        ObsFSFile obsFile = new ObsFSFile("bucketName", "objectKey", "etag", "versionId", StorageClassEnum.STANDARD, "objectUrl"); 
//        ReflectUtils.setInnerClient(obsFile, obsClient);
//    }
//    
//    @Test
//    public void test_set_ObsFSFolder_innerClient() {
//        ObsClient obsClient = TestTools.getExternalEnvironment();
//        
//        ObsFSFolder obsFolder = new ObsFSFolder("bucketName", "objectKey", "etag", "versionId", StorageClassEnum.STANDARD, "objectUrl"); 
//        ReflectUtils.setInnerClient(obsFolder, obsClient);
//    }
}
