{"devices": [{"manufacturerId": "LearningSupervisionTech001", "manufacturerName": "智能学习监督科技", "protocolType": "MQTT", "deviceType": "LearningSupervisionDevice", "serviceTypeCapabilities": [{"serviceId": "PostureMonitoring", "serviceType": "PostureMonitoring", "option": "Mandatory"}, {"serviceId": "EEGMonitoring", "serviceType": "EEGMonitoring", "option": "Mandatory"}, {"serviceId": "EnvironmentMonitoring", "serviceType": "EnvironmentMonitoring", "option": "Optional"}, {"serviceId": "DeviceManagement", "serviceType": "DeviceManagement", "option": "Mandatory"}]}]}