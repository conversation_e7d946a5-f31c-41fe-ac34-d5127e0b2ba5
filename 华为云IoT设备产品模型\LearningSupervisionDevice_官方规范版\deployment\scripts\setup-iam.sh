#!/bin/bash

# 华为云IAM权限配置自动化脚本
# 用途：自动创建学习监督项目所需的IAM用户、用户组和权限策略

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查华为云CLI是否已安装和配置
check_hcloud_cli() {
    log_info "检查华为云CLI配置..."
    
    if ! command -v hcloud &> /dev/null; then
        log_error "华为云CLI未安装，请先安装华为云CLI"
        exit 1
    fi
    
    # 检查是否已配置认证信息
    if ! hcloud configure list &> /dev/null; then
        log_error "华为云CLI未配置，请先运行 'hcloud configure' 配置认证信息"
        exit 1
    fi
    
    log_info "华为云CLI配置检查通过"
}

# 创建用户组
create_groups() {
    log_info "创建IAM用户组..."
    
    # 设备端用户组
    if hcloud iam create-group \
        --group-name "LearningSupervisionDeviceGroup" \
        --description "学习监督设备端用户组" 2>/dev/null; then
        log_info "✅ 创建设备端用户组成功"
    else
        log_warn "设备端用户组可能已存在"
    fi
    
    # 应用端用户组
    if hcloud iam create-group \
        --group-name "LearningSupervisionAppGroup" \
        --description "学习监督应用端用户组" 2>/dev/null; then
        log_info "✅ 创建应用端用户组成功"
    else
        log_warn "应用端用户组可能已存在"
    fi
    
    # 管理员用户组
    if hcloud iam create-group \
        --group-name "LearningSupervisionAdminGroup" \
        --description "学习监督管理员用户组" 2>/dev/null; then
        log_info "✅ 创建管理员用户组成功"
    else
        log_warn "管理员用户组可能已存在"
    fi
}

# 创建权限策略
create_policies() {
    log_info "创建IAM权限策略..."
    
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local policies_dir="$script_dir/../policies"
    
    # 设备端权限策略
    if hcloud iam create-policy \
        --policy-name "LearningSupervisionDevicePolicy" \
        --policy-document "file://$policies_dir/device-policy.json" \
        --description "学习监督设备端权限策略" 2>/dev/null; then
        log_info "✅ 创建设备端权限策略成功"
    else
        log_warn "设备端权限策略可能已存在"
    fi
    
    # 应用端权限策略
    if hcloud iam create-policy \
        --policy-name "LearningSupervisionAppPolicy" \
        --policy-document "file://$policies_dir/app-policy.json" \
        --description "学习监督应用端权限策略" 2>/dev/null; then
        log_info "✅ 创建应用端权限策略成功"
    else
        log_warn "应用端权限策略可能已存在"
    fi
    
    # 管理员权限策略
    if hcloud iam create-policy \
        --policy-name "LearningSupervisionAdminPolicy" \
        --policy-document "file://$policies_dir/admin-policy.json" \
        --description "学习监督管理员权限策略" 2>/dev/null; then
        log_info "✅ 创建管理员权限策略成功"
    else
        log_warn "管理员权限策略可能已存在"
    fi
}

# 附加策略到用户组
attach_policies() {
    log_info "附加权限策略到用户组..."
    
    # 附加设备端策略
    if hcloud iam attach-group-policy \
        --group-name "LearningSupervisionDeviceGroup" \
        --policy-name "LearningSupervisionDevicePolicy" 2>/dev/null; then
        log_info "✅ 附加设备端策略成功"
    else
        log_warn "设备端策略可能已附加"
    fi
    
    # 附加应用端策略
    if hcloud iam attach-group-policy \
        --group-name "LearningSupervisionAppGroup" \
        --policy-name "LearningSupervisionAppPolicy" 2>/dev/null; then
        log_info "✅ 附加应用端策略成功"
    else
        log_warn "应用端策略可能已附加"
    fi
    
    # 附加管理员策略
    if hcloud iam attach-group-policy \
        --group-name "LearningSupervisionAdminGroup" \
        --policy-name "LearningSupervisionAdminPolicy" 2>/dev/null; then
        log_info "✅ 附加管理员策略成功"
    else
        log_warn "管理员策略可能已附加"
    fi
}

# 创建IAM用户
create_users() {
    log_info "创建IAM用户..."
    
    # 设备端用户
    if hcloud iam create-user \
        --user-name "learning-supervision-device-user" \
        --description "学习监督设备端服务用户" 2>/dev/null; then
        log_info "✅ 创建设备端用户成功"
    else
        log_warn "设备端用户可能已存在"
    fi
    
    # 应用端用户
    if hcloud iam create-user \
        --user-name "learning-supervision-app-user" \
        --description "学习监督应用端服务用户" 2>/dev/null; then
        log_info "✅ 创建应用端用户成功"
    else
        log_warn "应用端用户可能已存在"
    fi
    
    # 管理员用户
    if hcloud iam create-user \
        --user-name "learning-supervision-admin-user" \
        --description "学习监督管理员用户" 2>/dev/null; then
        log_info "✅ 创建管理员用户成功"
    else
        log_warn "管理员用户可能已存在"
    fi
}

# 将用户添加到用户组
add_users_to_groups() {
    log_info "将用户添加到用户组..."
    
    # 设备端用户
    if hcloud iam add-user-to-group \
        --group-name "LearningSupervisionDeviceGroup" \
        --user-name "learning-supervision-device-user" 2>/dev/null; then
        log_info "✅ 添加设备端用户到用户组成功"
    else
        log_warn "设备端用户可能已在用户组中"
    fi
    
    # 应用端用户
    if hcloud iam add-user-to-group \
        --group-name "LearningSupervisionAppGroup" \
        --user-name "learning-supervision-app-user" 2>/dev/null; then
        log_info "✅ 添加应用端用户到用户组成功"
    else
        log_warn "应用端用户可能已在用户组中"
    fi
    
    # 管理员用户
    if hcloud iam add-user-to-group \
        --group-name "LearningSupervisionAdminGroup" \
        --user-name "learning-supervision-admin-user" 2>/dev/null; then
        log_info "✅ 添加管理员用户到用户组成功"
    else
        log_warn "管理员用户可能已在用户组中"
    fi
}

# 创建访问密钥
create_access_keys() {
    log_info "创建访问密钥..."
    
    local keys_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)/../keys"
    mkdir -p "$keys_dir"
    
    # 设备端用户访问密钥
    log_info "创建设备端用户访问密钥..."
    if device_key=$(hcloud iam create-access-key \
        --user-name "learning-supervision-device-user" \
        --output json 2>/dev/null); then
        echo "$device_key" > "$keys_dir/device-access-key.json"
        log_info "✅ 设备端访问密钥已保存到 $keys_dir/device-access-key.json"
    else
        log_warn "设备端用户可能已有访问密钥"
    fi
    
    # 应用端用户访问密钥
    log_info "创建应用端用户访问密钥..."
    if app_key=$(hcloud iam create-access-key \
        --user-name "learning-supervision-app-user" \
        --output json 2>/dev/null); then
        echo "$app_key" > "$keys_dir/app-access-key.json"
        log_info "✅ 应用端访问密钥已保存到 $keys_dir/app-access-key.json"
    else
        log_warn "应用端用户可能已有访问密钥"
    fi
    
    # 管理员用户访问密钥
    log_info "创建管理员用户访问密钥..."
    if admin_key=$(hcloud iam create-access-key \
        --user-name "learning-supervision-admin-user" \
        --output json 2>/dev/null); then
        echo "$admin_key" > "$keys_dir/admin-access-key.json"
        log_info "✅ 管理员访问密钥已保存到 $keys_dir/admin-access-key.json"
    else
        log_warn "管理员用户可能已有访问密钥"
    fi
    
    # 设置文件权限
    chmod 600 "$keys_dir"/*.json 2>/dev/null || true
    
    log_warn "⚠️  请妥善保管访问密钥文件，建议定期轮换"
}

# 验证配置
verify_configuration() {
    log_info "验证IAM配置..."
    
    # 检查用户组
    if hcloud iam list-groups | grep -q "LearningSupervisionDeviceGroup"; then
        log_info "✅ 设备端用户组存在"
    else
        log_error "❌ 设备端用户组不存在"
    fi
    
    # 检查用户
    if hcloud iam list-users | grep -q "learning-supervision-device-user"; then
        log_info "✅ 设备端用户存在"
    else
        log_error "❌ 设备端用户不存在"
    fi
    
    # 检查策略
    if hcloud iam list-policies | grep -q "LearningSupervisionDevicePolicy"; then
        log_info "✅ 设备端权限策略存在"
    else
        log_error "❌ 设备端权限策略不存在"
    fi
    
    log_info "IAM配置验证完成"
}

# 主函数
main() {
    log_info "开始华为云IAM权限配置..."
    
    check_hcloud_cli
    create_groups
    create_policies
    attach_policies
    create_users
    add_users_to_groups
    create_access_keys
    verify_configuration
    
    log_info "🎉 华为云IAM权限配置完成！"
    log_info "请查看 deployment/keys/ 目录中的访问密钥文件"
    log_warn "⚠️  请妥善保管访问密钥，不要提交到版本控制系统"
}

# 执行主函数
main "$@"
