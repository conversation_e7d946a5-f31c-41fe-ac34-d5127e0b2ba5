package com.example.iotandroidv20.ui.components

import android.content.Context
import android.content.pm.PackageManager
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat

/**
 * 权限请求卡片
 * 用于请求录音和存储权限
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PermissionRequestCard(
    onPermissionsGranted: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var recordPermissionGranted by remember { mutableStateOf(false) }
    var storagePermissionGranted by remember { mutableStateOf(false) }
    
    // 检查权限状态
    LaunchedEffect(Unit) {
        recordPermissionGranted = ContextCompat.checkSelfPermission(
            context, 
            android.Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED
        
        storagePermissionGranted = ContextCompat.checkSelfPermission(
            context, 
            android.Manifest.permission.WRITE_EXTERNAL_STORAGE
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    // 权限请求启动器
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        recordPermissionGranted = permissions[android.Manifest.permission.RECORD_AUDIO] ?: false
        storagePermissionGranted = permissions[android.Manifest.permission.WRITE_EXTERNAL_STORAGE] ?: false
        
        if (recordPermissionGranted && storagePermissionGranted) {
            onPermissionsGranted()
        }
    }
    
    val allPermissionsGranted = recordPermissionGranted && storagePermissionGranted
    
    if (!allPermissionsGranted) {
        Card(
            modifier = modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFFFFEBEE) // 红色背景表示需要注意
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                // 标题行
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "🔐 权限请求",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFFD32F2F)
                    )
                    
                    Icon(
                        imageVector = Icons.Default.Security,
                        contentDescription = "权限",
                        tint = Color(0xFFD32F2F),
                        modifier = Modifier.size(24.dp)
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 说明文字
                Text(
                    text = "语音交互功能需要以下权限才能正常工作：",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFFB71C1C)
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 权限状态列表
                PermissionStatusItem(
                    permissionName = "录音权限",
                    description = "用于录制家长语音消息",
                    isGranted = recordPermissionGranted,
                    icon = Icons.Default.Mic
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                PermissionStatusItem(
                    permissionName = "存储权限",
                    description = "用于保存录音文件",
                    isGranted = storagePermissionGranted,
                    icon = Icons.Default.Storage
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 请求权限按钮
                Button(
                    onClick = {
                        val permissions = mutableListOf<String>()
                        if (!recordPermissionGranted) {
                            permissions.add(android.Manifest.permission.RECORD_AUDIO)
                        }
                        if (!storagePermissionGranted) {
                            permissions.add(android.Manifest.permission.WRITE_EXTERNAL_STORAGE)
                        }
                        
                        if (permissions.isNotEmpty()) {
                            permissionLauncher.launch(permissions.toTypedArray())
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFFD32F2F)
                    )
                ) {
                    Icon(
                        imageVector = Icons.Default.Security,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("请求权限")
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 权限说明
                Text(
                    text = "💡 提示：授予权限后，您就可以使用语音交互功能与孩子进行实时沟通了。",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF8D6E63)
                )
            }
        }
    }
}

/**
 * 权限状态项目
 */
@Composable
private fun PermissionStatusItem(
    permissionName: String,
    description: String,
    isGranted: Boolean,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = if (isGranted) Color.Green else Color.Red,
            modifier = Modifier.size(20.dp)
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = permissionName,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium,
                color = if (isGranted) Color(0xFF2E7D32) else Color(0xFFD32F2F)
            )
            
            Text(
                text = description,
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF5D4037)
            )
        }
        
        Icon(
            imageVector = if (isGranted) Icons.Default.CheckCircle else Icons.Default.Cancel,
            contentDescription = if (isGranted) "已授予" else "未授予",
            tint = if (isGranted) Color.Green else Color.Red,
            modifier = Modifier.size(24.dp)
        )
    }
}

/**
 * 权限状态检查器
 */
@Composable
fun PermissionStatusChecker(
    context: Context = LocalContext.current,
    onPermissionStatusChanged: (Boolean, Boolean) -> Unit
) {
    LaunchedEffect(Unit) {
        val recordPermission = ContextCompat.checkSelfPermission(
            context, 
            android.Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED
        
        val storagePermission = ContextCompat.checkSelfPermission(
            context, 
            android.Manifest.permission.WRITE_EXTERNAL_STORAGE
        ) == PackageManager.PERMISSION_GRANTED
        
        onPermissionStatusChanged(recordPermission, storagePermission)
    }
}

/**
 * 权限状态显示卡片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PermissionStatusCard(
    recordPermissionGranted: Boolean,
    storagePermissionGranted: Boolean,
    modifier: Modifier = Modifier
) {
    val allPermissionsGranted = recordPermissionGranted && storagePermissionGranted
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 4.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (allPermissionsGranted) {
                Color(0xFFE8F5E8) // 绿色背景
            } else {
                Color(0xFFFFEBEE) // 红色背景
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "🔐 权限状态",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium,
                    color = if (allPermissionsGranted) Color(0xFF2E7D32) else Color(0xFFD32F2F)
                )
                
                Icon(
                    imageVector = if (allPermissionsGranted) Icons.Default.CheckCircle else Icons.Default.Warning,
                    contentDescription = null,
                    tint = if (allPermissionsGranted) Color.Green else Color.Red,
                    modifier = Modifier.size(20.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "录音权限",
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = if (recordPermissionGranted) "✅ 已授予" else "❌ 未授予",
                        style = MaterialTheme.typography.bodySmall,
                        color = if (recordPermissionGranted) Color.Green else Color.Red
                    )
                }
                
                Column {
                    Text(
                        text = "存储权限",
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = if (storagePermissionGranted) "✅ 已授予" else "❌ 未授予",
                        style = MaterialTheme.typography.bodySmall,
                        color = if (storagePermissionGranted) Color.Green else Color.Red
                    )
                }
                
                Column {
                    Text(
                        text = "整体状态",
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = if (allPermissionsGranted) "✅ 就绪" else "❌ 需要权限",
                        style = MaterialTheme.typography.bodySmall,
                        color = if (allPermissionsGranted) Color.Green else Color.Red
                    )
                }
            }
        }
    }
}
