# 学习监督项目音视频模块实施指南

## 🎯 实施概述

基于华为云OBS SDK和预签名URL直传技术的完整音视频模块实施指南，包含设备端、云平台、应用端的详细实施步骤。

## 📋 实施前准备

### **1. 华为云资源准备**

**必需的云服务**：
- ✅ 华为云IoT设备接入服务 (IoTDA)
- ✅ 对象存储服务 (OBS)
- ✅ 媒体处理服务 (MPS)
- ✅ 统一身份认证服务 (IAM)

**账号权限配置**：
```bash
# 创建IAM用户和权限策略
# 1. 设备端上传权限
{
  "Version": "1.1",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": ["obs:object:PutObject"],
      "Resource": ["obs:*:*:bucket:learning-supervision-obs/*"]
    }
  ]
}

# 2. 应用端访问权限
{
  "Version": "1.1", 
  "Statement": [
    {
      "Effect": "Allow",
      "Action": ["obs:object:GetObject", "obs:bucket:ListBucket"],
      "Resource": ["obs:*:*:bucket:learning-supervision-obs/*"]
    }
  ]
}
```

### **2. 开发环境准备**

**设备端开发环境**：
- STM32CubeIDE 或 Keil MDK
- ARM GCC工具链
- L610模组开发板
- USB摄像头和I2S麦克风

**应用端开发环境**：
- Android Studio Arctic Fox或更高版本
- JDK 11或更高版本
- Android SDK API 24+
- 华为云OBS Android SDK

## 🚀 第一阶段：云平台配置

### **步骤1：创建OBS存储桶**

```bash
# 使用华为云CLI创建OBS桶
hcloud obs mb obs://learning-supervision-obs --region cn-north-4

# 配置桶策略
hcloud obs put-bucket-policy obs://learning-supervision-obs --policy-file bucket-policy.json

# 启用版本控制
hcloud obs put-bucket-versioning obs://learning-supervision-obs --versioning-configuration Status=Enabled
```

### **步骤2：配置IoT设备接入**

```bash
# 创建产品
hcloud iotda create-product \
  --product-name "LearningSupervisionDevice" \
  --device-type "LearningSupervisionDevice" \
  --protocol-type "MQTT" \
  --data-format "json"

# 注册设备
hcloud iotda create-device \
  --device-id "device001" \
  --node-id "device001" \
  --product-id "your-product-id" \
  --device-name "学习监督设备001"
```

### **步骤3：配置MPS媒体处理**

```json
// 创建视频处理模板
{
  "template_name": "video_merge_template",
  "template_type": "merge",
  "input_config": {
    "source_type": "obs",
    "bucket": "learning-supervision-obs",
    "path_pattern": "video/{deviceId}/raw/{date}/{sessionId}/segment_*.mp4"
  },
  "output_config": {
    "target_type": "obs", 
    "bucket": "learning-supervision-obs",
    "path": "video/{deviceId}/processed/{date}/complete_{sessionId}.mp4"
  },
  "merge_config": {
    "sort_by": "filename",
    "order": "ascending"
  }
}
```

## 🔧 第二阶段：设备端开发

### **步骤1：硬件连接**

```c
// 硬件引脚定义
#define CAMERA_USB_PORT         USB_OTG_HS
#define MICROPHONE_I2S_PORT     I2S2
#define L610_UART_PORT          UART4
#define L610_POWER_PIN          GPIO_PIN_12
#define L610_RESET_PIN          GPIO_PIN_13

// 硬件初始化
int hardware_init() {
    // 初始化USB摄像头
    if (usb_camera_init(CAMERA_USB_PORT) != 0) {
        return -1;
    }
    
    // 初始化I2S麦克风
    if (i2s_microphone_init(MICROPHONE_I2S_PORT) != 0) {
        return -1;
    }
    
    // 初始化L610模组
    if (l610_module_init(L610_UART_PORT, L610_POWER_PIN, L610_RESET_PIN) != 0) {
        return -1;
    }
    
    return 0;
}
```

### **步骤2：实现核心功能模块**

**视频录制模块**：
```c
// 实现video_recorder.c
#include "video_recorder.h"

int start_video_recording(const char* session_id, uint32_t duration) {
    // 1. 初始化H.264编码器
    h264_encoder_config_t config = {
        .width = 1280,
        .height = 720,
        .fps = 30,
        .bitrate = 1500000,
        .gop_size = 30,
        .profile = H264_PROFILE_MAIN
    };
    
    if (h264_encoder_init(&config) != 0) {
        return -1;
    }
    
    // 2. 开始分片录制
    uint32_t segment_duration = 10; // 10秒分片
    uint32_t total_segments = duration / segment_duration;
    
    for (uint32_t i = 0; i < total_segments; i++) {
        char segment_id[64];
        sprintf(segment_id, "%s_segment_%03d", session_id, i);
        
        if (record_video_segment(segment_id, segment_duration) != 0) {
            LOG_ERROR("Failed to record segment: %s", segment_id);
            continue;
        }
        
        // 3. 上传分片
        if (upload_video_segment(segment_id) != 0) {
            LOG_ERROR("Failed to upload segment: %s", segment_id);
            // 加入重试队列
            add_to_retry_queue(segment_id);
        }
    }
    
    // 4. 清理资源
    h264_encoder_deinit();
    
    return 0;
}
```

### **步骤3：集成OBS上传**

```c
// 实现obs_uploader.c
#include "obs_uploader.h"

int upload_file_to_obs(const char* local_file_path, const char* object_key) {
    // 1. 请求预签名URL
    obs_upload_request_t request;
    strcpy(request.file_name, object_key);
    request.file_size = get_file_size(local_file_path);
    strcpy(request.content_type, get_content_type(local_file_path));
    request.expires = 3600;
    
    obs_upload_response_t response;
    if (obs_request_upload_url(&request, &response) != 0) {
        LOG_ERROR("Failed to get upload URL for: %s", object_key);
        return -1;
    }
    
    // 2. 执行HTTP PUT上传
    if (http_put_file(response.upload_url, local_file_path) != 0) {
        LOG_ERROR("Failed to upload file: %s", local_file_path);
        return -1;
    }
    
    // 3. 验证上传结果
    char local_hash[64], remote_hash[64];
    calculate_file_md5(local_file_path, local_hash);
    
    if (get_obs_object_hash(object_key, remote_hash) == 0) {
        if (strcmp(local_hash, remote_hash) == 0) {
            LOG_INFO("File uploaded successfully: %s", object_key);
            return 0;
        } else {
            LOG_ERROR("Hash mismatch for uploaded file: %s", object_key);
            return -1;
        }
    }
    
    return 0;
}
```

## 📱 第三阶段：Android应用开发

### **步骤1：项目初始化**

```bash
# 创建Android项目
android create project \
  --name "LearningSupervisionApp" \
  --package "com.learningsupervision.iot" \
  --activity "MainActivity" \
  --target android-30

# 添加依赖
echo 'implementation files("libs/esdk-obs-android-3.21.8.jar")' >> app/build.gradle
echo 'implementation "com.google.android.exoplayer:exoplayer:2.18.7"' >> app/build.gradle
```

### **步骤2：集成OBS SDK**

```kotlin
// 在Application类中初始化
class LearningSupervisionApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        
        // 初始化OBS管理器
        ObsManager.getInstance().initialize()
        
        // 初始化其他组件
        initializeComponents()
    }
    
    private fun initializeComponents() {
        // 初始化数据库
        MediaDatabase.getInstance(this)
        
        // 初始化网络组件
        NetworkManager.initialize()
        
        // 初始化性能监控
        AppPerformanceMonitor().startMonitoring()
    }
}
```

### **步骤3：实现核心功能**

**媒体列表界面**：
```kotlin
class VideoListFragment : Fragment() {
    private lateinit var binding: FragmentVideoListBinding
    private lateinit var videoAdapter: VideoListAdapter
    private lateinit var viewModel: VideoViewModel
    
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = FragmentVideoListBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupRecyclerView()
        setupViewModel()
        observeData()
    }
    
    private fun setupRecyclerView() {
        videoAdapter = VideoListAdapter { videoSession ->
            // 播放视频
            findNavController().navigate(
                VideoListFragmentDirections.actionToVideoPlayer(videoSession.id)
            )
        }
        
        binding.videoRecyclerView.apply {
            adapter = videoAdapter
            layoutManager = LinearLayoutManager(context)
        }
    }
    
    private fun setupViewModel() {
        viewModel = ViewModelProvider(this)[VideoViewModel::class.java]
        
        // 加载视频列表
        val deviceId = arguments?.getString("deviceId") ?: ""
        val date = arguments?.getString("date") ?: ""
        viewModel.loadVideoSessions(deviceId, date)
    }
    
    private fun observeData() {
        viewModel.videoSessions.observe(viewLifecycleOwner) { sessions ->
            videoAdapter.submitList(sessions)
        }
        
        viewModel.loading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.isVisible = isLoading
        }
        
        viewModel.error.observe(viewLifecycleOwner) { error ->
            if (error != null) {
                Snackbar.make(binding.root, error, Snackbar.LENGTH_LONG).show()
            }
        }
    }
}
```

## 🧪 第四阶段：测试验证

### **步骤1：单元测试**

```bash
# 运行设备端测试
cd device_firmware
make test
./test_runner

# 运行Android单元测试
cd android_app
./gradlew testDebugUnitTest

# 运行Android UI测试
./gradlew connectedDebugAndroidTest
```

### **步骤2：集成测试**

```bash
# 端到端测试脚本
#!/bin/bash

echo "Starting end-to-end test..."

# 1. 启动设备端录制
echo "Starting device recording..."
device_test_client start_recording --duration 60 --device-id test_device_001

# 2. 等待上传完成
echo "Waiting for upload completion..."
sleep 120

# 3. 验证云端文件
echo "Verifying cloud files..."
obs_test_client list_objects --bucket learning-supervision-obs --prefix video/test_device_001/

# 4. 测试应用端下载
echo "Testing app download..."
android_test_client download_video --device-id test_device_001 --session-id test_session_001

# 5. 测试播放功能
echo "Testing playback..."
android_test_client play_video --file test_session_001.mp4

echo "End-to-end test completed!"
```

## 🚀 第五阶段：部署上线

### **步骤1：生产环境配置**

```bash
# 部署生产环境配置
# 1. 更新设备端配置
scp production_config.json device:/etc/learning_supervision/
ssh device "systemctl restart learning_supervision"

# 2. 发布Android应用
./gradlew assembleRelease
# 上传到应用商店或内部分发平台

# 3. 配置监控告警
hcloud ces create-alarm \
  --alarm-name "device_offline_alarm" \
  --metric-name "device_status" \
  --threshold 0 \
  --comparison-operator LessThanThreshold
```

### **步骤2：监控部署**

```yaml
# monitoring/docker-compose.yml
version: '3.8'
services:
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana-storage:/var/lib/grafana

volumes:
  grafana-storage:
```

## 📊 成功指标

### **技术指标**
- ✅ 视频上传成功率 > 95%
- ✅ 音频上传成功率 > 98%
- ✅ 应用启动时间 < 3秒
- ✅ 视频播放延迟 < 2秒
- ✅ 设备端CPU使用率 < 70%
- ✅ 应用端内存使用 < 100MB

### **业务指标**
- ✅ 用户日活跃度 > 80%
- ✅ 功能使用率 > 60%
- ✅ 用户满意度 > 4.0/5.0
- ✅ 崩溃率 < 1%

## 🔧 故障排除

### **常见问题解决**

**1. 设备端上传失败**
```bash
# 检查网络连接
ping obs.cn-north-4.myhuaweicloud.com

# 检查设备证书
cat /etc/learning_supervision/device_cert.pem

# 查看上传日志
tail -f /var/log/learning_supervision/upload.log
```

**2. 应用端播放问题**
```bash
# 检查OBS访问权限
adb logcat | grep "ObsManager"

# 验证网络连接
adb shell ping obs.cn-north-4.myhuaweicloud.com

# 清除应用缓存
adb shell pm clear com.learningsupervision.iot
```

---

**实施完成后，您将拥有一个完整的基于华为云OBS的音视频监督系统！**
