package com.example.iotandroidv20.obs

import android.content.Context
import com.example.iotandroidv20.utils.Logger
import com.example.iotandroidv20.config.HuaweiCloudConfig
import com.example.iotandroidv20.iot.TokenIoTManager
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ConcurrentHashMap
import java.io.File
import kotlin.time.Duration.Companion.seconds

/**
 * 超高频视频管理器 - 3秒方案
 * 提供准直播级别的视频监控体验
 */
class UltraHighFrequencyVideoManager private constructor() {
    
    private val obsApiClient = HuaweiObsApiClient.getInstance()
    private val segmentQueue = SegmentQueueManager()
    
    // 状态管理
    private val _videoStatus = MutableStateFlow(VideoStatus.IDLE)
    val videoStatus: StateFlow<VideoStatus> = _videoStatus.asStateFlow()
    
    private val _currentSegment = MutableStateFlow<VideoSegment?>(null)
    val currentSegment: StateFlow<VideoSegment?> = _currentSegment.asStateFlow()
    
    private val _playbackState = MutableStateFlow(PlaybackState.IDLE)
    val playbackState: StateFlow<PlaybackState> = _playbackState.asStateFlow()
    
    private val _connectionQuality = MutableStateFlow(ConnectionQuality.UNKNOWN)
    val connectionQuality: StateFlow<ConnectionQuality> = _connectionQuality.asStateFlow()
    
    // 轮询控制
    private var pollingJob: Job? = null
    private var lastSegmentTimestamp = 0L
    private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    companion object {
        private const val TAG = "UltraHighFrequencyVideoManager"
        private const val UPDATE_INTERVAL = 3000L // 3秒更新间隔
        private const val SEGMENT_DURATION = 3 // 3秒片段
        private const val MAX_WAIT_TIME = 10000L // 最大等待10秒
        private const val MAX_CONSECUTIVE_FAILURES = 5
        
        @Volatile
        private var INSTANCE: UltraHighFrequencyVideoManager? = null
        
        fun getInstance(): UltraHighFrequencyVideoManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: UltraHighFrequencyVideoManager().also { INSTANCE = it }
            }
        }
    }
    
    /**
     * 启动超高频视频监控
     * 7秒首次响应，每3秒更新
     */
    suspend fun startUltraHighFrequencyView(context: Context, deviceId: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Logger.d("启动超高频视频监控", tag = TAG)
                _videoStatus.value = VideoStatus.REQUESTING
                
                // 1. 发送IoT命令请求实时视频
                val iotManager = TokenIoTManager(context)
                val commandSuccess = sendInstantVideoCommand(iotManager, deviceId)
                
                if (!commandSuccess) {
                    _videoStatus.value = VideoStatus.ERROR
                    Logger.e("发送IoT命令失败", tag = TAG)
                    return@withContext false
                }
                
                _videoStatus.value = VideoStatus.PREPARING
                Logger.d("IoT命令发送成功，开始轮询", tag = TAG)
                
                // 2. 启动超高频轮询
                startUltraHighFrequencyPolling(deviceId)
                
                true
                
            } catch (e: Exception) {
                Logger.e("启动超高频视频失败: ${e.message}", tag = TAG)
                _videoStatus.value = VideoStatus.ERROR
                false
            }
        }
    }
    
    /**
     * 发送即时视频命令到设备
     */
    private suspend fun sendInstantVideoCommand(iotManager: TokenIoTManager, deviceId: String): Boolean {
        val commandParams = mapOf(
            "command_type" to "REQUEST_INSTANT_VIDEO",
            "segment_duration" to SEGMENT_DURATION,
            "quality" to "medium",
            "priority" to "high",
            "request_id" to System.currentTimeMillis().toString(),
            "timestamp" to System.currentTimeMillis()
        )
        
        return iotManager.sendCommandToDevice(
            commandName = "REQUEST_INSTANT_VIDEO",
            parameters = commandParams
        )
    }
    
    /**
     * 启动超高频轮询机制
     */
    private fun startUltraHighFrequencyPolling(deviceId: String) {
        pollingJob?.cancel()
        
        pollingJob = coroutineScope.launch {
            var consecutiveFailures = 0
            var firstSegmentFound = false
            
            Logger.d("开始超高频轮询，间隔${UPDATE_INTERVAL}ms", tag = TAG)
            
            while (isActive && consecutiveFailures < MAX_CONSECUTIVE_FAILURES) {
                try {
                    // 检查新的3秒片段
                    val newSegments = getNewSegments(deviceId, since = lastSegmentTimestamp)
                    
                    if (newSegments.isNotEmpty()) {
                        Logger.d("发现${newSegments.size}个新片段", tag = TAG)
                        
                        // 处理新片段
                        processNewSegments(newSegments)
                        consecutiveFailures = 0
                        
                        // 首次发现片段时更新状态
                        if (!firstSegmentFound) {
                            firstSegmentFound = true
                            _videoStatus.value = VideoStatus.PLAYING
                            Logger.d("首次片段发现，开始播放", tag = TAG)
                        }
                        
                    } else {
                        consecutiveFailures++
                        if (consecutiveFailures > 2) {
                            Logger.w("连续${consecutiveFailures}次未发现新片段", tag = TAG)
                        }
                    }
                    
                    // 等待下次轮询
                    delay(UPDATE_INTERVAL)
                    
                } catch (e: Exception) {
                    Logger.e("轮询失败: ${e.message}", tag = TAG)
                    consecutiveFailures++
                    delay(1000) // 错误后短暂等待
                }
            }
            
            if (consecutiveFailures >= MAX_CONSECUTIVE_FAILURES) {
                _videoStatus.value = VideoStatus.ERROR
                Logger.e("连续轮询失败，停止轮询", tag = TAG)
            }
        }
    }
    
    /**
     * 获取新的视频片段
     */
    private suspend fun getNewSegments(deviceId: String, since: Long): List<VideoSegment> {
        val prefix = "${HuaweiCloudConfig.OBS_VIDEO_PATH}$deviceId/live/"
        val result = obsApiClient.listObjects(prefix, maxKeys = 20)
        
        return when (result) {
            is ObsApiResult.Success -> {
                result.data
                    .filter { obj ->
                        obj.key.contains("3s_") && 
                        obj.lastModified > since &&
                        obj.key.endsWith(".mp4")
                    }
                    .map { obj ->
                        VideoSegment(
                            objectKey = obj.key,
                            url = buildOptimizedUrl(obj.key),
                            timestamp = obj.lastModified,
                            size = obj.size,
                            duration = SEGMENT_DURATION
                        )
                    }
                    .sortedBy { it.timestamp }
            }
            is ObsApiResult.Error -> {
                Logger.e("获取片段列表失败: ${result.message}", tag = TAG)
                emptyList()
            }
        }
    }
    
    /**
     * 处理新发现的片段
     */
    private suspend fun processNewSegments(segments: List<VideoSegment>) {
        segments.forEach { segment ->
            // 更新最新时间戳
            if (segment.timestamp > lastSegmentTimestamp) {
                lastSegmentTimestamp = segment.timestamp
            }
            
            // 添加到播放队列
            segmentQueue.addSegment(segment)
            
            // 预加载片段
            launch {
                segmentQueue.preloadSegment(segment)
            }
            
            // 更新当前片段信息
            _currentSegment.value = segment
            
            Logger.d("处理新片段: ${segment.objectKey.substringAfterLast("/")}", tag = TAG)
        }
    }
    
    /**
     * 构建优化的访问URL (CDN或OBS直连)
     */
    private fun buildOptimizedUrl(objectKey: String): String {
        // 优先使用CDN加速
        return if (isCDNAvailable()) {
            "https://cdn.yourdomain.com/$objectKey"
        } else {
            "https://${HuaweiCloudConfig.OBS_BUCKET_NAME}.obs.${HuaweiCloudConfig.OBS_REGION}.myhuaweicloud.com/$objectKey"
        }
    }
    
    /**
     * 检查CDN可用性
     */
    private fun isCDNAvailable(): Boolean {
        // TODO: 实现CDN可用性检查
        return false // 暂时使用OBS直连
    }
    
    /**
     * 获取下一个播放片段
     */
    fun getNextSegment(): VideoSegment? {
        return segmentQueue.getNextSegment()
    }
    
    /**
     * 获取预加载的片段文件
     */
    fun getPreloadedSegment(objectKey: String): File? {
        return segmentQueue.getPreloadedSegment(objectKey)
    }
    
    /**
     * 停止超高频视频监控
     */
    fun stopUltraHighFrequencyView() {
        Logger.d("停止超高频视频监控", tag = TAG)
        
        pollingJob?.cancel()
        pollingJob = null
        
        segmentQueue.clearQueue()
        
        _videoStatus.value = VideoStatus.IDLE
        _currentSegment.value = null
        _playbackState.value = PlaybackState.IDLE
        
        lastSegmentTimestamp = 0L
    }
    
    /**
     * 更新播放状态
     */
    fun updatePlaybackState(state: PlaybackState) {
        _playbackState.value = state
    }
    
    /**
     * 更新连接质量
     */
    fun updateConnectionQuality(quality: ConnectionQuality) {
        _connectionQuality.value = quality
    }
    
    /**
     * 获取系统状态报告
     */
    fun getStatusReport(): VideoSystemReport {
        return VideoSystemReport(
            videoStatus = _videoStatus.value,
            playbackState = _playbackState.value,
            connectionQuality = _connectionQuality.value,
            currentSegment = _currentSegment.value,
            queueSize = segmentQueue.getQueueSize(),
            cacheSize = segmentQueue.getCacheSize(),
            lastUpdateTime = lastSegmentTimestamp,
            uptime = System.currentTimeMillis() - lastSegmentTimestamp
        )
    }
}

/**
 * 视频片段数据类
 */
data class VideoSegment(
    val objectKey: String,
    val url: String,
    val timestamp: Long,
    val size: Long,
    val duration: Int
)

/**
 * 视频状态枚举
 */
enum class VideoStatus {
    IDLE,           // 空闲
    REQUESTING,     // 请求中
    PREPARING,      // 准备中
    PLAYING,        // 播放中
    ERROR,          // 错误
    TIMEOUT         // 超时
}

/**
 * 播放状态枚举
 */
enum class PlaybackState {
    IDLE,           // 空闲
    BUFFERING,      // 缓冲中
    READY,          // 准备就绪
    PLAYING,        // 播放中
    PAUSED,         // 暂停
    ENDED,          // 播放结束
    ERROR           // 错误
}

/**
 * 连接质量枚举
 */
enum class ConnectionQuality {
    UNKNOWN,        // 未知
    POOR,           // 差
    FAIR,           // 一般
    GOOD,           // 良好
    EXCELLENT       // 优秀
}

/**
 * 视频系统报告
 */
data class VideoSystemReport(
    val videoStatus: VideoStatus,
    val playbackState: PlaybackState,
    val connectionQuality: ConnectionQuality,
    val currentSegment: VideoSegment?,
    val queueSize: Int,
    val cacheSize: Long,
    val lastUpdateTime: Long,
    val uptime: Long
)
