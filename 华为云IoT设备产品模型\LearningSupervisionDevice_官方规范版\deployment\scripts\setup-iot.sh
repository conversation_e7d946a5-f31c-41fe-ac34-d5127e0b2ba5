#!/bin/bash

# 华为云IoT设备接入配置自动化脚本
# 用途：自动创建和配置学习监督项目的IoT产品和设备

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 配置变量
PRODUCT_NAME="LearningSupervisionDevice"
DEVICE_TYPE="LearningSupervisionDevice"
PROTOCOL_TYPE="MQTT"
DATA_FORMAT="json"
MANUFACTURER_NAME="智能学习监督科技"
INDUSTRY="教育"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查华为云CLI是否已安装和配置
check_hcloud_cli() {
    log_info "检查华为云CLI配置..."
    
    if ! command -v hcloud &> /dev/null; then
        log_error "华为云CLI未安装，请先安装华为云CLI"
        exit 1
    fi
    
    if ! hcloud configure list &> /dev/null; then
        log_error "华为云CLI未配置，请先运行 'hcloud configure' 配置认证信息"
        exit 1
    fi
    
    log_info "华为云CLI配置检查通过"
}

# 创建IoT产品
create_product() {
    log_info "创建IoT产品: $PRODUCT_NAME"
    
    local product_result
    if product_result=$(hcloud iotda create-product \
        --product-name "$PRODUCT_NAME" \
        --device-type "$DEVICE_TYPE" \
        --protocol-type "$PROTOCOL_TYPE" \
        --data-format "$DATA_FORMAT" \
        --manufacturer-name "$MANUFACTURER_NAME" \
        --industry "$INDUSTRY" \
        --description "儿童学习监督智能设备" \
        --output json 2>/dev/null); then
        
        local product_id=$(echo "$product_result" | jq -r '.product_id')
        echo "$product_id" > "$CONFIG_DIR/product_id.txt"
        log_info "✅ IoT产品创建成功，产品ID: $product_id"
        
    else
        log_warn "IoT产品可能已存在，尝试获取现有产品ID..."
        
        # 尝试获取现有产品ID
        local existing_products
        if existing_products=$(hcloud iotda list-products --output json 2>/dev/null); then
            local product_id=$(echo "$existing_products" | jq -r ".products[] | select(.product_name==\"$PRODUCT_NAME\") | .product_id")
            if [ -n "$product_id" ] && [ "$product_id" != "null" ]; then
                echo "$product_id" > "$CONFIG_DIR/product_id.txt"
                log_info "✅ 找到现有产品，产品ID: $product_id"
            else
                log_error "❌ 无法创建或找到IoT产品"
                exit 1
            fi
        else
            log_error "❌ 无法获取产品列表"
            exit 1
        fi
    fi
}

# 上传产品模型
upload_product_model() {
    log_info "准备上传产品模型..."
    
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local model_dir="$script_dir/../../"
    local product_id=$(cat "$CONFIG_DIR/product_id.txt")
    
    # 检查产品模型文件是否存在
    if [ ! -d "$model_dir/profile" ] || [ ! -d "$model_dir/service" ]; then
        log_error "产品模型文件不存在，请确保profile和service目录存在"
        exit 1
    fi
    
    # 创建产品模型压缩包
    local model_zip="$CONFIG_DIR/LearningSupervisionDevice_Model.zip"
    cd "$model_dir"
    zip -r "$model_zip" profile/ service/ > /dev/null 2>&1
    
    if [ -f "$model_zip" ]; then
        log_info "✅ 产品模型压缩包创建成功"
        
        # 上传产品模型
        if hcloud iotda import-device-model \
            --product-id "$product_id" \
            --model-file "$model_zip" 2>/dev/null; then
            log_info "✅ 产品模型上传成功"
        else
            log_warn "产品模型上传可能失败，请手动检查"
        fi
    else
        log_error "❌ 产品模型压缩包创建失败"
        exit 1
    fi
}

# 创建设备组
create_device_groups() {
    log_info "创建设备组..."
    
    local groups=(
        "LearningSupervisionDevices:学习监督设备组:STATIC"
        "Classroom_A:A教室设备组:STATIC"
        "Classroom_B:B教室设备组:STATIC"
        "Video_Devices:视频监控设备组:STATIC"
        "Audio_Devices:音频监控设备组:STATIC"
    )
    
    for group_info in "${groups[@]}"; do
        IFS=':' read -r group_name description group_type <<< "$group_info"
        
        if hcloud iotda create-device-group \
            --group-name "$group_name" \
            --description "$description" \
            --group-type "$group_type" 2>/dev/null; then
            log_info "✅ 创建设备组成功: $group_name"
        else
            log_warn "设备组可能已存在: $group_name"
        fi
    done
}

# 注册示例设备
register_sample_devices() {
    log_info "注册示例设备..."
    
    local product_id=$(cat "$CONFIG_DIR/product_id.txt")
    local devices_config="$CONFIG_DIR/sample_devices.json"
    
    # 创建示例设备配置
    cat > "$devices_config" << EOF
{
  "devices": [
    {
      "device_id": "LS_DEVICE_001",
      "node_id": "LS_DEVICE_001", 
      "device_name": "学习监督设备001",
      "description": "教室A座位1号设备",
      "tags": "location=classroom_a,type=video,version=v1.0"
    },
    {
      "device_id": "LS_DEVICE_002",
      "node_id": "LS_DEVICE_002",
      "device_name": "学习监督设备002", 
      "description": "教室A座位2号设备",
      "tags": "location=classroom_a,type=audio,version=v1.0"
    },
    {
      "device_id": "LS_DEVICE_003",
      "node_id": "LS_DEVICE_003",
      "device_name": "学习监督设备003",
      "description": "教室B座位1号设备", 
      "tags": "location=classroom_b,type=video,version=v1.0"
    }
  ]
}
EOF
    
    # 注册设备
    local device_credentials="$CONFIG_DIR/device_credentials.json"
    echo '{"devices":[]}' > "$device_credentials"
    
    local devices=$(jq -r '.devices[] | @base64' "$devices_config")
    for device_data in $devices; do
        local device_info=$(echo "$device_data" | base64 --decode)
        local device_id=$(echo "$device_info" | jq -r '.device_id')
        local node_id=$(echo "$device_info" | jq -r '.node_id')
        local device_name=$(echo "$device_info" | jq -r '.device_name')
        local description=$(echo "$device_info" | jq -r '.description')
        
        # 生成设备密钥
        local device_secret=$(openssl rand -hex 16)
        
        if device_result=$(hcloud iotda create-device \
            --device-id "$device_id" \
            --node-id "$node_id" \
            --product-id "$product_id" \
            --device-name "$device_name" \
            --description "$description" \
            --auth-type "SECRET" \
            --secret "$device_secret" \
            --output json 2>/dev/null); then
            
            log_info "✅ 设备注册成功: $device_id"
            
            # 保存设备凭据
            local credential="{\"device_id\":\"$device_id\",\"device_secret\":\"$device_secret\",\"mqtt_broker\":\"iot-mqtts.cn-north-4.myhuaweicloud.com\",\"mqtt_port\":8883}"
            jq ".devices += [$credential]" "$device_credentials" > "$device_credentials.tmp" && mv "$device_credentials.tmp" "$device_credentials"
            
        else
            log_warn "设备可能已存在: $device_id"
        fi
    done
    
    log_info "✅ 设备凭据已保存到: $device_credentials"
}

# 下载CA根证书
download_ca_certificate() {
    log_info "下载CA根证书..."
    
    local ca_cert_file="$CONFIG_DIR/GlobalSign_Root_CA.crt"
    
    if curl -s -o "$ca_cert_file" \
        "https://iot-mqtts.cn-north-4.myhuaweicloud.com/certificate/GlobalSign_Root_CA.crt"; then
        log_info "✅ CA根证书下载成功: $ca_cert_file"
    else
        log_error "❌ CA根证书下载失败"
        exit 1
    fi
}

# 创建MQTT连接测试脚本
create_mqtt_test_script() {
    log_info "创建MQTT连接测试脚本..."
    
    local test_script="$CONFIG_DIR/test_mqtt_connection.sh"
    
    cat > "$test_script" << 'EOF'
#!/bin/bash

# MQTT连接测试脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEVICE_CREDENTIALS="$SCRIPT_DIR/device_credentials.json"
CA_CERT="$SCRIPT_DIR/GlobalSign_Root_CA.crt"

if [ ! -f "$DEVICE_CREDENTIALS" ]; then
    echo "错误: 设备凭据文件不存在: $DEVICE_CREDENTIALS"
    exit 1
fi

if [ ! -f "$CA_CERT" ]; then
    echo "错误: CA证书文件不存在: $CA_CERT"
    exit 1
fi

# 检查mosquitto客户端
if ! command -v mosquitto_pub &> /dev/null; then
    echo "错误: mosquitto客户端未安装，请安装mosquitto-clients"
    exit 1
fi

# 获取第一个设备的凭据进行测试
DEVICE_ID=$(jq -r '.devices[0].device_id' "$DEVICE_CREDENTIALS")
DEVICE_SECRET=$(jq -r '.devices[0].device_secret' "$DEVICE_CREDENTIALS")
MQTT_BROKER=$(jq -r '.devices[0].mqtt_broker' "$DEVICE_CREDENTIALS")
MQTT_PORT=$(jq -r '.devices[0].mqtt_port' "$DEVICE_CREDENTIALS")

echo "测试设备: $DEVICE_ID"
echo "MQTT代理: $MQTT_BROKER:$MQTT_PORT"

# 构建测试消息
TEST_MESSAGE='{
  "services": [{
    "service_id": "DeviceManagement",
    "properties": {
      "deviceStatus": "ONLINE",
      "batteryLevel": 85,
      "signalStrength": -45
    },
    "event_time": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'"
  }]
}'

# 发送测试消息
echo "发送测试消息..."
if mosquitto_pub \
    -h "$MQTT_BROKER" \
    -p "$MQTT_PORT" \
    -i "$DEVICE_ID" \
    -u "$DEVICE_ID" \
    -P "$DEVICE_SECRET" \
    --cafile "$CA_CERT" \
    -t "\$oc/devices/$DEVICE_ID/sys/properties/report" \
    -m "$TEST_MESSAGE"; then
    echo "✅ MQTT连接测试成功"
else
    echo "❌ MQTT连接测试失败"
    exit 1
fi
EOF
    
    chmod +x "$test_script"
    log_info "✅ MQTT测试脚本创建成功: $test_script"
}

# 创建设备配置摘要
create_configuration_summary() {
    log_info "创建配置摘要..."
    
    local summary_file="$CONFIG_DIR/iot_configuration_summary.md"
    local product_id=$(cat "$CONFIG_DIR/product_id.txt")
    
    cat > "$summary_file" << EOF
# IoT设备接入配置摘要

## 产品信息
- **产品名称**: $PRODUCT_NAME
- **产品ID**: $product_id
- **设备类型**: $DEVICE_TYPE
- **协议类型**: $PROTOCOL_TYPE
- **数据格式**: $DATA_FORMAT

## MQTT连接信息
- **代理地址**: iot-mqtts.cn-north-4.myhuaweicloud.com
- **端口**: 8883
- **认证方式**: 设备证书认证
- **CA证书**: GlobalSign_Root_CA.crt

## 主要MQTT主题
- **数据上报**: \$oc/devices/{device_id}/sys/properties/report
- **命令下发**: \$oc/devices/{device_id}/sys/commands/#
- **命令响应**: \$oc/devices/{device_id}/sys/commands/response/request_id={request_id}
- **事件上报**: \$oc/devices/{device_id}/sys/events/up
- **OBS请求**: \$oc/devices/{device_id}/sys/obs/url/request
- **OBS响应**: \$oc/devices/{device_id}/sys/obs/url/response

## 设备凭据
设备凭据已保存到: device_credentials.json

## 测试方法
运行测试脚本: ./test_mqtt_connection.sh

## 注意事项
1. 请妥善保管设备凭据文件
2. 定期更新设备证书
3. 监控设备在线状态
4. 及时处理设备告警

---
配置时间: $(date)
EOF
    
    log_info "✅ 配置摘要已保存到: $summary_file"
}

# 主函数
main() {
    log_info "开始IoT设备接入配置..."
    
    # 创建配置目录
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    CONFIG_DIR="$script_dir/../configs"
    mkdir -p "$CONFIG_DIR"
    
    check_hcloud_cli
    create_product
    upload_product_model
    create_device_groups
    register_sample_devices
    download_ca_certificate
    create_mqtt_test_script
    create_configuration_summary
    
    log_info "🎉 IoT设备接入配置完成！"
    log_info "配置文件位置: $CONFIG_DIR"
    log_info "运行测试: cd $CONFIG_DIR && ./test_mqtt_connection.sh"
    log_warn "⚠️  请妥善保管设备凭据文件"
}

# 执行主函数
main "$@"
