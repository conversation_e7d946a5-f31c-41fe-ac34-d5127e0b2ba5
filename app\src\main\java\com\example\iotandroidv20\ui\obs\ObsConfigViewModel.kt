package com.example.iotandroidv20.ui.obs

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.iotandroidv20.obs.ObsConfig
import com.example.iotandroidv20.obs.ObsManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * OBS配置界面的ViewModel
 */
class ObsConfigViewModel : ViewModel() {
    
    private val obsConfig = ObsConfig.getInstance()
    private val obsManager = ObsManager.getInstance()
    
    private val _uiState = MutableStateFlow(ObsConfigUiState())
    val uiState: StateFlow<ObsConfigUiState> = _uiState.asStateFlow()
    
    /**
     * 加载当前配置
     */
    fun loadConfig() {
        _uiState.value = _uiState.value.copy(
            accessKey = obsConfig.getAccessKey(),
            secretKey = obsConfig.getSecretKey(),
            endpoint = obsConfig.getEndpoint(),
            bucketName = obsConfig.getBucketName(),
            region = obsConfig.getRegion(),
            isConfigured = obsConfig.isConfigured()
        )
        validateAll()
    }
    
    /**
     * 更新Access Key
     */
    fun updateAccessKey(accessKey: String) {
        _uiState.value = _uiState.value.copy(
            accessKey = accessKey,
            accessKeyError = ""
        )
        validateAccessKey()
        updateValidState()
    }
    
    /**
     * 更新Secret Key
     */
    fun updateSecretKey(secretKey: String) {
        _uiState.value = _uiState.value.copy(
            secretKey = secretKey,
            secretKeyError = ""
        )
        validateSecretKey()
        updateValidState()
    }
    
    /**
     * 更新端点
     */
    fun updateEndpoint(endpoint: String) {
        _uiState.value = _uiState.value.copy(
            endpoint = endpoint,
            endpointError = ""
        )
        validateEndpoint()
        updateValidState()
    }
    
    /**
     * 更新存储桶名称
     */
    fun updateBucketName(bucketName: String) {
        _uiState.value = _uiState.value.copy(
            bucketName = bucketName,
            bucketNameError = ""
        )
        validateBucketName()
        updateValidState()
    }
    
    /**
     * 更新区域
     */
    fun updateRegion(region: String) {
        _uiState.value = _uiState.value.copy(region = region)
    }
    
    /**
     * 验证Access Key
     */
    private fun validateAccessKey() {
        val accessKey = _uiState.value.accessKey
        val error = when {
            accessKey.isEmpty() -> "Access Key不能为空"
            !obsConfig.validateAccessKey(accessKey) -> "Access Key格式不正确（应为20位字母数字组合）"
            else -> ""
        }
        _uiState.value = _uiState.value.copy(accessKeyError = error)
    }
    
    /**
     * 验证Secret Key
     */
    private fun validateSecretKey() {
        val secretKey = _uiState.value.secretKey
        val error = when {
            secretKey.isEmpty() -> "Secret Key不能为空"
            !obsConfig.validateSecretKey(secretKey) -> "Secret Key格式不正确（应为40位字符）"
            else -> ""
        }
        _uiState.value = _uiState.value.copy(secretKeyError = error)
    }
    
    /**
     * 验证端点
     */
    private fun validateEndpoint() {
        val endpoint = _uiState.value.endpoint
        val error = when {
            endpoint.isEmpty() -> "端点地址不能为空"
            !obsConfig.validateEndpoint(endpoint) -> "端点地址格式不正确"
            else -> ""
        }
        _uiState.value = _uiState.value.copy(endpointError = error)
    }
    
    /**
     * 验证存储桶名称
     */
    private fun validateBucketName() {
        val bucketName = _uiState.value.bucketName
        val error = when {
            bucketName.isEmpty() -> "存储桶名称不能为空"
            !obsConfig.validateBucketName(bucketName) -> "存储桶名称格式不正确（3-63位小写字母、数字和连字符）"
            else -> ""
        }
        _uiState.value = _uiState.value.copy(bucketNameError = error)
    }
    
    /**
     * 验证所有字段
     */
    private fun validateAll() {
        validateAccessKey()
        validateSecretKey()
        validateEndpoint()
        validateBucketName()
        updateValidState()
    }
    
    /**
     * 更新验证状态
     */
    private fun updateValidState() {
        val currentState = _uiState.value
        val isValid = currentState.accessKeyError.isEmpty() &&
                     currentState.secretKeyError.isEmpty() &&
                     currentState.endpointError.isEmpty() &&
                     currentState.bucketNameError.isEmpty() &&
                     currentState.accessKey.isNotEmpty() &&
                     currentState.secretKey.isNotEmpty() &&
                     currentState.endpoint.isNotEmpty() &&
                     currentState.bucketName.isNotEmpty()
        
        _uiState.value = currentState.copy(isValid = isValid)
    }
    
    /**
     * 测试连接
     */
    fun testConnection() {
        if (!_uiState.value.isValid) {
            _uiState.value = _uiState.value.copy(
                message = "请先完善配置信息",
                isError = true
            )
            return
        }
        
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isLoading = true,
                message = "",
                isError = false
            )
            
            try {
                // 临时初始化OBS管理器进行测试
                obsManager.initialize(_uiState.value.accessKey, _uiState.value.secretKey)
                
                // 尝试获取可用日期列表来测试连接
                val dates = obsManager.getAvailableDates("TEST_DEVICE")
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    message = "连接测试成功！",
                    isError = false
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    message = "连接测试失败：${e.message}",
                    isError = true
                )
            }
        }
    }
    
    /**
     * 保存配置
     */
    fun saveConfig() {
        if (!_uiState.value.isValid) {
            _uiState.value = _uiState.value.copy(
                message = "请先完善配置信息",
                isError = true
            )
            return
        }
        
        try {
            val currentState = _uiState.value
            
            // 保存配置到SharedPreferences
            obsConfig.setAccessKey(currentState.accessKey)
            obsConfig.setSecretKey(currentState.secretKey)
            obsConfig.setEndpoint(currentState.endpoint)
            obsConfig.setBucketName(currentState.bucketName)
            obsConfig.setRegion(currentState.region)
            
            // 初始化OBS管理器
            obsManager.initialize(currentState.accessKey, currentState.secretKey)
            
            _uiState.value = currentState.copy(
                message = "配置保存成功！",
                isError = false,
                isConfigured = true
            )
            
        } catch (e: Exception) {
            _uiState.value = _uiState.value.copy(
                message = "配置保存失败：${e.message}",
                isError = true
            )
        }
    }
    
    /**
     * 重置为默认配置
     */
    fun resetToDefault() {
        obsConfig.setDefaultConfig()
        loadConfig()
        _uiState.value = _uiState.value.copy(
            message = "已重置为默认配置",
            isError = false
        )
    }
    
    /**
     * 清除消息
     */
    fun clearMessage() {
        _uiState.value = _uiState.value.copy(
            message = "",
            isError = false
        )
    }
}

/**
 * OBS配置界面的UI状态
 */
data class ObsConfigUiState(
    val accessKey: String = "",
    val secretKey: String = "",
    val endpoint: String = "",
    val bucketName: String = "",
    val region: String = "",
    val accessKeyError: String = "",
    val secretKeyError: String = "",
    val endpointError: String = "",
    val bucketNameError: String = "",
    val isValid: Boolean = false,
    val isLoading: Boolean = false,
    val isConfigured: Boolean = false,
    val message: String = "",
    val isError: Boolean = false
)
