&com.example.iotandroidv20.MainActivity<com.example.iotandroidv20.analysis.IntelligentAnalysisEngineFcom.example.iotandroidv20.analysis.IntelligentAnalysisEngine.Companion+com.example.iotandroidv20.auth.TokenManager5com.example.iotandroidv20.auth.TokenManager.Companion.com.example.iotandroidv20.camera.CameraManager8com.example.iotandroidv20.camera.CameraManager.Companion1com.example.iotandroidv20.camera.StreamConnection;com.example.iotandroidv20.camera.StreamConnection.Companion2com.example.iotandroidv20.config.HuaweiCloudConfig5com.example.iotandroidv20.eeg.AdvancedFatigueDetector?com.example.iotandroidv20.eeg.AdvancedFatigueDetector.Companion5com.example.iotandroidv20.eeg.AdvancedFocusCalculator?com.example.iotandroidv20.eeg.AdvancedFocusCalculator.Companion.com.example.iotandroidv20.eeg.DeviceEEGManager8com.example.iotandroidv20.eeg.DeviceEEGManager.Companion>com.example.iotandroidv20.eeg.DeviceEEGManager.DeviceEEGStatus1com.example.iotandroidv20.eeg.EEGAssessmentEngine;com.example.iotandroidv20.eeg.EEGAssessmentEngine.Companion(com.example.iotandroidv20.eeg.EEGManager2com.example.iotandroidv20.eeg.EEGManager.Companion0com.example.iotandroidv20.eeg.EEGSignalProcessor:com.example.iotandroidv20.eeg.EEGSignalProcessor.Companion8com.example.iotandroidv20.eeg.EEGSignalProcessor.ComplexGcom.example.iotandroidv20.eeg.EEGSignalProcessor.ChannelFrequencyResult2com.example.iotandroidv20.eeg.FatigueDetectionDemo<com.example.iotandroidv20.eeg.FatigueDetectionDemo.Companion-com.example.iotandroidv20.eeg.FatigueScenario4com.example.iotandroidv20.eeg.FatigueDetectionResult2com.example.iotandroidv20.eeg.FocusCalculationDemo<com.example.iotandroidv20.eeg.FocusCalculationDemo.Companion+com.example.iotandroidv20.eeg.FocusScenario4com.example.iotandroidv20.eeg.FocusCalculationResult9com.example.iotandroidv20.fusion.RealTimeDataFusionEngineCcom.example.iotandroidv20.fusion.RealTimeDataFusionEngine.CompanionCcom.example.iotandroidv20.fusion.RealTimeDataFusionEngine.DataCacheGcom.example.iotandroidv20.fusion.RealTimeDataFusionEngine.QualityReportDcom.example.iotandroidv20.fusion.RealTimeDataFusionEngine.SyncStatus7com.example.iotandroidv20.health.HealthAssessmentSystemAcom.example.iotandroidv20.health.HealthAssessmentSystem.Companion=<EMAIL>?com.example.iotandroidv20.iot.HuaweiCloudOkHttpClient.CompanionEcom.example.iotandroidv20.iot.HuaweiCloudOkHttpClient.ConnectionStateCcom.example.iotandroidv20.iot.HuaweiCloudOkHttpClient.NetworkStatus.com.example.iotandroidv20.iot.HuaweiIoTManager8com.example.iotandroidv20.iot.HuaweiIoTManager.Companion>com.example.iotandroidv20.iot.HuaweiIoTManager.ConnectionState+com.example.iotandroidv20.iot.MqttIoTClient5com.example.iotandroidv20.iot.MqttIoTClient.Companion;com.example.iotandroidv20.iot.MqttIoTClient.ConnectionState*com.example.iotandroidv20.iot.DeviceStatus-com.example.iotandroidv20.iot.TokenIoTManager7com.example.iotandroidv20.iot.TokenIoTManager.Companion0com.example.iotandroidv20.live.HuaweiLiveManager:com.example.iotandroidv20.live.HuaweiLiveManager.Companion?com.example.iotandroidv20.live.HuaweiLiveManager.LiveStreamInfoAcom.example.iotandroidv20.live.HuaweiLiveManager.LiveStreamStatus+com.example.iotandroidv20.model.AppSettings5com.example.iotandroidv20.model.AppSettings.Companion,com.example.iotandroidv20.model.EEGRawSignal4com.example.iotandroidv20.model.EEGFrequencyAnalysis/com.example.iotandroidv20.model.FocusAssessment1com.example.iotandroidv20.model.FatigueAssessment4com.example.iotandroidv20.model.EEGSessionStatistics-com.example.iotandroidv20.model.SignalQuality-com.example.iotandroidv20.model.BrainWaveType*com.example.iotandroidv20.model.FocusLevel,com.example.iotandroidv20.model.FatigueLevel2com.example.iotandroidv20.model.RestRecommendation.com.example.iotandroidv20.model.SessionQuality/com.example.iotandroidv20.model.EEGDeviceStatus)com.example.iotandroidv20.model.EEGConfig)com.example.iotandroidv20.model.FocusType0com.example.iotandroidv20.model.FocusIndexResult/com.example.iotandroidv20.model.FocusStatistics+com.example.iotandroidv20.model.FatigueType4com.example.iotandroidv20.model.EnvironmentalFactors2com.example.iotandroidv20.model.FatigueIndexResult1com.example.iotandroidv20.model.FatigueStatistics0com.example.iotandroidv20.model.LearningGuidance,com.example.iotandroidv20.model.ChildProfile-com.example.iotandroidv20.model.LearningStyle-com.example.iotandroidv20.model.LearningState3com.example.iotandroidv20.model.DetectedEnvironment0com.example.iotandroidv20.model.BrainwavePattern-com.example.iotandroidv20.model.TrendAnalysis%com.example.iotandroidv20.model.Trend:com.example.iotandroidv20.model.EnvironmentRecommendations6com.example.iotandroidv20.model.LightingRecommendation.com.example.iotandroidv20.model.LightingAction3com.example.iotandroidv20.model.NoiseRecommendation+com.example.iotandroidv20.model.NoiseAction9com.example.iotandroidv20.model.TemperatureRecommendation1com.example.iotandroidv20.model.TemperatureAction7com.example.iotandroidv20.model.WorkspaceRecommendation/com.example.iotandroidv20.model.WorkspaceAction7com.example.iotandroidv20.model.ScheduleRecommendations-com.example.iotandroidv20.model.SessionAdvice-com.example.iotandroidv20.model.SessionAction3com.example.iotandroidv20.model.BreakRecommendation)com.example.iotandroidv20.model.BreakType-com.example.iotandroidv20.model.BreakActivity3com.example.iotandroidv20.model.NextSessionPlanning(com.example.iotandroidv20.model.TaskType9com.example.iotandroidv20.model.DailyScheduleOptimization*com.example.iotandroidv20.model.TimeWindow,com.example.iotandroidv20.model.BreakPattern/com.example.iotandroidv20.model.ImmediateAction-com.example.iotandroidv20.model.ActionUrgency2com.example.iotandroidv20.model.LongTermSuggestion2com.example.iotandroidv20.model.SuggestionCategory6com.example.iotandroidv20.model.RecommendationPriority)com.example.iotandroidv20.model.TimeRange)com.example.iotandroidv20.model.DayOfWeek)com.example.iotandroidv20.model.TimeOfDay6com.example.iotandroidv20.model.EnvironmentPreferences3com.example.iotandroidv20.model.PersonalizedFactors:com.example.iotandroidv20.model.LearningSupervisionSession3com.example.iotandroidv20.model.LearningSessionType-com.example.iotandroidv20.model.SessionStatus/com.example.iotandroidv20.model.LearningMetrics.com.example.iotandroidv20.model.PostureMetrics,com.example.iotandroidv20.model.FocusMetrics.com.example.iotandroidv20.model.FatigueMetrics1com.example.iotandroidv20.model.EngagementMetrics2com.example.iotandroidv20.model.EnvironmentMetrics/com.example.iotandroidv20.model.ProgressMetrics,com.example.iotandroidv20.model.FocusPattern0com.example.iotandroidv20.model.SessionMilestone-com.example.iotandroidv20.model.MilestoneType5com.example.iotandroidv20.model.MilestoneSignificance,com.example.iotandroidv20.model.Intervention0com.example.iotandroidv20.model.InterventionType3com.example.iotandroidv20.model.InterventionTrigger,com.example.iotandroidv20.model.UserResponse.com.example.iotandroidv20.model.SessionSummary0com.example.iotandroidv20.model.PerformanceLevel9com.example.iotandroidv20.model.LearningSupervisionConfig0com.example.iotandroidv20.model.AlertSensitivity4com.example.iotandroidv20.model.InterventionSettings1com.example.iotandroidv20.model.ReportingSettings/com.example.iotandroidv20.model.PrivacySettings6com.example.iotandroidv20.model.RealTimeLearningStatus3com.example.iotandroidv20.model.ParentDashboardData,com.example.iotandroidv20.model.PostureIssue6com.example.iotandroidv20.model.MultiModalDataSnapshot1com.example.iotandroidv20.model.VideoAnalysisData1com.example.iotandroidv20.model.VoiceAnalysisData0com.example.iotandroidv20.model.DataFusionResult+com.example.iotandroidv20.model.DataQuality(com.example.iotandroidv20.model.HeadPose+com.example.iotandroidv20.model.PostureRisk.com.example.iotandroidv20.model.EmotionalState)com.example.iotandroidv20.model.RiskLevel-com.example.iotandroidv20.model.GazeDirection0com.example.iotandroidv20.model.FacialExpression-com.example.iotandroidv20.model.EmotionalTone,com.example.iotandroidv20.model.FusionMethod2com.example.iotandroidv20.model.ConflictResolution-com.example.iotandroidv20.model.FusionQuality,com.example.iotandroidv20.model.QualityLevel8com.example.iotandroidv20.model.MultiModalDataConverters9com.example.iotandroidv20.model.IntelligentAnalysisResult0com.example.iotandroidv20.model.AnomalyDetection'com.example.iotandroidv20.model.Anomaly2com.example.iotandroidv20.model.PatternRecognition/com.example.iotandroidv20.model.AnalysisPattern3com.example.iotandroidv20.model.CorrelationAnalysis2com.example.iotandroidv20.model.PredictionAnalysis.com.example.iotandroidv20.model.RiskAssessment:com.example.iotandroidv20.model.PersonalizedRecommendation.com.example.iotandroidv20.model.TrendDirection/com.example.iotandroidv20.model.AnomalySeverity+com.example.iotandroidv20.model.PatternType2com.example.iotandroidv20.model.RecommendationType+com.example.iotandroidv20.model.UserProfile0com.example.iotandroidv20.model.AssessmentConfig6com.example.iotandroidv20.model.HealthAssessmentResult2com.example.iotandroidv20.model.BasicHealthMetrics0com.example.iotandroidv20.model.CognitiveMetrics/com.example.iotandroidv20.model.PhysicalMetrics4com.example.iotandroidv20.model.EnvironmentalMetrics1com.example.iotandroidv20.model.BehavioralMetrics0com.example.iotandroidv20.model.HealthRiskFactor4com.example.iotandroidv20.model.HealthRecommendation6com.example.iotandroidv20.model.PersonalizedAssessment/com.example.iotandroidv20.model.AlertAssessment/com.example.iotandroidv20.model.ImprovementPlan/com.example.iotandroidv20.model.PersonalityType.com.example.iotandroidv20.model.AssessmentMode+com.example.iotandroidv20.model.HealthLevel.com.example.iotandroidv20.model.RiskFactorType,com.example.iotandroidv20.model.RiskSeverity*com.example.iotandroidv20.model.RiskImpact6com.example.iotandroidv20.model.RecommendationCategory(com.example.iotandroidv20.model.AgeGroup*com.example.iotandroidv20.model.AlertLevel*com.example.iotandroidv20.model.RiskFactor+com.example.iotandroidv20.model.PostureData,com.example.iotandroidv20.model.DeviceStatus(com.example.iotandroidv20.model.AppState*com.example.iotandroidv20.model.DailyStats.com.example.iotandroidv20.model.ReminderRecord,com.example.iotandroidv20.model.ReminderType.com.example.iotandroidv20.model.PostureQuality,com.example.iotandroidv20.model.UserSettings&com.example.iotandroidv20.model.Gender3com.example.iotandroidv20.model.EEGAnalysisSettings/com.example.iotandroidv20.model.FocusThresholds1com.example.iotandroidv20.model.FatigueThresholds0com.example.iotandroidv20.model.FrequencyWeights3com.example.iotandroidv20.model.EnvironmentSettings/com.example.iotandroidv20.model.EnvironmentType*com.example.iotandroidv20.model.NoiseLevel1com.example.iotandroidv20.model.LightingCondition2com.example.iotandroidv20.model.TemperatureComfort2com.example.iotandroidv20.model.MonitoringSettings9com.example.iotandroidv20.model.DurationAdjustmentFactors0com.example.iotandroidv20.model.ReminderSettings.com.example.iotandroidv20.model.ReminderMethod)com.example.iotandroidv20.model.VISStream0com.example.iotandroidv20.model.VISStreamAddress/com.example.iotandroidv20.model.VISSubscription.com.example.iotandroidv20.model.VISStreamStats-com.example.iotandroidv20.model.StreamQuality1com.example.iotandroidv20.model.VISPlaybackStatus)com.example.iotandroidv20.model.VISConfig.com.example.iotandroidv20.model.VISApiResponse5com.example.iotandroidv20.model.VISStreamListResponse8com.example.iotandroidv20.model.VISStreamAddressResponse/com.example.iotandroidv20.model.VideoStreamData,com.example.iotandroidv20.model.VideoQuality6com.example.iotandroidv20.model.VideoQuality.Companion/com.example.iotandroidv20.model.VideoResolution9com.example.iotandroidv20.model.VideoResolution.Companion,com.example.iotandroidv20.model.StreamStatus.com.example.iotandroidv20.model.VideoRecording,com.example.iotandroidv20.model.CameraConfig6com.example.iotandroidv20.model.CameraConfig.Companion,com.example.iotandroidv20.model.CameraStatus6com.example.iotandroidv20.model.VoiceRecognitionResult,com.example.iotandroidv20.model.VoiceCommand0com.example.iotandroidv20.model.VoiceCommandType6com.example.iotandroidv20.model.CommandExecutionStatus5com.example.iotandroidv20.model.VoiceSynthesisRequest)com.example.iotandroidv20.model.VoiceType3com.example.iotandroidv20.model.VoiceType.Companion+com.example.iotandroidv20.model.VoiceStatus,com.example.iotandroidv20.model.AudioQuality+com.example.iotandroidv20.model.VoiceConfig5com.example.iotandroidv20.model.VoiceConfig.Companion/com.example.iotandroidv20.model.VoiceStatistics.com.example.iotandroidv20.navigation.AppRoutes0com.example.iotandroidv20.navigation.MediaRoutes;com.example.iotandroidv20.navigation.MediaNavigationManagerEcom.example.iotandroidv20.navigation.MediaNavigationManager.Companion,com.example.iotandroidv20.obs.DownloadStatus*com.example.iotandroidv20.obs.VideoSession*com.example.iotandroidv20.obs.AudioSession+com.example.iotandroidv20.obs.MediaFileInfo.com.example.iotandroidv20.obs.DownloadProgress+com.example.iotandroidv20.obs.PlaybackState(com.example.iotandroidv20.obs.PlayerInfo'com.example.iotandroidv20.obs.ObsConfig1com.example.iotandroidv20.obs.ObsConfig.Companion(com.example.iotandroidv20.obs.ObsManager2com.example.iotandroidv20.obs.ObsManager.Companion3com.example.iotandroidv20.player.AudioPlayerManager=com.example.iotandroidv20.player.AudioPlayerManager.Companion>com.example.iotandroidv20.player.AudioPlayerManager.RepeatModeLcom.example.iotandroidv20.player.AudioPlayerManager.AudioPlayerEventListener3com.example.iotandroidv20.player.VideoPlayerManager=com.example.iotandroidv20.player.VideoPlayerManager.CompanionGcom.example.iotandroidv20.player.VideoPlayerManager.PlayerEventListener9com.example.iotandroidv20.recording.VideoRecordingManagerCcom.example.iotandroidv20.recording.VideoRecordingManager.CompanionIcom.example.iotandroidv20.recording.VideoRecordingManager.RecordingConfigScom.example.iotandroidv20.recording.VideoRecordingManager.RecordingConfig.CompanionIcom.example.iotandroidv20.recording.VideoRecordingManager.RecordingFormat4com.example.iotandroidv20.repository.MediaRepository>com.example.iotandroidv20.repository.MediaRepository.Companion=com.example.iotandroidv20.repository.MultiModalDataRepositoryGcom.example.iotandroidv20.repository.MultiModalDataRepository.Companion:<EMAIL>@com.example.iotandroidv20.supervision.LearningSupervisionManagerJcom.example.iotandroidv20.supervision.LearningSupervisionManager.Companion4com.example.iotandroidv20.ui.components.DashboardTab7com.example.iotandroidv20.ui.components.BrainwaveValues6com.example.iotandroidv20.ui.components.SpectrumValues8com.example.iotandroidv20.ui.components.SpectrumViewMode5com.example.iotandroidv20.ui.components.FrequencyBand2com.example.iotandroidv20.ui.components.EEGChannel1com.example.iotandroidv20.ui.components.TimeScale1com.example.iotandroidv20.ui.components.ErrorType1com.example.iotandroidv20.ui.components.TimeRange8com.example.iotandroidv20.ui.components.VideoSurfaceViewBcom.example.iotandroidv20.ui.components.VideoSurfaceView.Companion8com.example.iotandroidv20.ui.media.MediaBrowserViewModel6com.example.iotandroidv20.ui.media.MediaBrowserUiState,com.example.iotandroidv20.ui.media.MediaType5com.example.iotandroidv20.ui.media.MediaListViewModel3com.example.iotandroidv20.ui.media.MediaListUiState3com.example.iotandroidv20.ui.obs.ObsConfigViewModel1com.example.iotandroidv20.ui.obs.ObsConfigUiState8com.example.iotandroidv20.ui.player.AudioPlayerViewModel6com.example.iotandroidv20.ui.player.AudioPlayerUiState8com.example.iotandroidv20.ui.player.VideoPlayerViewModel6com.example.iotandroidv20.ui.player.VideoPlayerUiState&com.example.iotandroidv20.utils.Logger,com.example.iotandroidv20.utils.ErrorHandler.com.example.iotandroidv20.utils.MediaTestUtils6com.example.iotandroidv20.utils.PostureScoreCalculator,com.example.iotandroidv20.utils.PostureGrade1com.example.iotandroidv20.viewmodel.MainViewModel;com.example.iotandroidv20.viewmodel.MainViewModel.Companion.com.example.iotandroidv20.vis.HuaweiVISManager8com.example.iotandroidv20.vis.HuaweiVISManager.Companion8com.example.iotandroidv20.vis.HuaweiVISManager.VISStatus<com.example.iotandroidv20.vis.HuaweiVISManager.VISStreamInfo?com.example.iotandroidv20.vis.HuaweiVISManager.VISStreamAddress3com.example.iotandroidv20.vis.VoiceRecordingManager=<EMAIL><<EMAIL>,com.example.iotandroidv20.voice.VoiceManager6com.example.iotandroidv20.voice.VoiceManager.Companion                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              