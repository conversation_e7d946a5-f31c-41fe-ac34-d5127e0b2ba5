package com.example.iotandroidv20.obs

import android.content.Context
import com.example.iotandroidv20.utils.Logger
import com.example.iotandroidv20.config.HuaweiCloudConfig
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*

/**
 * OBS自动配置管理器
 * 为家长提供一键式OBS配置体验，无需手动输入技术参数
 */
class ObsAutoConfigManager private constructor() {
    
    private val obsApiClient = HuaweiObsApiClient.getInstance()
    
    private val _configStatus = MutableStateFlow(AutoConfigStatus.NOT_STARTED)
    val configStatus: StateFlow<AutoConfigStatus> = _configStatus.asStateFlow()
    
    private val _configProgress = MutableStateFlow(0f)
    val configProgress: StateFlow<Float> = _configProgress.asStateFlow()
    
    private val _configMessage = MutableStateFlow("")
    val configMessage: StateFlow<String> = _configMessage.asStateFlow()
    
    companion object {
        private const val TAG = "ObsAutoConfigManager"
        
        @Volatile
        private var INSTANCE: ObsAutoConfigManager? = null
        
        fun getInstance(): ObsAutoConfigManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ObsAutoConfigManager().also { INSTANCE = it }
            }
        }
        
        // 华为云OBS真实配置
        private const val OBS_ACCESS_KEY = "HPUAFQCTHCE7ZQ854RXI"
        private const val OBS_SECRET_KEY = "OtPzqzS5FOA3spl75ISujwv7OIfjQHCee38cAgsH"
        private const val OBS_ENDPOINT = "https://obs.cn-north-4.myhuaweicloud.com"
        private const val OBS_BUCKET_NAME = "iotdavideo"
        private const val OBS_REGION = "cn-north-4"
    }
    
    /**
     * 一键自动配置OBS
     * 为家长提供零技术门槛的配置体验
     */
    suspend fun autoConfigureOBS(context: Context): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Logger.d("开始OBS自动配置", tag = TAG)
                _configStatus.value = AutoConfigStatus.CONFIGURING
                _configMessage.value = "正在初始化配置..."
                _configProgress.value = 0.1f
                
                // 步骤1: 初始化OBS配置
                delay(500) // 模拟配置时间
                obsConfig.initialize(context)
                _configProgress.value = 0.2f
                _configMessage.value = "正在设置华为云连接..."
                
                // 步骤2: 设置真实的华为云参数
                delay(800)
                obsConfig.setAccessKey(OBS_ACCESS_KEY)
                obsConfig.setSecretKey(OBS_SECRET_KEY)
                obsConfig.setEndpoint(OBS_ENDPOINT)
                obsConfig.setBucketName(OBS_BUCKET_NAME)
                obsConfig.setRegion(OBS_REGION)
                _configProgress.value = 0.5f
                _configMessage.value = "正在验证云存储连接..."
                
                // 步骤3: 初始化OBS管理器
                delay(1000)
                obsManager.initialize(OBS_ACCESS_KEY, OBS_SECRET_KEY)
                _configProgress.value = 0.7f
                _configMessage.value = "正在测试连接..."
                
                // 步骤4: 测试连接
                delay(800)
                val testResult = testConnection()
                _configProgress.value = 0.9f
                
                if (testResult) {
                    _configProgress.value = 1.0f
                    _configMessage.value = "配置完成！云存储服务已就绪"
                    _configStatus.value = AutoConfigStatus.SUCCESS
                    Logger.d("OBS自动配置成功", tag = TAG)
                    true
                } else {
                    _configMessage.value = "连接测试失败，请检查网络"
                    _configStatus.value = AutoConfigStatus.FAILED
                    Logger.e("OBS连接测试失败", tag = TAG)
                    false
                }
                
            } catch (e: Exception) {
                Logger.e("OBS自动配置失败: ${e.message}", tag = TAG)
                _configMessage.value = "配置失败: ${e.message}"
                _configStatus.value = AutoConfigStatus.FAILED
                false
            }
        }
    }
    
    /**
     * 智能检测并修复配置问题
     */
    suspend fun smartDiagnoseAndFix(context: Context): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Logger.d("开始智能诊断", tag = TAG)
                _configStatus.value = AutoConfigStatus.DIAGNOSING
                _configMessage.value = "正在诊断配置问题..."
                
                // 检查配置完整性 - 使用HuaweiCloudConfig
                if (HuaweiCloudConfig.OBS_ACCESS_KEY.isEmpty() || HuaweiCloudConfig.OBS_SECRET_KEY.isEmpty()) {
                    _configMessage.value = "检测到配置缺失，正在自动修复..."
                    return@withContext autoConfigureOBS(context)
                }
                
                // 检查连接状态
                _configMessage.value = "正在检查云存储连接..."
                val connectionOk = testConnection()
                
                if (!connectionOk) {
                    _configMessage.value = "检测到连接问题，正在重新配置..."
                    return@withContext autoConfigureOBS(context)
                }
                
                _configMessage.value = "配置正常，无需修复"
                _configStatus.value = AutoConfigStatus.SUCCESS
                true
                
            } catch (e: Exception) {
                Logger.e("智能诊断失败: ${e.message}", tag = TAG)
                _configMessage.value = "诊断失败: ${e.message}"
                _configStatus.value = AutoConfigStatus.FAILED
                false
            }
        }
    }
    
    /**
     * 测试OBS连接
     * 使用华为云OBS API进行连接测试
     */
    private suspend fun testConnection(): Boolean {
        return try {
            Logger.d("开始测试OBS连接", tag = TAG)

            // 尝试列举桶内对象来测试连接
            val result = obsApiClient.listObjects("", 1)

            when (result) {
                is ObsApiResult.Success -> {
                    Logger.d("OBS连接测试成功", tag = TAG)
                    true
                }
                is ObsApiResult.Error -> {
                    Logger.e("OBS连接测试失败: ${result.message}", tag = TAG)
                    false
                }
            }
        } catch (e: Exception) {
            Logger.e("连接测试异常: ${e.message}", tag = TAG)
            false
        }
    }
    
    /**
     * 获取用户友好的配置状态描述
     */
    fun getStatusDescription(): String {
        return when (_configStatus.value) {
            AutoConfigStatus.NOT_STARTED -> "尚未配置云存储服务"
            AutoConfigStatus.CONFIGURING -> "正在配置中..."
            AutoConfigStatus.DIAGNOSING -> "正在诊断问题..."
            AutoConfigStatus.SUCCESS -> "云存储服务已就绪"
            AutoConfigStatus.FAILED -> "配置失败，需要重试"
        }
    }
    
    /**
     * 重置配置状态
     */
    fun resetStatus() {
        _configStatus.value = AutoConfigStatus.NOT_STARTED
        _configProgress.value = 0f
        _configMessage.value = ""
    }
    
    /**
     * 检查是否需要重新配置
     */
    fun needsReconfiguration(): Boolean {
        return !obsConfig.isConfigured() || _configStatus.value == AutoConfigStatus.FAILED
    }
}

/**
 * 自动配置状态
 */
enum class AutoConfigStatus {
    NOT_STARTED,    // 未开始
    CONFIGURING,    // 配置中
    DIAGNOSING,     // 诊断中
    SUCCESS,        // 成功
    FAILED          // 失败
}
