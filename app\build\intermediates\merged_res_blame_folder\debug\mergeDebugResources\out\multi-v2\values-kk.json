{"logs": [{"outputFile": "com.example.iotandroidv20.app-mergeDebugResources-62:/values-kk/values-kk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5dca3f3744b4fa46ea145e6d4fc9225c\\transformed\\media3-exoplayer-1.2.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,184,252,316,401,488,585,681", "endColumns": "64,63,67,63,84,86,96,95,77", "endOffsets": "115,179,247,311,396,483,580,676,754"}, "to": {"startLines": "57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4137,4202,4266,4334,4398,4483,4570,4667,4763", "endColumns": "64,63,67,63,84,86,96,95,77", "endOffsets": "4197,4261,4329,4393,4478,4565,4662,4758,4836"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\835843a1fca13b839a80c71cfb075e12\\transformed\\media3-session-1.2.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,128,206,276,344,426,510,600", "endColumns": "72,77,69,67,81,83,89,94", "endOffsets": "123,201,271,339,421,505,595,690"}, "to": {"startLines": "19,30,143,144,145,146,147,148", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "727,1800,12405,12475,12543,12625,12709,12799", "endColumns": "72,77,69,67,81,83,89,94", "endOffsets": "795,1873,12470,12538,12620,12704,12794,12889"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\31c73cb6dfb12faac49eb1bae4282a74\\transformed\\core-1.16.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,359,462,566,663,774", "endColumns": "99,101,101,102,103,96,110,100", "endOffsets": "150,252,354,457,561,658,769,870"}, "to": {"startLines": "20,21,22,23,24,25,26,157", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "800,900,1002,1104,1207,1311,1408,13531", "endColumns": "99,101,101,102,103,96,110,100", "endOffsets": "895,997,1099,1202,1306,1403,1514,13627"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\752167a3e67da85263f648420d161268\\transformed\\ui-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,386,488,575,656,749,839,921,1004,1089,1162,1236,1312,1386,1462,1532", "endColumns": "92,82,104,101,86,80,92,89,81,82,84,72,73,75,73,75,69,117", "endOffsets": "193,276,381,483,570,651,744,834,916,999,1084,1157,1231,1307,1381,1457,1527,1645"}, "to": {"startLines": "27,28,29,31,32,84,85,149,150,151,152,153,154,155,156,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1519,1612,1695,1878,1980,5975,6056,12894,12984,13066,13149,13234,13307,13381,13457,13632,13708,13778", "endColumns": "92,82,104,101,86,80,92,89,81,82,84,72,73,75,73,75,69,117", "endOffsets": "1607,1690,1795,1975,2062,6051,6144,12979,13061,13144,13229,13302,13376,13452,13526,13703,13773,13891"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\01b6e8b8fea8bb45b55852d4590f3437\\transformed\\foundation-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,147", "endColumns": "91,95", "endOffsets": "142,238"}, "to": {"startLines": "161,162", "startColumns": "4,4", "startOffsets": "13896,13988", "endColumns": "91,95", "endOffsets": "13983,14079"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2c60f935d2795008c652ca71073c0e75\\transformed\\media3-ui-1.2.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,483,677,757,837,924,1021,1118,1203,1268,1364,1461,1528,1593,1659,1729,1862,1994,2125,2201,2277,2351,2437,2526,2615,2681,2747,2800,2860,2908,2969,3029,3096,3161,3226,3289,3346,3418,3483,3549,3601,3662,3744,3826", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,79,79,86,96,96,84,64,95,96,66,64,65,69,132,131,130,75,75,73,85,88,88,65,65,52,59,47,60,59,66,64,64,62,56,71,64,65,51,60,81,81,54", "endOffsets": "281,478,672,752,832,919,1016,1113,1198,1263,1359,1456,1523,1588,1654,1724,1857,1989,2120,2196,2272,2346,2432,2521,2610,2676,2742,2795,2855,2903,2964,3024,3091,3156,3221,3284,3341,3413,3478,3544,3596,3657,3739,3821,3876"}, "to": {"startLines": "2,11,15,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,533,2067,2147,2227,2314,2411,2508,2593,2658,2754,2851,2918,2983,3049,3119,3252,3384,3515,3591,3667,3741,3827,3916,4005,4071,4841,4894,4954,5002,5063,5123,5190,5255,5320,5383,5440,5512,5577,5643,5695,5756,5838,5920", "endLines": "10,14,18,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83", "endColumns": "17,12,12,79,79,86,96,96,84,64,95,96,66,64,65,69,132,131,130,75,75,73,85,88,88,65,65,52,59,47,60,59,66,64,64,62,56,71,64,65,51,60,81,81,54", "endOffsets": "331,528,722,2142,2222,2309,2406,2503,2588,2653,2749,2846,2913,2978,3044,3114,3247,3379,3510,3586,3662,3736,3822,3911,4000,4066,4132,4889,4949,4997,5058,5118,5185,5250,5315,5378,5435,5507,5572,5638,5690,5751,5833,5915,5970"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\398f61ea23c76807315a7831064b1215\\transformed\\material3-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,291,406,522,624,725,843,981,1106,1231,1315,1418,1508,1605,1721,1845,1953,2095,2235,2367,2526,2649,2764,2883,2998,3089,3187,3310,3445,3549,3660,3766,3905,4050,4158,4258,4344,4437,4530,4638,4724,4808,4912,5001,5086,5187,5291,5388,5484,5571,5675,5774,5872,6009,6099,6210", "endColumns": "117,117,114,115,101,100,117,137,124,124,83,102,89,96,115,123,107,141,139,131,158,122,114,118,114,90,97,122,134,103,110,105,138,144,107,99,85,92,92,107,85,83,103,88,84,100,103,96,95,86,103,98,97,136,89,110,100", "endOffsets": "168,286,401,517,619,720,838,976,1101,1226,1310,1413,1503,1600,1716,1840,1948,2090,2230,2362,2521,2644,2759,2878,2993,3084,3182,3305,3440,3544,3655,3761,3900,4045,4153,4253,4339,4432,4525,4633,4719,4803,4907,4996,5081,5182,5286,5383,5479,5566,5670,5769,5867,6004,6094,6205,6306"}, "to": {"startLines": "86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6149,6267,6385,6500,6616,6718,6819,6937,7075,7200,7325,7409,7512,7602,7699,7815,7939,8047,8189,8329,8461,8620,8743,8858,8977,9092,9183,9281,9404,9539,9643,9754,9860,9999,10144,10252,10352,10438,10531,10624,10732,10818,10902,11006,11095,11180,11281,11385,11482,11578,11665,11769,11868,11966,12103,12193,12304", "endColumns": "117,117,114,115,101,100,117,137,124,124,83,102,89,96,115,123,107,141,139,131,158,122,114,118,114,90,97,122,134,103,110,105,138,144,107,99,85,92,92,107,85,83,103,88,84,100,103,96,95,86,103,98,97,136,89,110,100", "endOffsets": "6262,6380,6495,6611,6713,6814,6932,7070,7195,7320,7404,7507,7597,7694,7810,7934,8042,8184,8324,8456,8615,8738,8853,8972,9087,9178,9276,9399,9534,9638,9749,9855,9994,10139,10247,10347,10433,10526,10619,10727,10813,10897,11001,11090,11175,11276,11380,11477,11573,11660,11764,11863,11961,12098,12188,12299,12400"}}]}]}