<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.0" type="conditional_incidents">

    <incident
        id="DuplicatePlatformClasses"
        severity="fatal"
        message="`httpclient` defines classes that conflict with classes now provided by Android. Solutions include finding newer versions or alternative libraries that don&apos;t have the same problem (for example, for `httpclient` use `HttpUrlConnection` or `okhttp` instead), or repackaging the library using something like `jarjar`.">
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="76"
            column="21"
            startOffset="2182"
            endLine="76"
            endColumn="64"
            endOffset="2225"/>
    </incident>

    <incident
        id="DuplicatePlatformClasses"
        severity="fatal"
        message="`commons-logging` defines classes that conflict with classes now provided by Android. Solutions include finding newer versions or alternative libraries that don&apos;t have the same problem (for example, for `httpclient` use `HttpUrlConnection` or `okhttp` instead), or repackaging the library using something like `jarjar`.">
        <fix-replace
            description="Delete dependency"
            replacement=""
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="84"
            column="5"
            startOffset="2519"
            endLine="84"
            endColumn="58"
            endOffset="2572"/>
    </incident>

    <incident
        id="ScopedStorage"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="12"
            column="36"
            startOffset="538"
            endLine="12"
            endColumn="77"
            endOffset="579"/>
        <map>
            <entry
                name="maxSdkVersion"
                int="28"/>
            <entry
                name="read"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 31">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
                startOffset="1150"
                endOffset="1170"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="26"
            column="9"
            startOffset="1150"
            endLine="26"
            endColumn="29"
            endOffset="1170"/>
        <map>
            <condition minGE="31-∞"/>
        </map>
    </incident>

</incidents>
