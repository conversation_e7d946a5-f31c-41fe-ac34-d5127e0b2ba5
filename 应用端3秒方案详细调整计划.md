# 应用端3秒方案详细调整计划

## 🎯 调整目标

将Android应用端适配3秒超高频视频方案，实现准直播级别的用户体验：
- **首次响应**: 7秒
- **更新频率**: 每3秒新片段
- **无缝播放**: 片段间平滑切换
- **智能缓存**: 减少重复下载

## 📋 核心模块调整计划

### **1. RealTimeVideoManager 重构**

#### **A. 高频轮询机制**
```kotlin
class RealTimeVideoManager {
    private val updateInterval = 3.seconds        // 3秒更新间隔
    private val maxWaitTime = 10.seconds         // 最大等待时间
    private val segmentDuration = 3              // 片段时长
    
    private val _videoStatus = MutableStateFlow(VideoStatus.IDLE)
    val videoStatus: StateFlow<VideoStatus> = _videoStatus.asStateFlow()
    
    private val _currentSegment = MutableStateFlow<VideoSegment?>(null)
    val currentSegment: StateFlow<VideoSegment?> = _currentSegment.asStateFlow()
    
    private var pollingJob: Job? = null
    private var lastSegmentTimestamp = 0L
    
    suspend fun startUltraHighFrequencyView(deviceId: String): Boolean {
        try {
            _videoStatus.value = VideoStatus.REQUESTING
            
            // 1. 发送IoT命令请求实时视频
            val commandSuccess = sendInstantVideoCommand(deviceId)
            if (!commandSuccess) {
                _videoStatus.value = VideoStatus.ERROR
                return false
            }
            
            _videoStatus.value = VideoStatus.PREPARING
            
            // 2. 启动超高频轮询
            startUltraHighFrequencyPolling(deviceId)
            
            return true
            
        } catch (e: Exception) {
            Logger.e("启动超高频视频失败: ${e.message}")
            _videoStatus.value = VideoStatus.ERROR
            return false
        }
    }
    
    private fun startUltraHighFrequencyPolling(deviceId: String) {
        pollingJob = viewModelScope.launch {
            var consecutiveFailures = 0
            val maxFailures = 5
            
            while (isActive && consecutiveFailures < maxFailures) {
                try {
                    // 检查新的3秒片段
                    val newSegments = getNewSegments(deviceId, since = lastSegmentTimestamp)
                    
                    if (newSegments.isNotEmpty()) {
                        // 处理新片段
                        processNewSegments(newSegments)
                        consecutiveFailures = 0
                    } else {
                        consecutiveFailures++
                    }
                    
                    // 等待3秒后继续轮询
                    delay(updateInterval)
                    
                } catch (e: Exception) {
                    Logger.e("轮询失败: ${e.message}")
                    consecutiveFailures++
                    delay(1000) // 错误后短暂等待
                }
            }
            
            if (consecutiveFailures >= maxFailures) {
                _videoStatus.value = VideoStatus.ERROR
                Logger.e("连续轮询失败，停止轮询")
            }
        }
    }
    
    private suspend fun getNewSegments(deviceId: String, since: Long): List<VideoSegment> {
        val prefix = "video/$deviceId/live/"
        val result = obsApiClient.listObjects(prefix, maxKeys = 10)
        
        return when (result) {
            is ObsApiResult.Success -> {
                result.data
                    .filter { obj ->
                        obj.key.contains("3s_") && 
                        obj.lastModified > since &&
                        obj.key.endsWith(".mp4")
                    }
                    .map { obj ->
                        VideoSegment(
                            objectKey = obj.key,
                            url = buildCDNUrl(obj.key),
                            timestamp = obj.lastModified,
                            size = obj.size,
                            duration = 3
                        )
                    }
                    .sortedBy { it.timestamp }
            }
            is ObsApiResult.Error -> {
                Logger.e("获取片段列表失败: ${result.message}")
                emptyList()
            }
        }
    }
    
    private suspend fun processNewSegments(segments: List<VideoSegment>) {
        segments.forEach { segment ->
            // 更新最新时间戳
            if (segment.timestamp > lastSegmentTimestamp) {
                lastSegmentTimestamp = segment.timestamp
            }
            
            // 添加到播放队列
            segmentQueue.offer(segment)
            
            // 预加载片段
            preloadSegment(segment)
            
            Logger.d("发现新片段: ${segment.objectKey}")
        }
        
        // 如果当前没有播放，开始播放
        if (_videoStatus.value != VideoStatus.PLAYING) {
            startPlayback()
        }
    }
}
```

#### **B. 智能片段队列管理**
```kotlin
class SegmentQueueManager {
    private val segmentQueue = LinkedBlockingQueue<VideoSegment>(10)
    private val preloadedSegments = ConcurrentHashMap<String, File>()
    private val maxCacheSize = 20.MB
    
    fun addSegment(segment: VideoSegment) {
        // 如果队列满了，移除最老的片段
        if (segmentQueue.size >= 10) {
            val oldSegment = segmentQueue.poll()
            oldSegment?.let { cleanupSegment(it) }
        }
        
        segmentQueue.offer(segment)
    }
    
    fun getNextSegment(): VideoSegment? {
        return segmentQueue.poll()
    }
    
    suspend fun preloadSegment(segment: VideoSegment) {
        if (preloadedSegments.containsKey(segment.objectKey)) {
            return // 已经预加载
        }
        
        try {
            val localFile = downloadSegment(segment)
            preloadedSegments[segment.objectKey] = localFile
            
            // 管理缓存大小
            manageCacheSize()
            
            Logger.d("预加载完成: ${segment.objectKey}")
            
        } catch (e: Exception) {
            Logger.e("预加载失败: ${e.message}")
        }
    }
    
    fun getPreloadedSegment(objectKey: String): File? {
        return preloadedSegments[objectKey]
    }
    
    private fun manageCacheSize() {
        val totalSize = preloadedSegments.values.sumOf { it.length() }
        
        if (totalSize > maxCacheSize) {
            // 删除最老的缓存文件
            val sortedEntries = preloadedSegments.entries.sortedBy { 
                it.value.lastModified() 
            }
            
            var deletedSize = 0L
            val iterator = sortedEntries.iterator()
            
            while (iterator.hasNext() && totalSize - deletedSize > maxCacheSize * 0.8) {
                val entry = iterator.next()
                deletedSize += entry.value.length()
                entry.value.delete()
                preloadedSegments.remove(entry.key)
            }
        }
    }
}
```

### **2. 无缝播放器重构**

#### **A. 双播放器无缝切换**
```kotlin
class SeamlessVideoPlayer {
    private var primaryPlayer: ExoPlayer? = null
    private var secondaryPlayer: ExoPlayer? = null
    private var isUsingPrimary = true
    
    private val _playbackState = MutableStateFlow(PlaybackState.IDLE)
    val playbackState: StateFlow<PlaybackState> = _playbackState.asStateFlow()
    
    private val segmentQueue = SegmentQueueManager()
    private var playbackJob: Job? = null
    
    fun initializePlayers(context: Context) {
        primaryPlayer = createExoPlayer(context)
        secondaryPlayer = createExoPlayer(context)
    }
    
    private fun createExoPlayer(context: Context): ExoPlayer {
        return ExoPlayer.Builder(context)
            .setMediaSourceFactory(
                DefaultMediaSourceFactory(context)
                    .setLoadErrorHandlingPolicy(
                        DefaultLoadErrorHandlingPolicy(3) // 3次重试
                    )
            )
            .build().apply {
                // 配置播放器
                playWhenReady = true
                repeatMode = Player.REPEAT_MODE_OFF
                
                // 添加监听器
                addListener(object : Player.Listener {
                    override fun onPlaybackStateChanged(playbackState: Int) {
                        handlePlaybackStateChange(playbackState)
                    }
                    
                    override fun onPlayerError(error: PlaybackException) {
                        Logger.e("播放器错误: ${error.message}")
                        handlePlaybackError(error)
                    }
                })
            }
    }
    
    fun startSeamlessPlayback() {
        playbackJob = CoroutineScope(Dispatchers.Main).launch {
            while (isActive) {
                try {
                    val nextSegment = segmentQueue.getNextSegment()
                    
                    if (nextSegment != null) {
                        playSegmentSeamlessly(nextSegment)
                    } else {
                        // 等待新片段
                        delay(100)
                    }
                    
                } catch (e: Exception) {
                    Logger.e("无缝播放错误: ${e.message}")
                    delay(1000)
                }
            }
        }
    }
    
    private suspend fun playSegmentSeamlessly(segment: VideoSegment) {
        try {
            // 获取预加载的文件或下载
            val localFile = segmentQueue.getPreloadedSegment(segment.objectKey)
                ?: downloadSegment(segment)
            
            // 准备下一个播放器
            val nextPlayer = if (isUsingPrimary) secondaryPlayer else primaryPlayer
            val currentPlayer = if (isUsingPrimary) primaryPlayer else secondaryPlayer
            
            // 设置媒体源
            val mediaItem = MediaItem.fromUri(Uri.fromFile(localFile))
            nextPlayer?.setMediaItem(mediaItem)
            nextPlayer?.prepare()
            
            // 等待准备完成
            waitForPlayerReady(nextPlayer!!)
            
            // 无缝切换
            withContext(Dispatchers.Main) {
                nextPlayer.play()
                
                // 短暂延迟后停止当前播放器
                delay(50)
                currentPlayer?.pause()
                
                // 切换播放器
                isUsingPrimary = !isUsingPrimary
                
                // 更新UI
                _currentSegment.value = segment
                _playbackState.value = PlaybackState.PLAYING
            }
            
            Logger.d("无缝切换到片段: ${segment.objectKey}")
            
        } catch (e: Exception) {
            Logger.e("播放片段失败: ${e.message}")
        }
    }
    
    private suspend fun waitForPlayerReady(player: ExoPlayer, timeout: Long = 3000) {
        val startTime = System.currentTimeMillis()
        
        while (player.playbackState != Player.STATE_READY) {
            if (System.currentTimeMillis() - startTime > timeout) {
                throw Exception("播放器准备超时")
            }
            delay(50)
        }
    }
}
```

### **3. UI状态管理优化**

#### **A. 实时状态显示**
```kotlin
@Composable
fun UltraHighFrequencyVideoScreen(
    viewModel: RealTimeVideoViewModel
) {
    val videoStatus by viewModel.videoStatus.collectAsState()
    val currentSegment by viewModel.currentSegment.collectAsState()
    val playbackState by viewModel.playbackState.collectAsState()
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 状态指示器
        VideoStatusIndicator(
            status = videoStatus,
            playbackState = playbackState
        )
        
        // 视频播放器
        VideoPlayerView(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(4f / 3f),
            player = viewModel.getCurrentPlayer()
        )
        
        // 实时信息显示
        RealTimeInfoPanel(
            currentSegment = currentSegment,
            updateFrequency = "每3秒更新"
        )
        
        // 控制按钮
        VideoControlButtons(
            onStartClick = { viewModel.startUltraHighFrequencyView() },
            onStopClick = { viewModel.stopVideo() },
            onQualityChange = { quality -> viewModel.changeQuality(quality) }
        )
    }
}

@Composable
fun VideoStatusIndicator(
    status: VideoStatus,
    playbackState: PlaybackState
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        // 连接状态
        StatusChip(
            text = when (status) {
                VideoStatus.IDLE -> "待机"
                VideoStatus.REQUESTING -> "请求中..."
                VideoStatus.PREPARING -> "准备中..."
                VideoStatus.PLAYING -> "播放中"
                VideoStatus.ERROR -> "错误"
            },
            color = when (status) {
                VideoStatus.PLAYING -> Color.Green
                VideoStatus.ERROR -> Color.Red
                else -> Color.Orange
            }
        )
        
        // 播放状态
        StatusChip(
            text = when (playbackState) {
                PlaybackState.BUFFERING -> "缓冲中"
                PlaybackState.PLAYING -> "播放中"
                PlaybackState.PAUSED -> "暂停"
                else -> "准备中"
            },
            color = if (playbackState == PlaybackState.PLAYING) Color.Green else Color.Gray
        )
        
        // 更新频率指示
        StatusChip(
            text = "3秒更新",
            color = Color.Blue
        )
    }
}

@Composable
fun RealTimeInfoPanel(
    currentSegment: VideoSegment?,
    updateFrequency: String
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "实时视频信息",
                style = MaterialTheme.typography.h6
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            currentSegment?.let { segment ->
                InfoRow("当前片段", segment.objectKey.substringAfterLast("/"))
                InfoRow("片段时长", "${segment.duration}秒")
                InfoRow("文件大小", "${segment.size / 1024}KB")
                InfoRow("更新时间", formatTimestamp(segment.timestamp))
            }
            
            InfoRow("更新频率", updateFrequency)
        }
    }
}
```

#### **B. 进度指示器优化**
```kotlin
@Composable
fun UltraFastProgressIndicator(
    status: VideoStatus,
    estimatedTime: Int
) {
    when (status) {
        VideoStatus.REQUESTING -> {
            LinearProgressIndicator(
                progress = 0.3f,
                modifier = Modifier.fillMaxWidth()
            )
            Text("正在联系设备... (3秒)")
        }
        
        VideoStatus.PREPARING -> {
            LinearProgressIndicator(
                progress = 0.7f,
                modifier = Modifier.fillMaxWidth()
            )
            Text("设备准备视频... (2秒)")
        }
        
        VideoStatus.PLAYING -> {
            // 显示实时更新指示器
            PulsingIndicator(
                text = "实时更新中",
                interval = 3.seconds
            )
        }
    }
}

@Composable
fun PulsingIndicator(
    text: String,
    interval: Duration
) {
    val infiniteTransition = rememberInfiniteTransition()
    val alpha by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(interval.inWholeMilliseconds.toInt()),
            repeatMode = RepeatMode.Reverse
        )
    )
    
    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .size(8.dp)
                .background(
                    Color.Green.copy(alpha = alpha),
                    CircleShape
                )
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Text(
            text = text,
            color = Color.Green.copy(alpha = alpha)
        )
    }
}
```

### **4. 网络优化管理**

#### **A. 智能下载管理**
```kotlin
class SmartDownloadManager {
    private val downloadQueue = PriorityQueue<DownloadTask>(
        compareBy { it.priority }
    )
    private val activeDownloads = ConcurrentHashMap<String, Job>()
    private val maxConcurrentDownloads = 3
    
    suspend fun downloadSegment(
        segment: VideoSegment,
        priority: DownloadPriority = DownloadPriority.NORMAL
    ): File {
        val task = DownloadTask(
            segment = segment,
            priority = priority,
            timestamp = System.currentTimeMillis()
        )
        
        return if (priority == DownloadPriority.URGENT) {
            // 紧急下载，立即执行
            executeDownload(task)
        } else {
            // 加入队列
            downloadQueue.offer(task)
            processDownloadQueue()
            waitForDownload(task)
        }
    }
    
    private suspend fun executeDownload(task: DownloadTask): File {
        val segment = task.segment
        val localFile = File(cacheDir, segment.objectKey.substringAfterLast("/"))
        
        try {
            val request = Request.Builder()
                .url(segment.url)
                .build()
            
            val response = httpClient.newCall(request).execute()
            
            if (response.isSuccessful) {
                response.body?.byteStream()?.use { input ->
                    localFile.outputStream().use { output ->
                        input.copyTo(output)
                    }
                }
                
                Logger.d("下载完成: ${segment.objectKey}")
                return localFile
                
            } else {
                throw Exception("下载失败: ${response.code}")
            }
            
        } catch (e: Exception) {
            Logger.e("下载错误: ${e.message}")
            throw e
        }
    }
}

enum class DownloadPriority(val value: Int) {
    URGENT(1),    // 当前播放
    HIGH(2),      // 下一个片段
    NORMAL(3),    // 预加载
    LOW(4)        // 后台缓存
}
```

### **5. 流量监控与控制**

#### **A. 实时流量监控**
```kotlin
class TrafficMonitor {
    private val monthlyUsage = AtomicLong(0)
    private val dailyUsage = AtomicLong(0)
    private val sessionUsage = AtomicLong(0)
    
    fun recordDownload(bytes: Long) {
        monthlyUsage.addAndGet(bytes)
        dailyUsage.addAndGet(bytes)
        sessionUsage.addAndGet(bytes)
        
        checkUsageLimits()
    }
    
    private fun checkUsageLimits() {
        val monthly = monthlyUsage.get()
        val freeQuota = 5L * 1024 * 1024 * 1024 // 5GB
        
        when {
            monthly > freeQuota * 0.9 -> {
                // 接近限额，启用超级省流模式
                enableUltraDataSavingMode()
            }
            monthly > freeQuota * 0.7 -> {
                // 70%使用量，启用省流模式
                enableDataSavingMode()
            }
        }
    }
    
    private fun enableUltraDataSavingMode() {
        // 降低更新频率到10秒
        updateInterval = 10.seconds
        
        // 降低视频质量
        requestQualityChange(VideoQuality.LOW)
        
        // 通知用户
        showDataUsageWarning("流量使用接近限额，已自动降低质量")
    }
}
```

## 🎯 实施时间表

### **第1周：核心功能重构**
- [ ] RealTimeVideoManager 3秒轮询机制
- [ ] SegmentQueueManager 队列管理
- [ ] 基础无缝播放功能

### **第2周：播放器优化**
- [ ] 双播放器无缝切换
- [ ] 预加载机制
- [ ] 错误处理和恢复

### **第3周：UI和用户体验**
- [ ] 实时状态显示
- [ ] 进度指示器优化
- [ ] 用户控制界面

### **第4周：性能优化**
- [ ] 智能下载管理
- [ ] 流量监控控制
- [ ] 缓存策略优化

### **第5周：测试和调优**
- [ ] 端到端测试
- [ ] 性能调优
- [ ] 用户体验优化

## 🎉 预期效果

### **用户体验提升**
- **首次响应**: 7秒 (vs 原来55秒)
- **更新频率**: 每3秒 (vs 原来30秒)
- **播放流畅度**: 准直播级别
- **操作响应**: 即时反馈

### **技术指标**
- **延时减少**: 87% 改善
- **更新频率**: 10倍提升
- **播放连续性**: 无缝衔接
- **资源使用**: 优化控制

这个3秒方案将为用户提供真正的准直播监控体验！🌟
