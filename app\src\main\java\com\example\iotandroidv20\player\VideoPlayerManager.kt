package com.example.iotandroidv20.player

import android.content.Context
import android.net.Uri
import android.util.Log
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import androidx.media3.datasource.DefaultDataSourceFactory
import androidx.media3.datasource.DefaultHttpDataSource
import com.example.iotandroidv20.obs.PlaybackState
import com.example.iotandroidv20.obs.PlayerInfo
import com.example.iotandroidv20.obs.VideoSession
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 视频播放器管理器
 * 基于ExoPlayer实现视频播放功能，支持本地文件和网络流播放
 */
class VideoPlayerManager(private val context: Context) {
    
    companion object {
        private const val TAG = "VideoPlayerManager"
    }
    
    private var exoPlayer: ExoPlayer? = null
    private var currentVideoSession: VideoSession? = null
    
    // 播放器状态
    private val _playerInfo = MutableStateFlow(
        PlayerInfo(
            state = PlaybackState.IDLE,
            currentPosition = 0L,
            duration = 0L,
            bufferedPosition = 0L
        )
    )
    val playerInfo: StateFlow<PlayerInfo> = _playerInfo.asStateFlow()
    
    // 播放器事件监听器
    private val playerEventListeners = mutableListOf<PlayerEventListener>()
    
    /**
     * 播放器事件监听器接口
     */
    interface PlayerEventListener {
        fun onPlayerStateChanged(isPlaying: Boolean, playbackState: Int) {}
        fun onPlayerError(error: Exception) {}
        fun onPositionChanged(position: Long, duration: Long) {}
        fun onBufferingChanged(isBuffering: Boolean) {}
    }
    
    /**
     * 初始化播放器
     */
    fun initializePlayer() {
        if (exoPlayer == null) {
            Log.d(TAG, "Initializing ExoPlayer")
            
            // 创建数据源工厂
            val dataSourceFactory = DefaultDataSourceFactory(
                context,
                DefaultHttpDataSource.Factory()
                    .setUserAgent("LearningSupervisionApp/1.0")
                    .setConnectTimeoutMs(30000)
                    .setReadTimeoutMs(30000)
            )
            
            // 创建媒体源工厂
            val mediaSourceFactory = DefaultMediaSourceFactory(dataSourceFactory)
            
            // 创建ExoPlayer实例
            exoPlayer = ExoPlayer.Builder(context)
                .setMediaSourceFactory(mediaSourceFactory)
                .build()
                .apply {
                    // 添加播放器监听器
                    addListener(object : Player.Listener {
                        override fun onPlaybackStateChanged(playbackState: Int) {
                            updatePlayerState(playbackState)
                            notifyPlayerStateChanged(isPlaying, playbackState)
                        }
                        
                        override fun onIsPlayingChanged(isPlaying: Boolean) {
                            updatePlayerState(playbackState)
                            notifyPlayerStateChanged(isPlaying, playbackState)
                        }
                        
                        override fun onPlayerError(error: androidx.media3.common.PlaybackException) {
                            Log.e(TAG, "Player error: ${error.message}", error)
                            updatePlayerState(Player.STATE_IDLE)
                            notifyPlayerError(Exception(error.message, error))
                        }
                        
                        override fun onPositionDiscontinuity(
                            oldPosition: androidx.media3.common.Player.PositionInfo,
                            newPosition: androidx.media3.common.Player.PositionInfo,
                            reason: Int
                        ) {
                            updatePlayerInfo()
                        }
                    })
                }
            
            Log.d(TAG, "ExoPlayer initialized successfully")
        }
    }
    
    /**
     * 播放视频会话
     */
    fun playVideoSession(videoSession: VideoSession) {
        try {
            Log.d(TAG, "Playing video session: ${videoSession.sessionId}")
            
            initializePlayer()
            currentVideoSession = videoSession
            
            // 确定播放源
            val uri = if (videoSession.isDownloaded() && !videoSession.localFilePath.isNullOrEmpty()) {
                // 播放本地文件
                Log.d(TAG, "Playing local file: ${videoSession.localFilePath}")
                Uri.fromFile(java.io.File(videoSession.localFilePath))
            } else {
                // 播放网络流
                Log.d(TAG, "Playing network stream: ${videoSession.streamUrl}")
                Uri.parse(videoSession.streamUrl)
            }
            
            // 创建媒体项目
            val mediaItem = MediaItem.Builder()
                .setUri(uri)
                .setMediaId(videoSession.sessionId)
                .build()
            
            // 设置媒体项目并准备播放
            exoPlayer?.apply {
                setMediaItem(mediaItem)
                prepare()
                playWhenReady = true
            }
            
            Log.d(TAG, "Video session prepared for playback")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to play video session", e)
            notifyPlayerError(e)
        }
    }
    
    /**
     * 播放/暂停
     */
    fun togglePlayPause() {
        exoPlayer?.let { player ->
            if (player.isPlaying) {
                pause()
            } else {
                play()
            }
        }
    }
    
    /**
     * 播放
     */
    fun play() {
        exoPlayer?.playWhenReady = true
        Log.d(TAG, "Play requested")
    }
    
    /**
     * 暂停
     */
    fun pause() {
        exoPlayer?.playWhenReady = false
        Log.d(TAG, "Pause requested")
    }
    
    /**
     * 停止
     */
    fun stop() {
        exoPlayer?.stop()
        currentVideoSession = null
        Log.d(TAG, "Stop requested")
    }
    
    /**
     * 跳转到指定位置
     */
    fun seekTo(positionMs: Long) {
        exoPlayer?.seekTo(positionMs)
        Log.d(TAG, "Seek to: ${positionMs}ms")
    }
    
    /**
     * 设置播放速度
     */
    fun setPlaybackSpeed(speed: Float) {
        exoPlayer?.setPlaybackSpeed(speed)
        updatePlayerInfo()
        Log.d(TAG, "Playback speed set to: ${speed}x")
    }
    
    /**
     * 设置音量
     */
    fun setVolume(volume: Float) {
        exoPlayer?.volume = volume.coerceIn(0f, 1f)
        updatePlayerInfo()
        Log.d(TAG, "Volume set to: $volume")
    }
    
    /**
     * 获取ExoPlayer实例（用于UI组件）
     */
    fun getExoPlayer(): ExoPlayer? = exoPlayer
    
    /**
     * 获取当前播放的视频会话
     */
    fun getCurrentVideoSession(): VideoSession? = currentVideoSession
    
    /**
     * 添加播放器事件监听器
     */
    fun addPlayerListener(listener: PlayerEventListener) {
        playerEventListeners.add(listener)
    }
    
    /**
     * 移除播放器事件监听器
     */
    fun removePlayerListener(listener: PlayerEventListener) {
        playerEventListeners.remove(listener)
    }
    
    /**
     * 释放播放器资源
     */
    fun releasePlayer() {
        try {
            Log.d(TAG, "Releasing ExoPlayer")
            exoPlayer?.release()
            exoPlayer = null
            currentVideoSession = null
            playerEventListeners.clear()
            
            // 重置播放器状态
            _playerInfo.value = PlayerInfo(
                state = PlaybackState.IDLE,
                currentPosition = 0L,
                duration = 0L,
                bufferedPosition = 0L
            )
            
            Log.d(TAG, "ExoPlayer released successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to release ExoPlayer", e)
        }
    }
    
    /**
     * 更新播放器状态
     */
    private fun updatePlayerState(exoPlayerState: Int) {
        val playbackState = when (exoPlayerState) {
            Player.STATE_IDLE -> PlaybackState.IDLE
            Player.STATE_BUFFERING -> PlaybackState.PREPARING
            Player.STATE_READY -> if (exoPlayer?.isPlaying == true) PlaybackState.PLAYING else PlaybackState.PAUSED
            Player.STATE_ENDED -> PlaybackState.ENDED
            else -> PlaybackState.IDLE
        }
        
        updatePlayerInfo(playbackState)
    }
    
    /**
     * 更新播放器信息
     */
    private fun updatePlayerInfo(state: PlaybackState? = null) {
        exoPlayer?.let { player ->
            val currentState = state ?: _playerInfo.value.state
            val currentPosition = player.currentPosition
            val duration = player.duration.takeIf { it != androidx.media3.common.C.TIME_UNSET } ?: 0L
            val bufferedPosition = player.bufferedPosition
            val playbackSpeed = player.playbackParameters.speed
            val volume = player.volume
            
            _playerInfo.value = PlayerInfo(
                state = currentState,
                currentPosition = currentPosition,
                duration = duration,
                bufferedPosition = bufferedPosition,
                playbackSpeed = playbackSpeed,
                volume = volume
            )
            
            // 通知位置变化
            notifyPositionChanged(currentPosition, duration)
        }
    }
    
    /**
     * 通知播放器状态变化
     */
    private fun notifyPlayerStateChanged(isPlaying: Boolean, playbackState: Int) {
        playerEventListeners.forEach { listener ->
            try {
                listener.onPlayerStateChanged(isPlaying, playbackState)
            } catch (e: Exception) {
                Log.e(TAG, "Error in player state listener", e)
            }
        }
    }
    
    /**
     * 通知播放器错误
     */
    private fun notifyPlayerError(error: Exception) {
        playerEventListeners.forEach { listener ->
            try {
                listener.onPlayerError(error)
            } catch (e: Exception) {
                Log.e(TAG, "Error in player error listener", e)
            }
        }
    }
    
    /**
     * 通知位置变化
     */
    private fun notifyPositionChanged(position: Long, duration: Long) {
        playerEventListeners.forEach { listener ->
            try {
                listener.onPositionChanged(position, duration)
            } catch (e: Exception) {
                Log.e(TAG, "Error in position change listener", e)
            }
        }
    }
    
    /**
     * 开始定期更新播放器信息
     */
    fun startPeriodicUpdates() {
        // 这里可以添加定期更新逻辑，如果需要的话
        // 通常ExoPlayer的监听器已经足够处理状态更新
    }
    
    /**
     * 停止定期更新
     */
    fun stopPeriodicUpdates() {
        // 对应的停止逻辑
    }
}
