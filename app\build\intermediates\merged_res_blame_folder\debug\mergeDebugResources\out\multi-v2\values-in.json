{"logs": [{"outputFile": "com.example.iotandroidv20.app-mergeDebugResources-62:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\31c73cb6dfb12faac49eb1bae4282a74\\transformed\\core-1.16.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "20,21,22,23,24,25,26,157", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "766,861,963,1060,1157,1263,1381,13348", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "856,958,1055,1152,1258,1376,1491,13444"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\835843a1fca13b839a80c71cfb075e12\\transformed\\media3-session-1.2.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,202,269,336,409,483,573", "endColumns": "69,76,66,66,72,73,89,93", "endOffsets": "120,197,264,331,404,478,568,662"}, "to": {"startLines": "19,30,143,144,145,146,147,148", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "696,1766,12247,12314,12381,12454,12528,12618", "endColumns": "69,76,66,66,72,73,89,93", "endOffsets": "761,1838,12309,12376,12449,12523,12613,12707"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\398f61ea23c76807315a7831064b1215\\transformed\\material3-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,289,401,517,615,721,844,991,1114,1264,1351,1455,1548,1652,1770,1890,1999,2139,2277,2406,2584,2706,2826,2949,3072,3166,3267,3387,3520,3622,3729,3836,3978,4125,4234,4334,4410,4506,4601,4719,4808,4893,4992,5072,5155,5254,5353,5450,5550,5637,5740,5839,5943,6060,6140,6245", "endColumns": "118,114,111,115,97,105,122,146,122,149,86,103,92,103,117,119,108,139,137,128,177,121,119,122,122,93,100,119,132,101,106,106,141,146,108,99,75,95,94,117,88,84,98,79,82,98,98,96,99,86,102,98,103,116,79,104,94", "endOffsets": "169,284,396,512,610,716,839,986,1109,1259,1346,1450,1543,1647,1765,1885,1994,2134,2272,2401,2579,2701,2821,2944,3067,3161,3262,3382,3515,3617,3724,3831,3973,4120,4229,4329,4405,4501,4596,4714,4803,4888,4987,5067,5150,5249,5348,5445,5545,5632,5735,5834,5938,6055,6135,6240,6335"}, "to": {"startLines": "86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5962,6081,6196,6308,6424,6522,6628,6751,6898,7021,7171,7258,7362,7455,7559,7677,7797,7906,8046,8184,8313,8491,8613,8733,8856,8979,9073,9174,9294,9427,9529,9636,9743,9885,10032,10141,10241,10317,10413,10508,10626,10715,10800,10899,10979,11062,11161,11260,11357,11457,11544,11647,11746,11850,11967,12047,12152", "endColumns": "118,114,111,115,97,105,122,146,122,149,86,103,92,103,117,119,108,139,137,128,177,121,119,122,122,93,100,119,132,101,106,106,141,146,108,99,75,95,94,117,88,84,98,79,82,98,98,96,99,86,102,98,103,116,79,104,94", "endOffsets": "6076,6191,6303,6419,6517,6623,6746,6893,7016,7166,7253,7357,7450,7554,7672,7792,7901,8041,8179,8308,8486,8608,8728,8851,8974,9068,9169,9289,9422,9524,9631,9738,9880,10027,10136,10236,10312,10408,10503,10621,10710,10795,10894,10974,11057,11156,11255,11352,11452,11539,11642,11741,11845,11962,12042,12147,12242"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5dca3f3744b4fa46ea145e6d4fc9225c\\transformed\\media3-exoplayer-1.2.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,185,250,313,389,453,553,647", "endColumns": "67,61,64,62,75,63,99,93,68", "endOffsets": "118,180,245,308,384,448,548,642,711"}, "to": {"startLines": "57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4003,4071,4133,4198,4261,4337,4401,4501,4595", "endColumns": "67,61,64,62,75,63,99,93,68", "endOffsets": "4066,4128,4193,4256,4332,4396,4496,4590,4659"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\752167a3e67da85263f648420d161268\\transformed\\ui-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,277,375,475,561,644,735,822,907,989,1072,1144,1221,1298,1371,1449,1515", "endColumns": "89,81,97,99,85,82,90,86,84,81,82,71,76,76,72,77,65,118", "endOffsets": "190,272,370,470,556,639,730,817,902,984,1067,1139,1216,1293,1366,1444,1510,1629"}, "to": {"startLines": "27,28,29,31,32,84,85,149,150,151,152,153,154,155,156,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1496,1586,1668,1843,1943,5788,5871,12712,12799,12884,12966,13049,13121,13198,13275,13449,13527,13593", "endColumns": "89,81,97,99,85,82,90,86,84,81,82,71,76,76,72,77,65,118", "endOffsets": "1581,1663,1761,1938,2024,5866,5957,12794,12879,12961,13044,13116,13193,13270,13343,13522,13588,13707"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\01b6e8b8fea8bb45b55852d4590f3437\\transformed\\foundation-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,89", "endOffsets": "136,226"}, "to": {"startLines": "161,162", "startColumns": "4,4", "startOffsets": "13712,13798", "endColumns": "85,89", "endOffsets": "13793,13883"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2c60f935d2795008c652ca71073c0e75\\transformed\\media3-ui-1.2.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,472,646,729,814,890,978,1071,1148,1217,1313,1407,1471,1535,1601,1674,1789,1907,2023,2095,2175,2245,2319,2403,2489,2556,2620,2673,2731,2779,2840,2904,2966,3029,3095,3157,3220,3286,3350,3416,3468,3530,3606,3682", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,84,75,87,92,76,68,95,93,63,63,65,72,114,117,115,71,79,69,73,83,85,66,63,52,57,47,60,63,61,62,65,61,62,65,63,65,51,61,75,75,61", "endOffsets": "280,467,641,724,809,885,973,1066,1143,1212,1308,1402,1466,1530,1596,1669,1784,1902,2018,2090,2170,2240,2314,2398,2484,2551,2615,2668,2726,2774,2835,2899,2961,3024,3090,3152,3215,3281,3345,3411,3463,3525,3601,3677,3739"}, "to": {"startLines": "2,11,15,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,522,2029,2112,2197,2273,2361,2454,2531,2600,2696,2790,2854,2918,2984,3057,3172,3290,3406,3478,3558,3628,3702,3786,3872,3939,4664,4717,4775,4823,4884,4948,5010,5073,5139,5201,5264,5330,5394,5460,5512,5574,5650,5726", "endLines": "10,14,18,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83", "endColumns": "17,12,12,82,84,75,87,92,76,68,95,93,63,63,65,72,114,117,115,71,79,69,73,83,85,66,63,52,57,47,60,63,61,62,65,61,62,65,63,65,51,61,75,75,61", "endOffsets": "330,517,691,2107,2192,2268,2356,2449,2526,2595,2691,2785,2849,2913,2979,3052,3167,3285,3401,3473,3553,3623,3697,3781,3867,3934,3998,4712,4770,4818,4879,4943,5005,5068,5134,5196,5259,5325,5389,5455,5507,5569,5645,5721,5783"}}]}]}