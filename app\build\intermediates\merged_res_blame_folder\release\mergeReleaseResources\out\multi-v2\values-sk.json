{"logs": [{"outputFile": "com.example.iotandroidv20.app-mergeReleaseResources-58:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\752167a3e67da85263f648420d161268\\transformed\\ui-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,284,379,482,574,653,747,837,918,1001,1088,1160,1238,1314,1389,1467,1535", "endColumns": "94,83,94,102,91,78,93,89,80,82,86,71,77,75,74,77,67,113", "endOffsets": "195,279,374,477,569,648,742,832,913,996,1083,1155,1233,1309,1384,1462,1530,1644"}, "to": {"startLines": "31,32,33,35,36,88,89,153,154,155,156,157,158,159,160,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1798,1893,1977,2146,2249,6214,6293,13101,13191,13272,13355,13442,13514,13592,13668,13844,13922,13990", "endColumns": "94,83,94,102,91,78,93,89,80,82,86,71,77,75,74,77,67,113", "endOffsets": "1888,1972,2067,2244,2336,6288,6382,13186,13267,13350,13437,13509,13587,13663,13738,13917,13985,14099"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\835843a1fca13b839a80c71cfb075e12\\transformed\\media3-session-1.2.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,128,202,275,344,431,522,615", "endColumns": "72,73,72,68,86,90,92,105", "endOffsets": "123,197,270,339,426,517,610,716"}, "to": {"startLines": "23,34,147,148,149,150,151,152", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "988,2072,12582,12655,12724,12811,12902,12995", "endColumns": "72,73,72,68,86,90,92,105", "endOffsets": "1056,2141,12650,12719,12806,12897,12990,13096"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\31c73cb6dfb12faac49eb1bae4282a74\\transformed\\core-1.16.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "24,25,26,27,28,29,30,161", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1061,1157,1259,1360,1458,1568,1676,13743", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "1152,1254,1355,1453,1563,1671,1793,13839"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\01b6e8b8fea8bb45b55852d4590f3437\\transformed\\foundation-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,86", "endOffsets": "134,221"}, "to": {"startLines": "165,166", "startColumns": "4,4", "startOffsets": "14104,14188", "endColumns": "83,86", "endOffsets": "14183,14270"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2c60f935d2795008c652ca71073c0e75\\transformed\\media3-ui-1.2.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,624,938,1019,1099,1181,1284,1383,1462,1527,1618,1712,1782,1848,1913,1990,2112,2229,2350,2424,2506,2579,2661,2761,2860,2927,2992,3045,3103,3151,3212,3284,3358,3421,3494,3559,3619,3684,3748,3814,3866,3930,4008,4086", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,80,79,81,102,98,78,64,90,93,69,65,64,76,121,116,120,73,81,72,81,99,98,66,64,52,57,47,60,71,73,62,72,64,59,64,63,65,51,63,77,77,53", "endOffsets": "288,619,933,1014,1094,1176,1279,1378,1457,1522,1613,1707,1777,1843,1908,1985,2107,2224,2345,2419,2501,2574,2656,2756,2855,2922,2987,3040,3098,3146,3207,3279,3353,3416,3489,3554,3614,3679,3743,3809,3861,3925,4003,4081,4135"}, "to": {"startLines": "2,11,17,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,343,674,2341,2422,2502,2584,2687,2786,2865,2930,3021,3115,3185,3251,3316,3393,3515,3632,3753,3827,3909,3982,4064,4164,4263,4330,5066,5119,5177,5225,5286,5358,5432,5495,5568,5633,5693,5758,5822,5888,5940,6004,6082,6160", "endLines": "10,16,22,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "endColumns": "17,12,12,80,79,81,102,98,78,64,90,93,69,65,64,76,121,116,120,73,81,72,81,99,98,66,64,52,57,47,60,71,73,62,72,64,59,64,63,65,51,63,77,77,53", "endOffsets": "338,669,983,2417,2497,2579,2682,2781,2860,2925,3016,3110,3180,3246,3311,3388,3510,3627,3748,3822,3904,3977,4059,4159,4258,4325,4390,5114,5172,5220,5281,5353,5427,5490,5563,5628,5688,5753,5817,5883,5935,5999,6077,6155,6209"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5dca3f3744b4fa46ea145e6d4fc9225c\\transformed\\media3-exoplayer-1.2.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,194,258,329,406,480,564,646", "endColumns": "76,61,63,70,76,73,83,81,79", "endOffsets": "127,189,253,324,401,475,559,641,721"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4395,4472,4534,4598,4669,4746,4820,4904,4986", "endColumns": "76,61,63,70,76,73,83,81,79", "endOffsets": "4467,4529,4593,4664,4741,4815,4899,4981,5061"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\398f61ea23c76807315a7831064b1215\\transformed\\material3-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,285,395,510,608,703,815,950,1066,1218,1303,1404,1496,1593,1709,1831,1937,2070,2203,2337,2501,2629,2753,2883,3003,3096,3193,3314,3437,3535,3638,3747,3888,4037,4146,4246,4330,4424,4519,4635,4722,4809,4910,4990,5076,5173,5276,5369,5466,5554,5659,5756,5855,5975,6055,6157", "endColumns": "114,114,109,114,97,94,111,134,115,151,84,100,91,96,115,121,105,132,132,133,163,127,123,129,119,92,96,120,122,97,102,108,140,148,108,99,83,93,94,115,86,86,100,79,85,96,102,92,96,87,104,96,98,119,79,101,92", "endOffsets": "165,280,390,505,603,698,810,945,1061,1213,1298,1399,1491,1588,1704,1826,1932,2065,2198,2332,2496,2624,2748,2878,2998,3091,3188,3309,3432,3530,3633,3742,3883,4032,4141,4241,4325,4419,4514,4630,4717,4804,4905,4985,5071,5168,5271,5364,5461,5549,5654,5751,5850,5970,6050,6152,6245"}, "to": {"startLines": "90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6387,6502,6617,6727,6842,6940,7035,7147,7282,7398,7550,7635,7736,7828,7925,8041,8163,8269,8402,8535,8669,8833,8961,9085,9215,9335,9428,9525,9646,9769,9867,9970,10079,10220,10369,10478,10578,10662,10756,10851,10967,11054,11141,11242,11322,11408,11505,11608,11701,11798,11886,11991,12088,12187,12307,12387,12489", "endColumns": "114,114,109,114,97,94,111,134,115,151,84,100,91,96,115,121,105,132,132,133,163,127,123,129,119,92,96,120,122,97,102,108,140,148,108,99,83,93,94,115,86,86,100,79,85,96,102,92,96,87,104,96,98,119,79,101,92", "endOffsets": "6497,6612,6722,6837,6935,7030,7142,7277,7393,7545,7630,7731,7823,7920,8036,8158,8264,8397,8530,8664,8828,8956,9080,9210,9330,9423,9520,9641,9764,9862,9965,10074,10215,10364,10473,10573,10657,10751,10846,10962,11049,11136,11237,11317,11403,11500,11603,11696,11793,11881,11986,12083,12182,12302,12382,12484,12577"}}]}]}