package com.example.iotandroidv20.database

import androidx.room.*
import com.example.iotandroidv20.model.MultiModalDataSnapshot
import com.example.iotandroidv20.model.RiskLevel
import kotlinx.coroutines.flow.Flow

/**
 * 多模态数据访问对象
 */
@Dao
interface MultiModalDataDao {
    
    /**
     * 插入多模态数据快照
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSnapshot(snapshot: MultiModalDataSnapshot)
    
    /**
     * 批量插入多模态数据快照
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSnapshots(snapshots: List<MultiModalDataSnapshot>)
    
    /**
     * 更新多模态数据快照
     */
    @Update
    suspend fun updateSnapshot(snapshot: MultiModalDataSnapshot)
    
    /**
     * 删除多模态数据快照
     */
    @Delete
    suspend fun deleteSnapshot(snapshot: MultiModalDataSnapshot)
    
    /**
     * 根据ID删除快照
     */
    @Query("DELETE FROM multimodal_data WHERE id = :id")
    suspend fun deleteSnapshotById(id: String)
    
    /**
     * 根据会话ID删除快照
     */
    @Query("DELETE FROM multimodal_data WHERE sessionId = :sessionId")
    suspend fun deleteSnapshotsBySessionId(sessionId: String)
    
    /**
     * 删除指定时间之前的数据
     */
    @Query("DELETE FROM multimodal_data WHERE timestamp < :timestamp")
    suspend fun deleteSnapshotsBeforeTimestamp(timestamp: Long)
    
    /**
     * 清空所有数据
     */
    @Query("DELETE FROM multimodal_data")
    suspend fun deleteAllSnapshots()
    
    /**
     * 根据ID获取快照
     */
    @Query("SELECT * FROM multimodal_data WHERE id = :id")
    suspend fun getSnapshotById(id: String): MultiModalDataSnapshot?
    
    /**
     * 获取所有快照
     */
    @Query("SELECT * FROM multimodal_data ORDER BY timestamp DESC")
    fun getAllSnapshots(): Flow<List<MultiModalDataSnapshot>>
    
    /**
     * 根据会话ID获取快照
     */
    @Query("SELECT * FROM multimodal_data WHERE sessionId = :sessionId ORDER BY timestamp DESC")
    fun getSnapshotsBySessionId(sessionId: String): Flow<List<MultiModalDataSnapshot>>
    
    /**
     * 获取指定时间范围内的快照
     */
    @Query("SELECT * FROM multimodal_data WHERE timestamp BETWEEN :startTime AND :endTime ORDER BY timestamp DESC")
    fun getSnapshotsByTimeRange(startTime: Long, endTime: Long): Flow<List<MultiModalDataSnapshot>>
    
    /**
     * 获取最近的N个快照
     */
    @Query("SELECT * FROM multimodal_data ORDER BY timestamp DESC LIMIT :limit")
    fun getRecentSnapshots(limit: Int): Flow<List<MultiModalDataSnapshot>>
    
    /**
     * 获取指定风险等级的快照
     */
    @Query("SELECT * FROM multimodal_data WHERE riskLevel = :riskLevel ORDER BY timestamp DESC")
    fun getSnapshotsByRiskLevel(riskLevel: RiskLevel): Flow<List<MultiModalDataSnapshot>>
    
    /**
     * 获取健康评分低于阈值的快照
     */
    @Query("SELECT * FROM multimodal_data WHERE overallHealthScore < :threshold ORDER BY timestamp DESC")
    fun getSnapshotsWithLowHealthScore(threshold: Float): Flow<List<MultiModalDataSnapshot>>
    
    /**
     * 获取置信度高于阈值的快照
     */
    @Query("SELECT * FROM multimodal_data WHERE confidence >= :threshold ORDER BY timestamp DESC")
    fun getHighConfidenceSnapshots(threshold: Float): Flow<List<MultiModalDataSnapshot>>
    
    /**
     * 获取数据统计信息
     */
    @Query("""
        SELECT 
            COUNT(*) as totalCount,
            AVG(overallHealthScore) as avgHealthScore,
            MIN(overallHealthScore) as minHealthScore,
            MAX(overallHealthScore) as maxHealthScore,
            AVG(confidence) as avgConfidence,
            MIN(timestamp) as earliestTimestamp,
            MAX(timestamp) as latestTimestamp
        FROM multimodal_data
    """)
    suspend fun getDataStatistics(): DataStatistics
    
    /**
     * 获取会话统计信息
     */
    @Query("""
        SELECT 
            sessionId,
            COUNT(*) as snapshotCount,
            AVG(overallHealthScore) as avgHealthScore,
            MIN(timestamp) as startTime,
            MAX(timestamp) as endTime
        FROM multimodal_data 
        GROUP BY sessionId 
        ORDER BY startTime DESC
    """)
    suspend fun getSessionStatistics(): List<SessionStatistics>
    
    /**
     * 获取每日统计信息
     */
    @Query("""
        SELECT 
            DATE(timestamp/1000, 'unixepoch') as date,
            COUNT(*) as snapshotCount,
            AVG(overallHealthScore) as avgHealthScore,
            AVG(focusScore) as avgFocusScore,
            AVG(fatigueScore) as avgFatigueScore,
            AVG(postureScore) as avgPostureScore
        FROM multimodal_data 
        WHERE timestamp >= :startTimestamp
        GROUP BY DATE(timestamp/1000, 'unixepoch')
        ORDER BY date DESC
    """)
    suspend fun getDailyStatistics(startTimestamp: Long): List<DailyStatistics>
    
    /**
     * 获取风险等级分布
     */
    @Query("""
        SELECT 
            riskLevel,
            COUNT(*) as count,
            COUNT(*) * 100.0 / (SELECT COUNT(*) FROM multimodal_data) as percentage
        FROM multimodal_data 
        GROUP BY riskLevel
    """)
    suspend fun getRiskLevelDistribution(): List<RiskLevelDistribution>
    
    /**
     * 搜索快照
     */
    @Query("""
        SELECT * FROM multimodal_data 
        WHERE sessionId LIKE '%' || :query || '%' 
        OR id LIKE '%' || :query || '%'
        ORDER BY timestamp DESC
    """)
    fun searchSnapshots(query: String): Flow<List<MultiModalDataSnapshot>>
    
    /**
     * 获取数据质量报告
     */
    @Query("""
        SELECT 
            AVG(confidence) as avgConfidence,
            COUNT(CASE WHEN confidence >= 0.8 THEN 1 END) as highQualityCount,
            COUNT(CASE WHEN confidence < 0.5 THEN 1 END) as lowQualityCount,
            COUNT(*) as totalCount
        FROM multimodal_data
        WHERE timestamp >= :startTimestamp
    """)
    suspend fun getDataQualityReport(startTimestamp: Long): DataQualityReport
}

/**
 * 数据统计信息
 */
data class DataStatistics(
    val totalCount: Int,
    val avgHealthScore: Float,
    val minHealthScore: Float,
    val maxHealthScore: Float,
    val avgConfidence: Float,
    val earliestTimestamp: Long,
    val latestTimestamp: Long
) {
    /**
     * 获取数据时间跨度（天）
     */
    fun getDataSpanDays(): Int {
        return ((latestTimestamp - earliestTimestamp) / (24 * 60 * 60 * 1000)).toInt()
    }
    
    /**
     * 获取平均每天的数据量
     */
    fun getAvgDataPerDay(): Float {
        val spanDays = getDataSpanDays()
        return if (spanDays > 0) totalCount.toFloat() / spanDays else totalCount.toFloat()
    }
}

/**
 * 会话统计信息
 */
data class SessionStatistics(
    val sessionId: String,
    val snapshotCount: Int,
    val avgHealthScore: Float,
    val startTime: Long,
    val endTime: Long
) {
    /**
     * 获取会话持续时间（分钟）
     */
    fun getSessionDurationMinutes(): Int {
        return ((endTime - startTime) / (60 * 1000)).toInt()
    }
    
    /**
     * 获取数据采集频率（次/分钟）
     */
    fun getDataFrequency(): Float {
        val durationMinutes = getSessionDurationMinutes()
        return if (durationMinutes > 0) snapshotCount.toFloat() / durationMinutes else 0f
    }
}

/**
 * 每日统计信息
 */
data class DailyStatistics(
    val date: String,
    val snapshotCount: Int,
    val avgHealthScore: Float,
    val avgFocusScore: Float,
    val avgFatigueScore: Float,
    val avgPostureScore: Float
) {
    /**
     * 获取综合健康等级
     */
    fun getHealthLevel(): String {
        return when {
            avgHealthScore >= 80f -> "优秀"
            avgHealthScore >= 60f -> "良好"
            avgHealthScore >= 40f -> "一般"
            avgHealthScore >= 20f -> "较差"
            else -> "很差"
        }
    }
}

/**
 * 风险等级分布
 */
data class RiskLevelDistribution(
    val riskLevel: RiskLevel,
    val count: Int,
    val percentage: Float
)

/**
 * 数据质量报告
 */
data class DataQualityReport(
    val avgConfidence: Float,
    val highQualityCount: Int,
    val lowQualityCount: Int,
    val totalCount: Int
) {
    /**
     * 获取高质量数据百分比
     */
    fun getHighQualityPercentage(): Float {
        return if (totalCount > 0) (highQualityCount.toFloat() / totalCount) * 100f else 0f
    }
    
    /**
     * 获取低质量数据百分比
     */
    fun getLowQualityPercentage(): Float {
        return if (totalCount > 0) (lowQualityCount.toFloat() / totalCount) * 100f else 0f
    }
    
    /**
     * 获取数据质量等级
     */
    fun getQualityLevel(): String {
        return when {
            avgConfidence >= 0.8f -> "优秀"
            avgConfidence >= 0.6f -> "良好"
            avgConfidence >= 0.4f -> "一般"
            avgConfidence >= 0.2f -> "较差"
            else -> "很差"
        }
    }
}
