package com.example.iotandroidv20.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.iotandroidv20.config.HuaweiCloudConfig

import com.example.iotandroidv20.iot.TokenIoTManager
import com.example.iotandroidv20.model.AppSettings
import com.example.iotandroidv20.model.AppState
import com.example.iotandroidv20.model.DeviceStatus
import com.example.iotandroidv20.model.PostureData
// import com.example.iotandroidv20.model.ComprehensiveHealthAssessment // 暂时注释掉
import com.example.iotandroidv20.model.FocusIndexResult
import com.example.iotandroidv20.model.FocusStatistics
import com.example.iotandroidv20.model.FatigueIndexResult
import com.example.iotandroidv20.model.FatigueStatistics
// import com.example.iotandroidv20.repository.PostureDataRepository // 暂时注释掉
import com.example.iotandroidv20.utils.Logger
import com.example.iotandroidv20.obs.ObsAutoConfigManager
import com.example.iotandroidv20.obs.RealTimeVideoManager
import com.example.iotandroidv20.obs.ParentVoiceInteractionManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 主界面ViewModel - v2.0 Token认证版本
 * 管理应用状态和华为云IoT Token认证连接
 */
class MainViewModel(application: Application) : AndroidViewModel(application) {

    companion object {
        private const val TAG = "MainViewModel"
    }

    private val iotManager = TokenIoTManager(application)
    // private val repository = PostureDataRepository(application) // 暂时注释掉

    // 新增的人性化功能管理器
    private val obsAutoConfigManager = ObsAutoConfigManager.getInstance()
    private val realTimeVideoManager = RealTimeVideoManager.getInstance()
    private val parentVoiceInteractionManager = ParentVoiceInteractionManager.getInstance()


    // 应用状态
    private val _appState = MutableStateFlow(AppState())
    val appState: StateFlow<AppState> = _appState.asStateFlow()

    // 连接状态
    private val _connectionState = MutableStateFlow("未连接")
    val connectionState: StateFlow<String> = _connectionState.asStateFlow()

    // 错误消息
    private val _errorMessage = MutableStateFlow("")
    val errorMessage: StateFlow<String> = _errorMessage.asStateFlow()

    // Token状态
    private val _tokenStatus = MutableStateFlow<Map<String, Any>>(emptyMap())
    val tokenStatus: StateFlow<Map<String, Any>> = _tokenStatus.asStateFlow()

    // 应用设置
    private val _appSettings = MutableStateFlow(AppSettings.getDefault())
    val appSettings: StateFlow<AppSettings> = _appSettings.asStateFlow()

    // VIS视频状态 - 简化版
    private val visManager = com.example.iotandroidv20.vis.HuaweiVISManager(application)
    private val voiceRecordingManager = com.example.iotandroidv20.vis.VoiceRecordingManager(application)

    // EEG脑电波管理器
    private val eegManager = com.example.iotandroidv20.eeg.EEGManager(application)

    // 用户设置管理器
    private val userSettingsManager = com.example.iotandroidv20.settings.UserSettingsManager(application)

    // 学习监督管理器
    private val learningSupervisionManager = com.example.iotandroidv20.supervision.LearningSupervisionManager(
        context = application,
        eegManager = eegManager,
        iotManager = iotManager
    )

    // 多模态数据分析器 - 暂时注释掉
    // private val multiModalAnalyzer = com.example.iotandroidv20.multimodal.MultiModalAnalyzer()

    val visStatus = visManager.visStatus
    val playbackUrl = visManager.playbackUrl
    val isVoiceRecording = voiceRecordingManager.isRecording

    // EEG脑电波状态
    val eegDeviceStatus = eegManager.deviceStatus
    val eegFrequencyAnalysis = eegManager.frequencyAnalysis
    val eegFocusAssessment = eegManager.focusAssessment
    val eegFatigueAssessment = eegManager.fatigueAssessment
    val eegIsRecording = eegManager.isRecording

    // 用户设置
    val userSettings = userSettingsManager.userSettings

    // 学习监督相关状态
    val currentLearningSession = learningSupervisionManager.currentSession
    val realTimeLearningStatus = learningSupervisionManager.realTimeStatus
    val parentDashboard = learningSupervisionManager.parentDashboard

    // 多模态综合健康评估 - 暂时注释掉
    // private val _comprehensiveHealthAssessment = MutableStateFlow<ComprehensiveHealthAssessment?>(null)
    // val comprehensiveHealthAssessment = _comprehensiveHealthAssessment.asStateFlow()

    // 语音交互计数器
    private var voiceInteractionCount = 0

    init {
        Logger.i("初始化MainViewModel v2.0 - Token认证版本", tag = TAG)

        // 监听Token IoT管理器的状态变化
        viewModelScope.launch {
            iotManager.connectionState.collect { state ->
                _connectionState.value = state
                Logger.d("连接状态更新: $state", tag = TAG)
            }
        }

        viewModelScope.launch {
            iotManager.errorMessage.collect { message ->
                message?.let {
                    _errorMessage.value = it
                    Logger.e("错误消息: $it", tag = TAG)
                }
            }
        }

        viewModelScope.launch {
            iotManager.deviceStatus.collect { status ->
                Logger.d("设备状态更新: $status", tag = TAG)
                updateDeviceStatus(status)
            }
        }

        viewModelScope.launch {
            iotManager.postureData.collect { postureData ->
                postureData?.let {
                    Logger.d("姿态数据更新: $it", tag = TAG)
                    updatePostureData(it)
                }
            }
        }

        // 定期更新Token状态
        viewModelScope.launch {
            while (true) {
                _tokenStatus.value = iotManager.getTokenStatus()
                kotlinx.coroutines.delay(30000) // 每30秒更新一次Token状态
            }
        }

        // 启动综合健康分析
        startComprehensiveAnalysis()
    }
    
    /**
     * 连接到IoT设备 - v2.0 Token认证版本
     */
    fun connectToDevice() {
        Logger.d("用户点击连接按钮 - Token认证模式", tag = TAG)
        viewModelScope.launch {
            try {
                _connectionState.value = "连接中..."
                val success = iotManager.initialize()
                if (success) {
                    Logger.i("Token IoT连接成功", tag = TAG)
                    _errorMessage.value = ""
                } else {
                    Logger.e("Token IoT连接失败", tag = TAG)
                    _errorMessage.value = "Token认证连接失败"
                }
            } catch (e: Exception) {
                Logger.e("连接设备时发生异常: ${e.message}", tag = TAG)
                _errorMessage.value = "连接失败: ${e.message}"
                _connectionState.value = "连接失败"
            }
        }
    }

    /**
     * 断开设备连接
     */
    fun disconnectFromDevice() {
        Logger.d("断开IoT连接", tag = TAG)
        iotManager.disconnect()
    }

    /**
     * 查询设备信息 - v2.0版本
     */
    fun queryDeviceInfo() {
        Logger.d("查询设备信息", tag = TAG)
        viewModelScope.launch {
            try {
                val deviceInfo = iotManager.getDeviceDetails()
                if (deviceInfo != null) {
                    Logger.i("设备信息查询成功", tag = TAG)
                } else {
                    Logger.e("设备信息查询失败", tag = TAG)
                }
            } catch (e: Exception) {
                Logger.e("查询设备信息异常: ${e.message}", tag = TAG)
                _errorMessage.value = "查询设备信息失败: ${e.message}"
            }
        }
    }

    /**
     * 查询设备属性 - v2.0版本
     */
    fun queryDeviceProperties() {
        Logger.d("查询设备属性", tag = TAG)
        viewModelScope.launch {
            try {
                val properties = iotManager.getDeviceProperties()
                if (properties != null) {
                    Logger.i("设备属性查询成功", tag = TAG)
                } else {
                    Logger.e("设备属性查询失败", tag = TAG)
                }
            } catch (e: Exception) {
                Logger.e("查询设备属性异常: ${e.message}", tag = TAG)
                _errorMessage.value = "查询设备属性失败: ${e.message}"
            }
        }
    }

    /**
     * 刷新Token
     */
    fun refreshToken() {
        Logger.d("手动刷新Token", tag = TAG)
        viewModelScope.launch {
            try {
                val success = iotManager.refreshToken()
                if (success) {
                    Logger.i("Token刷新成功", tag = TAG)
                    _errorMessage.value = "Token刷新成功"
                } else {
                    Logger.e("Token刷新失败", tag = TAG)
                    _errorMessage.value = "Token刷新失败"
                }
                _tokenStatus.value = iotManager.getTokenStatus()
            } catch (e: Exception) {
                Logger.e("刷新Token异常: ${e.message}", tag = TAG)
                _errorMessage.value = "刷新Token失败: ${e.message}"
            }
        }
    }
    
    /**
     * 切换监控状态
     */
    fun toggleMonitoring() {
        val currentState = _appState.value
        val newState = currentState.copy(isMonitoring = !currentState.isMonitoring)
        _appState.value = newState

        Logger.d("监控状态切换: ${newState.isMonitoring}", tag = TAG)
        // TODO: 在v2.0版本中实现设备命令发送功能
    }

    /**
     * 切换提醒功能
     */
    fun toggleReminder() {
        val currentState = _appState.value
        val newState = currentState.copy(reminderEnabled = !currentState.reminderEnabled)
        _appState.value = newState

        Logger.d("提醒功能切换: ${newState.reminderEnabled}", tag = TAG)
    }

    /**
     * 测试数据解析功能 - v2.1版本
     */
    fun testDataParsing() {
        Logger.d("测试数据解析功能", tag = TAG)
        viewModelScope.launch {
            try {
                iotManager.testDataParsing()
                Logger.i("数据解析测试完成", tag = TAG)
                _errorMessage.value = "数据解析测试完成，请查看日志"
            } catch (e: Exception) {
                Logger.e("数据解析测试异常: ${e.message}", tag = TAG)
                _errorMessage.value = "数据解析测试失败: ${e.message}"
            }
        }
    }

    // ==================== 设备控制命令功能 v2.1 ====================

    /**
     * 发送坐姿提醒命令
     */
    fun sendPostureReminder(message: String = "请注意坐姿！") {
        Logger.d("发送坐姿提醒命令", tag = TAG)
        viewModelScope.launch {
            try {
                val success = iotManager.sendPostureReminder(message)
                if (success) {
                    Logger.i("坐姿提醒命令发送成功", tag = TAG)
                    _errorMessage.value = "坐姿提醒已发送"
                } else {
                    Logger.e("坐姿提醒命令发送失败", tag = TAG)
                    _errorMessage.value = "坐姿提醒发送失败"
                }
            } catch (e: Exception) {
                Logger.e("发送坐姿提醒异常: ${e.message}", tag = TAG)
                _errorMessage.value = "发送坐姿提醒失败: ${e.message}"
            }
        }
    }

    /**
     * 发送设备状态查询命令
     */
    fun sendQueryStatusCommand() {
        Logger.d("发送设备状态查询命令", tag = TAG)
        viewModelScope.launch {
            try {
                val success = iotManager.sendQueryStatusCommand()
                if (success) {
                    Logger.i("设备状态查询命令发送成功", tag = TAG)
                    _errorMessage.value = "设备状态查询已发送"
                } else {
                    Logger.e("设备状态查询命令发送失败", tag = TAG)
                    _errorMessage.value = "设备状态查询发送失败"
                }
            } catch (e: Exception) {
                Logger.e("发送设备状态查询异常: ${e.message}", tag = TAG)
                _errorMessage.value = "发送设备状态查询失败: ${e.message}"
            }
        }
    }

    /**
     * 发送测试命令
     */
    fun sendTestCommand() {
        Logger.d("发送测试命令", tag = TAG)
        viewModelScope.launch {
            try {
                val success = iotManager.sendTestCommand()
                if (success) {
                    Logger.i("测试命令发送成功", tag = TAG)
                    _errorMessage.value = "测试命令已发送"
                } else {
                    Logger.e("测试命令发送失败", tag = TAG)
                    _errorMessage.value = "测试命令发送失败"
                }
            } catch (e: Exception) {
                Logger.e("发送测试命令异常: ${e.message}", tag = TAG)
                _errorMessage.value = "发送测试命令失败: ${e.message}"
            }
        }
    }

    /**
     * 控制设备监控状态
     */
    fun controlDeviceMonitoring(enabled: Boolean) {
        Logger.d("控制设备监控状态: ${if (enabled) "启用" else "禁用"}", tag = TAG)
        viewModelScope.launch {
            try {
                val success = iotManager.sendMonitoringControlCommand(enabled)
                if (success) {
                    Logger.i("设备监控控制命令发送成功", tag = TAG)
                    _errorMessage.value = "设备监控${if (enabled) "已启用" else "已禁用"}"

                    // 更新本地状态
                    val currentState = _appState.value
                    val newState = currentState.copy(isMonitoring = enabled)
                    _appState.value = newState
                } else {
                    Logger.e("设备监控控制命令发送失败", tag = TAG)
                    _errorMessage.value = "设备监控控制失败"
                }
            } catch (e: Exception) {
                Logger.e("控制设备监控异常: ${e.message}", tag = TAG)
                _errorMessage.value = "设备监控控制失败: ${e.message}"
            }
        }
    }

    /**
     * 配置坐姿阈值
     */
    fun configurePostureThresholds(
        goodThreshold: Float = 8f,
        warningThreshold: Float = 15f,
        badThreshold: Float = 30f
    ) {
        Logger.d("配置坐姿阈值", tag = TAG)
        viewModelScope.launch {
            try {
                val success = iotManager.sendPostureThresholdConfig(goodThreshold, warningThreshold, badThreshold)
                if (success) {
                    Logger.i("坐姿阈值配置成功", tag = TAG)
                    _errorMessage.value = "坐姿阈值配置已更新"
                } else {
                    Logger.e("坐姿阈值配置失败", tag = TAG)
                    _errorMessage.value = "坐姿阈值配置失败"
                }
            } catch (e: Exception) {
                Logger.e("配置坐姿阈值异常: ${e.message}", tag = TAG)
                _errorMessage.value = "坐姿阈值配置失败: ${e.message}"
            }
        }
    }

    /**
     * 配置提醒频率
     */
    fun configureReminderFrequency(
        intervalMinutes: Int = 5,
        maxRemindersPerHour: Int = 10
    ) {
        Logger.d("配置提醒频率", tag = TAG)
        viewModelScope.launch {
            try {
                val intervalMs = intervalMinutes * 60 * 1000L
                val success = iotManager.sendReminderFrequencyConfig(intervalMs, maxRemindersPerHour)
                if (success) {
                    Logger.i("提醒频率配置成功", tag = TAG)
                    _errorMessage.value = "提醒频率配置已更新"
                } else {
                    Logger.e("提醒频率配置失败", tag = TAG)
                    _errorMessage.value = "提醒频率配置失败"
                }
            } catch (e: Exception) {
                Logger.e("配置提醒频率异常: ${e.message}", tag = TAG)
                _errorMessage.value = "提醒频率配置失败: ${e.message}"
            }
        }
    }

    // ==================== 数据库操作功能 v2.1 ====================

    /**
     * 获取历史坐姿数据 - 暂时注释掉
     */
    /*
    fun getHistoricalPostureData(limit: Int = 100) {
        Logger.d("获取历史坐姿数据", tag = TAG)
        viewModelScope.launch {
            try {
                repository.getRecentPostureData(limit).collect { postureDataList ->
                    val currentState = _appState.value
                    val newState = currentState.copy(postureHistory = postureDataList)
                    _appState.value = newState
                    Logger.d("历史坐姿数据已更新: ${postureDataList.size}条", tag = TAG)
                }
            } catch (e: Exception) {
                Logger.e("获取历史坐姿数据异常: ${e.message}", tag = TAG)
                _errorMessage.value = "获取历史数据失败: ${e.message}"
            }
        }
    }
    */

    /**
     * 获取每日统计数据
     */
    /*
    fun getDailyStatistics(limit: Int = 30) {
        Logger.d("获取每日统计数据", tag = TAG)
        viewModelScope.launch {
            try {
                repository.getDailyStats(limit).collect { dailyStatsList ->
                    if (dailyStatsList.isNotEmpty()) {
                        val currentState = _appState.value
                        val newState = currentState.copy(dailyStats = dailyStatsList.first())
                        _appState.value = newState
                        Logger.d("每日统计数据已更新", tag = TAG)
                    }
                }
            } catch (e: Exception) {
                Logger.e("获取每日统计数据异常: ${e.message}", tag = TAG)
                _errorMessage.value = "获取统计数据失败: ${e.message}"
            }
        }
    }
    */

    /**
     * 获取数据库统计信息 - 暂时禁用数据库功能
     */
    fun getDatabaseStatistics() {
        Logger.d("数据库功能暂时禁用", tag = TAG)
        _errorMessage.value = "数据库功能暂时禁用，无法获取统计信息"
    }

    // ==================== 设置管理功能 v2.1 ====================

    /**
     * 更新应用设置
     */
    fun updateSettings(newSettings: AppSettings) {
        Logger.d("更新应用设置", tag = TAG)
        try {
            if (newSettings.isValid()) {
                _appSettings.value = newSettings
                Logger.i("应用设置已更新", tag = TAG)

                // 应用新设置
                applySettings(newSettings)
            } else {
                Logger.e("设置验证失败", tag = TAG)
                _errorMessage.value = "设置参数无效，请检查输入"
            }
        } catch (e: Exception) {
            Logger.e("更新设置异常: ${e.message}", tag = TAG)
            _errorMessage.value = "更新设置失败: ${e.message}"
        }
    }

    /**
     * 应用设置到系统
     */
    private fun applySettings(settings: AppSettings) {
        viewModelScope.launch {
            try {
                // 应用坐姿阈值设置
                if (connectionState.value == "已连接 (Token认证)") {
                    iotManager.sendPostureThresholdConfig(
                        goodThreshold = settings.goodPostureThreshold,
                        warningThreshold = settings.warningPostureThreshold,
                        badThreshold = settings.badPostureThreshold
                    )

                    // 应用提醒频率设置
                    iotManager.sendReminderFrequencyConfig(
                        reminderInterval = settings.reminderInterval,
                        maxRemindersPerHour = settings.maxRemindersPerHour
                    )

                    Logger.i("设备配置已同步", tag = TAG)
                }

                // 更新本地状态
                val currentState = _appState.value
                val newState = currentState.copy(
                    reminderEnabled = settings.reminderEnabled
                )
                _appState.value = newState

                Logger.i("设置应用完成", tag = TAG)
            } catch (e: Exception) {
                Logger.e("应用设置异常: ${e.message}", tag = TAG)
                _errorMessage.value = "应用设置失败: ${e.message}"
            }
        }
    }

    /**
     * 重置设置为默认值
     */
    fun resetSettingsToDefault() {
        Logger.d("重置设置为默认值", tag = TAG)
        updateSettings(AppSettings.getDefault())
        _errorMessage.value = "设置已重置为默认值"
    }

    /**
     * 导出设置
     */
    fun exportSettings(): String {
        Logger.d("导出设置", tag = TAG)
        return try {
            val settings = _appSettings.value
            val gson = com.google.gson.Gson()
            gson.toJson(settings)
        } catch (e: Exception) {
            Logger.e("导出设置异常: ${e.message}", tag = TAG)
            ""
        }
    }

    /**
     * 导入设置
     */
    fun importSettings(settingsJson: String) {
        Logger.d("导入设置", tag = TAG)
        try {
            val gson = com.google.gson.Gson()
            val settings = gson.fromJson(settingsJson, AppSettings::class.java)
            updateSettings(settings)
            _errorMessage.value = "设置导入成功"
        } catch (e: Exception) {
            Logger.e("导入设置异常: ${e.message}", tag = TAG)
            _errorMessage.value = "设置导入失败: ${e.message}"
        }
    }

    // ==================== VIS视频功能 - 简化版 ====================

    /**
     * 初始化VIS服务
     */
    fun initializeVIS() {
        Logger.d("初始化VIS服务", tag = TAG)
        viewModelScope.launch {
            try {
                val success = visManager.initializeVISService()
                if (success) {
                    Logger.i("VIS服务初始化成功", tag = TAG)
                    _errorMessage.value = "VIS服务已就绪"
                } else {
                    Logger.e("VIS服务初始化失败", tag = TAG)
                    _errorMessage.value = "VIS服务初始化失败"
                }
            } catch (e: Exception) {
                Logger.e("初始化VIS服务异常: ${e.message}", tag = TAG)
                _errorMessage.value = "VIS服务初始化失败: ${e.message}"
            }
        }
    }

    /**
     * 开始VIS视频流
     */
    fun startVISStream() {
        Logger.d("开始VIS视频流", tag = TAG)
        viewModelScope.launch {
            try {
                val success = visManager.activateStream()
                if (success) {
                    // 获取播放地址
                    visManager.getStreamPlayUrl()
                    Logger.i("VIS视频流启动成功", tag = TAG)
                    _errorMessage.value = "视频流已启动"
                } else {
                    Logger.e("VIS视频流启动失败", tag = TAG)
                    _errorMessage.value = "视频流启动失败"
                }
            } catch (e: Exception) {
                Logger.e("启动VIS视频流异常: ${e.message}", tag = TAG)
                _errorMessage.value = "视频流启动失败: ${e.message}"
            }
        }
    }

    /**
     * 停止VIS视频流
     */
    fun stopVISStream() {
        Logger.d("停止VIS视频流", tag = TAG)
        viewModelScope.launch {
            try {
                val success = visManager.deactivateStream()
                if (success) {
                    Logger.i("VIS视频流停止成功", tag = TAG)
                    _errorMessage.value = "视频流已停止"
                } else {
                    Logger.e("VIS视频流停止失败", tag = TAG)
                    _errorMessage.value = "视频流停止失败"
                }
            } catch (e: Exception) {
                Logger.e("停止VIS视频流异常: ${e.message}", tag = TAG)
                _errorMessage.value = "视频流停止失败: ${e.message}"
            }
        }
    }

    // ==================== 语音录制功能 - 简化版 ====================

    /**
     * 开始语音录制
     */
    fun startVoiceRecording() {
        Logger.d("开始语音录制", tag = TAG)
        viewModelScope.launch {
            try {
                val success = voiceRecordingManager.startRecording()
                if (success) {
                    Logger.i("语音录制启动成功", tag = TAG)
                    _errorMessage.value = "开始录制语音"
                } else {
                    Logger.e("语音录制启动失败", tag = TAG)
                    _errorMessage.value = "语音录制启动失败"
                }
            } catch (e: Exception) {
                Logger.e("启动语音录制异常: ${e.message}", tag = TAG)
                _errorMessage.value = "语音录制启动失败: ${e.message}"
            }
        }
    }

    /**
     * 停止语音录制
     */
    fun stopVoiceRecording() {
        Logger.d("停止语音录制", tag = TAG)
        viewModelScope.launch {
            try {
                val success = voiceRecordingManager.stopRecording()
                if (success) {
                    Logger.i("语音录制停止成功", tag = TAG)
                    _errorMessage.value = "语音已发送到设备"

                    // 增加语音交互计数
                    incrementVoiceInteraction()
                } else {
                    Logger.e("语音录制停止失败", tag = TAG)
                    _errorMessage.value = "语音录制停止失败"
                }
            } catch (e: Exception) {
                Logger.e("停止语音录制异常: ${e.message}", tag = TAG)
                _errorMessage.value = "语音录制停止失败: ${e.message}"
            }
        }
    }

    // ==================== EEG脑电波功能 ====================

    /**
     * 连接EEG设备
     */
    fun connectEEGDevice() {
        Logger.d("连接EEG设备", tag = TAG)
        viewModelScope.launch {
            try {
                val success = eegManager.connectDevice()
                if (success) {
                    Logger.i("EEG设备连接成功", tag = TAG)
                    _errorMessage.value = "EEG设备已连接"
                } else {
                    Logger.e("EEG设备连接失败", tag = TAG)
                    _errorMessage.value = "EEG设备连接失败"
                }
            } catch (e: Exception) {
                Logger.e("连接EEG设备异常: ${e.message}", tag = TAG)
                _errorMessage.value = "EEG设备连接失败: ${e.message}"
            }
        }
    }

    /**
     * 断开EEG设备
     */
    fun disconnectEEGDevice() {
        Logger.d("断开EEG设备", tag = TAG)
        viewModelScope.launch {
            try {
                val success = eegManager.disconnectDevice()
                if (success) {
                    Logger.i("EEG设备断开成功", tag = TAG)
                    _errorMessage.value = "EEG设备已断开"
                } else {
                    Logger.e("EEG设备断开失败", tag = TAG)
                    _errorMessage.value = "EEG设备断开失败"
                }
            } catch (e: Exception) {
                Logger.e("断开EEG设备异常: ${e.message}", tag = TAG)
                _errorMessage.value = "EEG设备断开失败: ${e.message}"
            }
        }
    }

    /**
     * 开始EEG数据记录
     */
    fun startEEGRecording() {
        Logger.d("开始EEG数据记录", tag = TAG)
        viewModelScope.launch {
            try {
                val success = eegManager.startRecording()
                if (success) {
                    Logger.i("EEG数据记录启动成功", tag = TAG)
                    _errorMessage.value = "开始EEG监控"
                } else {
                    Logger.e("EEG数据记录启动失败", tag = TAG)
                    _errorMessage.value = "EEG监控启动失败"
                }
            } catch (e: Exception) {
                Logger.e("启动EEG记录异常: ${e.message}", tag = TAG)
                _errorMessage.value = "EEG监控启动失败: ${e.message}"
            }
        }
    }

    /**
     * 停止EEG数据记录
     */
    fun stopEEGRecording() {
        Logger.d("停止EEG数据记录", tag = TAG)
        viewModelScope.launch {
            try {
                val success = eegManager.stopRecording()
                if (success) {
                    Logger.i("EEG数据记录停止成功", tag = TAG)
                    _errorMessage.value = "EEG监控已停止"
                } else {
                    Logger.e("EEG数据记录停止失败", tag = TAG)
                    _errorMessage.value = "EEG监控停止失败"
                }
            } catch (e: Exception) {
                Logger.e("停止EEG记录异常: ${e.message}", tag = TAG)
                _errorMessage.value = "EEG监控停止失败: ${e.message}"
            }
        }
    }

    /**
     * 重置EEG评估历史
     */
    fun resetEEGHistory() {
        Logger.d("重置EEG评估历史", tag = TAG)
        try {
            eegManager.resetAssessmentHistory()
            Logger.i("EEG评估历史重置成功", tag = TAG)
            _errorMessage.value = "EEG历史数据已重置"
        } catch (e: Exception) {
            Logger.e("重置EEG历史异常: ${e.message}", tag = TAG)
            _errorMessage.value = "EEG历史重置失败: ${e.message}"
        }
    }

    /**
     * 获取当前专注度分数
     */
    fun getCurrentFocusScore(): Float {
        return eegManager.getCurrentFocusScore()
    }

    /**
     * 获取当前疲劳度分数
     */
    fun getCurrentFatigueScore(): Float {
        return eegManager.getCurrentFatigueScore()
    }

    /**
     * 检查是否需要休息
     */
    fun shouldTakeBreak(): Boolean {
        return eegManager.shouldTakeBreak()
    }

    /**
     * 获取休息建议
     */
    fun getRestRecommendation(): String {
        return eegManager.getRestRecommendationDescription()
    }

    /**
     * 获取EEG统计信息
     */
    fun getEEGStatistics(): Map<String, Any> {
        return try {
            eegManager.getEEGStatistics()
        } catch (e: Exception) {
            Logger.e("获取EEG统计信息异常: ${e.message}", tag = TAG)
            emptyMap()
        }
    }

    /**
     * 获取脑波状态描述
     */
    fun getBrainwaveStatus(): String {
        return eegManager.getCurrentBrainwaveStatus()
    }

    /**
     * 获取高级专注度结果
     */
    fun getAdvancedFocusResult(): FocusIndexResult? {
        return eegManager.getAdvancedFocusResult()
    }

    /**
     * 获取专注度统计信息
     */
    fun getFocusStatistics(): FocusStatistics {
        return eegManager.getFocusStatistics()
    }

    /**
     * 分析专注度趋势
     */
    fun analyzeFocusTrend(): Map<String, Any> {
        return eegManager.analyzeFocusTrend()
    }

    /**
     * 获取高级疲劳结果
     */
    fun getAdvancedFatigueResult(): FatigueIndexResult? {
        return eegManager.getAdvancedFatigueResult()
    }

    /**
     * 获取疲劳统计信息
     */
    fun getFatigueStatistics(): FatigueStatistics {
        return eegManager.getFatigueStatistics()
    }

    /**
     * 分析疲劳趋势
     */
    fun analyzeFatigueTrend(): Map<String, Any> {
        return eegManager.analyzeFatigueTrend()
    }

    /**
     * 运行专注度计算演示
     */
    fun runFocusCalculationDemo(): String {
        Logger.d("运行专注度计算演示", tag = TAG)
        return try {
            val demo = com.example.iotandroidv20.eeg.FocusCalculationDemo()
            val results = demo.runFocusCalculationDemo()
            val report = demo.generateFocusCalculationReport(results)
            demo.cleanup()

            Logger.i("专注度计算演示完成", tag = TAG)
            _errorMessage.value = "专注度计算演示已完成，查看日志获取详细报告"

            report
        } catch (e: Exception) {
            Logger.e("专注度计算演示异常: ${e.message}", tag = TAG)
            _errorMessage.value = "专注度计算演示失败: ${e.message}"
            "演示失败: ${e.message}"
        }
    }

    /**
     * 运行疲劳检测演示
     */
    fun runFatigueDetectionDemo(): String {
        Logger.d("运行疲劳检测演示", tag = TAG)
        return try {
            val demo = com.example.iotandroidv20.eeg.FatigueDetectionDemo()
            val results = demo.runFatigueDetectionDemo()
            val report = demo.generateFatigueDetectionReport(results)
            demo.cleanup()

            Logger.i("疲劳检测演示完成", tag = TAG)
            _errorMessage.value = "疲劳检测演示已完成，查看日志获取详细报告"

            report
        } catch (e: Exception) {
            Logger.e("疲劳检测演示异常: ${e.message}", tag = TAG)
            _errorMessage.value = "疲劳检测演示失败: ${e.message}"
            "演示失败: ${e.message}"
        }
    }

    // ==================== 多模态数据融合功能 ====================

    /**
     * 执行综合健康分析
     */
    fun performComprehensiveHealthAnalysis() {
        Logger.d("执行综合健康分析", tag = TAG)
        viewModelScope.launch {
            try {
                val currentPosture = _appState.value.currentPosture
                val eegFocus = eegFocusAssessment.value
                val eegFatigue = eegFatigueAssessment.value
                val eegFrequency = eegFrequencyAnalysis.value
                val visStatusMap = getVISStatusMap()

                // val assessment = multiModalAnalyzer.analyzeComprehensiveHealth(
                //     postureData = currentPosture,
                //     eegFocusAssessment = eegFocus,
                //     eegFatigueAssessment = eegFatigue,
                //     eegFrequencyAnalysis = eegFrequency,
                //     visStatus = visStatusMap,
                //     voiceInteractionCount = voiceInteractionCount
                // )

                // _comprehensiveHealthAssessment.value = assessment

                Logger.i("综合健康分析完成", tag = TAG)

                // 如果需要立即关注，显示警告
                // if (assessment.requiresImmediateAttention()) {
                //     _errorMessage.value = "⚠️ ${assessment.getPrimaryRiskDescription()}"
                // }

            } catch (e: Exception) {
                Logger.e("综合健康分析异常: ${e.message}", tag = TAG)
                _errorMessage.value = "综合健康分析失败: ${e.message}"
            }
        }
    }

    /**
     * 获取VIS状态映射
     */
    private fun getVISStatusMap(): Map<String, Any> {
        val visStatusValue = visStatus.value
        return mapOf(
            "isStreaming" to visStatusValue.isStreamActive,
            "quality" to if (visStatusValue.isStreamActive) "high" else "unknown",
            "streamUrl" to (playbackUrl.value ?: ""),
            "isInitialized" to visStatusValue.isServiceSubscribed
        )
    }

    /**
     * 增加语音交互计数
     */
    private fun incrementVoiceInteraction() {
        voiceInteractionCount++
        Logger.d("语音交互计数: $voiceInteractionCount", tag = TAG)

        // 每次语音交互后触发综合分析
        performComprehensiveHealthAnalysis()
    }

    /**
     * 重置语音交互计数
     */
    fun resetVoiceInteractionCount() {
        voiceInteractionCount = 0
        Logger.d("语音交互计数已重置", tag = TAG)
    }

    /**
     * 获取当前健康评分
     */
    fun getCurrentHealthScore(): Float {
        // return _comprehensiveHealthAssessment.value?.overallScore ?: 0f
        return 0f
    }

    /**
     * 获取健康状态描述
     */
    fun getHealthStatusDescription(): String {
        // return _comprehensiveHealthAssessment.value?.getHealthStatusDescription() ?: "数据收集中..."
        return "数据收集中..."
    }

    /**
     * 检查是否需要立即关注
     */
    fun requiresImmediateAttention(): Boolean {
        // return _comprehensiveHealthAssessment.value?.requiresImmediateAttention() ?: false
        return false
    }

    /**
     * 获取优先建议
     */
    fun getPriorityRecommendation(): String {
        // return _comprehensiveHealthAssessment.value?.getPriorityRecommendation() ?: "继续保持当前状态"
        return "继续保持当前状态"
    }

    /**
     * 启动综合健康分析定时器
     */
    private fun startComprehensiveAnalysis() {
        viewModelScope.launch {
            while (true) {
                kotlinx.coroutines.delay(60000) // 每60秒执行一次综合分析
                performComprehensiveHealthAnalysis()
            }
        }
    }

    /**
     * 清除错误消息
     */
    fun clearErrorMessage() {
        _errorMessage.value = ""
        Logger.d("错误消息已清除", tag = TAG)
    }

    /**
     * 更新设备状态 - v2.0版本
     */
    private fun updateDeviceStatus(status: String) {
        val currentState = _appState.value
        val isConnected = status == "ONLINE"
        val newDeviceStatus = currentState.deviceStatus.copy(
            isConnected = isConnected,
            lastUpdateTime = System.currentTimeMillis()
        )
        _appState.value = currentState.copy(deviceStatus = newDeviceStatus)
        Logger.d("设备状态更新: $status (连接: $isConnected)", tag = TAG)
    }

    /**
     * 更新姿态数据 - v2.0版本
     */
    private fun updatePostureData(postureData: PostureData) {
        val currentState = _appState.value
        val newHistory = (currentState.postureHistory + postureData).takeLast(100) // 保留最近100条记录

        _appState.value = currentState.copy(
            currentPosture = postureData,
            postureHistory = newHistory
        )
        Logger.d("姿态数据更新: $postureData", tag = TAG)
    }





    /**
     * 更新用户设置
     */
    fun updateUserSettings(settings: com.example.iotandroidv20.model.UserSettings) {
        Logger.d("更新用户设置", tag = TAG)
        userSettingsManager.saveSettings(settings)
    }

    /**
     * 获取年龄调整系数
     */
    fun getAgeAdjustmentFactor(): Float {
        return userSettingsManager.getAgeAdjustmentFactor()
    }

    /**
     * 获取环境调整系数
     */
    fun getEnvironmentAdjustmentFactor(): Float {
        return userSettingsManager.getEnvironmentAdjustmentFactor()
    }

    /**
     * 获取时长调整系数
     */
    fun getDurationAdjustmentFactor(sessionDurationMinutes: Int): Float {
        return userSettingsManager.getDurationAdjustmentFactor(sessionDurationMinutes)
    }

    // ==================== 学习监督相关方法 ====================

    /**
     * 初始化学习监督系统
     */
    fun initializeLearningSupervision() {
        viewModelScope.launch {
            try {
                val childProfile = createChildProfileFromSettings()
                val config = createLearningSupervisionConfig(childProfile)
                learningSupervisionManager.initialize(config)
                Logger.d("学习监督系统初始化成功", tag = TAG)
            } catch (e: Exception) {
                Logger.e("学习监督系统初始化失败: ${e.message}", tag = TAG)
                _errorMessage.value = "学习监督系统初始化失败"
            }
        }
    }

    /**
     * 开始学习监督会话
     */
    fun startLearningSession(sessionType: com.example.iotandroidv20.model.LearningSessionType, targetDuration: Int) {
        viewModelScope.launch {
            try {
                Logger.d("开始学习监督会话: ${sessionType.displayName}, 时长: ${targetDuration}分钟", tag = TAG)
                val success = learningSupervisionManager.startLearningSession(sessionType, targetDuration)
                if (success) {
                    Logger.d("学习监督会话启动成功", tag = TAG)
                } else {
                    _errorMessage.value = "启动学习会话失败"
                }
            } catch (e: Exception) {
                Logger.e("启动学习会话异常: ${e.message}", tag = TAG)
                _errorMessage.value = "启动学习会话失败: ${e.message}"
            }
        }
    }

    /**
     * 暂停学习会话
     */
    fun pauseLearningSession() {
        viewModelScope.launch {
            try {
                learningSupervisionManager.pauseLearningSession()
                Logger.d("学习会话已暂停", tag = TAG)
            } catch (e: Exception) {
                Logger.e("暂停学习会话异常: ${e.message}", tag = TAG)
                _errorMessage.value = "暂停学习会话失败"
            }
        }
    }

    /**
     * 恢复学习会话
     */
    fun resumeLearningSession() {
        viewModelScope.launch {
            try {
                learningSupervisionManager.resumeLearningSession()
                Logger.d("学习会话已恢复", tag = TAG)
            } catch (e: Exception) {
                Logger.e("恢复学习会话异常: ${e.message}", tag = TAG)
                _errorMessage.value = "恢复学习会话失败"
            }
        }
    }

    /**
     * 结束学习会话
     */
    fun endLearningSession() {
        viewModelScope.launch {
            try {
                val summary = learningSupervisionManager.endLearningSession()
                if (summary != null) {
                    Logger.d("学习会话已结束，生成总结", tag = TAG)
                    // 可以在这里处理会话总结，比如显示给用户或保存到数据库
                } else {
                    Logger.w("结束学习会话但未生成总结", tag = TAG)
                }
            } catch (e: Exception) {
                Logger.e("结束学习会话异常: ${e.message}", tag = TAG)
                _errorMessage.value = "结束学习会话失败"
            }
        }
    }

    /**
     * 重置学习监督系统
     */
    fun resetLearningSupervision() {
        viewModelScope.launch {
            try {
                Logger.d("重置学习监督系统", tag = TAG)
                val success = learningSupervisionManager.resetLearningSupervision()
                if (success) {
                    Logger.d("学习监督系统重置成功", tag = TAG)
                    _errorMessage.value = "系统已重置，可以开始新的学习会话"
                } else {
                    Logger.e("学习监督系统重置失败", tag = TAG)
                    _errorMessage.value = "系统重置失败"
                }
            } catch (e: Exception) {
                Logger.e("重置学习监督系统异常: ${e.message}", tag = TAG)
                _errorMessage.value = "系统重置失败: ${e.message}"
            }
        }
    }

    /**
     * 检查是否可以开始新会话
     */
    fun canStartNewLearningSession(): Boolean {
        return learningSupervisionManager.canStartNewSession()
    }

    /**
     * 获取会话重置建议
     */
    fun getSessionResetRecommendation(): String {
        return learningSupervisionManager.getSessionResetRecommendation()
    }

    /**
     * 从用户设置创建儿童档案
     */
    private fun createChildProfileFromSettings(): com.example.iotandroidv20.model.ChildProfile {
        val settings = userSettings.value
        return com.example.iotandroidv20.model.ChildProfile(
            age = settings.age,
            gender = settings.gender,
            learningStyle = com.example.iotandroidv20.model.LearningStyle.UNKNOWN, // 可以后续通过学习获得
            attentionSpanBaseline = getEstimatedAttentionSpan(settings.age),
            optimalLearningTimes = emptyList(), // 通过学习获得
            environmentPreferences = com.example.iotandroidv20.model.EnvironmentPreferences(
                preferredNoiseLevel = com.example.iotandroidv20.model.NoiseLevel.NORMAL,
                preferredLighting = com.example.iotandroidv20.model.LightingCondition.NORMAL,
                preferredTemperature = com.example.iotandroidv20.model.TemperatureComfort.COMFORTABLE
            )
        )
    }

    /**
     * 创建学习监督配置
     */
    private fun createLearningSupervisionConfig(
        childProfile: com.example.iotandroidv20.model.ChildProfile
    ): com.example.iotandroidv20.model.LearningSupervisionConfig {
        return com.example.iotandroidv20.model.LearningSupervisionConfig(
            childProfile = childProfile,
            monitoringSettings = com.example.iotandroidv20.model.MonitoringSettings(
                sessionDuration = 60,
                breakInterval = 15,
                dailyMaxHours = 8,
                dataCollectionInterval = 5,
                postureMonitoringEnabled = true,
                focusMonitoringEnabled = true,
                fatigueMonitoringEnabled = true,
                environmentMonitoringEnabled = true,
                realTimeAnalysisEnabled = true,
                alertSensitivity = 0.7f
            ),
            interventionSettings = com.example.iotandroidv20.model.InterventionSettings(
                enableAutomaticInterventions = true,
                interventionDelay = 30,
                maxInterventionsPerHour = 6,
                gentleReminderMode = true,
                parentNotificationEnabled = true,
                adaptiveInterventions = true
            ),
            reportingSettings = com.example.iotandroidv20.model.ReportingSettings(
                generateDailyReports = true,
                generateWeeklyReports = true,
                generateMonthlyReports = true,
                includeDetailedAnalytics = true,
                shareWithEducators = false,
                anonymizeData = true
            ),
            privacySettings = com.example.iotandroidv20.model.PrivacySettings(
                dataRetentionDays = 90,
                allowDataSharing = false,
                encryptSensitiveData = true,
                requireParentalConsent = true,
                allowResearchParticipation = false
            )
        )
    }

    /**
     * 根据年龄估算注意力持续时间基线
     */
    private fun getEstimatedAttentionSpan(age: Int): Int {
        return when (age) {
            in 6..8 -> 15   // 6-8岁：15分钟
            in 9..12 -> 25  // 9-12岁：25分钟
            in 13..15 -> 35 // 13-15岁：35分钟
            else -> 30      // 默认30分钟
        }
    }

    override fun onCleared() {
        super.onCleared()
        Logger.i("MainViewModel清理资源", tag = TAG)
        iotManager.disconnect()
        visManager.cleanup()
        voiceRecordingManager.destroy()
        eegManager.cleanup()
        learningSupervisionManager.cleanup()
    }
}
