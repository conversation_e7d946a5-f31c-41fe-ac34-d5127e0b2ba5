package com.example.iotandroidv20.obs

import java.util.*

/**
 * 下载状态枚举
 */
enum class DownloadStatus {
    NOT_DOWNLOADED,    // 未下载
    DOWNLOADING,       // 下载中
    DOWNLOADED,        // 已下载
    DOWNLOAD_FAILED    // 下载失败
}

/**
 * 视频会话数据模型
 */
data class VideoSession(
    val sessionId: String,           // 会话ID
    val deviceId: String,            // 设备ID
    val date: String,                // 日期 (YYYYMMDD格式)
    val fileName: String,            // 文件名
    val fileSize: Long,              // 文件大小 (字节)
    val lastModified: Date,          // 最后修改时间
    val objectKey: String,           // OBS对象键
    val streamUrl: String,           // 流媒体URL
    val downloadStatus: DownloadStatus = DownloadStatus.NOT_DOWNLOADED, // 下载状态
    val localFilePath: String? = null, // 本地文件路径
    val thumbnailUrl: String? = null,  // 缩略图URL
    val duration: Long = 0L,           // 视频时长 (秒)
    val resolution: String = "1280x720", // 分辨率
    val bitrate: Int = 1500,           // 码率 (kbps)
    val createdAt: Date = Date()       // 创建时间
) {
    /**
     * 获取格式化的文件大小
     */
    fun getFormattedFileSize(): String {
        return when {
            fileSize < 1024 -> "${fileSize}B"
            fileSize < 1024 * 1024 -> "${fileSize / 1024}KB"
            fileSize < 1024 * 1024 * 1024 -> "${fileSize / (1024 * 1024)}MB"
            else -> "${fileSize / (1024 * 1024 * 1024)}GB"
        }
    }
    
    /**
     * 获取格式化的时长
     */
    fun getFormattedDuration(): String {
        val hours = duration / 3600
        val minutes = (duration % 3600) / 60
        val seconds = duration % 60
        
        return when {
            hours > 0 -> String.format("%02d:%02d:%02d", hours, minutes, seconds)
            else -> String.format("%02d:%02d", minutes, seconds)
        }
    }
    
    /**
     * 是否已下载到本地
     */
    fun isDownloaded(): Boolean {
        return downloadStatus == DownloadStatus.DOWNLOADED && 
               !localFilePath.isNullOrEmpty() && 
               java.io.File(localFilePath).exists()
    }
}

/**
 * 音频会话数据模型
 */
data class AudioSession(
    val sessionId: String,           // 会话ID
    val deviceId: String,            // 设备ID
    val date: String,                // 日期 (YYYYMMDD格式)
    val fileName: String,            // 文件名
    val fileSize: Long,              // 文件大小 (字节)
    val lastModified: Date,          // 最后修改时间
    val objectKey: String,           // OBS对象键
    val streamUrl: String,           // 流媒体URL
    val downloadStatus: DownloadStatus = DownloadStatus.NOT_DOWNLOADED, // 下载状态
    val localFilePath: String? = null, // 本地文件路径
    val waveformUrl: String? = null,   // 波形图URL
    val duration: Long = 0L,           // 音频时长 (秒)
    val sampleRate: Int = 44100,       // 采样率 (Hz)
    val bitrate: Int = 128,            // 码率 (kbps)
    val channels: Int = 2,             // 声道数
    val createdAt: Date = Date()       // 创建时间
) {
    /**
     * 获取格式化的文件大小
     */
    fun getFormattedFileSize(): String {
        return when {
            fileSize < 1024 -> "${fileSize}B"
            fileSize < 1024 * 1024 -> "${fileSize / 1024}KB"
            fileSize < 1024 * 1024 * 1024 -> "${fileSize / (1024 * 1024)}MB"
            else -> "${fileSize / (1024 * 1024 * 1024)}GB"
        }
    }
    
    /**
     * 获取格式化的时长
     */
    fun getFormattedDuration(): String {
        val hours = duration / 3600
        val minutes = (duration % 3600) / 60
        val seconds = duration % 60
        
        return when {
            hours > 0 -> String.format("%02d:%02d:%02d", hours, minutes, seconds)
            else -> String.format("%02d:%02d", minutes, seconds)
        }
    }
    
    /**
     * 获取音频质量描述
     */
    fun getQualityDescription(): String {
        return when {
            bitrate >= 256 -> "高质量"
            bitrate >= 128 -> "标准质量"
            else -> "低质量"
        }
    }
    
    /**
     * 获取声道描述
     */
    fun getChannelDescription(): String {
        return when (channels) {
            1 -> "单声道"
            2 -> "立体声"
            else -> "${channels}声道"
        }
    }
    
    /**
     * 是否已下载到本地
     */
    fun isDownloaded(): Boolean {
        return downloadStatus == DownloadStatus.DOWNLOADED && 
               !localFilePath.isNullOrEmpty() && 
               java.io.File(localFilePath).exists()
    }
}

/**
 * 媒体文件信息
 */
data class MediaFileInfo(
    val objectKey: String,           // OBS对象键
    val fileName: String,            // 文件名
    val fileSize: Long,              // 文件大小
    val contentType: String,         // 内容类型
    val lastModified: Date,          // 最后修改时间
    val etag: String,                // ETag
    val storageClass: String = "STANDARD" // 存储类别
)

/**
 * 下载进度信息
 */
data class DownloadProgress(
    val objectKey: String,           // 对象键
    val totalBytes: Long,            // 总字节数
    val downloadedBytes: Long,       // 已下载字节数
    val progress: Float,             // 进度百分比 (0.0-1.0)
    val speed: Long,                 // 下载速度 (字节/秒)
    val remainingTime: Long          // 剩余时间 (秒)
) {
    /**
     * 获取格式化的进度百分比
     */
    fun getFormattedProgress(): String {
        return "${(progress * 100).toInt()}%"
    }
    
    /**
     * 获取格式化的下载速度
     */
    fun getFormattedSpeed(): String {
        return when {
            speed < 1024 -> "${speed}B/s"
            speed < 1024 * 1024 -> "${speed / 1024}KB/s"
            else -> "${speed / (1024 * 1024)}MB/s"
        }
    }
    
    /**
     * 获取格式化的剩余时间
     */
    fun getFormattedRemainingTime(): String {
        val hours = remainingTime / 3600
        val minutes = (remainingTime % 3600) / 60
        val seconds = remainingTime % 60
        
        return when {
            hours > 0 -> "${hours}h ${minutes}m"
            minutes > 0 -> "${minutes}m ${seconds}s"
            else -> "${seconds}s"
        }
    }
}

/**
 * 媒体播放状态
 */
enum class PlaybackState {
    IDLE,           // 空闲
    PREPARING,      // 准备中
    READY,          // 准备就绪
    PLAYING,        // 播放中
    PAUSED,         // 暂停
    ENDED,          // 播放结束
    ERROR           // 错误
}

/**
 * 播放器信息
 */
data class PlayerInfo(
    val state: PlaybackState,        // 播放状态
    val currentPosition: Long,       // 当前播放位置 (毫秒)
    val duration: Long,              // 总时长 (毫秒)
    val bufferedPosition: Long,      // 缓冲位置 (毫秒)
    val playbackSpeed: Float = 1.0f, // 播放速度
    val volume: Float = 1.0f,        // 音量 (0.0-1.0)
    val isLooping: Boolean = false   // 是否循环播放
) {
    /**
     * 获取播放进度百分比
     */
    fun getProgress(): Float {
        return if (duration > 0) {
            currentPosition.toFloat() / duration.toFloat()
        } else {
            0f
        }
    }
    
    /**
     * 获取缓冲进度百分比
     */
    fun getBufferedProgress(): Float {
        return if (duration > 0) {
            bufferedPosition.toFloat() / duration.toFloat()
        } else {
            0f
        }
    }
}
