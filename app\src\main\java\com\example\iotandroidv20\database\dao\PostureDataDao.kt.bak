package com.example.iotandroidv20.database.dao

import androidx.room.*
import com.example.iotandroidv20.database.entity.PostureDataEntity
import kotlinx.coroutines.flow.Flow

/**
 * 坐姿数据DAO
 */
@Dao
interface PostureDataDao {
    
    /**
     * 插入坐姿数据
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPostureData(postureData: PostureDataEntity): Long
    
    /**
     * 批量插入坐姿数据
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPostureDataList(postureDataList: List<PostureDataEntity>)
    
    /**
     * 更新坐姿数据
     */
    @Update
    suspend fun updatePostureData(postureData: PostureDataEntity)
    
    /**
     * 删除坐姿数据
     */
    @Delete
    suspend fun deletePostureData(postureData: PostureDataEntity)
    
    /**
     * 根据ID删除坐姿数据
     */
    @Query("DELETE FROM posture_data WHERE id = :id")
    suspend fun deletePostureDataById(id: Long)
    
    /**
     * 删除指定时间之前的数据
     */
    @Query("DELETE FROM posture_data WHERE timestamp < :timestamp")
    suspend fun deletePostureDataBefore(timestamp: Long)
    
    /**
     * 获取所有坐姿数据（按时间倒序）
     */
    @Query("SELECT * FROM posture_data ORDER BY timestamp DESC")
    fun getAllPostureData(): Flow<List<PostureDataEntity>>
    
    /**
     * 获取最近N条坐姿数据
     */
    @Query("SELECT * FROM posture_data ORDER BY timestamp DESC LIMIT :limit")
    fun getRecentPostureData(limit: Int): Flow<List<PostureDataEntity>>
    
    /**
     * 获取指定时间范围内的坐姿数据
     */
    @Query("SELECT * FROM posture_data WHERE timestamp BETWEEN :startTime AND :endTime ORDER BY timestamp DESC")
    fun getPostureDataByTimeRange(startTime: Long, endTime: Long): Flow<List<PostureDataEntity>>
    
    /**
     * 获取指定日期的坐姿数据
     */
    @Query("SELECT * FROM posture_data WHERE timestamp BETWEEN :dayStart AND :dayEnd ORDER BY timestamp DESC")
    fun getPostureDataByDate(dayStart: Long, dayEnd: Long): Flow<List<PostureDataEntity>>
    
    /**
     * 获取指定设备的坐姿数据
     */
    @Query("SELECT * FROM posture_data WHERE device_id = :deviceId ORDER BY timestamp DESC")
    fun getPostureDataByDevice(deviceId: String): Flow<List<PostureDataEntity>>
    
    /**
     * 获取指定坐姿状态的数据
     */
    @Query("SELECT * FROM posture_data WHERE posture_status = :status ORDER BY timestamp DESC")
    fun getPostureDataByStatus(status: String): Flow<List<PostureDataEntity>>
    
    /**
     * 获取最新的坐姿数据
     */
    @Query("SELECT * FROM posture_data ORDER BY timestamp DESC LIMIT 1")
    suspend fun getLatestPostureData(): PostureDataEntity?
    
    /**
     * 获取数据总数
     */
    @Query("SELECT COUNT(*) FROM posture_data")
    suspend fun getPostureDataCount(): Int
    
    /**
     * 获取指定时间范围内的数据统计
     */
    @Query("""
        SELECT 
            COUNT(*) as total_count,
            AVG(angle_x) as avg_angle_x,
            AVG(angle_y) as avg_angle_y,
            AVG(angle_z) as avg_angle_z,
            AVG(battery_level) as avg_battery,
            AVG(signal_strength) as avg_signal
        FROM posture_data 
        WHERE timestamp BETWEEN :startTime AND :endTime
    """)
    suspend fun getPostureDataStats(startTime: Long, endTime: Long): PostureDataStats?
    
    /**
     * 获取指定时间范围内各坐姿状态的统计
     */
    @Query("""
        SELECT 
            posture_status,
            COUNT(*) as count,
            AVG(confidence) as avg_confidence
        FROM posture_data 
        WHERE timestamp BETWEEN :startTime AND :endTime
        GROUP BY posture_status
    """)
    suspend fun getPostureStatusStats(startTime: Long, endTime: Long): List<PostureStatusStat>
}

/**
 * 坐姿数据统计结果
 */
data class PostureDataStats(
    val total_count: Int,
    val avg_angle_x: Float,
    val avg_angle_y: Float,
    val avg_angle_z: Float,
    val avg_battery: Float,
    val avg_signal: Float
)

/**
 * 坐姿状态统计结果
 */
data class PostureStatusStat(
    val posture_status: String,
    val count: Int,
    val avg_confidence: Float
)
