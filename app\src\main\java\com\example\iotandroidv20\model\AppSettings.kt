package com.example.iotandroidv20.model

/**
 * 应用设置数据模型
 */
data class AppSettings(
    // 监控设置
    val monitoringEnabled: Boolean = true,
    val autoStartMonitoring: Boolean = false,
    val monitoringInterval: Long = 5000L, // 5秒
    
    // 提醒设置
    val reminderEnabled: Boolean = true,
    val reminderInterval: Long = 300000L, // 5分钟
    val maxRemindersPerHour: Int = 10,
    val reminderSound: Boolean = true,
    val reminderVibration: Boolean = true,
    
    // 坐姿阈值设置
    val goodPostureThreshold: Float = 8f,
    val warningPostureThreshold: Float = 15f,
    val badPostureThreshold: Float = 30f,
    
    // 数据设置
    val dataCacheEnabled: Boolean = true,
    val dataCacheDays: Int = 30,
    val autoCleanupEnabled: Boolean = true,
    val exportDataEnabled: Boolean = true,
    
    // 显示设置
    val showDetailedStats: Boolean = true,
    val show3DVisualization: Boolean = true,
    val showHistoryChart: Boolean = true,
    val darkModeEnabled: Boolean = false,
    
    // 网络设置
    val autoReconnect: Boolean = true,
    val connectionTimeout: Long = 30000L, // 30秒
    val retryAttempts: Int = 3,
    
    // 调试设置
    val debugMode: Boolean = false,
    val verboseLogging: Boolean = false,
    val showTokenStatus: Boolean = false,

    // 学习监督设置
    val postureMonitoringEnabled: Boolean = true,
    val focusMonitoringEnabled: Boolean = true,
    val fatigueMonitoringEnabled: Boolean = true,
    val environmentMonitoringEnabled: Boolean = true,
    val realTimeAnalysisEnabled: Boolean = true,
    val dataCollectionInterval: Long = 5000L, // 5秒
    val alertSensitivity: Float = 0.7f // 0.0-1.0
) {
    
    /**
     * 获取提醒间隔的分钟数
     */
    fun getReminderIntervalMinutes(): Int {
        return (reminderInterval / 60000L).toInt()
    }
    
    /**
     * 获取监控间隔的秒数
     */
    fun getMonitoringIntervalSeconds(): Int {
        return (monitoringInterval / 1000L).toInt()
    }
    
    /**
     * 获取连接超时的秒数
     */
    fun getConnectionTimeoutSeconds(): Int {
        return (connectionTimeout / 1000L).toInt()
    }
    
    /**
     * 验证设置的有效性
     */
    fun isValid(): Boolean {
        return monitoringInterval > 0 &&
                reminderInterval > 0 &&
                maxRemindersPerHour > 0 &&
                goodPostureThreshold > 0 &&
                warningPostureThreshold > goodPostureThreshold &&
                badPostureThreshold > warningPostureThreshold &&
                dataCacheDays > 0 &&
                connectionTimeout > 0 &&
                retryAttempts > 0
    }
    
    /**
     * 获取设置摘要
     */
    fun getSummary(): Map<String, String> {
        return mapOf(
            "监控间隔" to "${getMonitoringIntervalSeconds()}秒",
            "提醒间隔" to "${getReminderIntervalMinutes()}分钟",
            "每小时最大提醒" to "${maxRemindersPerHour}次",
            "良好坐姿阈值" to "${goodPostureThreshold}°",
            "警告坐姿阈值" to "${warningPostureThreshold}°",
            "不良坐姿阈值" to "${badPostureThreshold}°",
            "数据缓存天数" to "${dataCacheDays}天",
            "连接超时" to "${getConnectionTimeoutSeconds()}秒",
            "重试次数" to "${retryAttempts}次"
        )
    }
    
    companion object {
        /**
         * 默认设置
         */
        fun getDefault(): AppSettings {
            return AppSettings()
        }
        
        /**
         * 高性能设置（更频繁的监控和提醒）
         */
        fun getHighPerformance(): AppSettings {
            return AppSettings(
                monitoringInterval = 2000L, // 2秒
                reminderInterval = 180000L, // 3分钟
                maxRemindersPerHour = 15,
                goodPostureThreshold = 5f,
                warningPostureThreshold = 10f,
                badPostureThreshold = 20f
            )
        }
        
        /**
         * 省电设置（较少的监控和提醒）
         */
        fun getPowerSaving(): AppSettings {
            return AppSettings(
                monitoringInterval = 10000L, // 10秒
                reminderInterval = 600000L, // 10分钟
                maxRemindersPerHour = 5,
                autoReconnect = false,
                show3DVisualization = false,
                verboseLogging = false
            )
        }
        
        /**
         * 调试设置
         */
        fun getDebug(): AppSettings {
            return AppSettings(
                debugMode = true,
                verboseLogging = true,
                showTokenStatus = true,
                showDetailedStats = true
            )
        }
    }
}
