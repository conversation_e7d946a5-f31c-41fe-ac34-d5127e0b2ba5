{"logs": [{"outputFile": "com.example.iotandroidv20.app-mergeReleaseResources-58:/values-ta/values-ta.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2c60f935d2795008c652ca71073c0e75\\transformed\\media3-ui-1.2.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,505,700,789,878,959,1064,1168,1247,1313,1409,1506,1577,1642,1704,1776,1923,2066,2215,2284,2368,2441,2521,2623,2725,2792,2860,2913,2976,3024,3085,3152,3217,3278,3347,3410,3473,3539,3602,3669,3723,3787,3865,3943", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,88,80,104,103,78,65,95,96,70,64,61,71,146,142,148,68,83,72,79,101,101,66,67,52,62,47,60,66,64,60,68,62,62,65,62,66,53,63,77,77,55", "endOffsets": "280,500,695,784,873,954,1059,1163,1242,1308,1404,1501,1572,1637,1699,1771,1918,2061,2210,2279,2363,2436,2516,2618,2720,2787,2855,2908,2971,3019,3080,3147,3212,3273,3342,3405,3468,3534,3597,3664,3718,3782,3860,3938,3994"}, "to": {"startLines": "2,11,15,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,555,2111,2200,2289,2370,2475,2579,2658,2724,2820,2917,2988,3053,3115,3187,3334,3477,3626,3695,3779,3852,3932,4034,4136,4203,4979,5032,5095,5143,5204,5271,5336,5397,5466,5529,5592,5658,5721,5788,5842,5906,5984,6062", "endLines": "10,14,18,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83", "endColumns": "17,12,12,88,88,80,104,103,78,65,95,96,70,64,61,71,146,142,148,68,83,72,79,101,101,66,67,52,62,47,60,66,64,60,68,62,62,65,62,66,53,63,77,77,55", "endOffsets": "330,550,745,2195,2284,2365,2470,2574,2653,2719,2815,2912,2983,3048,3110,3182,3329,3472,3621,3690,3774,3847,3927,4029,4131,4198,4266,5027,5090,5138,5199,5266,5331,5392,5461,5524,5587,5653,5716,5783,5837,5901,5979,6057,6113"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\31c73cb6dfb12faac49eb1bae4282a74\\transformed\\core-1.16.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "20,21,22,23,24,25,26,157", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "818,914,1017,1116,1214,1321,1436,14063", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "909,1012,1111,1209,1316,1431,1559,14159"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\752167a3e67da85263f648420d161268\\transformed\\ui-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,380,481,572,655,764,855,950,1032,1118,1208,1293,1366,1437,1517,1586", "endColumns": "96,83,93,100,90,82,108,90,94,81,85,89,84,72,70,79,68,119", "endOffsets": "197,281,375,476,567,650,759,850,945,1027,1113,1203,1288,1361,1432,1512,1581,1701"}, "to": {"startLines": "27,28,29,31,32,84,85,149,150,151,152,153,154,155,156,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1564,1661,1745,1919,2020,6118,6201,13390,13481,13576,13658,13744,13834,13919,13992,14164,14244,14313", "endColumns": "96,83,93,100,90,82,108,90,94,81,85,89,84,72,70,79,68,119", "endOffsets": "1656,1740,1834,2015,2106,6196,6305,13476,13571,13653,13739,13829,13914,13987,14058,14239,14308,14428"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5dca3f3744b4fa46ea145e6d4fc9225c\\transformed\\media3-exoplayer-1.2.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,197,266,336,418,499,596,681", "endColumns": "68,72,68,69,81,80,96,84,81", "endOffsets": "119,192,261,331,413,494,591,676,758"}, "to": {"startLines": "57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4271,4340,4413,4482,4552,4634,4715,4812,4897", "endColumns": "68,72,68,69,81,80,96,84,81", "endOffsets": "4335,4408,4477,4547,4629,4710,4807,4892,4974"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\398f61ea23c76807315a7831064b1215\\transformed\\material3-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,181,307,428,552,653,749,862,1013,1144,1285,1369,1473,1573,1681,1798,1921,2030,2176,2320,2454,2660,2789,2910,3035,3181,3282,3380,3526,3662,3768,3881,3988,4134,4286,4395,4507,4585,4687,4790,4907,4993,5086,5199,5279,5367,5466,5586,5681,5786,5875,5997,6101,6208,6341,6421,6532", "endColumns": "125,125,120,123,100,95,112,150,130,140,83,103,99,107,116,122,108,145,143,133,205,128,120,124,145,100,97,145,135,105,112,106,145,151,108,111,77,101,102,116,85,92,112,79,87,98,119,94,104,88,121,103,106,132,79,110,101", "endOffsets": "176,302,423,547,648,744,857,1008,1139,1280,1364,1468,1568,1676,1793,1916,2025,2171,2315,2449,2655,2784,2905,3030,3176,3277,3375,3521,3657,3763,3876,3983,4129,4281,4390,4502,4580,4682,4785,4902,4988,5081,5194,5274,5362,5461,5581,5676,5781,5870,5992,6096,6203,6336,6416,6527,6629"}, "to": {"startLines": "86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6310,6436,6562,6683,6807,6908,7004,7117,7268,7399,7540,7624,7728,7828,7936,8053,8176,8285,8431,8575,8709,8915,9044,9165,9290,9436,9537,9635,9781,9917,10023,10136,10243,10389,10541,10650,10762,10840,10942,11045,11162,11248,11341,11454,11534,11622,11721,11841,11936,12041,12130,12252,12356,12463,12596,12676,12787", "endColumns": "125,125,120,123,100,95,112,150,130,140,83,103,99,107,116,122,108,145,143,133,205,128,120,124,145,100,97,145,135,105,112,106,145,151,108,111,77,101,102,116,85,92,112,79,87,98,119,94,104,88,121,103,106,132,79,110,101", "endOffsets": "6431,6557,6678,6802,6903,6999,7112,7263,7394,7535,7619,7723,7823,7931,8048,8171,8280,8426,8570,8704,8910,9039,9160,9285,9431,9532,9630,9776,9912,10018,10131,10238,10384,10536,10645,10757,10835,10937,11040,11157,11243,11336,11449,11529,11617,11716,11836,11931,12036,12125,12247,12351,12458,12591,12671,12782,12884"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\01b6e8b8fea8bb45b55852d4590f3437\\transformed\\foundation-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,148", "endColumns": "92,97", "endOffsets": "143,241"}, "to": {"startLines": "161,162", "startColumns": "4,4", "startOffsets": "14433,14526", "endColumns": "92,97", "endOffsets": "14521,14619"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\835843a1fca13b839a80c71cfb075e12\\transformed\\media3-session-1.2.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,123,203,279,354,433,515,607", "endColumns": "67,79,75,74,78,81,91,96", "endOffsets": "118,198,274,349,428,510,602,699"}, "to": {"startLines": "19,30,143,144,145,146,147,148", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "750,1839,12889,12965,13040,13119,13201,13293", "endColumns": "67,79,75,74,78,81,91,96", "endOffsets": "813,1914,12960,13035,13114,13196,13288,13385"}}]}]}