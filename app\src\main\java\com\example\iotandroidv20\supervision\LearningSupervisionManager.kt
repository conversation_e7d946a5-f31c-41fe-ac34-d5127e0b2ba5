package com.example.iotandroidv20.supervision

import android.content.Context
import com.example.iotandroidv20.model.*
import com.example.iotandroidv20.intelligence.LearningGuidanceEngine
import com.example.iotandroidv20.eeg.EEGManager
import com.example.iotandroidv20.iot.TokenIoTManager
import com.example.iotandroidv20.utils.Logger
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import kotlin.math.*

/**
 * 学习监督管理器 - 集成坐姿、专注度、疲劳度的综合监控
 */
class LearningSupervisionManager(
    private val context: Context,
    private val eegManager: EEGManager,
    private val iotManager: TokenIoTManager
) {
    
    private val coroutineScope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    
    // 智能指导引擎
    private val guidanceEngine = LearningGuidanceEngine()
    
    // 状态流
    private val _currentSession = MutableStateFlow<LearningSupervisionSession?>(null)
    val currentSession: StateFlow<LearningSupervisionSession?> = _currentSession.asStateFlow()
    
    private val _realTimeStatus = MutableStateFlow<RealTimeLearningStatus?>(null)
    val realTimeStatus: StateFlow<RealTimeLearningStatus?> = _realTimeStatus.asStateFlow()
    
    private val _parentDashboard = MutableStateFlow<ParentDashboardData?>(null)
    val parentDashboard: StateFlow<ParentDashboardData?> = _parentDashboard.asStateFlow()
    
    // 配置和档案
    private var supervisionConfig: LearningSupervisionConfig? = null
    private var childProfile: ChildProfile? = null
    
    // 监控任务
    private var monitoringJob: Job? = null
    private var analysisJob: Job? = null
    private var guidanceJob: Job? = null
    
    companion object {
        private const val TAG = "LearningSupervisionManager"
        private const val MONITORING_INTERVAL = 5000L // 5秒
        private const val ANALYSIS_INTERVAL = 10000L // 10秒
        private const val GUIDANCE_INTERVAL = 30000L // 30秒
    }
    
    /**
     * 初始化学习监督
     */
    fun initialize(config: LearningSupervisionConfig) {
        this.supervisionConfig = config
        this.childProfile = config.childProfile
        Logger.d("学习监督管理器初始化完成", tag = TAG)
    }
    
    /**
     * 开始学习监督会话
     */
    suspend fun startLearningSession(
        sessionType: LearningSessionType,
        targetDuration: Int // 分钟
    ): Boolean {
        return try {
            Logger.d("开始学习监督会话: ${sessionType.displayName}, 目标时长: ${targetDuration}分钟", tag = TAG)
            
            // 1. 创建新会话
            val session = LearningSupervisionSession(
                sessionId = generateSessionId(),
                childId = childProfile?.age?.toString() ?: "unknown",
                startTime = System.currentTimeMillis(),
                sessionType = sessionType,
                targetDuration = targetDuration,
                status = SessionStatus.PREPARING,
                metrics = createInitialMetrics()
            )
            
            _currentSession.value = session
            
            // 2. 启动各种监控
            if (supervisionConfig?.monitoringSettings?.postureMonitoringEnabled == true) {
                startPostureMonitoring()
            }
            
            if (supervisionConfig?.monitoringSettings?.focusMonitoringEnabled == true) {
                startFocusMonitoring()
            }
            
            if (supervisionConfig?.monitoringSettings?.fatigueMonitoringEnabled == true) {
                startFatigueMonitoring()
            }
            
            // 3. 启动实时分析
            startRealTimeAnalysis()
            
            // 4. 启动智能指导
            startIntelligentGuidance()
            
            // 5. 更新会话状态
            updateSessionStatus(SessionStatus.ACTIVE)
            
            Logger.d("学习监督会话启动成功", tag = TAG)
            true
            
        } catch (e: Exception) {
            Logger.e("启动学习监督会话失败: ${e.message}", tag = TAG)
            false
        }
    }
    
    /**
     * 暂停学习会话
     */
    suspend fun pauseLearningSession() {
        Logger.d("暂停学习会话", tag = TAG)
        updateSessionStatus(SessionStatus.PAUSED)
        pauseMonitoring()
    }
    
    /**
     * 恢复学习会话
     */
    suspend fun resumeLearningSession() {
        Logger.d("恢复学习会话", tag = TAG)
        updateSessionStatus(SessionStatus.ACTIVE)
        resumeMonitoring()
    }
    
    /**
     * 结束学习会话
     */
    suspend fun endLearningSession(): SessionSummary? {
        return try {
            Logger.d("结束学习会话", tag = TAG)
            
            val currentSession = _currentSession.value ?: return null
            
            // 1. 停止所有监控
            stopAllMonitoring()
            
            // 2. 生成会话总结
            val summary = generateSessionSummary(currentSession)
            
            // 3. 更新会话状态
            val finalSession = currentSession.copy(
                endTime = System.currentTimeMillis(),
                status = SessionStatus.COMPLETED,
                summary = summary,
                actualDuration = ((System.currentTimeMillis() - currentSession.startTime) / 60000).toInt()
            )
            
            _currentSession.value = finalSession
            
            // 4. 清理状态
            _realTimeStatus.value = null
            
            Logger.d("学习会话结束，总时长: ${finalSession.actualDuration}分钟", tag = TAG)
            summary
            
        } catch (e: Exception) {
            Logger.e("结束学习会话失败: ${e.message}", tag = TAG)
            null
        }
    }
    
    /**
     * 启动坐姿监控
     */
    private suspend fun startPostureMonitoring() {
        Logger.d("启动坐姿监控", tag = TAG)
        // 这里会集成现有的IoT坐姿监控功能
        // iotManager.startPostureMonitoring() // 暂时注释掉，方法不存在
        Logger.d("坐姿监控已启动（模拟）", tag = TAG)
    }
    
    /**
     * 启动专注度监控
     */
    private suspend fun startFocusMonitoring() {
        Logger.d("启动专注度监控", tag = TAG)
        eegManager.startRecording()
    }
    
    /**
     * 启动疲劳度监控
     */
    private suspend fun startFatigueMonitoring() {
        Logger.d("启动疲劳度监控", tag = TAG)
        // 疲劳度监控通常与EEG监控集成
    }
    
    /**
     * 启动实时分析
     */
    private fun startRealTimeAnalysis() {
        analysisJob = coroutineScope.launch {
            while (isActive && _currentSession.value?.status == SessionStatus.ACTIVE) {
                try {
                    performRealTimeAnalysis()
                    delay(ANALYSIS_INTERVAL)
                } catch (e: Exception) {
                    Logger.e("实时分析异常: ${e.message}", tag = TAG)
                }
            }
        }
    }
    
    /**
     * 启动智能指导
     */
    private fun startIntelligentGuidance() {
        guidanceJob = coroutineScope.launch {
            while (isActive && _currentSession.value?.status == SessionStatus.ACTIVE) {
                try {
                    generateIntelligentGuidance()
                    delay(GUIDANCE_INTERVAL)
                } catch (e: Exception) {
                    Logger.e("智能指导异常: ${e.message}", tag = TAG)
                }
            }
        }
    }
    
    /**
     * 执行实时分析
     */
    private suspend fun performRealTimeAnalysis() {
        val currentSession = _currentSession.value ?: return
        val childProfile = this.childProfile ?: return
        
        // 1. 收集当前数据
        val currentState = collectCurrentLearningState()
        
        // 2. 更新实时状态
        val realTimeStatus = RealTimeLearningStatus(
            currentFocusLevel = currentState.focusLevel,
            currentFatigueLevel = currentState.fatigueLevel,
            currentPostureQuality = currentState.postureQuality,
            sessionProgress = calculateSessionProgress(currentSession),
            timeRemaining = calculateTimeRemaining(currentSession),
            currentRecommendation = getCurrentRecommendation(currentState),
            nextMilestone = getNextMilestone(currentSession, currentState),
            parentDashboardData = generateParentDashboardData(currentSession, currentState)
        )
        
        _realTimeStatus.value = realTimeStatus
        _parentDashboard.value = realTimeStatus.parentDashboardData
        
        // 3. 检查是否需要干预
        checkForInterventions(currentState, currentSession)
    }
    
    /**
     * 生成智能指导
     */
    private suspend fun generateIntelligentGuidance() {
        val currentSession = _currentSession.value ?: return
        val childProfile = this.childProfile ?: return
        
        val currentState = collectCurrentLearningState()
        val guidance = guidanceEngine.generateLearningGuidance(
            childProfile, currentState, currentSession
        )
        
        Logger.d("生成智能指导: ${guidance.immediateActions.size}个即时建议", tag = TAG)
    }
    
    /**
     * 收集当前学习状态
     */
    private suspend fun collectCurrentLearningState(): LearningState {
        // 从各个管理器收集数据
        val focusAssessment = eegManager.focusAssessment.value
        val fatigueAssessment = eegManager.fatigueAssessment.value
        // val postureData = iotManager.currentPostureData.value // 属性不存在，暂时注释

        return LearningState(
            focusLevel = focusAssessment?.focusLevel ?: FocusLevel.MODERATE,
            fatigueLevel = fatigueAssessment?.fatigueLevel ?: FatigueLevel.LOW,
            postureQuality = PostureQuality.GOOD, // 暂时使用默认值
            sessionDuration = _currentSession.value?.let {
                System.currentTimeMillis() - it.startTime
            } ?: 0L,
            currentEnvironment = detectCurrentEnvironment(),
            brainwavePattern = createBrainwavePattern(),
            trendAnalysis = createTrendAnalysis()
        )
    }
    
    /**
     * 生成会话总结
     */
    private fun generateSessionSummary(session: LearningSupervisionSession): SessionSummary {
        val performance = determinePerformanceLevel(session)
        
        return SessionSummary(
            overallPerformance = performance,
            strengths = generateStrengths(session),
            areasForImprovement = generateImprovementAreas(session),
            keyInsights = generateKeyInsights(session),
            recommendations = generateRecommendations(session),
            nextSessionPreparation = generateNextSessionPrep(session),
            parentGuidance = generateParentGuidance(session),
            celebrationMoments = generateCelebrationMoments(session)
        )
    }
    
    // 辅助方法
    private fun generateSessionId(): String = "session_${System.currentTimeMillis()}"
    
    private fun createInitialMetrics() = LearningMetrics(
        postureMetrics = PostureMetrics(
            averageScore = 0f,
            goodPosturePercentage = 0f,
            poorPostureEvents = 0,
            longestGoodPostureStreak = 0,
            postureStability = 0f,
            improvementTrend = Trend.STABLE,
            commonIssues = emptyList()
        ),
        focusMetrics = FocusMetrics(0f, 0f, 0f, 0, 0, 0f, FocusPattern.STEADY_FOCUS, emptyList()),
        fatigueMetrics = FatigueMetrics(0f, 0, 0f, 0f, 0f, 0f, 0f),
        engagementMetrics = EngagementMetrics(0f, 0f, 0, 0, 0f, 0f),
        environmentMetrics = EnvironmentMetrics(0f, 0f, 0, 0f, 0f),
        progressMetrics = ProgressMetrics(0f, 0f, 0f, 0f, 0f, 0f)
    )
    
    private fun updateSessionStatus(status: SessionStatus) {
        _currentSession.value = _currentSession.value?.copy(status = status)
    }
    
    private fun pauseMonitoring() {
        monitoringJob?.cancel()
        analysisJob?.cancel()
        guidanceJob?.cancel()
    }
    
    private fun resumeMonitoring() {
        startRealTimeAnalysis()
        startIntelligentGuidance()
    }
    
    private fun stopAllMonitoring() {
        monitoringJob?.cancel()
        analysisJob?.cancel()
        guidanceJob?.cancel()
        // eegManager.stopRecording() // 这是suspend函数，需要在协程中调用
        // iotManager.stopPostureMonitoring() // 方法不存在，暂时注释
        Logger.d("所有监控已停止（模拟）", tag = TAG)
    }
    
    private fun calculateSessionProgress(session: LearningSupervisionSession): Float {
        val elapsed = (System.currentTimeMillis() - session.startTime) / 60000f
        return (elapsed / session.targetDuration).coerceIn(0f, 1f)
    }
    
    private fun calculateTimeRemaining(session: LearningSupervisionSession): Int {
        val elapsed = (System.currentTimeMillis() - session.startTime) / 60000
        return maxOf(0, session.targetDuration - elapsed.toInt())
    }
    
    private fun getCurrentRecommendation(state: LearningState): String {
        return when {
            state.fatigueLevel == FatigueLevel.HIGH -> "建议休息5-10分钟"
            state.focusLevel == FocusLevel.LOW -> "尝试深呼吸或调整坐姿"
            state.postureQuality == PostureQuality.POOR -> "请调整坐姿"
            else -> "保持当前状态，继续学习"
        }
    }
    
    private fun getNextMilestone(session: LearningSupervisionSession, state: LearningState): String {
        val progress = calculateSessionProgress(session)
        return when {
            progress < 0.25f -> "完成第一个25%"
            progress < 0.5f -> "达到学习中点"
            progress < 0.75f -> "进入最后冲刺阶段"
            else -> "即将完成学习目标"
        }
    }
    
    private fun generateParentDashboardData(session: LearningSupervisionSession, state: LearningState): ParentDashboardData {
        return ParentDashboardData(
            childCurrentStatus = when (state.focusLevel) {
                FocusLevel.HIGH, FocusLevel.VERY_HIGH -> "专注学习中"
                FocusLevel.MODERATE -> "正常学习中"
                else -> "需要关注"
            },
            todayProgress = calculateSessionProgress(session),
            weeklyTrends = mapOf(
                "专注度" to Trend.STABLE,
                "坐姿" to Trend.IMPROVING,
                "疲劳度" to Trend.STABLE
            ),
            upcomingRecommendations = listOf("建议在下午2-4点安排学习时间"),
            celebrationMoments = if (state.focusLevel == FocusLevel.HIGH) listOf("专注度表现优秀！") else emptyList(),
            concernAreas = if (state.postureQuality == PostureQuality.POOR) listOf("坐姿需要改善") else emptyList()
        )
    }
    
    private fun checkForInterventions(state: LearningState, session: LearningSupervisionSession) {
        // 检查是否需要干预措施
        val interventions = mutableListOf<Intervention>()
        
        if (state.fatigueLevel == FatigueLevel.HIGH) {
            interventions.add(Intervention(
                timestamp = System.currentTimeMillis(),
                type = InterventionType.BREAK_SUGGESTION,
                trigger = InterventionTrigger.HIGH_FATIGUE,
                action = "建议立即休息10分钟"
            ))
        }
        
        if (state.postureQuality == PostureQuality.POOR) {
            interventions.add(Intervention(
                timestamp = System.currentTimeMillis(),
                type = InterventionType.POSTURE_REMINDER,
                trigger = InterventionTrigger.POOR_POSTURE,
                action = "请调整坐姿"
            ))
        }
        
        // 应用干预措施
        interventions.forEach { intervention ->
            applyIntervention(intervention)
        }
    }
    
    private fun applyIntervention(intervention: Intervention) {
        Logger.d("应用干预措施: ${intervention.action}", tag = TAG)
        // 这里可以触发通知、语音提醒等
    }
    
    private fun detectCurrentEnvironment() = DetectedEnvironment(
        NoiseLevel.NORMAL, LightingCondition.NORMAL, TimeOfDay.MORNING, DayOfWeek.MONDAY
    )
    
    private fun createBrainwavePattern() = BrainwavePattern(
        BrainwaveType.ACTIVE_LEARNING, 0.8f, 0.7f, 0.9f, 10.5f
    )
    
    private fun createTrendAnalysis() = TrendAnalysis(
        Trend.STABLE, Trend.STABLE, Trend.STABLE, Trend.STABLE, 30
    )
    
    private fun determinePerformanceLevel(session: LearningSupervisionSession) = PerformanceLevel.GOOD
    private fun generateStrengths(session: LearningSupervisionSession) = listOf("专注度稳定", "坐姿良好")
    private fun generateImprovementAreas(session: LearningSupervisionSession) = listOf("可以适当延长学习时间")
    private fun generateKeyInsights(session: LearningSupervisionSession) = listOf("上午学习效果最佳")
    private fun generateRecommendations(session: LearningSupervisionSession) = listOf("保持当前学习节奏")
    private fun generateNextSessionPrep(session: LearningSupervisionSession) = listOf("准备充足的水和健康零食")
    private fun generateParentGuidance(session: LearningSupervisionSession) = listOf("孩子学习状态良好，请继续鼓励")
    private fun generateCelebrationMoments(session: LearningSupervisionSession) = listOf("今天的专注度比昨天提高了15%！")
    
    /**
     * 清理资源
     */
    fun cleanup() {
        Logger.d("清理学习监督管理器资源", tag = TAG)
        stopAllMonitoring()
        coroutineScope.cancel()
    }
}
