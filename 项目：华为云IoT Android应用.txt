项目：华为云IoT Android应用
当前模块：[项目架构 + 基础配置]
前置依赖：[请阅读上一个窗口创建的关键文件]
实现目标：[本窗口在前置文件的基础上要完成剩余的任务
华为云SDK集成配置
连接华为云这部分先放下，首先完成基本的项目框架后续在加入华为云调试
Gradle依赖配置
基础工具类其中剩余的功能]
要求：尽量少改动文件结构和命名，可在原有基础上增删调整。
其他设备配置信息：硬件模块：stm32N6,通信模块l610
其他项目背景信息:
android studio 版本：
Android Studio Meerkat Feature Drop | 2024.3.2
Build #AI-243.25659.59.2432.13423653, built on April 30, 2025
Runtime version: 21.0.6+-13368085-b895.109 amd64
VM: OpenJDK 64-Bit Server VM by JetBrains s.r.o.
Toolkit: sun.awt.windows.WToolkit
Windows 11.0
Kotlin plugin: K2 mode
GC: G1 Young Generation, G1 Concurrent GC, G1 Old Generation
Memory: 2048M
Cores: 24
Registry:
  ide.experimental.ui=true
Non-Bundled Plugins:
  com.github.platan.gradle-dependencies-formatter (0.6.1)
  com.github.copilot (1.5.46-243)
gradle 构建工具版本: 8.11.1
JDK:17
compileSdk:34
minSdk:24