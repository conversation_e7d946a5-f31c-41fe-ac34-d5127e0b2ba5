#!/bin/bash

# 华为云OBS存储桶配置自动化脚本
# 用途：自动创建和配置学习监督项目所需的OBS存储桶

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 配置变量
BUCKET_NAME="learning-supervision-obs"
REGION="cn-north-4"
STORAGE_CLASS="STANDARD"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查华为云CLI是否已安装和配置
check_hcloud_cli() {
    log_info "检查华为云CLI配置..."
    
    if ! command -v hcloud &> /dev/null; then
        log_error "华为云CLI未安装，请先安装华为云CLI"
        exit 1
    fi
    
    if ! hcloud configure list &> /dev/null; then
        log_error "华为云CLI未配置，请先运行 'hcloud configure' 配置认证信息"
        exit 1
    fi
    
    log_info "华为云CLI配置检查通过"
}

# 创建OBS存储桶
create_bucket() {
    log_info "创建OBS存储桶: $BUCKET_NAME"
    
    if hcloud obs mb obs://$BUCKET_NAME --region $REGION --storage-class $STORAGE_CLASS 2>/dev/null; then
        log_info "✅ OBS存储桶创建成功"
    else
        log_warn "OBS存储桶可能已存在，继续配置..."
    fi
    
    # 验证桶是否存在
    if hcloud obs ls | grep -q $BUCKET_NAME; then
        log_info "✅ OBS存储桶验证成功"
    else
        log_error "❌ OBS存储桶创建失败"
        exit 1
    fi
}

# 创建目录结构
create_directory_structure() {
    log_info "创建OBS目录结构..."
    
    # 创建标记文件来建立目录结构
    local temp_file=$(mktemp)
    echo "Directory marker" > $temp_file
    
    # 创建主要目录
    local directories=(
        "video/raw/"
        "video/processed/"
        "audio/raw/"
        "audio/processed/"
        "logs/upload/"
        "logs/processing/"
        "logs/access/"
        "temp/uploads/"
        "temp/processing/"
    )
    
    for dir in "${directories[@]}"; do
        if hcloud obs cp $temp_file obs://$BUCKET_NAME/${dir}.gitkeep 2>/dev/null; then
            log_info "✅ 创建目录: $dir"
        else
            log_warn "目录可能已存在: $dir"
        fi
    done
    
    rm -f $temp_file
}

# 配置桶策略
configure_bucket_policy() {
    log_info "配置OBS桶策略..."
    
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local config_dir="$script_dir/../configs"
    
    # 创建桶策略文件
    cat > "$config_dir/bucket-policy.json" << 'EOF'
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "DeviceUploadPolicy",
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::*:user/learning-supervision-device-user"
      },
      "Action": [
        "s3:PutObject",
        "s3:PutObjectAcl"
      ],
      "Resource": [
        "arn:aws:s3:::learning-supervision-obs/video/*/raw/*",
        "arn:aws:s3:::learning-supervision-obs/audio/*/raw/*"
      ]
    },
    {
      "Sid": "AppAccessPolicy",
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::*:user/learning-supervision-app-user"
      },
      "Action": [
        "s3:GetObject",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::learning-supervision-obs",
        "arn:aws:s3:::learning-supervision-obs/video/*/processed/*",
        "arn:aws:s3:::learning-supervision-obs/audio/*/processed/*"
      ]
    }
  ]
}
EOF
    
    if hcloud obs put-bucket-policy obs://$BUCKET_NAME --policy file://"$config_dir/bucket-policy.json" 2>/dev/null; then
        log_info "✅ 桶策略配置成功"
    else
        log_warn "桶策略配置可能失败，请手动检查"
    fi
}

# 配置生命周期规则
configure_lifecycle() {
    log_info "配置OBS生命周期规则..."
    
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local config_dir="$script_dir/../configs"
    
    # 创建生命周期配置文件
    cat > "$config_dir/lifecycle-config.json" << 'EOF'
{
  "Rules": [
    {
      "ID": "VideoLifecycleRule",
      "Status": "Enabled",
      "Filter": {
        "Prefix": "video/"
      },
      "Transitions": [
        {
          "Days": 30,
          "StorageClass": "WARM"
        },
        {
          "Days": 365,
          "StorageClass": "COLD"
        }
      ]
    },
    {
      "ID": "AudioLifecycleRule",
      "Status": "Enabled",
      "Filter": {
        "Prefix": "audio/"
      },
      "Transitions": [
        {
          "Days": 30,
          "StorageClass": "WARM"
        },
        {
          "Days": 365,
          "StorageClass": "COLD"
        }
      ]
    },
    {
      "ID": "TempFileCleanup",
      "Status": "Enabled",
      "Filter": {
        "Prefix": "temp/"
      },
      "Expiration": {
        "Days": 7
      }
    },
    {
      "ID": "LogFileCleanup",
      "Status": "Enabled",
      "Filter": {
        "Prefix": "logs/"
      },
      "Expiration": {
        "Days": 90
      }
    }
  ]
}
EOF
    
    if hcloud obs put-bucket-lifecycle obs://$BUCKET_NAME --lifecycle-configuration file://"$config_dir/lifecycle-config.json" 2>/dev/null; then
        log_info "✅ 生命周期规则配置成功"
    else
        log_warn "生命周期规则配置可能失败，请手动检查"
    fi
}

# 启用版本控制
enable_versioning() {
    log_info "启用OBS版本控制..."
    
    if hcloud obs put-bucket-versioning obs://$BUCKET_NAME --versioning-configuration Status=Enabled 2>/dev/null; then
        log_info "✅ 版本控制启用成功"
    else
        log_warn "版本控制启用可能失败，请手动检查"
    fi
}

# 配置CORS
configure_cors() {
    log_info "配置OBS CORS规则..."
    
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local config_dir="$script_dir/../configs"
    
    # 创建CORS配置文件
    cat > "$config_dir/cors-config.json" << 'EOF'
{
  "CORSRules": [
    {
      "AllowedOrigins": [
        "https://*.learningsupervision.com",
        "http://localhost:*"
      ],
      "AllowedMethods": [
        "GET",
        "PUT",
        "POST",
        "DELETE",
        "HEAD"
      ],
      "AllowedHeaders": [
        "*"
      ],
      "ExposeHeaders": [
        "ETag",
        "x-obs-request-id"
      ],
      "MaxAgeSeconds": 3600
    }
  ]
}
EOF
    
    if hcloud obs put-bucket-cors obs://$BUCKET_NAME --cors-configuration file://"$config_dir/cors-config.json" 2>/dev/null; then
        log_info "✅ CORS规则配置成功"
    else
        log_warn "CORS规则配置可能失败，请手动检查"
    fi
}

# 启用服务端加密
enable_encryption() {
    log_info "启用OBS服务端加密..."
    
    local encryption_config='{
        "Rules": [
            {
                "ApplyServerSideEncryptionByDefault": {
                    "SSEAlgorithm": "AES256"
                }
            }
        ]
    }'
    
    if hcloud obs put-bucket-encryption obs://$BUCKET_NAME --server-side-encryption-configuration "$encryption_config" 2>/dev/null; then
        log_info "✅ 服务端加密启用成功"
    else
        log_warn "服务端加密启用可能失败，请手动检查"
    fi
}

# 配置访问日志
configure_logging() {
    log_info "配置OBS访问日志..."
    
    local logging_config='{
        "LoggingEnabled": {
            "TargetBucket": "'$BUCKET_NAME'",
            "TargetPrefix": "logs/access/"
        }
    }'
    
    if hcloud obs put-bucket-logging obs://$BUCKET_NAME --bucket-logging-status "$logging_config" 2>/dev/null; then
        log_info "✅ 访问日志配置成功"
    else
        log_warn "访问日志配置可能失败，请手动检查"
    fi
}

# 测试OBS功能
test_obs_functionality() {
    log_info "测试OBS基本功能..."
    
    # 创建测试文件
    local test_file=$(mktemp)
    echo "OBS functionality test - $(date)" > $test_file
    
    # 测试上传
    if hcloud obs cp $test_file obs://$BUCKET_NAME/test/functionality-test.txt 2>/dev/null; then
        log_info "✅ 文件上传测试成功"
    else
        log_error "❌ 文件上传测试失败"
        rm -f $test_file
        return 1
    fi
    
    # 测试下载
    local download_file=$(mktemp)
    if hcloud obs cp obs://$BUCKET_NAME/test/functionality-test.txt $download_file 2>/dev/null; then
        log_info "✅ 文件下载测试成功"
    else
        log_error "❌ 文件下载测试失败"
        rm -f $test_file $download_file
        return 1
    fi
    
    # 测试列表
    if hcloud obs ls obs://$BUCKET_NAME/test/ 2>/dev/null | grep -q "functionality-test.txt"; then
        log_info "✅ 文件列表测试成功"
    else
        log_error "❌ 文件列表测试失败"
        rm -f $test_file $download_file
        return 1
    fi
    
    # 清理测试文件
    hcloud obs rm obs://$BUCKET_NAME/test/functionality-test.txt 2>/dev/null || true
    rm -f $test_file $download_file
    
    log_info "✅ OBS功能测试完成"
}

# 显示配置摘要
show_configuration_summary() {
    log_info "OBS配置摘要:"
    echo "=================================="
    echo "存储桶名称: $BUCKET_NAME"
    echo "区域: $REGION"
    echo "存储类别: $STORAGE_CLASS"
    echo "版本控制: 已启用"
    echo "服务端加密: AES256"
    echo "生命周期规则: 已配置"
    echo "CORS规则: 已配置"
    echo "访问日志: 已启用"
    echo "=================================="
}

# 主函数
main() {
    log_info "开始OBS存储桶配置..."
    
    # 创建配置目录
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local config_dir="$script_dir/../configs"
    mkdir -p "$config_dir"
    
    check_hcloud_cli
    create_bucket
    create_directory_structure
    configure_bucket_policy
    configure_lifecycle
    enable_versioning
    configure_cors
    enable_encryption
    configure_logging
    test_obs_functionality
    show_configuration_summary
    
    log_info "🎉 OBS存储桶配置完成！"
    log_info "存储桶地址: obs://$BUCKET_NAME"
    log_warn "⚠️  请定期监控存储使用量和成本"
}

# 执行主函数
main "$@"
