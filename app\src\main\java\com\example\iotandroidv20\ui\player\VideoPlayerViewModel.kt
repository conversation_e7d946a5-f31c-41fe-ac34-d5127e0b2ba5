package com.example.iotandroidv20.ui.player

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.media3.exoplayer.ExoPlayer
import com.example.iotandroidv20.obs.PlayerInfo
import com.example.iotandroidv20.obs.VideoSession
import com.example.iotandroidv20.player.VideoPlayerManager
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 视频播放器界面的ViewModel
 */
class VideoPlayerViewModel : ViewModel() {
    
    private var videoPlayerManager: VideoPlayerManager? = null
    private var controlsHideJob: Job? = null
    
    private val _uiState = MutableStateFlow(VideoPlayerUiState())
    val uiState: StateFlow<VideoPlayerUiState> = _uiState.asStateFlow()
    
    /**
     * 初始化播放器
     */
    fun initialize(context: Context, videoSession: VideoSession) {
        try {
            // 创建播放器管理器
            videoPlayerManager = VideoPlayerManager(context).apply {
                // 添加播放器事件监听器
                addPlayerListener(object : VideoPlayerManager.PlayerEventListener {
                    override fun onPlayerStateChanged(isPlaying: Boolean, playbackState: Int) {
                        // 播放器状态变化时自动隐藏控制器
                        if (isPlaying) {
                            scheduleControlsHide()
                        }
                    }
                    
                    override fun onPlayerError(error: Exception) {
                        _uiState.value = _uiState.value.copy(
                            error = error.message ?: "播放出错"
                        )
                    }
                })
                
                // 初始化播放器
                initializePlayer()
                
                // 开始播放视频
                playVideoSession(videoSession)
            }
            
            // 监听播放器信息变化
            viewModelScope.launch {
                videoPlayerManager?.playerInfo?.collect { playerInfo ->
                    _uiState.value = _uiState.value.copy(playerInfo = playerInfo)
                }
            }
            
            // 显示控制器
            showControls()
            
        } catch (e: Exception) {
            _uiState.value = _uiState.value.copy(
                error = "初始化播放器失败: ${e.message}"
            )
        }
    }
    
    /**
     * 获取ExoPlayer实例
     */
    fun getExoPlayer(): ExoPlayer? {
        return videoPlayerManager?.getExoPlayer()
    }
    
    /**
     * 播放/暂停切换
     */
    fun togglePlayPause() {
        videoPlayerManager?.togglePlayPause()
        showControls()
        scheduleControlsHide()
    }
    
    /**
     * 跳转到指定位置
     */
    fun seekTo(positionMs: Long) {
        videoPlayerManager?.seekTo(positionMs)
        showControls()
        scheduleControlsHide()
    }
    
    /**
     * 设置播放速度
     */
    fun setPlaybackSpeed(speed: Float) {
        videoPlayerManager?.setPlaybackSpeed(speed)
        showControls()
        scheduleControlsHide()
    }
    
    /**
     * 设置音量
     */
    fun setVolume(volume: Float) {
        videoPlayerManager?.setVolume(volume)
        showControls()
        scheduleControlsHide()
    }
    
    /**
     * 切换控制器显示/隐藏
     */
    fun toggleControls() {
        if (_uiState.value.isControlsVisible) {
            hideControls()
        } else {
            showControls()
            scheduleControlsHide()
        }
    }
    
    /**
     * 重试播放
     */
    fun retry() {
        _uiState.value = _uiState.value.copy(error = "")
        videoPlayerManager?.getCurrentVideoSession()?.let { videoSession ->
            videoPlayerManager?.playVideoSession(videoSession)
        }
    }
    
    /**
     * 释放播放器资源
     */
    fun releasePlayer() {
        try {
            controlsHideJob?.cancel()
            videoPlayerManager?.releasePlayer()
            videoPlayerManager = null
            
            _uiState.value = VideoPlayerUiState()
            
        } catch (e: Exception) {
            // 忽略释放时的错误
        }
    }
    
    /**
     * 显示控制器
     */
    private fun showControls() {
        _uiState.value = _uiState.value.copy(isControlsVisible = true)
    }
    
    /**
     * 隐藏控制器
     */
    private fun hideControls() {
        _uiState.value = _uiState.value.copy(isControlsVisible = false)
        controlsHideJob?.cancel()
    }
    
    /**
     * 安排控制器自动隐藏
     */
    private fun scheduleControlsHide() {
        controlsHideJob?.cancel()
        controlsHideJob = viewModelScope.launch {
            delay(3000) // 3秒后自动隐藏
            hideControls()
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        releasePlayer()
    }
}

/**
 * 视频播放器界面的UI状态
 */
data class VideoPlayerUiState(
    val playerInfo: PlayerInfo = PlayerInfo(
        state = com.example.iotandroidv20.obs.PlaybackState.IDLE,
        currentPosition = 0L,
        duration = 0L,
        bufferedPosition = 0L
    ),
    val isControlsVisible: Boolean = true,
    val error: String = ""
)
