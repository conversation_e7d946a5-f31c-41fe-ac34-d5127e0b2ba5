package com.example.iotandroidv20.ui.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.iotandroidv20.model.*

/**
 * 学习监督主界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LearningSupervisionScreen(
    currentSession: LearningSupervisionSession?,
    realTimeStatus: RealTimeLearningStatus?,
    currentGuidance: LearningGuidance?,
    onStartSession: (LearningSessionType, Int) -> Unit,
    onPauseSession: () -> Unit,
    onResumeSession: () -> Unit,
    onEndSession: () -> Unit,
    onBack: () -> Unit
) {
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("学习监督") },
                navigationIcon = {
                    IconButton(onClick = onBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    if (currentSession != null) {
                        when (currentSession.status) {
                            SessionStatus.ACTIVE -> {
                                IconButton(onClick = onPauseSession) {
                                    Icon(Icons.Default.Pause, contentDescription = "暂停")
                                }
                                IconButton(onClick = onEndSession) {
                                    Icon(Icons.Default.Stop, contentDescription = "结束")
                                }
                            }
                            SessionStatus.PAUSED -> {
                                IconButton(onClick = onResumeSession) {
                                    Icon(Icons.Default.PlayArrow, contentDescription = "继续")
                                }
                                IconButton(onClick = onEndSession) {
                                    Icon(Icons.Default.Stop, contentDescription = "结束")
                                }
                            }
                            else -> {}
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            if (currentSession == null) {
                // 显示开始学习界面
                item {
                    StartLearningCard(onStartSession = onStartSession)
                }
            } else {
                // 显示学习监督界面
                item {
                    SessionOverviewCard(
                        session = currentSession,
                        realTimeStatus = realTimeStatus
                    )
                }
                
                if (realTimeStatus != null) {
                    item {
                        RealTimeMetricsCard(realTimeStatus = realTimeStatus)
                    }
                }
                
                if (currentGuidance != null) {
                    item {
                        IntelligentGuidanceCard(guidance = currentGuidance)
                    }
                }
                
                item {
                    ParentDashboardCard(
                        parentData = realTimeStatus?.parentDashboardData
                    )
                }
            }
        }
    }
}

/**
 * 开始学习卡片
 */
@Composable
fun StartLearningCard(
    onStartSession: (LearningSessionType, Int) -> Unit
) {
    var selectedSessionType by remember { mutableStateOf(LearningSessionType.HOMEWORK) }
    var targetDuration by remember { mutableStateOf(45) }
    var showDurationDialog by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.School,
                contentDescription = "开始学习",
                modifier = Modifier.size(64.dp),
                tint = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "开始学习监督",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold
            )
            
            Text(
                text = "选择学习类型和时长，开启智能监督模式",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 学习类型选择
            Text(
                text = "学习类型",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            LazyColumn(
                modifier = Modifier.height(200.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(LearningSessionType.values()) { sessionType ->
                    SessionTypeItem(
                        sessionType = sessionType,
                        isSelected = selectedSessionType == sessionType,
                        onSelect = { selectedSessionType = sessionType }
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 时长设置
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "目标时长",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                
                TextButton(
                    onClick = { showDurationDialog = true }
                ) {
                    Text("${targetDuration}分钟")
                    Icon(Icons.Default.Edit, contentDescription = "编辑")
                }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            Button(
                onClick = { onStartSession(selectedSessionType, targetDuration) },
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(Icons.Default.PlayArrow, contentDescription = null)
                Spacer(modifier = Modifier.width(8.dp))
                Text("开始学习")
            }
        }
    }
    
    // 时长选择对话框
    if (showDurationDialog) {
        DurationPickerDialog(
            currentDuration = targetDuration,
            onDurationSelected = { targetDuration = it },
            onDismiss = { showDurationDialog = false }
        )
    }
}

/**
 * 学习类型选择项
 */
@Composable
fun SessionTypeItem(
    sessionType: LearningSessionType,
    isSelected: Boolean,
    onSelect: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) 
                MaterialTheme.colorScheme.primaryContainer 
            else 
                MaterialTheme.colorScheme.surface
        ),
        onClick = onSelect
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = getSessionTypeIcon(sessionType),
                contentDescription = null,
                tint = if (isSelected) 
                    MaterialTheme.colorScheme.primary 
                else 
                    MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = sessionType.displayName,
                    fontWeight = FontWeight.Medium,
                    color = if (isSelected) 
                        MaterialTheme.colorScheme.primary 
                    else 
                        MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = "建议时长: ${sessionType.recommendedDuration}分钟",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            if (isSelected) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = "已选择",
                    tint = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}

/**
 * 会话概览卡片
 */
@Composable
fun SessionOverviewCard(
    session: LearningSupervisionSession,
    realTimeStatus: RealTimeLearningStatus?
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = session.sessionType.displayName,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold
                )
                
                StatusChip(status = session.status)
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            if (realTimeStatus != null) {
                // 进度条
                LinearProgressIndicator(
                    progress = realTimeStatus.sessionProgress,
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "进度: ${(realTimeStatus.sessionProgress * 100).toInt()}%",
                        fontSize = 14.sp
                    )
                    Text(
                        text = "剩余: ${realTimeStatus.timeRemaining}分钟",
                        fontSize = 14.sp
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 当前建议
                if (realTimeStatus.currentRecommendation.isNotEmpty()) {
                    Card(
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.secondaryContainer
                        )
                    ) {
                        Row(
                            modifier = Modifier.padding(12.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Lightbulb,
                                contentDescription = "建议",
                                tint = MaterialTheme.colorScheme.secondary
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = realTimeStatus.currentRecommendation,
                                fontSize = 14.sp
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 实时指标卡片
 */
@Composable
fun RealTimeMetricsCard(
    realTimeStatus: RealTimeLearningStatus
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "实时状态",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                MetricItem(
                    icon = Icons.Default.Psychology,
                    label = "专注度",
                    value = realTimeStatus.currentFocusLevel.displayName,
                    color = getFocusLevelColor(realTimeStatus.currentFocusLevel)
                )
                
                MetricItem(
                    icon = Icons.Default.BatteryAlert,
                    label = "疲劳度",
                    value = realTimeStatus.currentFatigueLevel.displayName,
                    color = getFatigueLevelColor(realTimeStatus.currentFatigueLevel)
                )
                
                MetricItem(
                    icon = Icons.Default.Chair,
                    label = "坐姿",
                    value = realTimeStatus.currentPostureQuality.displayName,
                    color = getPostureQualityColor(realTimeStatus.currentPostureQuality)
                )
            }
        }
    }
}

/**
 * 智能指导卡片
 */
@Composable
fun IntelligentGuidanceCard(
    guidance: LearningGuidance
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "智能建议",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 即时行动建议
            if (guidance.immediateActions.isNotEmpty()) {
                Text(
                    text = "即时建议",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                guidance.immediateActions.forEach { action ->
                    ImmediateActionItem(action = action)
                    Spacer(modifier = Modifier.height(8.dp))
                }
            }
            
            // 环境建议
            if (guidance.environmentRecommendations.priority != RecommendationPriority.LOW) {
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "环境优化",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                EnvironmentRecommendationItem(
                    recommendations = guidance.environmentRecommendations
                )
            }
        }
    }
}

// 辅助函数
private fun getSessionTypeIcon(sessionType: LearningSessionType) = when (sessionType) {
    LearningSessionType.HOMEWORK -> Icons.Default.Assignment
    LearningSessionType.READING -> Icons.Default.MenuBook
    LearningSessionType.CREATIVE -> Icons.Default.Palette
    LearningSessionType.PRACTICE -> Icons.Default.Repeat
    LearningSessionType.REVIEW -> Icons.Default.Quiz
    LearningSessionType.PROJECT -> Icons.Default.Build
    LearningSessionType.FREE_STUDY -> Icons.Default.School
}

private fun getFocusLevelColor(level: FocusLevel) = when (level) {
    FocusLevel.VERY_HIGH, FocusLevel.HIGH -> Color(0xFF4CAF50)
    FocusLevel.MODERATE -> Color(0xFFFF9800)
    FocusLevel.LOW, FocusLevel.VERY_LOW -> Color(0xFFF44336)
}

private fun getFatigueLevelColor(level: FatigueLevel) = when (level) {
    FatigueLevel.NONE, FatigueLevel.LOW, FatigueLevel.MILD -> Color(0xFF4CAF50)
    FatigueLevel.MODERATE -> Color(0xFFFF9800)
    FatigueLevel.HIGH, FatigueLevel.SEVERE, FatigueLevel.EXTREME -> Color(0xFFF44336)
    FatigueLevel.MENTAL, FatigueLevel.PHYSICAL -> Color(0xFFFF5722)
}

private fun getPostureQualityColor(quality: PostureQuality) = when (quality) {
    PostureQuality.EXCELLENT, PostureQuality.GOOD -> Color(0xFF4CAF50)
    PostureQuality.FAIR -> Color(0xFFFF9800)
    PostureQuality.POOR, PostureQuality.VERY_POOR -> Color(0xFFF44336)
}

@Composable
private fun StatusChip(status: SessionStatus) {
    val color = when (status) {
        SessionStatus.ACTIVE -> Color(0xFF4CAF50)
        SessionStatus.PAUSED -> Color(0xFFFF9800)
        SessionStatus.COMPLETED -> Color(0xFF2196F3)
        else -> Color(0xFF9E9E9E)
    }
    
    Surface(
        color = color.copy(alpha = 0.1f),
        shape = MaterialTheme.shapes.small
    ) {
        Text(
            text = status.displayName,
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
            fontSize = 12.sp,
            color = color
        )
    }
}

@Composable
private fun MetricItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    label: String,
    value: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            tint = color,
            modifier = Modifier.size(24.dp)
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = value,
            fontSize = 12.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            text = label,
            fontSize = 10.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
private fun ImmediateActionItem(action: ImmediateAction) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = when (action.urgency) {
                ActionUrgency.CRITICAL -> MaterialTheme.colorScheme.errorContainer
                ActionUrgency.HIGH -> MaterialTheme.colorScheme.primaryContainer
                ActionUrgency.MEDIUM -> MaterialTheme.colorScheme.secondaryContainer
                ActionUrgency.LOW -> MaterialTheme.colorScheme.surfaceVariant
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = when (action.urgency) {
                        ActionUrgency.CRITICAL -> Icons.Default.Warning
                        ActionUrgency.HIGH -> Icons.Default.PriorityHigh
                        ActionUrgency.MEDIUM -> Icons.Default.Info
                        ActionUrgency.LOW -> Icons.Default.Lightbulb
                    },
                    contentDescription = action.urgency.displayName,
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = action.action,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium
                )
            }
            if (action.reason.isNotEmpty()) {
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = action.reason,
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
private fun EnvironmentRecommendationItem(
    recommendations: EnvironmentRecommendations
) {
    Column {
        if (recommendations.lighting.action != LightingAction.MAINTAIN_CURRENT) {
            RecommendationRow(
                icon = Icons.Default.Lightbulb,
                title = "光照",
                recommendation = recommendations.lighting.action.displayName,
                reason = recommendations.lighting.reason
            )
        }
        
        if (recommendations.noise.action != NoiseAction.MAINTAIN_CURRENT) {
            RecommendationRow(
                icon = Icons.Default.VolumeUp,
                title = "声音",
                recommendation = recommendations.noise.action.displayName,
                reason = recommendations.noise.reason
            )
        }
        
        if (recommendations.workspace.action != WorkspaceAction.MAINTAIN_CURRENT) {
            RecommendationRow(
                icon = Icons.Default.Chair,
                title = "工作空间",
                recommendation = recommendations.workspace.action.displayName,
                reason = recommendations.workspace.reason
            )
        }
    }
}

@Composable
private fun RecommendationRow(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    recommendation: String,
    reason: String
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = title,
            modifier = Modifier.size(16.dp),
            tint = MaterialTheme.colorScheme.primary
        )
        Spacer(modifier = Modifier.width(8.dp))
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = "$title: $recommendation",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = reason,
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun ParentDashboardCard(
    parentData: ParentDashboardData?
) {
    if (parentData == null) return
    
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "家长视角",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "当前状态: ${parentData.childCurrentStatus}",
                fontSize = 14.sp
            )
            
            if (parentData.celebrationMoments.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                parentData.celebrationMoments.forEach { moment ->
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Star,
                            contentDescription = "表扬",
                            tint = Color(0xFFFFD700),
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = moment,
                            fontSize = 14.sp,
                            color = Color(0xFFFFD700)
                        )
                    }
                }
            }
            
            if (parentData.concernAreas.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                parentData.concernAreas.forEach { concern ->
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Warning,
                            contentDescription = "关注",
                            tint = MaterialTheme.colorScheme.error,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = concern,
                            fontSize = 14.sp,
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun DurationPickerDialog(
    currentDuration: Int,
    onDurationSelected: (Int) -> Unit,
    onDismiss: () -> Unit
) {
    var selectedDuration by remember { mutableStateOf(currentDuration) }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("选择学习时长") },
        text = {
            Column {
                Text("选择目标学习时长（分钟）")
                Spacer(modifier = Modifier.height(16.dp))
                
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    IconButton(
                        onClick = { if (selectedDuration > 15) selectedDuration -= 15 }
                    ) {
                        Icon(Icons.Default.Remove, contentDescription = "减少")
                    }
                    
                    Text(
                        text = "$selectedDuration 分钟",
                        modifier = Modifier.padding(horizontal = 16.dp),
                        fontSize = 18.sp
                    )
                    
                    IconButton(
                        onClick = { if (selectedDuration < 180) selectedDuration += 15 }
                    ) {
                        Icon(Icons.Default.Add, contentDescription = "增加")
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    onDurationSelected(selectedDuration)
                    onDismiss()
                }
            ) {
                Text("确定")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}
