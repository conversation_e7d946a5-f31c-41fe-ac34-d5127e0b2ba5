1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.iotandroidv20"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- 网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:8:5-76
14-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:8:22-73
15
16    <!-- MQTT服务权限 -->
17    <uses-permission android:name="android.permission.WAKE_LOCK" />
17-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:11:5-68
17-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:11:22-65
18    <uses-permission
18-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:12:5-13:38
19        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
19-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:12:22-78
20        android:maxSdkVersion="28" />
20-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:13:9-35
21
22    <permission
22-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
23        android:name="com.example.iotandroidv20.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
23-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
24        android:protectionLevel="signature" />
24-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
25
26    <uses-permission android:name="com.example.iotandroidv20.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
26-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
26-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
27
28    <application
28-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:15:5-41:19
29        android:allowBackup="true"
29-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:16:9-35
30        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
30-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
31        android:dataExtractionRules="@xml/data_extraction_rules"
31-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:17:9-65
32        android:extractNativeLibs="false"
33        android:fullBackupContent="@xml/backup_rules"
33-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:18:9-54
34        android:icon="@mipmap/ic_launcher"
34-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:19:9-43
35        android:label="@string/app_name"
35-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:20:9-41
36        android:networkSecurityConfig="@xml/network_security_config"
36-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:25:9-69
37        android:roundIcon="@mipmap/ic_launcher_round"
37-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:21:9-54
38        android:supportsRtl="true"
38-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:22:9-35
39        android:theme="@style/Theme.IotAndroidV20"
39-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:23:9-51
40        android:usesCleartextTraffic="true" >
40-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:24:9-44
41        <activity
41-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:27:9-37:20
42            android:name="com.example.iotandroidv20.MainActivity"
42-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:28:13-41
43            android:exported="true"
43-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:29:13-36
44            android:label="@string/app_name"
44-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:30:13-45
45            android:theme="@style/Theme.IotAndroidV20" >
45-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:31:13-55
46            <intent-filter>
46-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:32:13-36:29
47                <action android:name="android.intent.action.MAIN" />
47-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:33:17-69
47-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:33:25-66
48
49                <category android:name="android.intent.category.LAUNCHER" />
49-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:35:17-77
49-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:35:27-74
50            </intent-filter>
51        </activity>
52
53        <!-- MQTT服务 -->
54        <service android:name="org.eclipse.paho.android.service.MqttService" />
54-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:40:9-80
54-->C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:40:18-77
55
56        <provider
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
57            android:name="androidx.startup.InitializationProvider"
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
58            android:authorities="com.example.iotandroidv20.androidx-startup"
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
59            android:exported="false" >
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
60            <meta-data
60-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
61                android:name="androidx.emoji2.text.EmojiCompatInitializer"
61-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
62                android:value="androidx.startup" />
62-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
63            <meta-data
63-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0005777f67894f5f1d095ec8641c55b8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
64                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
64-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0005777f67894f5f1d095ec8641c55b8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
65                android:value="androidx.startup" />
65-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0005777f67894f5f1d095ec8641c55b8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
66            <meta-data
66-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
67                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
68                android:value="androidx.startup" />
68-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
69        </provider>
70
71        <receiver
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
72            android:name="androidx.profileinstaller.ProfileInstallReceiver"
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
73            android:directBootAware="false"
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
74            android:enabled="true"
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
75            android:exported="true"
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
76            android:permission="android.permission.DUMP" >
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
78                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
79            </intent-filter>
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
81                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
82            </intent-filter>
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
84                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
85            </intent-filter>
86            <intent-filter>
86-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
87                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
87-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
87-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
88            </intent-filter>
89        </receiver>
90    </application>
91
92</manifest>
