-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:2:1-43:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:2:1-43:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:2:1-43:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:2:1-43:12
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c2a3ec570fcff7a4af0dcb26c4ac20c\transformed\coil-compose-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d1718a21afde4b63c098b6215c6fe7f\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c01906570a821eaddb602d864da8ca78\transformed\coil-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7235d196f56494969715e85d3652ab7a\transformed\coil-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05c7a864efea222398c1cdd0d89a32c1\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb18151aaf917c25a26fedc6cf286b2d\transformed\navigation-common-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3b7fa97e364d9ad8b2814831030b81d\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fcab351fa9c6b2cdf383352dfc520ae8\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f6cf93735bb81cfd0888eac91acb12d\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b2e2bf0e9775b94e315d9185f46687d\transformed\navigation-compose-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\398f61ea23c76807315a7831064b1215\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c00b816185abfca92d469b17ccfd85e\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dca3f3744b4fa46ea145e6d4fc9225c\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-session:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\835843a1fca13b839a80c71cfb075e12\transformed\media3-session-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a686cae471e3429a8b23210c9a7c5423\transformed\media3-extractor-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abaabb3212a9d4a2706bc5b03234890e\transformed\media3-container-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\076532d56d5ecf19bb61941693871f9d\transformed\media3-datasource-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0283f27bf725890e7f7b64158e103f6e\transformed\media3-decoder-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4df5ab8020f53237e0e34373e813748f\transformed\media3-database-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9a4b36439a72f435686b51f450a3632\transformed\media3-common-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-ui:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c60f935d2795008c652ca71073c0e75\transformed\media3-ui-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac6cf4e4cf3046c8796a2a18ffa7c217\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476832c20a3d9dd64162d4134700dd9c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d54f0b5d4da4389b63f0d8fa65a23ce\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca9a1989e4fb40b0a996c6c529b59cd5\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd785d79675de8add6b4b0fb3fb2ccc7\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba2cac711f302b6f20581763de200e3f\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e5aa7bdc369930b79313277e0e11e3f\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d89c21d82f6b6520aebbc416c45cdd2\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455f5565b0302375e88c75a79d32cbf3\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\300b6093ff08274c17de23235d109ac9\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01b6e8b8fea8bb45b55852d4590f3437\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ead9e45fef2eb7cdd78688caddb1e10\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfb8ff5e00e58f3670cadce3d263c65e\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe681a066bb9f16f82c1d9151f660d84\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b75b07d2429a0c33dc266adff362f\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f40427e470632624b00a5ee1917d6bd\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0005777f67894f5f1d095ec8641c55b8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c58315aeedd16e1bbe4b641a531a2e18\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2f564e131bf620eddaa6ac47b2e0c85\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a7b738fe73569af25e9727733e76bdb\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5afdd81734c4d833cc227fb3ab82e86e\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d988664e14e85d09db02f9c5ed015d4\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2dd4e01ddbfb796184cbc0dd78c972e5\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a1e544bfd28dfa7114f03c4611100d0\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c3afdb338e41ac30445e38334661457\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6a78435ea9b103085a8bda7e03c131f\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\de19e379d14d1f39ee96e7327b472e4c\transformed\media-1.6.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc697780e135e34f4fe8a74241973fdc\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\120aba5530967f823682dff5e31591c9\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b133f7f762cc9e5d1ac9c68451dbc4b1\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af7ff821d6306903c078395aa79eb7c5\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e2ab6f912e34b8499a75dc6d29929a1\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\22bc4d45b5cee2c46b108316c18aacf8\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e09d0326515e514bb831e61ed2892f09\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7a27c6faed9062dfec530816b9f514e\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\772622f5f112a5ab0a011a660e4668a2\transformed\lifecycle-livedata-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\950ec8429897d8d8a1a9dc497f556989\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad139ea1d90e6a96be969790ab4beece\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2bcc9b6de69352bbc08f73d19a51a72\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\752167a3e67da85263f648420d161268\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82f7be9abeb980aa60080eceafa53f63\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d3e1235aadef9afdb3676245f6331de\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e1953c86de81573fe85a247df34b805\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47ac6366278db220243b009b248be4a4\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f1b179f520ef77fe9de7a3e1cad1474\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3748d19ccb04c79ea61f25b72349cac3\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c9e35233ad111d8cce2498f3ccfd6d4\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6c4ac26bb9fdb9b854de273c8884c737\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bd6821415c472a8ee2b4482086533df\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5df48378003f00a6ffd82e6d2bb6b445\transformed\napier-debug\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbe20606275a7c8a2eba80a8bf227e82\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a805b0b630e3638b3ce495c9370794c\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7b928b4ae739136bec8fb15d5e5b1f9\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2d994ae1eb1d6872e0205116c65da83\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad3ffbfa1b979d5afc54adb66472197d\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10edba98b69ab65896b1a00117cae132\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dca3f3744b4fa46ea145e6d4fc9225c\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dca3f3744b4fa46ea145e6d4fc9225c\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9a4b36439a72f435686b51f450a3632\transformed\media3-common-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9a4b36439a72f435686b51f450a3632\transformed\media3-common-1.2.1\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:8:5-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:8:22-73
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:11:5-68
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:11:22-65
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:12:5-13:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:13:9-35
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:12:22-78
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:15:5-41:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:15:5-41:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd785d79675de8add6b4b0fb3fb2ccc7\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd785d79675de8add6b4b0fb3fb2ccc7\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0005777f67894f5f1d095ec8641c55b8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0005777f67894f5f1d095ec8641c55b8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82f7be9abeb980aa60080eceafa53f63\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82f7be9abeb980aa60080eceafa53f63\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbe20606275a7c8a2eba80a8bf227e82\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbe20606275a7c8a2eba80a8bf227e82\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2d994ae1eb1d6872e0205116c65da83\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2d994ae1eb1d6872e0205116c65da83\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:22:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:20:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:18:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:21:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:26:9-29
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:19:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:16:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:23:9-51
	android:networkSecurityConfig
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:25:9-69
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:17:9-65
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:24:9-44
activity#com.example.iotandroidv20.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:27:9-37:20
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:30:13-45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:29:13-36
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:31:13-55
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:28:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:32:13-36:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:33:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:33:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:35:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:35:27-74
service#org.eclipse.paho.android.service.MqttService
ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:40:9-80
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:40:18-77
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c2a3ec570fcff7a4af0dcb26c4ac20c\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c2a3ec570fcff7a4af0dcb26c4ac20c\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d1718a21afde4b63c098b6215c6fe7f\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d1718a21afde4b63c098b6215c6fe7f\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c01906570a821eaddb602d864da8ca78\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c01906570a821eaddb602d864da8ca78\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7235d196f56494969715e85d3652ab7a\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7235d196f56494969715e85d3652ab7a\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05c7a864efea222398c1cdd0d89a32c1\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05c7a864efea222398c1cdd0d89a32c1\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb18151aaf917c25a26fedc6cf286b2d\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb18151aaf917c25a26fedc6cf286b2d\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3b7fa97e364d9ad8b2814831030b81d\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3b7fa97e364d9ad8b2814831030b81d\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fcab351fa9c6b2cdf383352dfc520ae8\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fcab351fa9c6b2cdf383352dfc520ae8\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f6cf93735bb81cfd0888eac91acb12d\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f6cf93735bb81cfd0888eac91acb12d\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b2e2bf0e9775b94e315d9185f46687d\transformed\navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b2e2bf0e9775b94e315d9185f46687d\transformed\navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\398f61ea23c76807315a7831064b1215\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\398f61ea23c76807315a7831064b1215\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c00b816185abfca92d469b17ccfd85e\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c00b816185abfca92d469b17ccfd85e\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dca3f3744b4fa46ea145e6d4fc9225c\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dca3f3744b4fa46ea145e6d4fc9225c\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\835843a1fca13b839a80c71cfb075e12\transformed\media3-session-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\835843a1fca13b839a80c71cfb075e12\transformed\media3-session-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a686cae471e3429a8b23210c9a7c5423\transformed\media3-extractor-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a686cae471e3429a8b23210c9a7c5423\transformed\media3-extractor-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abaabb3212a9d4a2706bc5b03234890e\transformed\media3-container-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abaabb3212a9d4a2706bc5b03234890e\transformed\media3-container-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\076532d56d5ecf19bb61941693871f9d\transformed\media3-datasource-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\076532d56d5ecf19bb61941693871f9d\transformed\media3-datasource-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0283f27bf725890e7f7b64158e103f6e\transformed\media3-decoder-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0283f27bf725890e7f7b64158e103f6e\transformed\media3-decoder-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4df5ab8020f53237e0e34373e813748f\transformed\media3-database-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4df5ab8020f53237e0e34373e813748f\transformed\media3-database-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9a4b36439a72f435686b51f450a3632\transformed\media3-common-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9a4b36439a72f435686b51f450a3632\transformed\media3-common-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c60f935d2795008c652ca71073c0e75\transformed\media3-ui-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c60f935d2795008c652ca71073c0e75\transformed\media3-ui-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac6cf4e4cf3046c8796a2a18ffa7c217\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac6cf4e4cf3046c8796a2a18ffa7c217\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476832c20a3d9dd64162d4134700dd9c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476832c20a3d9dd64162d4134700dd9c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d54f0b5d4da4389b63f0d8fa65a23ce\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d54f0b5d4da4389b63f0d8fa65a23ce\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca9a1989e4fb40b0a996c6c529b59cd5\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca9a1989e4fb40b0a996c6c529b59cd5\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd785d79675de8add6b4b0fb3fb2ccc7\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd785d79675de8add6b4b0fb3fb2ccc7\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba2cac711f302b6f20581763de200e3f\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba2cac711f302b6f20581763de200e3f\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e5aa7bdc369930b79313277e0e11e3f\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e5aa7bdc369930b79313277e0e11e3f\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d89c21d82f6b6520aebbc416c45cdd2\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d89c21d82f6b6520aebbc416c45cdd2\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455f5565b0302375e88c75a79d32cbf3\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455f5565b0302375e88c75a79d32cbf3\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\300b6093ff08274c17de23235d109ac9\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\300b6093ff08274c17de23235d109ac9\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01b6e8b8fea8bb45b55852d4590f3437\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01b6e8b8fea8bb45b55852d4590f3437\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ead9e45fef2eb7cdd78688caddb1e10\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ead9e45fef2eb7cdd78688caddb1e10\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfb8ff5e00e58f3670cadce3d263c65e\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfb8ff5e00e58f3670cadce3d263c65e\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe681a066bb9f16f82c1d9151f660d84\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe681a066bb9f16f82c1d9151f660d84\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b75b07d2429a0c33dc266adff362f\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b75b07d2429a0c33dc266adff362f\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f40427e470632624b00a5ee1917d6bd\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f40427e470632624b00a5ee1917d6bd\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0005777f67894f5f1d095ec8641c55b8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0005777f67894f5f1d095ec8641c55b8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c58315aeedd16e1bbe4b641a531a2e18\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c58315aeedd16e1bbe4b641a531a2e18\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2f564e131bf620eddaa6ac47b2e0c85\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2f564e131bf620eddaa6ac47b2e0c85\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a7b738fe73569af25e9727733e76bdb\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a7b738fe73569af25e9727733e76bdb\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5afdd81734c4d833cc227fb3ab82e86e\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5afdd81734c4d833cc227fb3ab82e86e\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d988664e14e85d09db02f9c5ed015d4\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d988664e14e85d09db02f9c5ed015d4\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2dd4e01ddbfb796184cbc0dd78c972e5\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2dd4e01ddbfb796184cbc0dd78c972e5\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a1e544bfd28dfa7114f03c4611100d0\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a1e544bfd28dfa7114f03c4611100d0\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c3afdb338e41ac30445e38334661457\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c3afdb338e41ac30445e38334661457\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6a78435ea9b103085a8bda7e03c131f\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6a78435ea9b103085a8bda7e03c131f\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\de19e379d14d1f39ee96e7327b472e4c\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\de19e379d14d1f39ee96e7327b472e4c\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc697780e135e34f4fe8a74241973fdc\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc697780e135e34f4fe8a74241973fdc\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\120aba5530967f823682dff5e31591c9\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\120aba5530967f823682dff5e31591c9\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b133f7f762cc9e5d1ac9c68451dbc4b1\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b133f7f762cc9e5d1ac9c68451dbc4b1\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af7ff821d6306903c078395aa79eb7c5\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af7ff821d6306903c078395aa79eb7c5\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e2ab6f912e34b8499a75dc6d29929a1\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e2ab6f912e34b8499a75dc6d29929a1\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\22bc4d45b5cee2c46b108316c18aacf8\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\22bc4d45b5cee2c46b108316c18aacf8\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e09d0326515e514bb831e61ed2892f09\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e09d0326515e514bb831e61ed2892f09\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7a27c6faed9062dfec530816b9f514e\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7a27c6faed9062dfec530816b9f514e\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\772622f5f112a5ab0a011a660e4668a2\transformed\lifecycle-livedata-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\772622f5f112a5ab0a011a660e4668a2\transformed\lifecycle-livedata-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\950ec8429897d8d8a1a9dc497f556989\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\950ec8429897d8d8a1a9dc497f556989\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad139ea1d90e6a96be969790ab4beece\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad139ea1d90e6a96be969790ab4beece\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2bcc9b6de69352bbc08f73d19a51a72\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2bcc9b6de69352bbc08f73d19a51a72\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\752167a3e67da85263f648420d161268\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\752167a3e67da85263f648420d161268\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82f7be9abeb980aa60080eceafa53f63\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82f7be9abeb980aa60080eceafa53f63\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d3e1235aadef9afdb3676245f6331de\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d3e1235aadef9afdb3676245f6331de\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e1953c86de81573fe85a247df34b805\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e1953c86de81573fe85a247df34b805\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47ac6366278db220243b009b248be4a4\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47ac6366278db220243b009b248be4a4\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f1b179f520ef77fe9de7a3e1cad1474\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f1b179f520ef77fe9de7a3e1cad1474\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3748d19ccb04c79ea61f25b72349cac3\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3748d19ccb04c79ea61f25b72349cac3\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c9e35233ad111d8cce2498f3ccfd6d4\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c9e35233ad111d8cce2498f3ccfd6d4\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6c4ac26bb9fdb9b854de273c8884c737\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6c4ac26bb9fdb9b854de273c8884c737\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bd6821415c472a8ee2b4482086533df\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bd6821415c472a8ee2b4482086533df\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5df48378003f00a6ffd82e6d2bb6b445\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5df48378003f00a6ffd82e6d2bb6b445\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbe20606275a7c8a2eba80a8bf227e82\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbe20606275a7c8a2eba80a8bf227e82\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a805b0b630e3638b3ce495c9370794c\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a805b0b630e3638b3ce495c9370794c\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7b928b4ae739136bec8fb15d5e5b1f9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7b928b4ae739136bec8fb15d5e5b1f9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2d994ae1eb1d6872e0205116c65da83\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2d994ae1eb1d6872e0205116c65da83\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad3ffbfa1b979d5afc54adb66472197d\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad3ffbfa1b979d5afc54adb66472197d\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10edba98b69ab65896b1a00117cae132\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10edba98b69ab65896b1a00117cae132\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd785d79675de8add6b4b0fb3fb2ccc7\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd785d79675de8add6b4b0fb3fb2ccc7\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd785d79675de8add6b4b0fb3fb2ccc7\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0005777f67894f5f1d095ec8641c55b8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0005777f67894f5f1d095ec8641c55b8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2d994ae1eb1d6872e0205116c65da83\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2d994ae1eb1d6872e0205116c65da83\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944f6448c49ef5bbcd62d0ae5afd744e\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0005777f67894f5f1d095ec8641c55b8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0005777f67894f5f1d095ec8641c55b8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0005777f67894f5f1d095ec8641c55b8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.example.iotandroidv20.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.iotandroidv20.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c73cb6dfb12faac49eb1bae4282a74\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82f7be9abeb980aa60080eceafa53f63\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82f7be9abeb980aa60080eceafa53f63\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82f7be9abeb980aa60080eceafa53f63\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e02b9cf56841b30f903162a9d4c9de78\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
