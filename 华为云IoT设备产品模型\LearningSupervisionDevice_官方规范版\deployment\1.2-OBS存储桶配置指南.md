# OBS存储桶配置指南

## 🎯 配置目标

创建和配置华为云OBS存储桶，用于存储学习监督项目的音视频文件，包括原始分片文件和处理后的完整文件。

## 📋 存储需求分析

### **存储结构设计**
```
learning-supervision-obs/
├── video/                          # 视频文件根目录
│   ├── raw/                        # 原始分片文件
│   │   ├── {deviceId}/
│   │   │   ├── {date}/             # YYYYMMDD格式
│   │   │   │   ├── {sessionId}/
│   │   │   │   │   ├── segment_001.mp4
│   │   │   │   │   ├── segment_002.mp4
│   │   │   │   │   └── ...
│   ├── processed/                  # 处理后文件
│   │   ├── {deviceId}/
│   │   │   ├── {date}/
│   │   │   │   ├── complete_{sessionId}.mp4
│   │   │   │   ├── optimized_{sessionId}.mp4
│   │   │   │   └── thumbnail_{sessionId}.jpg
├── audio/                          # 音频文件根目录
│   ├── raw/                        # 原始分片文件
│   │   ├── {deviceId}/
│   │   │   ├── {date}/
│   │   │   │   ├── {sessionId}/
│   │   │   │   │   ├── segment_001.mp3
│   │   │   │   │   ├── segment_002.mp3
│   │   │   │   │   └── ...
│   ├── processed/                  # 处理后文件
│   │   ├── {deviceId}/
│   │   │   ├── {date}/
│   │   │   │   ├── complete_{sessionId}.mp3
│   │   │   │   └── optimized_{sessionId}.mp3
├── logs/                           # 日志文件
│   ├── upload/                     # 上传日志
│   ├── processing/                 # 处理日志
│   └── access/                     # 访问日志
└── temp/                           # 临时文件
    ├── uploads/                    # 上传中的文件
    └── processing/                 # 处理中的文件
```

### **存储类别选择**
- **原始分片文件**: 标准存储（频繁访问）
- **处理后文件**: 标准存储（应用端访问）
- **历史文件**: 低频访问存储（30天后转换）
- **归档文件**: 归档存储（1年后转换）

## 🔧 实施步骤

### **步骤1：创建OBS存储桶**

```bash
# 创建主存储桶
hcloud obs mb obs://learning-supervision-obs \
  --region cn-north-4 \
  --storage-class STANDARD

# 验证桶创建
hcloud obs ls | grep learning-supervision-obs
```

### **步骤2：配置桶策略**

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "DeviceUploadPolicy",
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::account:user/learning-supervision-device-user"
      },
      "Action": [
        "s3:PutObject",
        "s3:PutObjectAcl"
      ],
      "Resource": [
        "arn:aws:s3:::learning-supervision-obs/video/*/raw/*",
        "arn:aws:s3:::learning-supervision-obs/audio/*/raw/*"
      ]
    },
    {
      "Sid": "AppAccessPolicy",
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::account:user/learning-supervision-app-user"
      },
      "Action": [
        "s3:GetObject",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::learning-supervision-obs",
        "arn:aws:s3:::learning-supervision-obs/video/*/processed/*",
        "arn:aws:s3:::learning-supervision-obs/audio/*/processed/*"
      ]
    },
    {
      "Sid": "MPSProcessingPolicy",
      "Effect": "Allow",
      "Principal": {
        "Service": "mps.myhuaweicloud.com"
      },
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::learning-supervision-obs",
        "arn:aws:s3:::learning-supervision-obs/*"
      ]
    }
  ]
}
```

### **步骤3：配置生命周期规则**

```json
{
  "Rules": [
    {
      "ID": "VideoLifecycleRule",
      "Status": "Enabled",
      "Filter": {
        "Prefix": "video/"
      },
      "Transitions": [
        {
          "Days": 30,
          "StorageClass": "WARM"
        },
        {
          "Days": 365,
          "StorageClass": "COLD"
        }
      ],
      "Expiration": {
        "Days": 2555
      }
    },
    {
      "ID": "AudioLifecycleRule",
      "Status": "Enabled",
      "Filter": {
        "Prefix": "audio/"
      },
      "Transitions": [
        {
          "Days": 30,
          "StorageClass": "WARM"
        },
        {
          "Days": 365,
          "StorageClass": "COLD"
        }
      ],
      "Expiration": {
        "Days": 2555
      }
    },
    {
      "ID": "TempFileCleanup",
      "Status": "Enabled",
      "Filter": {
        "Prefix": "temp/"
      },
      "Expiration": {
        "Days": 7
      }
    },
    {
      "ID": "LogFileCleanup",
      "Status": "Enabled",
      "Filter": {
        "Prefix": "logs/"
      },
      "Expiration": {
        "Days": 90
      }
    }
  ]
}
```

### **步骤4：启用版本控制**

```bash
# 启用版本控制
hcloud obs put-bucket-versioning obs://learning-supervision-obs \
  --versioning-configuration Status=Enabled

# 配置版本清理规则
hcloud obs put-bucket-lifecycle obs://learning-supervision-obs \
  --lifecycle-configuration file://lifecycle-config.json
```

### **步骤5：配置跨域资源共享(CORS)**

```json
{
  "CORSRules": [
    {
      "AllowedOrigins": [
        "https://*.learningsupervision.com",
        "http://localhost:*"
      ],
      "AllowedMethods": [
        "GET",
        "PUT",
        "POST",
        "DELETE",
        "HEAD"
      ],
      "AllowedHeaders": [
        "*"
      ],
      "ExposeHeaders": [
        "ETag",
        "x-obs-request-id"
      ],
      "MaxAgeSeconds": 3600
    }
  ]
}
```

### **步骤6：配置事件通知**

```json
{
  "TopicConfigurations": [
    {
      "Id": "VideoUploadNotification",
      "Topic": "arn:aws:sns:cn-north-4:account:video-upload-topic",
      "Events": [
        "s3:ObjectCreated:*"
      ],
      "Filter": {
        "Key": {
          "FilterRules": [
            {
              "Name": "prefix",
              "Value": "video/"
            },
            {
              "Name": "suffix",
              "Value": ".mp4"
            }
          ]
        }
      }
    },
    {
      "Id": "AudioUploadNotification",
      "Topic": "arn:aws:sns:cn-north-4:account:audio-upload-topic",
      "Events": [
        "s3:ObjectCreated:*"
      ],
      "Filter": {
        "Key": {
          "FilterRules": [
            {
              "Name": "prefix",
              "Value": "audio/"
            },
            {
              "Name": "suffix",
              "Value": ".mp3"
            }
          ]
        }
      }
    }
  ]
}
```

## 🔐 安全配置

### **访问控制列表(ACL)**
```bash
# 设置桶ACL为私有
hcloud obs put-bucket-acl obs://learning-supervision-obs \
  --acl private

# 设置默认对象ACL
hcloud obs put-bucket-acl obs://learning-supervision-obs \
  --grant-read uri=http://acs.amazonaws.com/groups/global/AuthenticatedUsers
```

### **服务端加密**
```bash
# 启用服务端加密
hcloud obs put-bucket-encryption obs://learning-supervision-obs \
  --server-side-encryption-configuration '{
    "Rules": [
      {
        "ApplyServerSideEncryptionByDefault": {
          "SSEAlgorithm": "AES256"
        }
      }
    ]
  }'
```

### **访问日志配置**
```bash
# 启用访问日志
hcloud obs put-bucket-logging obs://learning-supervision-obs \
  --bucket-logging-status '{
    "LoggingEnabled": {
      "TargetBucket": "learning-supervision-obs",
      "TargetPrefix": "logs/access/"
    }
  }'
```

## 📊 监控配置

### **CloudWatch指标**
```json
{
  "MetricConfigurations": [
    {
      "Id": "StorageMetrics",
      "Filter": {
        "Prefix": ""
      },
      "OptionalFields": [
        "BucketSizeBytes",
        "NumberOfObjects"
      ]
    },
    {
      "Id": "RequestMetrics",
      "Filter": {
        "Prefix": ""
      },
      "OptionalFields": [
        "AllRequests",
        "GetRequests",
        "PutRequests"
      ]
    }
  ]
}
```

### **告警配置**
```bash
# 创建存储使用量告警
hcloud ces create-alarm \
  --alarm-name "obs-storage-usage-alarm" \
  --alarm-description "OBS存储使用量告警" \
  --metric-name "BucketSizeBytes" \
  --namespace "AWS/S3" \
  --statistic "Average" \
  --period 86400 \
  --evaluation-periods 1 \
  --threshold 1073741824000 \
  --comparison-operator "GreaterThanThreshold"

# 创建请求频率告警
hcloud ces create-alarm \
  --alarm-name "obs-request-rate-alarm" \
  --alarm-description "OBS请求频率告警" \
  --metric-name "AllRequests" \
  --namespace "AWS/S3" \
  --statistic "Sum" \
  --period 300 \
  --evaluation-periods 2 \
  --threshold 1000 \
  --comparison-operator "GreaterThanThreshold"
```

## 🧪 测试验证

### **功能测试脚本**
```bash
#!/bin/bash

echo "=== OBS存储桶功能测试 ==="

BUCKET_NAME="learning-supervision-obs"
TEST_FILE="test-upload.txt"

# 创建测试文件
echo "This is a test file for OBS upload" > $TEST_FILE

# 测试上传功能
echo "1. 测试文件上传..."
if hcloud obs cp $TEST_FILE obs://$BUCKET_NAME/test/; then
    echo "✅ 文件上传成功"
else
    echo "❌ 文件上传失败"
    exit 1
fi

# 测试下载功能
echo "2. 测试文件下载..."
if hcloud obs cp obs://$BUCKET_NAME/test/$TEST_FILE downloaded-$TEST_FILE; then
    echo "✅ 文件下载成功"
else
    echo "❌ 文件下载失败"
    exit 1
fi

# 测试列表功能
echo "3. 测试文件列表..."
if hcloud obs ls obs://$BUCKET_NAME/test/; then
    echo "✅ 文件列表成功"
else
    echo "❌ 文件列表失败"
    exit 1
fi

# 测试删除功能
echo "4. 测试文件删除..."
if hcloud obs rm obs://$BUCKET_NAME/test/$TEST_FILE; then
    echo "✅ 文件删除成功"
else
    echo "❌ 文件删除失败"
    exit 1
fi

# 清理测试文件
rm -f $TEST_FILE downloaded-$TEST_FILE

echo "=== OBS存储桶功能测试完成 ==="
```

### **性能测试**
```bash
#!/bin/bash

echo "=== OBS存储桶性能测试 ==="

BUCKET_NAME="learning-supervision-obs"

# 创建不同大小的测试文件
create_test_files() {
    dd if=/dev/zero of=test_1mb.dat bs=1M count=1
    dd if=/dev/zero of=test_10mb.dat bs=1M count=10
    dd if=/dev/zero of=test_100mb.dat bs=1M count=100
}

# 测试上传性能
test_upload_performance() {
    local file=$1
    local start_time=$(date +%s.%N)
    
    hcloud obs cp $file obs://$BUCKET_NAME/performance-test/
    
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc)
    local file_size=$(stat -c%s $file)
    local speed=$(echo "scale=2; $file_size / $duration / 1024 / 1024" | bc)
    
    echo "文件: $file, 大小: $(($file_size/1024/1024))MB, 时间: ${duration}s, 速度: ${speed}MB/s"
}

create_test_files

echo "测试上传性能..."
test_upload_performance "test_1mb.dat"
test_upload_performance "test_10mb.dat"
test_upload_performance "test_100mb.dat"

# 清理测试文件
rm -f test_*.dat
hcloud obs rm obs://$BUCKET_NAME/performance-test/ --recursive

echo "=== OBS存储桶性能测试完成 ==="
```

## ✅ 完成检查清单

- [ ] 创建OBS存储桶
- [ ] 配置桶策略
- [ ] 设置生命周期规则
- [ ] 启用版本控制
- [ ] 配置CORS规则
- [ ] 设置事件通知
- [ ] 配置访问控制
- [ ] 启用服务端加密
- [ ] 配置访问日志
- [ ] 设置监控告警
- [ ] 执行功能测试
- [ ] 执行性能测试

---

**注意**: 请根据实际需求调整存储类别和生命周期规则，定期监控存储使用量和成本。
