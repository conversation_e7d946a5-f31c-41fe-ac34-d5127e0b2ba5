{"logs": [{"outputFile": "com.example.iotandroidv20.app-mergeDebugResources-2:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\5dca3f3744b4fa46ea145e6d4fc9225c\\transformed\\media3-exoplayer-1.2.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,138,201,266,340,417,484,571,657", "endColumns": "82,62,64,73,76,66,86,85,68", "endOffsets": "133,196,261,335,412,479,566,652,721"}, "to": {"startLines": "57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4055,4138,4201,4266,4340,4417,4484,4571,4657", "endColumns": "82,62,64,73,76,66,86,85,68", "endOffsets": "4133,4196,4261,4335,4412,4479,4566,4652,4721"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\835843a1fca13b839a80c71cfb075e12\\transformed\\media3-session-1.2.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,133,208,277,349,422,499,593", "endColumns": "77,74,68,71,72,76,93,96", "endOffsets": "128,203,272,344,417,494,588,685"}, "to": {"startLines": "19,30,143,144,145,146,147,148", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "708,1794,12448,12517,12589,12662,12739,12833", "endColumns": "77,74,68,71,72,76,93,96", "endOffsets": "781,1864,12512,12584,12657,12734,12828,12925"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\31c73cb6dfb12faac49eb1bae4282a74\\transformed\\core-1.16.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "20,21,22,23,24,25,26,157", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "786,885,987,1087,1185,1292,1398,13595", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "880,982,1082,1180,1287,1393,1513,13691"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\752167a3e67da85263f648420d161268\\transformed\\ui-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,283,381,484,573,652,745,837,924,1010,1101,1178,1254,1334,1410,1492,1562", "endColumns": "95,81,97,102,88,78,92,91,86,85,90,76,75,79,75,81,69,120", "endOffsets": "196,278,376,479,568,647,740,832,919,1005,1096,1173,1249,1329,1405,1487,1557,1678"}, "to": {"startLines": "27,28,29,31,32,84,85,149,150,151,152,153,154,155,156,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1518,1614,1696,1869,1972,5865,5944,12930,13022,13109,13195,13286,13363,13439,13519,13696,13778,13848", "endColumns": "95,81,97,102,88,78,92,91,86,85,90,76,75,79,75,81,69,120", "endOffsets": "1609,1691,1789,1967,2056,5939,6032,13017,13104,13190,13281,13358,13434,13514,13590,13773,13843,13964"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\01b6e8b8fea8bb45b55852d4590f3437\\transformed\\foundation-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,155", "endColumns": "99,101", "endOffsets": "150,252"}, "to": {"startLines": "161,162", "startColumns": "4,4", "startOffsets": "13969,14069", "endColumns": "99,101", "endOffsets": "14064,14166"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\2c60f935d2795008c652ca71073c0e75\\transformed\\media3-ui-1.2.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,658,744,831,916,1012,1108,1183,1251,1346,1441,1507,1576,1642,1713,1821,1927,2034,2104,2191,2261,2341,2431,2522,2588,2652,2705,2763,2811,2870,2935,2997,3063,3135,3199,3260,3326,3391,3457,3510,3575,3654,3733", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,84,95,95,74,67,94,94,65,68,65,70,107,105,106,69,86,69,79,89,90,65,63,52,57,47,58,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "280,466,653,739,826,911,1007,1103,1178,1246,1341,1436,1502,1571,1637,1708,1816,1922,2029,2099,2186,2256,2336,2426,2517,2583,2647,2700,2758,2806,2865,2930,2992,3058,3130,3194,3255,3321,3386,3452,3505,3570,3649,3728,3786"}, "to": {"startLines": "2,11,15,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,521,2061,2147,2234,2319,2415,2511,2586,2654,2749,2844,2910,2979,3045,3116,3224,3330,3437,3507,3594,3664,3744,3834,3925,3991,4726,4779,4837,4885,4944,5009,5071,5137,5209,5273,5334,5400,5465,5531,5584,5649,5728,5807", "endLines": "10,14,18,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83", "endColumns": "17,12,12,85,86,84,95,95,74,67,94,94,65,68,65,70,107,105,106,69,86,69,79,89,90,65,63,52,57,47,58,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "330,516,703,2142,2229,2314,2410,2506,2581,2649,2744,2839,2905,2974,3040,3111,3219,3325,3432,3502,3589,3659,3739,3829,3920,3986,4050,4774,4832,4880,4939,5004,5066,5132,5204,5268,5329,5395,5460,5526,5579,5644,5723,5802,5860"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\398f61ea23c76807315a7831064b1215\\transformed\\material3-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,295,418,539,639,739,856,999,1117,1265,1350,1457,1554,1656,1770,1888,2000,2138,2275,2419,2588,2724,2844,2966,3096,3194,3290,3411,3546,3649,3763,3878,4015,4156,4267,4372,4459,4555,4651,4767,4854,4940,5051,5134,5218,5319,5425,5525,5628,5717,5828,5929,6038,6157,6240,6357", "endColumns": "120,118,122,120,99,99,116,142,117,147,84,106,96,101,113,117,111,137,136,143,168,135,119,121,129,97,95,120,134,102,113,114,136,140,110,104,86,95,95,115,86,85,110,82,83,100,105,99,102,88,110,100,108,118,82,116,108", "endOffsets": "171,290,413,534,634,734,851,994,1112,1260,1345,1452,1549,1651,1765,1883,1995,2133,2270,2414,2583,2719,2839,2961,3091,3189,3285,3406,3541,3644,3758,3873,4010,4151,4262,4367,4454,4550,4646,4762,4849,4935,5046,5129,5213,5314,5420,5520,5623,5712,5823,5924,6033,6152,6235,6352,6461"}, "to": {"startLines": "86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6037,6158,6277,6400,6521,6621,6721,6838,6981,7099,7247,7332,7439,7536,7638,7752,7870,7982,8120,8257,8401,8570,8706,8826,8948,9078,9176,9272,9393,9528,9631,9745,9860,9997,10138,10249,10354,10441,10537,10633,10749,10836,10922,11033,11116,11200,11301,11407,11507,11610,11699,11810,11911,12020,12139,12222,12339", "endColumns": "120,118,122,120,99,99,116,142,117,147,84,106,96,101,113,117,111,137,136,143,168,135,119,121,129,97,95,120,134,102,113,114,136,140,110,104,86,95,95,115,86,85,110,82,83,100,105,99,102,88,110,100,108,118,82,116,108", "endOffsets": "6153,6272,6395,6516,6616,6716,6833,6976,7094,7242,7327,7434,7531,7633,7747,7865,7977,8115,8252,8396,8565,8701,8821,8943,9073,9171,9267,9388,9523,9626,9740,9855,9992,10133,10244,10349,10436,10532,10628,10744,10831,10917,11028,11111,11195,11296,11402,11502,11605,11694,11805,11906,12015,12134,12217,12334,12443"}}]}]}