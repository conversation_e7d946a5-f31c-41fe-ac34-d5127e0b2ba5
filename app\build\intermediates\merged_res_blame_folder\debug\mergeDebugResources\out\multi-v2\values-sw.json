{"logs": [{"outputFile": "com.example.iotandroidv20.app-mergeDebugResources-62:/values-sw/values-sw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5dca3f3744b4fa46ea145e6d4fc9225c\\transformed\\media3-exoplayer-1.2.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,177,239,306,384,465,552,634", "endColumns": "62,58,61,66,77,80,86,81,69", "endOffsets": "113,172,234,301,379,460,547,629,699"}, "to": {"startLines": "57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4089,4152,4211,4273,4340,4418,4499,4586,4668", "endColumns": "62,58,61,66,77,80,86,81,69", "endOffsets": "4147,4206,4268,4335,4413,4494,4581,4663,4733"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\398f61ea23c76807315a7831064b1215\\transformed\\material3-release\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,286,402,516,616,715,831,972,1088,1239,1325,1425,1518,1620,1738,1865,1970,2100,2229,2365,2530,2659,2783,2912,3021,3115,3211,3334,3462,3559,3671,3781,3913,4054,4166,4266,4345,4441,4538,4653,4740,4825,4939,5019,5102,5201,5301,5396,5495,5583,5688,5788,5891,6007,6087,6205", "endColumns": "115,114,115,113,99,98,115,140,115,150,85,99,92,101,117,126,104,129,128,135,164,128,123,128,108,93,95,122,127,96,111,109,131,140,111,99,78,95,96,114,86,84,113,79,82,98,99,94,98,87,104,99,102,115,79,117,109", "endOffsets": "166,281,397,511,611,710,826,967,1083,1234,1320,1420,1513,1615,1733,1860,1965,2095,2224,2360,2525,2654,2778,2907,3016,3110,3206,3329,3457,3554,3666,3776,3908,4049,4161,4261,4340,4436,4533,4648,4735,4820,4934,5014,5097,5196,5296,5391,5490,5578,5683,5783,5886,6002,6082,6200,6310"}, "to": {"startLines": "86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6063,6179,6294,6410,6524,6624,6723,6839,6980,7096,7247,7333,7433,7526,7628,7746,7873,7978,8108,8237,8373,8538,8667,8791,8920,9029,9123,9219,9342,9470,9567,9679,9789,9921,10062,10174,10274,10353,10449,10546,10661,10748,10833,10947,11027,11110,11209,11309,11404,11503,11591,11696,11796,11899,12015,12095,12213", "endColumns": "115,114,115,113,99,98,115,140,115,150,85,99,92,101,117,126,104,129,128,135,164,128,123,128,108,93,95,122,127,96,111,109,131,140,111,99,78,95,96,114,86,84,113,79,82,98,99,94,98,87,104,99,102,115,79,117,109", "endOffsets": "6174,6289,6405,6519,6619,6718,6834,6975,7091,7242,7328,7428,7521,7623,7741,7868,7973,8103,8232,8368,8533,8662,8786,8915,9024,9118,9214,9337,9465,9562,9674,9784,9916,10057,10169,10269,10348,10444,10541,10656,10743,10828,10942,11022,11105,11204,11304,11399,11498,11586,11691,11791,11894,12010,12090,12208,12318"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\835843a1fca13b839a80c71cfb075e12\\transformed\\media3-session-1.2.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,133,211,281,348,427,509,611", "endColumns": "77,77,69,66,78,81,101,107", "endOffsets": "128,206,276,343,422,504,606,714"}, "to": {"startLines": "19,30,143,144,145,146,147,148", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "756,1833,12323,12393,12460,12539,12621,12723", "endColumns": "77,77,69,66,78,81,101,107", "endOffsets": "829,1906,12388,12455,12534,12616,12718,12826"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\752167a3e67da85263f648420d161268\\transformed\\ui-release\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,280,381,482,568,649,750,841,923,1008,1095,1169,1244,1321,1398,1475,1545", "endColumns": "93,80,100,100,85,80,100,90,81,84,86,73,74,76,76,76,69,120", "endOffsets": "194,275,376,477,563,644,745,836,918,1003,1090,1164,1239,1316,1393,1470,1540,1661"}, "to": {"startLines": "27,28,29,31,32,84,85,149,150,151,152,153,154,155,156,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1557,1651,1732,1911,2012,5881,5962,12831,12922,13004,13089,13176,13250,13325,13402,13580,13657,13727", "endColumns": "93,80,100,100,85,80,100,90,81,84,86,73,74,76,76,76,69,120", "endOffsets": "1646,1727,1828,2007,2093,5957,6058,12917,12999,13084,13171,13245,13320,13397,13474,13652,13722,13843"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\01b6e8b8fea8bb45b55852d4590f3437\\transformed\\foundation-release\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,156", "endColumns": "100,102", "endOffsets": "151,254"}, "to": {"startLines": "161,162", "startColumns": "4,4", "startOffsets": "13848,13949", "endColumns": "100,102", "endOffsets": "13944,14047"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\31c73cb6dfb12faac49eb1bae4282a74\\transformed\\core-1.16.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "20,21,22,23,24,25,26,157", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "834,928,1030,1127,1228,1335,1442,13479", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "923,1025,1122,1223,1330,1437,1552,13575"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2c60f935d2795008c652ca71073c0e75\\transformed\\media3-ui-1.2.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,289,506,706,785,862,947,1037,1125,1201,1267,1360,1455,1522,1586,1647,1722,1835,1952,2065,2139,2220,2293,2371,2462,2551,2619,2697,2750,2808,2856,2917,2978,3045,3109,3175,3238,3297,3363,3432,3498,3550,3616,3699,3782", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,78,76,84,89,87,75,65,92,94,66,63,60,74,112,116,112,73,80,72,77,90,88,67,77,52,57,47,60,60,66,63,65,62,58,65,68,65,51,65,82,82,57", "endOffsets": "284,501,701,780,857,942,1032,1120,1196,1262,1355,1450,1517,1581,1642,1717,1830,1947,2060,2134,2215,2288,2366,2457,2546,2614,2692,2745,2803,2851,2912,2973,3040,3104,3170,3233,3292,3358,3427,3493,3545,3611,3694,3777,3835"}, "to": {"startLines": "2,11,15,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,339,556,2098,2177,2254,2339,2429,2517,2593,2659,2752,2847,2914,2978,3039,3114,3227,3344,3457,3531,3612,3685,3763,3854,3943,4011,4738,4791,4849,4897,4958,5019,5086,5150,5216,5279,5338,5404,5473,5539,5591,5657,5740,5823", "endLines": "10,14,18,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83", "endColumns": "17,12,12,78,76,84,89,87,75,65,92,94,66,63,60,74,112,116,112,73,80,72,77,90,88,67,77,52,57,47,60,60,66,63,65,62,58,65,68,65,51,65,82,82,57", "endOffsets": "334,551,751,2172,2249,2334,2424,2512,2588,2654,2747,2842,2909,2973,3034,3109,3222,3339,3452,3526,3607,3680,3758,3849,3938,4006,4084,4786,4844,4892,4953,5014,5081,5145,5211,5274,5333,5399,5468,5534,5586,5652,5735,5818,5876"}}]}]}