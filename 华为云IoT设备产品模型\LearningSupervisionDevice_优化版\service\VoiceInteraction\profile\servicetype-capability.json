{"services": [{"serviceType": "VoiceInteraction", "description": "VoiceInteraction", "properties": [{"propertyName": "voiceStatus", "dataType": "string", "required": true, "min": 0, "max": 0, "step": 1, "maxLength": 15, "unit": null, "method": "R", "enumList": ["ACTIVE", "INACTIVE", "RECORDING", "PLAYING", "ERROR"]}, {"propertyName": "audioLevel", "dataType": "decimal", "required": false, "min": 0.0, "max": 100.0, "step": 1, "maxLength": 0, "unit": "dB", "method": "R", "enumList": null}, {"propertyName": "ambientNoiseLevel", "dataType": "decimal", "required": false, "min": 0.0, "max": 120.0, "step": 1, "maxLength": 0, "unit": "dB", "method": "R", "enumList": null}, {"propertyName": "microphoneStatus", "dataType": "string", "required": true, "min": 0, "max": 0, "step": 1, "maxLength": 15, "unit": null, "method": "R", "enumList": ["ACTIVE", "MUTED", "ERROR", "DISCONNECTED"]}, {"propertyName": "speaker<PERSON><PERSON>us", "dataType": "string", "required": true, "min": 0, "max": 0, "step": 1, "maxLength": 15, "unit": null, "method": "R", "enumList": ["ACTIVE", "MUTED", "ERROR", "DISCONNECTED"]}, {"propertyName": "audioQuality", "dataType": "decimal", "required": false, "min": 0.0, "max": 1.0, "step": 1, "maxLength": 0, "unit": null, "method": "R", "enumList": null}, {"propertyName": "voiceActivityDetected", "dataType": "boolean", "required": false, "min": 0, "max": 0, "step": 1, "maxLength": 0, "unit": null, "method": "R", "enumList": null}, {"propertyName": "audioSampleRate", "dataType": "int", "required": false, "min": 8000, "max": 48000, "step": 1, "maxLength": 0, "unit": "Hz", "method": "R", "enumList": null}, {"propertyName": "audioChannels", "dataType": "int", "required": false, "min": 1, "max": 8, "step": 1, "maxLength": 0, "unit": null, "method": "R", "enumList": null}, {"propertyName": "volumeLevel", "dataType": "int", "required": false, "min": 0, "max": 100, "step": 1, "maxLength": 0, "unit": "%", "method": "RW", "enumList": null}, {"propertyName": "lastAudioEvent", "dataType": "string", "required": false, "min": 0, "max": 0, "step": 1, "maxLength": 20, "unit": null, "method": "R", "enumList": null}], "commands": [{"commandName": "START_VOICE_RECORDING", "paras": [{"paraName": "duration", "dataType": "int", "required": false, "min": 1, "max": 300, "step": 1, "maxLength": 0, "unit": "s", "enumList": null}, {"paraName": "sampleRate", "dataType": "int", "required": false, "min": 8000, "max": 48000, "step": 1, "maxLength": 0, "unit": "Hz", "enumList": null}], "responses": [{"responseName": "START_VOICE_RECORDING_RSP", "paras": [{"paraName": "result", "dataType": "string", "required": true, "min": 0, "max": 0, "step": 1, "maxLength": 10, "unit": null, "enumList": ["success", "failed"]}]}]}, {"commandName": "STOP_VOICE_RECORDING", "paras": [], "responses": [{"responseName": "STOP_VOICE_RECORDING_RSP", "paras": [{"paraName": "result", "dataType": "string", "required": true, "min": 0, "max": 0, "step": 1, "maxLength": 10, "unit": null, "enumList": ["success", "failed"]}]}]}, {"commandName": "PLAY_VOICE_MESSAGE", "paras": [{"paraName": "messageType", "dataType": "string", "required": true, "min": 0, "max": 0, "step": 1, "maxLength": 15, "unit": null, "enumList": ["REMINDER", "WARNING", "ENCOURAGEMENT", "INSTRUCTION"]}, {"paraName": "volume", "dataType": "int", "required": false, "min": 0, "max": 100, "step": 1, "maxLength": 0, "unit": "%", "enumList": null}], "responses": [{"responseName": "PLAY_VOICE_MESSAGE_RSP", "paras": [{"paraName": "result", "dataType": "string", "required": true, "min": 0, "max": 0, "step": 1, "maxLength": 10, "unit": null, "enumList": ["success", "failed"]}]}]}, {"commandName": "PLAY_PARENT_VOICE", "paras": [{"paraName": "voiceUrl", "dataType": "string", "required": true, "min": 0, "max": 0, "step": 1, "maxLength": 512, "unit": null, "enumList": null}, {"paraName": "playMode", "dataType": "string", "required": false, "min": 0, "max": 0, "step": 1, "maxLength": 15, "unit": null, "enumList": ["immediate", "queue", "interrupt"]}, {"paraName": "volume", "dataType": "int", "required": false, "min": 0, "max": 100, "step": 1, "maxLength": 0, "unit": "%", "enumList": null}, {"paraName": "priority", "dataType": "string", "required": false, "min": 0, "max": 0, "step": 1, "maxLength": 10, "unit": null, "enumList": ["low", "normal", "high", "urgent"]}, {"paraName": "timestamp", "dataType": "long", "required": false, "min": 0, "max": 9223372036854775807, "step": 1, "maxLength": 0, "unit": "ms", "enumList": null}], "responses": [{"responseName": "PLAY_PARENT_VOICE_RSP", "paras": [{"paraName": "result", "dataType": "string", "required": true, "min": 0, "max": 0, "step": 1, "maxLength": 10, "unit": null, "enumList": ["success", "failed"]}, {"paraName": "playbackId", "dataType": "string", "required": false, "min": 0, "max": 0, "step": 1, "maxLength": 32, "unit": null, "enumList": null}, {"paraName": "errorCode", "dataType": "string", "required": false, "min": 0, "max": 0, "step": 1, "maxLength": 20, "unit": null, "enumList": null}]}]}, {"commandName": "SET_AUDIO_PARAMETERS", "paras": [{"paraName": "microphoneGain", "dataType": "decimal", "required": false, "min": 0.0, "max": 10.0, "step": 1, "maxLength": 0, "unit": "dB", "enumList": null}, {"paraName": "noiseReduction", "dataType": "boolean", "required": false, "min": 0, "max": 0, "step": 1, "maxLength": 0, "unit": null, "enumList": null}], "responses": [{"responseName": "SET_AUDIO_PARAMETERS_RSP", "paras": [{"paraName": "result", "dataType": "string", "required": true, "min": 0, "max": 0, "step": 1, "maxLength": 10, "unit": null, "enumList": ["success", "failed"]}]}]}]}]}