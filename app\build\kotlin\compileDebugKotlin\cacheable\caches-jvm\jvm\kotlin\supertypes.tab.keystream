&com.example.iotandroidv20.MainActivity-com.example.iotandroidv20.eeg.FatigueScenario+com.example.iotandroidv20.eeg.FocusScenarioJcom.example.iotandroidv20.intelligence.LearningGuidanceEngine.ConcernLevelGcom.example.iotandroidv20.intelligence.LearningGuidanceEngine.RiskLevelEcom.example.iotandroidv20.iot.HuaweiCloudOkHttpClient.ConnectionState>com.example.iotandroidv20.iot.HuaweiIoTManager.ConnectionState;com.example.iotandroidv20.iot.MqttIoTClient.ConnectionState-com.example.iotandroidv20.model.SignalQuality-com.example.iotandroidv20.model.BrainWaveType*com.example.iotandroidv20.model.FocusLevel,com.example.iotandroidv20.model.FatigueLevel2com.example.iotandroidv20.model.RestRecommendation.com.example.iotandroidv20.model.SessionQuality)com.example.iotandroidv20.model.FocusType+com.example.iotandroidv20.model.FatigueType-com.example.iotandroidv20.model.LearningStyle%com.example.iotandroidv20.model.Trend.com.example.iotandroidv20.model.LightingAction+com.example.iotandroidv20.model.NoiseAction1com.example.iotandroidv20.model.TemperatureAction/com.example.iotandroidv20.model.WorkspaceAction-com.example.iotandroidv20.model.SessionAction)com.example.iotandroidv20.model.BreakType-com.example.iotandroidv20.model.BreakActivity(com.example.iotandroidv20.model.TaskType-com.example.iotandroidv20.model.ActionUrgency2com.example.iotandroidv20.model.SuggestionCategory6com.example.iotandroidv20.model.RecommendationPriority)com.example.iotandroidv20.model.DayOfWeek)com.example.iotandroidv20.model.TimeOfDay3com.example.iotandroidv20.model.LearningSessionType-com.example.iotandroidv20.model.SessionStatus,com.example.iotandroidv20.model.FocusPattern-com.example.iotandroidv20.model.MilestoneType5com.example.iotandroidv20.model.MilestoneSignificance0com.example.iotandroidv20.model.InterventionType3com.example.iotandroidv20.model.InterventionTrigger,com.example.iotandroidv20.model.UserResponse0com.example.iotandroidv20.model.PerformanceLevel0com.example.iotandroidv20.model.AlertSensitivity,com.example.iotandroidv20.model.PostureIssue+com.example.iotandroidv20.model.PostureRisk.com.example.iotandroidv20.model.EmotionalState)com.example.iotandroidv20.model.RiskLevel-com.example.iotandroidv20.model.GazeDirection0com.example.iotandroidv20.model.FacialExpression-com.example.iotandroidv20.model.EmotionalTone,com.example.iotandroidv20.model.FusionMethod2com.example.iotandroidv20.model.ConflictResolution-com.example.iotandroidv20.model.FusionQuality,com.example.iotandroidv20.model.QualityLevel.com.example.iotandroidv20.model.TrendDirection/com.example.iotandroidv20.model.AnomalySeverity+com.example.iotandroidv20.model.PatternType2com.example.iotandroidv20.model.RecommendationType/com.example.iotandroidv20.model.PersonalityType.com.example.iotandroidv20.model.AssessmentMode+com.example.iotandroidv20.model.HealthLevel.com.example.iotandroidv20.model.RiskFactorType,com.example.iotandroidv20.model.RiskSeverity*com.example.iotandroidv20.model.RiskImpact6com.example.iotandroidv20.model.RecommendationCategory(com.example.iotandroidv20.model.AgeGroup*com.example.iotandroidv20.model.AlertLevel*com.example.iotandroidv20.model.RiskFactor,com.example.iotandroidv20.model.ReminderType.com.example.iotandroidv20.model.PostureQuality&com.example.iotandroidv20.model.Gender/com.example.iotandroidv20.model.EnvironmentType*com.example.iotandroidv20.model.NoiseLevel1com.example.iotandroidv20.model.LightingCondition2com.example.iotandroidv20.model.TemperatureComfort.com.example.iotandroidv20.model.ReminderMethod-com.example.iotandroidv20.model.StreamQuality,com.example.iotandroidv20.model.VideoQuality,com.example.iotandroidv20.model.StreamStatus0com.example.iotandroidv20.model.VoiceCommandType6com.example.iotandroidv20.model.CommandExecutionStatus)com.example.iotandroidv20.model.VoiceType,com.example.iotandroidv20.model.AudioQuality2com.example.iotandroidv20.obs.ObsApiResult.Success0com.example.iotandroidv20.obs.ObsApiResult.Error,com.example.iotandroidv20.obs.DownloadStatus+com.example.iotandroidv20.obs.PlaybackState/com.example.iotandroidv20.obs.InteractionStatus4com.example.iotandroidv20.obs.VoiceInteractionStatus.com.example.iotandroidv20.obs.LiveStreamStatus/com.example.iotandroidv20.obs.ConnectionQuality0com.example.iotandroidv20.obs.VerificationStatus.com.example.iotandroidv20.obs.DiagnosticStatus>com.example.iotandroidv20.player.AudioPlayerManager.RepeatModeIcom.example.iotandroidv20.recording.VideoRecordingManager.RecordingFormat4com.example.iotandroidv20.ui.components.DashboardTab8com.example.iotandroidv20.ui.components.SpectrumViewMode5com.example.iotandroidv20.ui.components.FrequencyBand2com.example.iotandroidv20.ui.components.EEGChannel1com.example.iotandroidv20.ui.components.TimeScale1com.example.iotandroidv20.ui.components.ErrorType1com.example.iotandroidv20.ui.components.TimeRange8com.example.iotandroidv20.ui.components.VideoSurfaceView8com.example.iotandroidv20.ui.player.AudioPlayerViewModel8com.example.iotandroidv20.ui.player.VideoPlayerViewModel,com.example.iotandroidv20.utils.PostureGrade1com.example.iotandroidv20.viewmodel.MainViewModelCcom.example.iotandroidv20.vis.VoiceRecordingManager.RecordingStatusEcom.example.iotandroidv20.vision.PostureDetectionManager.KeyPointTypeFcom.example.iotandroidv20.vision.PostureDetectionManager.PostureStatusDcom.example.iotandroidv20.vision.PostureDetectionManager.WarningTypeHcom.example.iotandroidv20.vision.PostureDetectionManager.WarningSeverityFcom.example.iotandroidv20.voice.SmartVoiceReminderManager.ReminderTypeJcom.example.iotandroidv20.voice.SmartVoiceReminderManager.ReminderPriorityFcom.example.iotandroidv20.voice.SmartVoiceReminderManager.UserResponse                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          