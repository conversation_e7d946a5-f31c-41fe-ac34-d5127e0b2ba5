{"Version": "1.1", "Statement": [{"Effect": "Allow", "Action": ["obs:object:PutObject", "obs:object:PutObjectAcl", "obs:object:GetObjectMetadata"], "Resource": ["obs:*:*:bucket:learning-supervision-obs", "obs:*:*:object:learning-supervision-obs/video/*/raw/*", "obs:*:*:object:learning-supervision-obs/audio/*/raw/*"], "Condition": {"StringLike": {"obs:object-key": ["video/*/raw/*/*", "audio/*/raw/*/*"]}}}, {"Effect": "Allow", "Action": ["iotda:device:reportData", "iotda:device:reportEvent", "iotda:device:reportBatch"], "Resource": ["iotda:*:*:device:*"]}, {"Effect": "Allow", "Action": ["obs:bucket:GetBucketLocation"], "Resource": ["obs:*:*:bucket:learning-supervision-obs"]}]}