package com.example.iotandroidv20.ui.components

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.provider.Settings
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat

/**
 * 简化的权限请求卡片
 * 提供权限状态显示和手动设置指导
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SimplePermissionCard(
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var recordPermissionGranted by remember { mutableStateOf(false) }
    var storagePermissionGranted by remember { mutableStateOf(false) }
    
    // 检查权限状态
    fun checkPermissions() {
        recordPermissionGranted = ContextCompat.checkSelfPermission(
            context, 
            android.Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED
        
        storagePermissionGranted = ContextCompat.checkSelfPermission(
            context, 
            android.Manifest.permission.WRITE_EXTERNAL_STORAGE
        ) == PackageManager.PERMISSION_GRANTED
        
        android.util.Log.d("SimplePermissionCard", "🔐 权限检查 - 录音: $recordPermissionGranted, 存储: $storagePermissionGranted")
    }
    
    // 初始检查和定期刷新
    LaunchedEffect(Unit) {
        checkPermissions()
    }
    
    val allPermissionsGranted = recordPermissionGranted && storagePermissionGranted
    
    if (!allPermissionsGranted) {
        Card(
            modifier = modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFFFFEBEE) // 红色背景表示需要注意
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                // 标题行
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "🔐 权限设置",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFFD32F2F)
                    )
                    
                    Icon(
                        imageVector = Icons.Default.Security,
                        contentDescription = "权限",
                        tint = Color(0xFFD32F2F),
                        modifier = Modifier.size(24.dp)
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 说明文字
                Text(
                    text = "语音交互功能需要以下权限，请手动在设置中授予：",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFFB71C1C)
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 权限状态列表
                SimplePermissionStatusItem(
                    permissionName = "🎙️ 录音权限",
                    description = "用于录制家长语音消息",
                    isGranted = recordPermissionGranted
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                SimplePermissionStatusItem(
                    permissionName = "💾 存储权限",
                    description = "用于保存录音文件",
                    isGranted = storagePermissionGranted
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 操作按钮区域
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 刷新状态按钮
                    OutlinedButton(
                        onClick = {
                            android.util.Log.d("SimplePermissionCard", "🔄 刷新权限状态")
                            checkPermissions()
                        },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color(0xFF7B1FA2)
                        )
                    ) {
                        Icon(
                            imageVector = Icons.Default.Refresh,
                            contentDescription = null,
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("刷新状态")
                    }
                    
                    // 打开设置按钮
                    Button(
                        onClick = {
                            android.util.Log.d("SimplePermissionCard", "⚙️ 打开应用设置")
                            try {
                                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                                    data = Uri.fromParts("package", context.packageName, null)
                                }
                                context.startActivity(intent)
                            } catch (e: Exception) {
                                android.util.Log.e("SimplePermissionCard", "❌ 打开设置失败: ${e.message}")
                            }
                        },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFD32F2F)
                        )
                    ) {
                        Icon(
                            imageVector = Icons.Default.Settings,
                            contentDescription = null,
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("打开设置")
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 操作指导
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFFE1BEE7).copy(alpha = 0.5f)
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(12.dp)
                    ) {
                        Text(
                            text = "📋 操作步骤",
                            style = MaterialTheme.typography.labelMedium,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF4A148C)
                        )
                        
                        Spacer(modifier = Modifier.height(4.dp))
                        
                        val steps = listOf(
                            "点击\"打开设置\"按钮",
                            "找到\"权限\"或\"应用权限\"选项",
                            "开启\"麦克风\"和\"存储\"权限",
                            "返回应用，点击\"刷新状态\"按钮"
                        )
                        
                        steps.forEachIndexed { index: Int, step: String ->
                            Text(
                                text = "${index + 1}. $step",
                                style = MaterialTheme.typography.bodySmall,
                                color = Color(0xFF6A1B9A),
                                modifier = Modifier.padding(vertical = 1.dp)
                            )
                        }
                    }
                }
            }
        }
    } else {
        // 权限已授予的成功提示
        Card(
            modifier = modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 4.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFFE8F5E8) // 绿色背景
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.CheckCircle,
                        contentDescription = "权限已授予",
                        tint = Color.Green,
                        modifier = Modifier.size(24.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = "✅ 权限已授予，语音功能就绪",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF2E7D32)
                    )
                }
                
                OutlinedButton(
                    onClick = {
                        checkPermissions()
                    },
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = Color(0xFF2E7D32)
                    )
                ) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
}

/**
 * 简化的权限状态项目
 */
@Composable
private fun SimplePermissionStatusItem(
    permissionName: String,
    description: String,
    isGranted: Boolean,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = permissionName,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium,
                color = if (isGranted) Color(0xFF2E7D32) else Color(0xFFD32F2F)
            )
            
            Text(
                text = description,
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF5D4037)
            )
        }
        
        Icon(
            imageVector = if (isGranted) Icons.Default.CheckCircle else Icons.Default.Cancel,
            contentDescription = if (isGranted) "已授予" else "未授予",
            tint = if (isGranted) Color.Green else Color.Red,
            modifier = Modifier.size(24.dp)
        )
    }
}
