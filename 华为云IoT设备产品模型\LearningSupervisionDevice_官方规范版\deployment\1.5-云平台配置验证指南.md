# 云平台配置验证指南

## 🎯 验证目标

对已配置的华为云服务进行全面验证，确保IAM权限、OBS存储、IoT设备接入、MPS媒体处理等服务能够正常协同工作。

## 📋 验证清单

### **1. IAM权限验证**
- [ ] 设备端用户权限验证
- [ ] 应用端用户权限验证
- [ ] 管理员用户权限验证
- [ ] 访问密钥有效性验证

### **2. OBS存储验证**
- [ ] 存储桶创建验证
- [ ] 文件上传权限验证
- [ ] 文件下载权限验证
- [ ] 生命周期规则验证
- [ ] CORS配置验证

### **3. IoT设备接入验证**
- [ ] 产品模型验证
- [ ] 设备注册验证
- [ ] MQTT连接验证
- [ ] 数据上报验证
- [ ] 命令下发验证

### **4. MPS媒体处理验证**
- [ ] 处理模板验证
- [ ] 工作流创建验证
- [ ] 触发机制验证
- [ ] 处理结果验证

### **5. 端到端流程验证**
- [ ] 完整视频处理流程
- [ ] 完整音频处理流程
- [ ] 错误处理机制
- [ ] 监控告警功能

## 🔧 验证步骤

### **步骤1：IAM权限验证**

#### **设备端权限测试**
```bash
#!/bin/bash

echo "=== 设备端权限验证 ==="

# 设置设备端凭据
export HUAWEICLOUD_ACCESS_KEY="device_access_key"
export HUAWEICLOUD_SECRET_KEY="device_secret_key"

# 测试OBS上传权限
echo "1. 测试OBS上传权限..."
echo "test content from device" > device_test.txt

if hcloud obs cp device_test.txt obs://learning-supervision-obs/video/TEST_DEVICE/raw/20250101/test_upload.txt; then
    echo "✅ 设备端OBS上传权限正常"
else
    echo "❌ 设备端OBS上传权限异常"
fi

# 测试IoT数据上报权限
echo "2. 测试IoT数据上报权限..."
# 这里需要使用MQTT客户端测试
echo "请运行MQTT连接测试脚本验证IoT权限"

# 清理测试文件
rm -f device_test.txt
```

#### **应用端权限测试**
```bash
#!/bin/bash

echo "=== 应用端权限验证 ==="

# 设置应用端凭据
export HUAWEICLOUD_ACCESS_KEY="app_access_key"
export HUAWEICLOUD_SECRET_KEY="app_secret_key"

# 测试OBS下载权限
echo "1. 测试OBS下载权限..."
if hcloud obs cp obs://learning-supervision-obs/video/TEST_DEVICE/raw/20250101/test_upload.txt app_download_test.txt; then
    echo "✅ 应用端OBS下载权限正常"
else
    echo "❌ 应用端OBS下载权限异常"
fi

# 测试OBS列表权限
echo "2. 测试OBS列表权限..."
if hcloud obs ls obs://learning-supervision-obs/video/TEST_DEVICE/processed/; then
    echo "✅ 应用端OBS列表权限正常"
else
    echo "❌ 应用端OBS列表权限异常"
fi

# 清理测试文件
rm -f app_download_test.txt
```

### **步骤2：OBS存储验证**

#### **存储功能测试**
```bash
#!/bin/bash

echo "=== OBS存储功能验证 ==="

BUCKET_NAME="learning-supervision-obs"
TEST_PREFIX="validation_test"

# 测试文件上传
echo "1. 测试文件上传..."
echo "OBS validation test - $(date)" > obs_test.txt

if hcloud obs cp obs_test.txt obs://$BUCKET_NAME/$TEST_PREFIX/upload_test.txt; then
    echo "✅ 文件上传成功"
else
    echo "❌ 文件上传失败"
    exit 1
fi

# 测试文件下载
echo "2. 测试文件下载..."
if hcloud obs cp obs://$BUCKET_NAME/$TEST_PREFIX/upload_test.txt download_test.txt; then
    echo "✅ 文件下载成功"
else
    echo "❌ 文件下载失败"
    exit 1
fi

# 验证文件内容
if diff obs_test.txt download_test.txt > /dev/null; then
    echo "✅ 文件内容验证成功"
else
    echo "❌ 文件内容验证失败"
    exit 1
fi

# 测试文件列表
echo "3. 测试文件列表..."
if hcloud obs ls obs://$BUCKET_NAME/$TEST_PREFIX/ | grep -q "upload_test.txt"; then
    echo "✅ 文件列表功能正常"
else
    echo "❌ 文件列表功能异常"
fi

# 测试文件删除
echo "4. 测试文件删除..."
if hcloud obs rm obs://$BUCKET_NAME/$TEST_PREFIX/upload_test.txt; then
    echo "✅ 文件删除成功"
else
    echo "❌ 文件删除失败"
fi

# 清理本地文件
rm -f obs_test.txt download_test.txt

echo "OBS存储功能验证完成"
```

#### **存储策略验证**
```bash
#!/bin/bash

echo "=== OBS存储策略验证 ==="

BUCKET_NAME="learning-supervision-obs"

# 验证桶策略
echo "1. 验证桶策略..."
if hcloud obs get-bucket-policy obs://$BUCKET_NAME; then
    echo "✅ 桶策略配置正常"
else
    echo "❌ 桶策略配置异常"
fi

# 验证生命周期规则
echo "2. 验证生命周期规则..."
if hcloud obs get-bucket-lifecycle obs://$BUCKET_NAME; then
    echo "✅ 生命周期规则配置正常"
else
    echo "❌ 生命周期规则配置异常"
fi

# 验证CORS配置
echo "3. 验证CORS配置..."
if hcloud obs get-bucket-cors obs://$BUCKET_NAME; then
    echo "✅ CORS配置正常"
else
    echo "❌ CORS配置异常"
fi

# 验证版本控制
echo "4. 验证版本控制..."
if hcloud obs get-bucket-versioning obs://$BUCKET_NAME; then
    echo "✅ 版本控制配置正常"
else
    echo "❌ 版本控制配置异常"
fi

echo "OBS存储策略验证完成"
```

### **步骤3：IoT设备接入验证**

#### **设备连接测试**
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import time
import ssl
import paho.mqtt.client as mqtt
from datetime import datetime

class IoTConnectionValidator:
    def __init__(self, device_id, device_secret, broker, port=8883):
        self.device_id = device_id
        self.device_secret = device_secret
        self.broker = broker
        self.port = port
        self.client = mqtt.Client(client_id=device_id)
        self.connected = False
        self.message_received = False
        
    def on_connect(self, client, userdata, flags, rc):
        if rc == 0:
            print(f"✅ MQTT连接成功: {self.device_id}")
            self.connected = True
            
            # 订阅命令主题
            command_topic = f"$oc/devices/{self.device_id}/sys/commands/#"
            client.subscribe(command_topic)
            print(f"✅ 订阅命令主题: {command_topic}")
            
        else:
            print(f"❌ MQTT连接失败: {self.device_id}, 错误码: {rc}")
            
    def on_message(self, client, userdata, msg):
        print(f"✅ 收到消息: {msg.topic}")
        print(f"消息内容: {msg.payload.decode()}")
        self.message_received = True
        
    def on_disconnect(self, client, userdata, rc):
        print(f"MQTT连接断开: {self.device_id}")
        self.connected = False
        
    def connect(self):
        try:
            # 设置回调函数
            self.client.on_connect = self.on_connect
            self.client.on_message = self.on_message
            self.client.on_disconnect = self.on_disconnect
            
            # 设置认证信息
            self.client.username_pw_set(self.device_id, self.device_secret)
            
            # 设置TLS
            context = ssl.create_default_context(ssl.Purpose.SERVER_AUTH)
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
            self.client.tls_set_context(context)
            
            # 连接到MQTT代理
            self.client.connect(self.broker, self.port, 60)
            self.client.loop_start()
            
            # 等待连接建立
            timeout = 10
            while not self.connected and timeout > 0:
                time.sleep(1)
                timeout -= 1
                
            return self.connected
            
        except Exception as e:
            print(f"❌ 连接异常: {e}")
            return False
            
    def test_data_report(self):
        if not self.connected:
            print("❌ 设备未连接，无法测试数据上报")
            return False
            
        # 构建测试数据
        test_data = {
            "services": [{
                "service_id": "DeviceManagement",
                "properties": {
                    "deviceStatus": "ONLINE",
                    "batteryLevel": 85,
                    "signalStrength": -45,
                    "testTimestamp": datetime.now().isoformat()
                },
                "event_time": datetime.now().isoformat() + "Z"
            }]
        }
        
        # 发送数据
        topic = f"$oc/devices/{self.device_id}/sys/properties/report"
        try:
            result = self.client.publish(topic, json.dumps(test_data))
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                print("✅ 数据上报测试成功")
                return True
            else:
                print(f"❌ 数据上报测试失败: {result.rc}")
                return False
        except Exception as e:
            print(f"❌ 数据上报异常: {e}")
            return False
            
    def test_event_report(self):
        if not self.connected:
            print("❌ 设备未连接，无法测试事件上报")
            return False
            
        # 构建测试事件
        test_event = {
            "services": [{
                "service_id": "VideoMonitoring",
                "event_type": "recording_session_complete",
                "paras": {
                    "sessionId": "test_session_001",
                    "totalSegments": 5,
                    "totalDuration": 50
                },
                "event_time": datetime.now().isoformat() + "Z"
            }]
        }
        
        # 发送事件
        topic = f"$oc/devices/{self.device_id}/sys/events/up"
        try:
            result = self.client.publish(topic, json.dumps(test_event))
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                print("✅ 事件上报测试成功")
                return True
            else:
                print(f"❌ 事件上报测试失败: {result.rc}")
                return False
        except Exception as e:
            print(f"❌ 事件上报异常: {e}")
            return False
            
    def disconnect(self):
        if self.connected:
            self.client.loop_stop()
            self.client.disconnect()

def main():
    print("=== IoT设备接入验证 ===")
    
    # 设备配置（需要从配置文件读取）
    device_config = {
        "device_id": "LS_DEVICE_001",
        "device_secret": "your_device_secret",
        "broker": "iot-mqtts.cn-north-4.myhuaweicloud.com",
        "port": 8883
    }
    
    # 创建验证器
    validator = IoTConnectionValidator(**device_config)
    
    try:
        # 测试连接
        print("1. 测试MQTT连接...")
        if validator.connect():
            print("✅ MQTT连接验证成功")
            
            # 测试数据上报
            print("2. 测试数据上报...")
            validator.test_data_report()
            
            # 测试事件上报
            print("3. 测试事件上报...")
            validator.test_event_report()
            
            # 等待一段时间接收可能的消息
            print("4. 等待接收消息...")
            time.sleep(5)
            
        else:
            print("❌ MQTT连接验证失败")
            
    finally:
        validator.disconnect()
        
    print("IoT设备接入验证完成")

if __name__ == "__main__":
    main()
```

### **步骤4：MPS媒体处理验证**

#### **处理模板验证**
```bash
#!/bin/bash

echo "=== MPS媒体处理验证 ==="

REGION="cn-north-4"

# 验证处理模板
echo "1. 验证处理模板..."
templates=(
    "video_merge_template"
    "video_transcode_template"
    "video_thumbnail_template"
    "audio_merge_template"
    "audio_transcode_template"
    "audio_waveform_template"
)

for template in "${templates[@]}"; do
    if hcloud mps describe-template --template-name "$template" --region "$REGION" > /dev/null 2>&1; then
        echo "✅ 模板存在: $template"
    else
        echo "❌ 模板不存在: $template"
    fi
done

# 验证工作流
echo "2. 验证工作流..."
workflows=(
    "video_processing_workflow"
    "audio_processing_workflow"
)

for workflow in "${workflows[@]}"; do
    if hcloud mps describe-workflow --workflow-name "$workflow" --region "$REGION" > /dev/null 2>&1; then
        echo "✅ 工作流存在: $workflow"
    else
        echo "❌ 工作流不存在: $workflow"
    fi
done

echo "MPS媒体处理验证完成"
```

### **步骤5：端到端流程验证**

#### **完整流程测试**
```bash
#!/bin/bash

echo "=== 端到端流程验证 ==="

BUCKET_NAME="learning-supervision-obs"
DEVICE_ID="TEST_DEVICE_001"
DATE=$(date +%Y%m%d)
SESSION_ID="validation_session_$(date +%s)"

# 模拟设备端上传视频分片
echo "1. 模拟设备端上传视频分片..."
for i in {001..003}; do
    segment_file="segment_$i.mp4"
    echo "Creating test segment: $segment_file"
    
    # 创建测试视频文件（需要实际的视频内容）
    # 这里使用空文件作为示例
    touch "$segment_file"
    
    # 上传到OBS
    target_path="video/$DEVICE_ID/raw/$DATE/$SESSION_ID/$segment_file"
    if hcloud obs cp "$segment_file" "obs://$BUCKET_NAME/$target_path"; then
        echo "✅ 分片上传成功: $segment_file"
    else
        echo "❌ 分片上传失败: $segment_file"
    fi
    
    rm -f "$segment_file"
done

# 模拟会话完成事件
echo "2. 模拟会话完成事件..."
# 这里需要通过IoT平台发送会话完成事件
echo "请通过IoT设备或API发送会话完成事件以触发MPS处理"

# 等待处理完成
echo "3. 等待MPS处理完成..."
echo "预计等待时间: 5-10分钟"

# 检查处理结果
check_processing_results() {
    echo "4. 检查处理结果..."
    
    local processed_path="video/$DEVICE_ID/processed/$DATE"
    
    # 检查合并后的完整视频
    if hcloud obs ls "obs://$BUCKET_NAME/$processed_path/" | grep -q "complete_$SESSION_ID.mp4"; then
        echo "✅ 视频合并成功"
    else
        echo "❌ 视频合并失败"
    fi
    
    # 检查转码后的多质量视频
    local qualities=("high_quality" "medium_quality" "low_quality")
    for quality in "${qualities[@]}"; do
        if hcloud obs ls "obs://$BUCKET_NAME/$processed_path/" | grep -q "${quality}_$SESSION_ID.mp4"; then
            echo "✅ $quality 转码成功"
        else
            echo "❌ $quality 转码失败"
        fi
    done
    
    # 检查缩略图
    if hcloud obs ls "obs://$BUCKET_NAME/$processed_path/thumbnails/" | grep -q "$SESSION_ID"; then
        echo "✅ 缩略图生成成功"
    else
        echo "❌ 缩略图生成失败"
    fi
}

# 延迟检查结果（实际使用中应该通过回调通知）
echo "等待处理完成后，运行以下命令检查结果："
echo "check_processing_results"

echo "端到端流程验证启动完成"
```

## ✅ 验证完成标准

### **成功标准**
- [ ] 所有IAM权限测试通过
- [ ] OBS存储功能正常
- [ ] IoT设备连接稳定
- [ ] MPS处理流程完整
- [ ] 端到端流程无错误

### **性能标准**
- [ ] MQTT连接延迟 < 5秒
- [ ] OBS上传速度 > 1MB/s
- [ ] MPS处理时间 < 10分钟
- [ ] 系统可用性 > 99%

### **安全标准**
- [ ] 权限隔离有效
- [ ] 数据传输加密
- [ ] 访问日志完整
- [ ] 告警机制正常

---

**注意**: 验证过程中发现的问题需要及时修复，确保所有服务能够稳定协同工作。
