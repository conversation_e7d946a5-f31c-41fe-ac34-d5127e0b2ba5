package com.example.iotandroidv20.obs

import android.content.Context
import com.example.iotandroidv20.config.HuaweiCloudConfig
import com.example.iotandroidv20.utils.Logger
import kotlinx.coroutines.*
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.File
import java.io.IOException
import java.security.MessageDigest
import java.text.SimpleDateFormat
import java.util.*
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine

/**
 * 华为云OBS API客户端
 * 基于官方API文档实现核心功能
 * 
 * 支持的API：
 * - PUT上传 - 上传家长语音文件
 * - 下载对象 - 下载实时视频流
 * - 列举桶内对象 - 获取可用的视频会话
 * - 获取对象元数据 - 检查文件状态
 * - 删除对象 - 清理临时文件
 */
class HuaweiObsApiClient private constructor() {
    
    private val httpClient: OkHttpClient by lazy {
        OkHttpClient.Builder()
            .connectTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
            .readTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
            .writeTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
            .build()
    }
    
    companion object {
        private const val TAG = "HuaweiObsApiClient"
        
        @Volatile
        private var INSTANCE: HuaweiObsApiClient? = null
        
        fun getInstance(): HuaweiObsApiClient {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: HuaweiObsApiClient().also { INSTANCE = it }
            }
        }
        
        // 华为云OBS配置
        private const val OBS_ENDPOINT = HuaweiCloudConfig.OBS_ENDPOINT
        private const val BUCKET_NAME = HuaweiCloudConfig.OBS_BUCKET_NAME
        private const val ACCESS_KEY = HuaweiCloudConfig.OBS_ACCESS_KEY
        private const val SECRET_KEY = HuaweiCloudConfig.OBS_SECRET_KEY
        private const val REGION = HuaweiCloudConfig.OBS_REGION
    }
    
    /**
     * PUT上传 - 上传家长语音文件
     * 参考：https://support.huaweicloud.com/api-obs/obs_04_0080.html
     */
    suspend fun putObject(
        objectKey: String,
        file: File,
        contentType: String = "audio/aac"
    ): ObsApiResult<String> = withContext(Dispatchers.IO) {
        try {
            Logger.d("开始上传对象: $objectKey", tag = TAG)
            
            // 1. 计算文件MD5
            val md5Hash = calculateMD5(file)
            
            // 2. 构建请求URL
            val url = "$OBS_ENDPOINT/$BUCKET_NAME/$objectKey"

            Logger.d("🔗 [OBS请求] URL: $url", tag = TAG)
            Logger.d("🔗 [OBS请求] ObjectKey: $objectKey", tag = TAG)
            Logger.d("🔗 [OBS请求] Bucket: $BUCKET_NAME", tag = TAG)
            Logger.d("🔗 [OBS请求] Endpoint: $OBS_ENDPOINT", tag = TAG)

            // 3. 创建请求体
            val requestBody = file.readBytes().toRequestBody(contentType.toMediaType())

            // 4. 生成认证头
            val date = getGMTDate()
            val authorization = generateAuthorization("PUT", objectKey, contentType, date, md5Hash)

            // 5. 构建请求
            val hostHeader = "obs.$REGION.myhuaweicloud.com"  // 修正Host头
            val request = Request.Builder()
                .url(url)
                .put(requestBody)
                .header("Host", hostHeader)
                .header("Date", date)
                .header("Authorization", authorization)
                .header("Content-Type", contentType)
                .header("Content-MD5", md5Hash)
                .header("Content-Length", file.length().toString())
                .build()

            Logger.d("🔗 [OBS请求] Host: $hostHeader", tag = TAG)
            Logger.d("🔗 [OBS请求] Date: $date", tag = TAG)
            Logger.d("🔗 [OBS请求] Authorization: $authorization", tag = TAG)
            
            // 6. 执行请求
            val response = httpClient.newCall(request).await()
            
            if (response.isSuccessful) {
                val etag = response.header("ETag") ?: ""
                Logger.d("对象上传成功: $objectKey, ETag: $etag", tag = TAG)
                ObsApiResult.Success(url)
            } else {
                val errorBody = response.body?.string() ?: "Unknown error"
                Logger.e("对象上传失败: ${response.code}, $errorBody", tag = TAG)
                ObsApiResult.Error("上传失败: ${response.code}", Exception(errorBody))
            }
            
        } catch (e: Exception) {
            Logger.e("上传对象异常: ${e.message}", tag = TAG)
            ObsApiResult.Error("上传异常: ${e.message}", e)
        }
    }
    
    /**
     * 下载对象 - 下载实时视频流
     * 参考：https://support.huaweicloud.com/api-obs/obs_04_0083.html
     */
    suspend fun getObject(objectKey: String): ObsApiResult<ByteArray> = withContext(Dispatchers.IO) {
        try {
            Logger.d("开始下载对象: $objectKey", tag = TAG)
            
            // 1. 构建请求URL
            val url = "$OBS_ENDPOINT/$BUCKET_NAME/$objectKey"
            
            // 2. 生成认证头
            val date = getGMTDate()
            val authorization = generateAuthorization("GET", objectKey, "", date, "")
            
            // 3. 构建请求
            val request = Request.Builder()
                .url(url)
                .get()
                .header("Host", "$BUCKET_NAME.obs.$REGION.myhuaweicloud.com")
                .header("Date", date)
                .header("Authorization", authorization)
                .build()
            
            // 4. 执行请求
            val response = httpClient.newCall(request).await()
            
            if (response.isSuccessful) {
                val data = response.body?.bytes() ?: ByteArray(0)
                Logger.d("对象下载成功: $objectKey, 大小: ${data.size} bytes", tag = TAG)
                ObsApiResult.Success(data)
            } else {
                val errorBody = response.body?.string() ?: "Unknown error"
                Logger.e("对象下载失败: ${response.code}, $errorBody", tag = TAG)
                ObsApiResult.Error("下载失败: ${response.code}", Exception(errorBody))
            }
            
        } catch (e: Exception) {
            Logger.e("下载对象异常: ${e.message}", tag = TAG)
            ObsApiResult.Error("下载异常: ${e.message}", e)
        }
    }
    
    /**
     * 列举桶内对象 - 获取可用的视频会话
     * 参考：https://support.huaweicloud.com/api-obs/obs_04_0022.html
     */
    suspend fun listObjects(
        prefix: String = "",
        maxKeys: Int = 1000
    ): ObsApiResult<List<ObsObject>> = withContext(Dispatchers.IO) {
        try {
            Logger.d("开始列举对象: prefix=$prefix, maxKeys=$maxKeys", tag = TAG)
            
            // 1. 构建请求URL
            val urlBuilder = HttpUrl.Builder()
                .scheme("https")
                .host("$BUCKET_NAME.obs.$REGION.myhuaweicloud.com")
                .addPathSegment("")
            
            if (prefix.isNotEmpty()) {
                urlBuilder.addQueryParameter("prefix", prefix)
            }
            urlBuilder.addQueryParameter("max-keys", maxKeys.toString())
            
            val url = urlBuilder.build().toString()
            
            // 2. 生成认证头
            val date = getGMTDate()
            val authorization = generateAuthorization("GET", "", "", date, "")
            
            // 3. 构建请求
            val request = Request.Builder()
                .url(url)
                .get()
                .header("Host", "$BUCKET_NAME.obs.$REGION.myhuaweicloud.com")
                .header("Date", date)
                .header("Authorization", authorization)
                .build()
            
            // 4. 执行请求
            val response = httpClient.newCall(request).await()
            
            if (response.isSuccessful) {
                val xmlBody = response.body?.string() ?: ""
                val objects = parseListObjectsResponse(xmlBody)
                Logger.d("对象列举成功: 找到 ${objects.size} 个对象", tag = TAG)
                ObsApiResult.Success(objects)
            } else {
                val errorBody = response.body?.string() ?: "Unknown error"
                Logger.e("对象列举失败: ${response.code}, $errorBody", tag = TAG)
                ObsApiResult.Error("列举失败: ${response.code}", Exception(errorBody))
            }
            
        } catch (e: Exception) {
            Logger.e("列举对象异常: ${e.message}", tag = TAG)
            ObsApiResult.Error("列举异常: ${e.message}", e)
        }
    }
    
    /**
     * 获取对象元数据 - 检查文件状态
     * 参考：https://support.huaweicloud.com/api-obs/obs_04_0084.html
     */
    suspend fun headObject(objectKey: String): ObsApiResult<ObsObjectMetadata> = withContext(Dispatchers.IO) {
        try {
            Logger.d("开始获取对象元数据: $objectKey", tag = TAG)
            
            // 1. 构建请求URL
            val url = "$OBS_ENDPOINT/$BUCKET_NAME/$objectKey"
            
            // 2. 生成认证头
            val date = getGMTDate()
            val authorization = generateAuthorization("HEAD", objectKey, "", date, "")
            
            // 3. 构建请求
            val request = Request.Builder()
                .url(url)
                .head()
                .header("Host", "$BUCKET_NAME.obs.$REGION.myhuaweicloud.com")
                .header("Date", date)
                .header("Authorization", authorization)
                .build()
            
            // 4. 执行请求
            val response = httpClient.newCall(request).await()
            
            if (response.isSuccessful) {
                val metadata = ObsObjectMetadata(
                    contentLength = response.header("Content-Length")?.toLongOrNull() ?: 0L,
                    contentType = response.header("Content-Type") ?: "",
                    etag = response.header("ETag") ?: "",
                    lastModified = response.header("Last-Modified") ?: "",
                    storageClass = response.header("x-obs-storage-class") ?: "STANDARD"
                )
                Logger.d("对象元数据获取成功: $objectKey", tag = TAG)
                ObsApiResult.Success(metadata)
            } else {
                val errorBody = response.body?.string() ?: "Unknown error"
                Logger.e("对象元数据获取失败: ${response.code}, $errorBody", tag = TAG)
                ObsApiResult.Error("获取元数据失败: ${response.code}", Exception(errorBody))
            }
            
        } catch (e: Exception) {
            Logger.e("获取对象元数据异常: ${e.message}", tag = TAG)
            ObsApiResult.Error("获取元数据异常: ${e.message}", e)
        }
    }
    
    /**
     * 删除对象 - 清理临时文件
     * 参考：https://support.huaweicloud.com/api-obs/obs_04_0085.html
     */
    suspend fun deleteObject(objectKey: String): ObsApiResult<Unit> = withContext(Dispatchers.IO) {
        try {
            Logger.d("开始删除对象: $objectKey", tag = TAG)
            
            // 1. 构建请求URL
            val url = "$OBS_ENDPOINT/$BUCKET_NAME/$objectKey"
            
            // 2. 生成认证头
            val date = getGMTDate()
            val authorization = generateAuthorization("DELETE", objectKey, "", date, "")
            
            // 3. 构建请求
            val request = Request.Builder()
                .url(url)
                .delete()
                .header("Host", "$BUCKET_NAME.obs.$REGION.myhuaweicloud.com")
                .header("Date", date)
                .header("Authorization", authorization)
                .build()
            
            // 4. 执行请求
            val response = httpClient.newCall(request).await()
            
            if (response.isSuccessful || response.code == 404) {
                Logger.d("对象删除成功: $objectKey", tag = TAG)
                ObsApiResult.Success(Unit)
            } else {
                val errorBody = response.body?.string() ?: "Unknown error"
                Logger.e("对象删除失败: ${response.code}, $errorBody", tag = TAG)
                ObsApiResult.Error("删除失败: ${response.code}", Exception(errorBody))
            }
            
        } catch (e: Exception) {
            Logger.e("删除对象异常: ${e.message}", tag = TAG)
            ObsApiResult.Error("删除异常: ${e.message}", e)
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 计算文件MD5值
     */
    private fun calculateMD5(file: File): String {
        val md = MessageDigest.getInstance("MD5")
        val bytes = file.readBytes()
        val digest = md.digest(bytes)
        return android.util.Base64.encodeToString(digest, android.util.Base64.NO_WRAP)
    }

    /**
     * 获取GMT格式的日期
     */
    private fun getGMTDate(): String {
        val dateFormat = SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss 'GMT'", Locale.US)
        dateFormat.timeZone = TimeZone.getTimeZone("GMT")
        return dateFormat.format(Date())
    }

    /**
     * 生成华为云OBS认证头
     * 基于官方文档的签名算法
     */
    private fun generateAuthorization(
        method: String,
        objectKey: String,
        contentType: String,
        date: String,
        contentMD5: String
    ): String {
        // 构建待签名字符串 - 根据华为云OBS API文档
        val canonicalizedResource = "/$BUCKET_NAME/$objectKey"
        val stringToSign = "$method\n$contentMD5\n$contentType\n$date\n$canonicalizedResource"

        Logger.d("🔐 [OBS签名] Method: $method", tag = TAG)
        Logger.d("🔐 [OBS签名] ObjectKey: $objectKey", tag = TAG)
        Logger.d("🔐 [OBS签名] BUCKET_NAME: $BUCKET_NAME", tag = TAG)
        Logger.d("🔐 [OBS签名] ContentType: $contentType", tag = TAG)
        Logger.d("🔐 [OBS签名] Date: $date", tag = TAG)
        Logger.d("🔐 [OBS签名] ContentMD5: $contentMD5", tag = TAG)
        Logger.d("🔐 [OBS签名] CanonicalizedResource: $canonicalizedResource", tag = TAG)
        Logger.d("🔐 [OBS签名] StringToSign: $stringToSign", tag = TAG)
        Logger.d("🔐 [OBS签名] ACCESS_KEY: $ACCESS_KEY", tag = TAG)
        Logger.d("🔐 [OBS签名] SECRET_KEY前4位: ${SECRET_KEY.take(4)}...", tag = TAG)

        // 使用HMAC-SHA1签名
        val mac = Mac.getInstance("HmacSHA1")
        val secretKeySpec = SecretKeySpec(SECRET_KEY.toByteArray(), "HmacSHA1")
        mac.init(secretKeySpec)
        val signature = mac.doFinal(stringToSign.toByteArray())
        val signatureBase64 = android.util.Base64.encodeToString(signature, android.util.Base64.NO_WRAP)

        Logger.d("🔐 [OBS签名] Signature: $signatureBase64", tag = TAG)

        return "OBS $ACCESS_KEY:$signatureBase64"
    }

    /**
     * 解析列举对象响应的XML
     */
    private fun parseListObjectsResponse(xmlBody: String): List<ObsObject> {
        val objects = mutableListOf<ObsObject>()

        try {
            // 简单的XML解析（实际项目中建议使用专业的XML解析库）
            val contentPattern = Regex("<Contents>(.*?)</Contents>", RegexOption.DOT_MATCHES_ALL)
            val keyPattern = Regex("<Key>(.*?)</Key>")
            val sizePattern = Regex("<Size>(.*?)</Size>")
            val lastModifiedPattern = Regex("<LastModified>(.*?)</LastModified>")
            val etagPattern = Regex("<ETag>(.*?)</ETag>")

            contentPattern.findAll(xmlBody).forEach { contentMatch ->
                val content = contentMatch.groupValues[1]

                val key = keyPattern.find(content)?.groupValues?.get(1) ?: ""
                val size = sizePattern.find(content)?.groupValues?.get(1)?.toLongOrNull() ?: 0L
                val lastModified = lastModifiedPattern.find(content)?.groupValues?.get(1) ?: ""
                val etag = etagPattern.find(content)?.groupValues?.get(1) ?: ""

                if (key.isNotEmpty()) {
                    objects.add(ObsObject(key, size, lastModified, etag))
                }
            }
        } catch (e: Exception) {
            Logger.e("解析XML响应失败: ${e.message}", tag = TAG)
        }

        return objects
    }

    /**
     * OkHttp Call的协程扩展
     */
    private suspend fun Call.await(): Response = suspendCoroutine { continuation ->
        enqueue(object : Callback {
            override fun onResponse(call: Call, response: Response) {
                continuation.resume(response)
            }

            override fun onFailure(call: Call, e: IOException) {
                continuation.resumeWithException(e)
            }
        })
    }
}

// ==================== 数据类 ====================

/**
 * OBS API结果封装
 */
sealed class ObsApiResult<out T> {
    data class Success<T>(val data: T) : ObsApiResult<T>()
    data class Error(val message: String, val exception: Exception) : ObsApiResult<Nothing>()
}

/**
 * OBS对象信息
 */
data class ObsObject(
    val key: String,
    val size: Long,
    val lastModified: String,
    val etag: String
)

/**
 * OBS对象元数据
 */
data class ObsObjectMetadata(
    val contentLength: Long,
    val contentType: String,
    val etag: String,
    val lastModified: String,
    val storageClass: String
)
