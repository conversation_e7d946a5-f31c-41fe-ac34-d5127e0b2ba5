# 音视频模块OBS直传架构设计

## 🎯 总体架构概述

基于华为云OBS SDK和预签名URL直传技术，重新设计音视频模块，实现**设备端→华为云OBS→应用端**的完整数据流，严格遵循设备端vs应用端职责分离原则。

## 📊 一、设备端到云平台部分

### **（一）嵌入式设备端开发**

#### **1. 采集硬件驱动与数据获取**

**视频采集**：
- **硬件接口**: USB、MIPI等接口连接摄像头
- **原始数据**: 采集YUV格式原始视频数据
- **初步处理**: 降噪、亮度调节、自动对焦
- **分辨率支持**: 640x480、1280x720、1920x1080

**音频采集**：
- **硬件接口**: I2S、PCM等音频输入接口
- **原始数据**: 采集PCM格式原始音频数据
- **初步处理**: 降噪、音量调节、回声消除
- **采样率支持**: 16kHz、44.1kHz、48kHz

#### **2. 数据编码**

**视频编码**：
- **编码算法**: H.264/H.265
- **码率控制**: 500-2000kbps (720P@30fps)
- **分片策略**: 按10秒/片分割为MP4文件
- **动态调整**: 根据网络质量调整分辨率、帧率

**音频编码**：
- **编码算法**: MP3、AAC、OPUS
- **比特率**: 64-320kbps
- **分片策略**: 按30秒/片分割
- **动态调整**: 根据网络和设备性能调整参数

#### **3. L610模组配置与MQTT连接**

**网络配置**：
```c
// L610模组初始化
AT+CFUN=1                    // 启用射频功能
AT+CGDCONT=1,"IP","CMNET"    // 配置APN
AT+CGACT=1,1                 // 激活PDP上下文
```

**MQTT连接**：
```c
// 连接华为云IoT平台
AT+MQTTCONN="ssl://iot-mqtts.cn-north-4.myhuaweicloud.com:8883"
// 使用设备证书认证
AT+MQTTAUTH="deviceId","deviceSecret"
```

#### **4. OBS直传准备**

**预签名URL请求**：
```json
// MQTT主题: $oc/devices/${deviceId}/sys/obs/url/request
{
  "object_id": "video_********_120000_001.mp4",
  "file_size": 2048576,
  "content_type": "video/mp4",
  "expires": 3600
}
```

**分片上传策略**：
- **视频**: 10秒/片，文件名格式：`${deviceId}_video_${timestamp}_${segmentId}.mp4`
- **音频**: 30秒/片，文件名格式：`${deviceId}_audio_${timestamp}_${segmentId}.mp3`

### **（二）华为云平台配置**

#### **1. IoT产品与设备创建**

**产品模型定义**：
```json
{
  "video_segment_info": {
    "segment_id": "string",
    "upload_status": "enum",
    "file_size": "int"
  },
  "audio_segment_info": {
    "segment_id": "string", 
    "upload_status": "enum",
    "file_size": "int"
  }
}
```

#### **2. OBS桶配置**

**桶策略配置**：
```json
{
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {"ID": "iot-service-account"},
      "Action": ["obs:PutObject", "obs:GetObject"],
      "Resource": "arn:aws:s3:::learning-supervision-media/*"
    }
  ]
}
```

**存储结构**：
```
learning-supervision-media/
├── video/
│   ├── device001/
│   │   ├── ********/
│   │   │   ├── session_001/
│   │   │   │   ├── segment_001.mp4
│   │   │   │   └── segment_002.mp4
│   │   │   └── merged/
│   │   │       └── complete_001.mp4
└── audio/
    ├── device001/
        ├── ********/
            ├── session_001/
            │   ├── segment_001.mp3
            │   └── segment_002.mp3
            └── merged/
                └── complete_001.mp3
```

#### **3. 媒体处理服务(MPS)配置**

**视频处理模板**：
```json
{
  "template_name": "video_merge_template",
  "input": {
    "bucket": "learning-supervision-media",
    "path": "video/{deviceId}/{date}/{sessionId}/"
  },
  "output": {
    "bucket": "learning-supervision-media", 
    "path": "video/{deviceId}/{date}/merged/",
    "format": "mp4",
    "codec": "h264"
  },
  "merge_rule": {
    "sort_by": "segment_id",
    "trigger": "session_complete_event"
  }
}
```

**音频处理模板**：
```json
{
  "template_name": "audio_merge_template",
  "input": {
    "bucket": "learning-supervision-media",
    "path": "audio/{deviceId}/{date}/{sessionId}/"
  },
  "output": {
    "bucket": "learning-supervision-media",
    "path": "audio/{deviceId}/{date}/merged/",
    "format": "mp3",
    "codec": "aac"
  },
  "merge_rule": {
    "sort_by": "segment_id", 
    "trigger": "session_complete_event"
  }
}
```

### **（三）设备端到云平台数据传输流程**

#### **1. 设备端数据上报**

**分片上传流程**：
```c
// 1. 请求预签名URL
mqtt_publish("$oc/devices/device001/sys/obs/url/request", request_payload);

// 2. 接收预签名URL响应
mqtt_subscribe("$oc/devices/device001/sys/obs/url/response", url_response);

// 3. HTTP PUT上传分片
http_put(presigned_url, video_segment_data);

// 4. 验证上传结果
verify_upload_hash(local_hash, obs_hash);

// 5. 上报上传状态
mqtt_publish("$oc/devices/device001/sys/events/up", upload_status);
```

**重试机制**：
```c
int upload_with_retry(char* url, char* data, int max_retries) {
    for(int i = 0; i < max_retries; i++) {
        if(http_put(url, data) == SUCCESS) {
            return SUCCESS;
        }
        delay(1000 * (i + 1)); // 指数退避
    }
    return FAILED;
}
```

#### **2. 华为云对设备上传数据的处理**

**MPS自动处理流程**：
1. **文件监控**: MPS定期扫描OBS桶中的分片文件
2. **会话检测**: 根据`session_complete_event`触发合并
3. **分片排序**: 按`segment_id`顺序排列分片
4. **文件合并**: 将分片合并成完整文件
5. **转码处理**: 根据模板进行转码优化
6. **结果存储**: 存储到`merged/`目录

## 📱 二、应用端到云平台部分

### **（一）华为云平台配置（应用端访问）**

#### **1. OBS桶访问权限配置**

**应用端访问策略**：
```json
{
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {"ID": "app-service-account"},
      "Action": ["obs:GetObject", "obs:ListBucket"],
      "Resource": [
        "arn:aws:s3:::learning-supervision-media",
        "arn:aws:s3:::learning-supervision-media/*"
      ]
    }
  ]
}
```

### **（二）应用端开发（Android）**

#### **1. OBS SDK集成**

**依赖配置**：
```gradle
dependencies {
    implementation 'com.huaweicloud:esdk-obs-android:3.21.8'
    implementation 'com.squareup.okhttp3:okhttp:4.9.3'
}
```

**SDK初始化**：
```kotlin
class ObsManager {
    private val obsClient: ObsClient
    
    init {
        val config = ObsConfiguration().apply {
            socketTimeout = 30000
            connectionTimeout = 10000
            endPoint = "https://obs.cn-north-4.myhuaweicloud.com"
        }
        obsClient = ObsClient(accessKey, secretKey, config)
    }
}
```

#### **2. 与云平台的数据交互功能**

**文件下载功能**：
```kotlin
class MediaDownloader {
    suspend fun downloadVideoSegments(deviceId: String, sessionId: String): List<File> {
        return withContext(Dispatchers.IO) {
            val objects = obsClient.listObjects(ListObjectsRequest().apply {
                bucketName = "learning-supervision-media"
                prefix = "video/$deviceId/$sessionId/"
            })
            
            objects.objects.map { obj ->
                downloadFile(obj.objectKey)
            }
        }
    }
    
    private fun downloadFile(objectKey: String): File {
        val request = GetObjectRequest("learning-supervision-media", objectKey)
        val result = obsClient.getObject(request)
        
        val localFile = File(context.cacheDir, objectKey.substringAfterLast("/"))
        result.objectContent.use { input ->
            localFile.outputStream().use { output ->
                input.copyTo(output)
            }
        }
        return localFile
    }
}
```

**流播放功能**：
```kotlin
class MediaPlayer {
    fun playVideoStream(deviceId: String, sessionId: String) {
        val streamUrl = generateStreamUrl(deviceId, sessionId)
        
        val mediaItem = MediaItem.fromUri(streamUrl)
        exoPlayer.setMediaItem(mediaItem)
        exoPlayer.prepare()
        exoPlayer.play()
    }
    
    private fun generateStreamUrl(deviceId: String, sessionId: String): String {
        // 生成OBS对象的访问URL
        val objectKey = "video/$deviceId/merged/complete_$sessionId.mp4"
        return obsClient.generatePresignedUrl(
            CreatePresignedUrlRequest().apply {
                method = HttpMethodEnum.GET
                bucketName = "learning-supervision-media"
                objectKey = objectKey
                expires = 3600 // 1小时有效期
            }
        )
    }
}
```

### **（三）应用端到云平台交互流程**

#### **1. 应用端获取文件**

**获取文件列表**：
```kotlin
suspend fun getAvailableMedia(deviceId: String, date: String): MediaList {
    val videoObjects = obsClient.listObjects(ListObjectsRequest().apply {
        bucketName = "learning-supervision-media"
        prefix = "video/$deviceId/$date/merged/"
    })
    
    val audioObjects = obsClient.listObjects(ListObjectsRequest().apply {
        bucketName = "learning-supervision-media"
        prefix = "audio/$deviceId/$date/merged/"
    })
    
    return MediaList(
        videos = videoObjects.objects.map { VideoInfo(it.objectKey, it.metadata.contentLength) },
        audios = audioObjects.objects.map { AudioInfo(it.objectKey, it.metadata.contentLength) }
    )
}
```

#### **2. 交互保障机制**

**异常处理**：
```kotlin
class MediaService {
    suspend fun downloadWithRetry(objectKey: String, maxRetries: Int = 3): File? {
        repeat(maxRetries) { attempt ->
            try {
                return downloadFile(objectKey)
            } catch (e: Exception) {
                Log.w("MediaService", "Download attempt ${attempt + 1} failed", e)
                if (attempt == maxRetries - 1) throw e
                delay(1000L * (attempt + 1)) // 指数退避
            }
        }
        return null
    }
}
```

## 🔧 三、设备端IoT产品模型更新

### **新增属性**

**VideoMonitoring服务**：
- `recordingStatus`: 录制状态 (RECORDING/STOPPED/UPLOADING)
- `currentSegmentId`: 当前分片ID
- `segmentDuration`: 分片时长 (5-60秒)
- `uploadProgress`: 上传进度 (0-100%)
- `encodingFormat`: 编码格式 (H264/H265/MP4)

**VoiceInteraction服务**：
- `recordingStatus`: 录音状态 (RECORDING/STOPPED/UPLOADING)
- `currentSegmentId`: 当前分片ID
- `segmentDuration`: 分片时长 (10-60秒)
- `audioBitrate`: 音频比特率 (64-320kbps)
- `encodingFormat`: 编码格式 (MP3/AAC/OPUS)

### **新增命令**

**OBS上传相关**：
- `REQUEST_OBS_UPLOAD_URL`: 请求预签名上传URL
- `START_VIDEO_RECORDING`: 启动视频录制
- `START_VOICE_RECORDING`: 启动音频录制

### **新增事件**

**上传状态事件**：
- `video_segment_uploaded`: 视频分片上传完成
- `audio_segment_uploaded`: 音频分片上传完成
- `recording_session_complete`: 录制会话完成

## 🎯 四、性能优化与测试

### **性能指标**

**设备端性能**：
- 视频编码: H.264 720P@30fps, 1-2Mbps
- 音频编码: AAC 44.1kHz, 128kbps
- 上传速度: >500KB/s (4G网络)
- 分片大小: 视频2-5MB, 音频1-2MB

**应用端性能**：
- 下载速度: >1MB/s (WiFi)
- 播放延迟: <2秒
- 缓存策略: 预加载下一分片
- 内存使用: <100MB

### **测试策略**

**功能测试**：
1. 设备端录制和上传测试
2. 云端文件合并和转码测试  
3. 应用端下载和播放测试

**性能测试**：
1. 不同网络条件下的上传性能
2. 大文件分片上传稳定性
3. 应用端播放流畅度测试

**异常测试**：
1. 网络中断恢复测试
2. 设备重启数据恢复测试
3. 云端服务异常处理测试

## 🚀 五、Android应用端集成示例

### **（一）OBS SDK集成配置**

**build.gradle配置**：
```gradle
android {
    compileSdk 34

    defaultConfig {
        minSdk 24
        targetSdk 34
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    // 华为云OBS SDK
    implementation files('libs/esdk-obs-android-3.21.8.jar')
    implementation 'com.squareup.okhttp3:okhttp:4.9.3'
    implementation 'com.squareup.okio:okio:2.10.0'

    // 媒体播放
    implementation 'com.google.android.exoplayer:exoplayer:2.18.7'

    // 协程支持
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4'
}
```

### **（二）核心服务类实现**

**ObsMediaManager.kt**：
```kotlin
class ObsMediaManager(private val context: Context) {
    private val obsClient: ObsClient
    private val bucketName = "learning-supervision-media"

    init {
        val config = ObsConfiguration().apply {
            socketTimeout = 30000
            connectionTimeout = 10000
            endPoint = "https://obs.cn-north-4.myhuaweicloud.com"
        }
        obsClient = ObsClient(
            BuildConfig.OBS_ACCESS_KEY,
            BuildConfig.OBS_SECRET_KEY,
            config
        )
    }

    suspend fun getDeviceMediaList(deviceId: String, date: String): MediaList {
        return withContext(Dispatchers.IO) {
            try {
                val videoList = getVideoList(deviceId, date)
                val audioList = getAudioList(deviceId, date)
                MediaList(videoList, audioList)
            } catch (e: Exception) {
                Log.e("ObsMediaManager", "Failed to get media list", e)
                MediaList(emptyList(), emptyList())
            }
        }
    }

    private fun getVideoList(deviceId: String, date: String): List<VideoInfo> {
        val request = ListObjectsRequest().apply {
            bucketName = <EMAIL>
            prefix = "video/$deviceId/$date/merged/"
        }

        return obsClient.listObjects(request).objects.map { obj ->
            VideoInfo(
                objectKey = obj.objectKey,
                fileName = obj.objectKey.substringAfterLast("/"),
                fileSize = obj.metadata.contentLength,
                lastModified = obj.metadata.lastModified,
                streamUrl = generatePresignedUrl(obj.objectKey)
            )
        }
    }

    private fun generatePresignedUrl(objectKey: String): String {
        val request = CreatePresignedUrlRequest().apply {
            method = HttpMethodEnum.GET
            bucketName = <EMAIL>
            objectKey = objectKey
            expires = 3600 // 1小时有效期
        }
        return obsClient.generatePresignedUrl(request)
    }
}
```

### **（三）媒体播放器集成**

**MediaPlayerManager.kt**：
```kotlin
class MediaPlayerManager(private val context: Context) {
    private var exoPlayer: ExoPlayer? = null
    private var currentVideoInfo: VideoInfo? = null

    fun initializePlayer() {
        exoPlayer = ExoPlayer.Builder(context)
            .setLoadControl(
                DefaultLoadControl.Builder()
                    .setBufferDurationsMs(
                        15000, // 最小缓冲
                        50000, // 最大缓冲
                        1500,  // 播放缓冲
                        5000   // 重新缓冲
                    )
                    .build()
            )
            .build()
    }

    fun playVideo(videoInfo: VideoInfo, playerView: PlayerView) {
        exoPlayer?.let { player ->
            currentVideoInfo = videoInfo

            val mediaItem = MediaItem.Builder()
                .setUri(videoInfo.streamUrl)
                .setMediaMetadata(
                    MediaMetadata.Builder()
                        .setTitle(videoInfo.fileName)
                        .build()
                )
                .build()

            player.setMediaItem(mediaItem)
            player.prepare()
            player.playWhenReady = true

            playerView.player = player
        }
    }

    fun releasePlayer() {
        exoPlayer?.release()
        exoPlayer = null
    }
}
```

---

**总结**: 基于华为云OBS的音视频直传架构实现了设备端轻量化、云端智能处理、应用端高效访问的完整解决方案，大幅提升了系统性能和用户体验。
