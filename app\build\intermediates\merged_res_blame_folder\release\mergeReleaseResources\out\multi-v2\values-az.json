{"logs": [{"outputFile": "com.example.iotandroidv20.app-mergeReleaseResources-58:/values-az/values-az.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\752167a3e67da85263f648420d161268\\transformed\\ui-release\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,293,390,491,582,663,751,843,925,1006,1095,1167,1241,1317,1390,1471,1537", "endColumns": "99,87,96,100,90,80,87,91,81,80,88,71,73,75,72,80,65,116", "endOffsets": "200,288,385,486,577,658,746,838,920,1001,1090,1162,1236,1312,1385,1466,1532,1649"}, "to": {"startLines": "27,28,29,31,32,84,85,149,150,151,152,153,154,155,156,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1513,1613,1701,1874,1975,5856,5937,12768,12860,12942,13023,13112,13184,13258,13334,13508,13589,13655", "endColumns": "99,87,96,100,90,80,87,91,81,80,88,71,73,75,72,80,65,116", "endOffsets": "1608,1696,1793,1970,2061,5932,6020,12855,12937,13018,13107,13179,13253,13329,13402,13584,13650,13767"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\01b6e8b8fea8bb45b55852d4590f3437\\transformed\\foundation-release\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,93", "endOffsets": "139,233"}, "to": {"startLines": "161,162", "startColumns": "4,4", "startOffsets": "13772,13861", "endColumns": "88,93", "endOffsets": "13856,13950"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2c60f935d2795008c652ca71073c0e75\\transformed\\media3-ui-1.2.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,476,660,748,837,914,1006,1094,1170,1234,1325,1416,1481,1546,1608,1676,1804,1936,2062,2133,2214,2284,2360,2456,2553,2622,2688,2741,2799,2847,2908,2972,3044,3103,3166,3229,3289,3355,3419,3485,3537,3595,3667,3739", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,87,88,76,91,87,75,63,90,90,64,64,61,67,127,131,125,70,80,69,75,95,96,68,65,52,57,47,60,63,71,58,62,62,59,65,63,65,51,57,71,71,53", "endOffsets": "280,471,655,743,832,909,1001,1089,1165,1229,1320,1411,1476,1541,1603,1671,1799,1931,2057,2128,2209,2279,2355,2451,2548,2617,2683,2736,2794,2842,2903,2967,3039,3098,3161,3224,3284,3350,3414,3480,3532,3590,3662,3734,3788"}, "to": {"startLines": "2,11,15,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,526,2066,2154,2243,2320,2412,2500,2576,2640,2731,2822,2887,2952,3014,3082,3210,3342,3468,3539,3620,3690,3766,3862,3959,4028,4751,4804,4862,4910,4971,5035,5107,5166,5229,5292,5352,5418,5482,5548,5600,5658,5730,5802", "endLines": "10,14,18,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83", "endColumns": "17,12,12,87,88,76,91,87,75,63,90,90,64,64,61,67,127,131,125,70,80,69,75,95,96,68,65,52,57,47,60,63,71,58,62,62,59,65,63,65,51,57,71,71,53", "endOffsets": "330,521,705,2149,2238,2315,2407,2495,2571,2635,2726,2817,2882,2947,3009,3077,3205,3337,3463,3534,3615,3685,3761,3857,3954,4023,4089,4799,4857,4905,4966,5030,5102,5161,5224,5287,5347,5413,5477,5543,5595,5653,5725,5797,5851"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\31c73cb6dfb12faac49eb1bae4282a74\\transformed\\core-1.16.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,782", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,878"}, "to": {"startLines": "20,21,22,23,24,25,26,157", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "786,887,989,1092,1196,1297,1402,13407", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "882,984,1087,1191,1292,1397,1508,13503"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\835843a1fca13b839a80c71cfb075e12\\transformed\\media3-session-1.2.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,131,207,278,346,423,504,596", "endColumns": "75,75,70,67,76,80,91,95", "endOffsets": "126,202,273,341,418,499,591,687"}, "to": {"startLines": "19,30,143,144,145,146,147,148", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "710,1798,12283,12354,12422,12499,12580,12672", "endColumns": "75,75,70,67,76,80,91,95", "endOffsets": "781,1869,12349,12417,12494,12575,12667,12763"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\398f61ea23c76807315a7831064b1215\\transformed\\material3-release\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,183,305,418,543,645,747,866,1002,1123,1269,1354,1455,1546,1644,1756,1878,1984,2123,2260,2390,2549,2674,2789,2907,3023,3115,3214,3331,3463,3568,3673,3779,3917,4060,4170,4271,4347,4450,4550,4673,4761,4850,4955,5035,5119,5219,5319,5416,5514,5602,5706,5806,5908,6026,6106,6215", "endColumns": "127,121,112,124,101,101,118,135,120,145,84,100,90,97,111,121,105,138,136,129,158,124,114,117,115,91,98,116,131,104,104,105,137,142,109,100,75,102,99,122,87,88,104,79,83,99,99,96,97,87,103,99,101,117,79,108,97", "endOffsets": "178,300,413,538,640,742,861,997,1118,1264,1349,1450,1541,1639,1751,1873,1979,2118,2255,2385,2544,2669,2784,2902,3018,3110,3209,3326,3458,3563,3668,3774,3912,4055,4165,4266,4342,4445,4545,4668,4756,4845,4950,5030,5114,5214,5314,5411,5509,5597,5701,5801,5903,6021,6101,6210,6308"}, "to": {"startLines": "86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6025,6153,6275,6388,6513,6615,6717,6836,6972,7093,7239,7324,7425,7516,7614,7726,7848,7954,8093,8230,8360,8519,8644,8759,8877,8993,9085,9184,9301,9433,9538,9643,9749,9887,10030,10140,10241,10317,10420,10520,10643,10731,10820,10925,11005,11089,11189,11289,11386,11484,11572,11676,11776,11878,11996,12076,12185", "endColumns": "127,121,112,124,101,101,118,135,120,145,84,100,90,97,111,121,105,138,136,129,158,124,114,117,115,91,98,116,131,104,104,105,137,142,109,100,75,102,99,122,87,88,104,79,83,99,99,96,97,87,103,99,101,117,79,108,97", "endOffsets": "6148,6270,6383,6508,6610,6712,6831,6967,7088,7234,7319,7420,7511,7609,7721,7843,7949,8088,8225,8355,8514,8639,8754,8872,8988,9080,9179,9296,9428,9533,9638,9744,9882,10025,10135,10236,10312,10415,10515,10638,10726,10815,10920,11000,11084,11184,11284,11381,11479,11567,11671,11771,11873,11991,12071,12180,12278"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5dca3f3744b4fa46ea145e6d4fc9225c\\transformed\\media3-exoplayer-1.2.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,186,249,314,392,459,548,641", "endColumns": "69,60,62,64,77,66,88,92,70", "endOffsets": "120,181,244,309,387,454,543,636,707"}, "to": {"startLines": "57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4094,4164,4225,4288,4353,4431,4498,4587,4680", "endColumns": "69,60,62,64,77,66,88,92,70", "endOffsets": "4159,4220,4283,4348,4426,4493,4582,4675,4746"}}]}]}