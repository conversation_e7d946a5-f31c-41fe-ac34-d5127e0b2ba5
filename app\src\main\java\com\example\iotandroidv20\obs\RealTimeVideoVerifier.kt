package com.example.iotandroidv20.obs

import android.content.Context
import com.example.iotandroidv20.config.HuaweiCloudConfig
import com.example.iotandroidv20.utils.Logger
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import java.text.SimpleDateFormat
import java.util.*

/**
 * 实时视频监控功能验证器
 * 专门用于验证实时视频监控功能的各个环节
 */
class RealTimeVideoVerifier {
    
    private val realTimeVideoManager = RealTimeVideoManager.getInstance()
    private val obsApiClient = HuaweiObsApiClient.getInstance()
    
    private val _verificationStatus = MutableStateFlow(VerificationStatus.NOT_STARTED)
    val verificationStatus: StateFlow<VerificationStatus> = _verificationStatus.asStateFlow()
    
    private val _verificationProgress = MutableStateFlow(0f)
    val verificationProgress: StateFlow<Float> = _verificationProgress.asStateFlow()
    
    private val _verificationMessage = MutableStateFlow("")
    val verificationMessage: StateFlow<String> = _verificationMessage.asStateFlow()
    
    private val _verificationResults = MutableStateFlow<List<VerificationStep>>(emptyList())
    val verificationResults: StateFlow<List<VerificationStep>> = _verificationResults.asStateFlow()
    
    companion object {
        private const val TAG = "RealTimeVideoVerifier"
        private const val TEST_DEVICE_ID = HuaweiCloudConfig.DEVICE_ID
    }
    
    /**
     * 开始完整的实时视频监控功能验证
     */
    suspend fun startFullVerification(): VerificationReport {
        return withContext(Dispatchers.IO) {
            try {
                Logger.d("开始实时视频监控功能验证", tag = TAG)
                _verificationStatus.value = VerificationStatus.RUNNING
                _verificationProgress.value = 0f
                _verificationMessage.value = "开始验证实时视频监控功能..."
                
                val steps = mutableListOf<VerificationStep>()
                
                // 步骤1: 验证OBS连接
                _verificationMessage.value = "步骤1: 验证OBS连接..."
                _verificationProgress.value = 0.1f
                val obsConnectionStep = verifyObsConnection()
                steps.add(obsConnectionStep)
                delay(500)
                
                // 步骤2: 验证视频文件存在性
                _verificationMessage.value = "步骤2: 检查视频文件..."
                _verificationProgress.value = 0.3f
                val videoFilesStep = verifyVideoFilesExistence()
                steps.add(videoFilesStep)
                delay(500)
                
                // 步骤3: 验证实时流URL生成
                _verificationMessage.value = "步骤3: 验证实时流URL生成..."
                _verificationProgress.value = 0.5f
                val streamUrlStep = verifyStreamUrlGeneration()
                steps.add(streamUrlStep)
                delay(500)
                
                // 步骤4: 验证实时视频管理器状态
                _verificationMessage.value = "步骤4: 验证视频管理器状态..."
                _verificationProgress.value = 0.7f
                val managerStatusStep = verifyManagerStatus()
                steps.add(managerStatusStep)
                delay(500)
                
                // 步骤5: 验证完整的启动流程
                _verificationMessage.value = "步骤5: 验证完整启动流程..."
                _verificationProgress.value = 0.9f
                val fullFlowStep = verifyFullStartupFlow()
                steps.add(fullFlowStep)
                delay(500)
                
                _verificationResults.value = steps
                _verificationProgress.value = 1.0f
                
                val passedSteps = steps.count { it.success }
                val totalSteps = steps.size
                
                if (passedSteps == totalSteps) {
                    _verificationStatus.value = VerificationStatus.SUCCESS
                    _verificationMessage.value = "✅ 实时视频监控功能验证完成！所有功能正常"
                } else {
                    _verificationStatus.value = VerificationStatus.PARTIAL_SUCCESS
                    _verificationMessage.value = "⚠️ 验证完成: $passedSteps/$totalSteps 项通过"
                }
                
                Logger.d("实时视频监控功能验证完成", tag = TAG)
                
                VerificationReport(
                    totalSteps = totalSteps,
                    passedSteps = passedSteps,
                    steps = steps,
                    overallSuccess = passedSteps == totalSteps,
                    timestamp = System.currentTimeMillis()
                )
                
            } catch (e: Exception) {
                Logger.e("验证过程异常: ${e.message}", tag = TAG)
                _verificationStatus.value = VerificationStatus.FAILED
                _verificationMessage.value = "验证失败: ${e.message}"
                
                VerificationReport(
                    totalSteps = 0,
                    passedSteps = 0,
                    steps = emptyList(),
                    overallSuccess = false,
                    timestamp = System.currentTimeMillis(),
                    error = e.message
                )
            }
        }
    }
    
    /**
     * 验证OBS连接
     */
    private suspend fun verifyObsConnection(): VerificationStep {
        return try {
            Logger.d("验证OBS连接", tag = TAG)
            
            val result = obsApiClient.listObjects("", 1)
            
            when (result) {
                is ObsApiResult.Success -> {
                    Logger.d("OBS连接验证成功", tag = TAG)
                    VerificationStep(
                        name = "OBS连接验证",
                        success = true,
                        message = "OBS连接正常，可以访问存储桶",
                        details = "成功列举对象，连接状态良好"
                    )
                }
                is ObsApiResult.Error -> {
                    Logger.e("OBS连接验证失败: ${result.message}", tag = TAG)
                    VerificationStep(
                        name = "OBS连接验证",
                        success = false,
                        message = "OBS连接失败",
                        details = "错误信息: ${result.message}"
                    )
                }
            }
        } catch (e: Exception) {
            Logger.e("OBS连接验证异常: ${e.message}", tag = TAG)
            VerificationStep(
                name = "OBS连接验证",
                success = false,
                message = "OBS连接验证异常",
                details = "异常信息: ${e.message}"
            )
        }
    }
    
    /**
     * 验证视频文件存在性
     */
    private suspend fun verifyVideoFilesExistence(): VerificationStep {
        return try {
            Logger.d("验证视频文件存在性", tag = TAG)
            
            // 检查今天的视频文件
            val today = SimpleDateFormat("yyyyMMdd", Locale.getDefault()).format(Date())
            val videoPrefix = "${HuaweiCloudConfig.OBS_VIDEO_PATH}$TEST_DEVICE_ID/$today/"
            
            val result = obsApiClient.listObjects(videoPrefix, 50)
            
            when (result) {
                is ObsApiResult.Success -> {
                    val objects = result.data
                    val videoFiles = objects.filter { 
                        it.key.endsWith(".mp4") || it.key.endsWith(".m3u8") || it.key.endsWith(".ts")
                    }
                    
                    if (videoFiles.isNotEmpty()) {
                        Logger.d("找到 ${videoFiles.size} 个视频文件", tag = TAG)
                        VerificationStep(
                            name = "视频文件检查",
                            success = true,
                            message = "找到 ${videoFiles.size} 个视频文件",
                            details = "最新文件: ${videoFiles.maxByOrNull { it.lastModified }?.key ?: "无"}"
                        )
                    } else {
                        Logger.w("未找到视频文件", tag = TAG)
                        VerificationStep(
                            name = "视频文件检查",
                            success = false,
                            message = "未找到今天的视频文件",
                            details = "路径: $videoPrefix，可能设备尚未开始录制"
                        )
                    }
                }
                is ObsApiResult.Error -> {
                    Logger.e("视频文件检查失败: ${result.message}", tag = TAG)
                    VerificationStep(
                        name = "视频文件检查",
                        success = false,
                        message = "视频文件检查失败",
                        details = "错误信息: ${result.message}"
                    )
                }
            }
        } catch (e: Exception) {
            Logger.e("视频文件检查异常: ${e.message}", tag = TAG)
            VerificationStep(
                name = "视频文件检查",
                success = false,
                message = "视频文件检查异常",
                details = "异常信息: ${e.message}"
            )
        }
    }
    
    /**
     * 验证实时流URL生成
     */
    private suspend fun verifyStreamUrlGeneration(): VerificationStep {
        return try {
            Logger.d("验证实时流URL生成", tag = TAG)
            
            // 模拟获取实时流URL的过程
            val liveStreamUrl = "https://live-stream.example.com/device/$TEST_DEVICE_ID/live.m3u8"
            
            // 检查URL格式是否正确
            val isValidUrl = liveStreamUrl.startsWith("http") && 
                           (liveStreamUrl.contains(".m3u8") || liveStreamUrl.contains(".mp4"))
            
            if (isValidUrl) {
                Logger.d("实时流URL生成成功", tag = TAG)
                VerificationStep(
                    name = "实时流URL生成",
                    success = true,
                    message = "实时流URL生成成功",
                    details = "URL: $liveStreamUrl"
                )
            } else {
                Logger.e("实时流URL格式无效", tag = TAG)
                VerificationStep(
                    name = "实时流URL生成",
                    success = false,
                    message = "实时流URL格式无效",
                    details = "生成的URL: $liveStreamUrl"
                )
            }
        } catch (e: Exception) {
            Logger.e("实时流URL生成异常: ${e.message}", tag = TAG)
            VerificationStep(
                name = "实时流URL生成",
                success = false,
                message = "实时流URL生成异常",
                details = "异常信息: ${e.message}"
            )
        }
    }
    
    /**
     * 验证实时视频管理器状态
     */
    private suspend fun verifyManagerStatus(): VerificationStep {
        return try {
            Logger.d("验证实时视频管理器状态", tag = TAG)
            
            // 检查管理器的初始状态
            val initialStatus = realTimeVideoManager.liveStreamStatus.value
            val initialUrl = realTimeVideoManager.currentStreamUrl.value
            
            val isInitialStateCorrect = initialStatus == LiveStreamStatus.DISCONNECTED && initialUrl == null
            
            if (isInitialStateCorrect) {
                Logger.d("实时视频管理器状态正常", tag = TAG)
                VerificationStep(
                    name = "视频管理器状态",
                    success = true,
                    message = "视频管理器初始状态正常",
                    details = "状态: $initialStatus, URL: ${initialUrl ?: "无"}"
                )
            } else {
                Logger.w("实时视频管理器状态异常", tag = TAG)
                VerificationStep(
                    name = "视频管理器状态",
                    success = false,
                    message = "视频管理器状态异常",
                    details = "状态: $initialStatus, URL: ${initialUrl ?: "无"}"
                )
            }
        } catch (e: Exception) {
            Logger.e("视频管理器状态检查异常: ${e.message}", tag = TAG)
            VerificationStep(
                name = "视频管理器状态",
                success = false,
                message = "视频管理器状态检查异常",
                details = "异常信息: ${e.message}"
            )
        }
    }
    
    /**
     * 验证完整的启动流程
     */
    private suspend fun verifyFullStartupFlow(): VerificationStep {
        return try {
            Logger.d("验证完整启动流程", tag = TAG)
            
            // 尝试启动实时视频查看
            val startResult = realTimeVideoManager.startInstantLiveView(TEST_DEVICE_ID)
            
            // 检查状态变化
            delay(1000) // 等待状态更新
            val finalStatus = realTimeVideoManager.liveStreamStatus.value
            val finalUrl = realTimeVideoManager.currentStreamUrl.value
            
            if (startResult && finalStatus == LiveStreamStatus.CONNECTED && finalUrl != null) {
                Logger.d("完整启动流程验证成功", tag = TAG)
                VerificationStep(
                    name = "完整启动流程",
                    success = true,
                    message = "完整启动流程验证成功",
                    details = "状态: $finalStatus, URL: $finalUrl"
                )
            } else {
                Logger.w("完整启动流程验证失败", tag = TAG)
                VerificationStep(
                    name = "完整启动流程",
                    success = false,
                    message = "完整启动流程验证失败",
                    details = "启动结果: $startResult, 状态: $finalStatus, URL: $finalUrl"
                )
            }
        } catch (e: Exception) {
            Logger.e("完整启动流程验证异常: ${e.message}", tag = TAG)
            VerificationStep(
                name = "完整启动流程",
                success = false,
                message = "完整启动流程验证异常",
                details = "异常信息: ${e.message}"
            )
        }
    }
    
    /**
     * 生成详细的验证报告
     */
    fun generateDetailedReport(report: VerificationReport): String {
        val sb = StringBuilder()
        sb.appendLine("=== 实时视频监控功能验证报告 ===")
        sb.appendLine("验证时间: ${SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(Date(report.timestamp))}")
        sb.appendLine("设备ID: $TEST_DEVICE_ID")
        sb.appendLine()
        
        sb.appendLine("总体结果: ${if (report.overallSuccess) "✅ 全部通过" else "❌ 部分失败"}")
        sb.appendLine("通过率: ${report.passedSteps}/${report.totalSteps} (${(report.passedSteps * 100 / report.totalSteps.coerceAtLeast(1))}%)")
        sb.appendLine()
        
        sb.appendLine("详细结果:")
        report.steps.forEachIndexed { index, step ->
            sb.appendLine("${index + 1}. ${step.name}: ${if (step.success) "✅" else "❌"}")
            sb.appendLine("   ${step.message}")
            sb.appendLine("   详情: ${step.details}")
            sb.appendLine()
        }
        
        if (report.error != null) {
            sb.appendLine("整体错误: ${report.error}")
        }
        
        return sb.toString()
    }
}

// ==================== 数据类 ====================

/**
 * 验证状态
 */
enum class VerificationStatus {
    NOT_STARTED,    // 未开始
    RUNNING,        // 运行中
    SUCCESS,        // 成功
    PARTIAL_SUCCESS, // 部分成功
    FAILED          // 失败
}

/**
 * 验证步骤
 */
data class VerificationStep(
    val name: String,
    val success: Boolean,
    val message: String,
    val details: String
)

/**
 * 验证报告
 */
data class VerificationReport(
    val totalSteps: Int,
    val passedSteps: Int,
    val steps: List<VerificationStep>,
    val overallSuccess: Boolean,
    val timestamp: Long,
    val error: String? = null
)
