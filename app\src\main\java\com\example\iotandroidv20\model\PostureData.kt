package com.example.iotandroidv20.model

import com.google.gson.annotations.SerializedName
import java.util.Date

/**
 * 坐姿数据模型
 */
data class PostureData(
    @SerializedName("timestamp")
    val timestamp: Long = System.currentTimeMillis(),

    @SerializedName("device_id")
    val deviceId: String = "",

    @SerializedName("posture_status")
    val postureStatus: String = "unknown", // good, bad, unknown

    @SerializedName("angle_x")
    val angleX: Float = 0f,

    @SerializedName("angle_y")
    val angleY: Float = 0f,

    @SerializedName("angle_z")
    val angleZ: Float = 0f,

    @SerializedName("confidence")
    val confidence: Float = 0f, // 检测置信度 0-1

    @SerializedName("duration")
    val duration: Long = 0L, // 持续时间（毫秒）

    @SerializedName("sensor_data")
    val sensorData: Map<String, Any> = emptyMap(),

    @SerializedName("battery_level")
    val batteryLevel: Int = 0,

    @SerializedName("signal_strength")
    val signalStrength: Int = 0
) {
    
    /**
     * 获取格式化的时间字符串
     */
    fun getFormattedTime(): String {
        val date = Date(timestamp)
        return android.text.format.DateFormat.format("HH:mm:ss", date).toString()
    }
    
    /**
     * 判断是否为良好坐姿
     */
    fun isGoodPosture(): Boolean {
        return postureStatus.equals("good", ignoreCase = true)
    }
    
    /**
     * 获取坐姿状态的中文描述
     */
    fun getPostureDescription(): String {
        return when (postureStatus.lowercase()) {
            "good" -> "坐姿良好"
            "bad" -> "坐姿不良"
            "warning" -> "坐姿警告"
            "critical" -> "坐姿严重不良"
            else -> "状态未知"
        }
    }

    /**
     * 获取坐姿状态的详细描述
     */
    fun getDetailedDescription(): String {
        val baseDescription = getPostureDescription()
        val confidenceText = if (confidence > 0) " (置信度: ${(confidence * 100).toInt()}%)" else ""
        val durationText = if (duration > 0) " 持续: ${formatDuration(duration)}" else ""
        return "$baseDescription$confidenceText$durationText"
    }

    /**
     * 获取角度信息的描述
     */
    fun getAngleDescription(): String {
        return "X: ${String.format("%.1f", angleX)}° Y: ${String.format("%.1f", angleY)}° Z: ${String.format("%.1f", angleZ)}°"
    }

    /**
     * 获取设备状态描述
     */
    fun getDeviceStatusDescription(): String {
        return "电量: $batteryLevel% 信号: $signalStrength%"
    }

    /**
     * 格式化持续时间
     */
    private fun formatDuration(durationMs: Long): String {
        val seconds = durationMs / 1000
        val minutes = seconds / 60
        val hours = minutes / 60

        return when {
            hours > 0 -> "${hours}小时${minutes % 60}分钟"
            minutes > 0 -> "${minutes}分钟${seconds % 60}秒"
            else -> "${seconds}秒"
        }
    }

    /**
     * 判断是否需要提醒
     */
    fun needsReminder(): Boolean {
        return !isGoodPosture() && confidence > 0.7f && duration > 30000L // 30秒以上的不良坐姿
    }
}

/**
 * IoT设备状态
 */
data class DeviceStatus(
    val isConnected: Boolean = false,
    val lastUpdateTime: Long = 0L,
    val batteryLevel: Int = 0,
    val signalStrength: Int = 0
)

/**
 * 应用状态
 */
data class AppState(
    val deviceStatus: DeviceStatus = DeviceStatus(),
    val currentPosture: PostureData = PostureData(),
    val postureHistory: List<PostureData> = emptyList(),
    val isMonitoring: Boolean = false,
    val reminderEnabled: Boolean = true,
    val reminderInterval: Int = 30, // 提醒间隔（分钟）
    val autoReminderEnabled: Boolean = true,
    val dailyStats: DailyStats = DailyStats()
)

/**
 * 每日统计数据
 */
data class DailyStats(
    val date: String = java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault()).format(java.util.Date()),
    val totalMonitoringTime: Long = 0L, // 总监控时间（毫秒）
    val goodPostureTime: Long = 0L, // 良好坐姿时间（毫秒）
    val badPostureTime: Long = 0L, // 不良坐姿时间（毫秒）
    val reminderCount: Int = 0, // 提醒次数
    val postureScore: Float = 0f // 坐姿评分 0-100
) {
    /**
     * 获取良好坐姿百分比
     */
    fun getGoodPosturePercentage(): Float {
        return if (totalMonitoringTime > 0) {
            (goodPostureTime.toFloat() / totalMonitoringTime.toFloat()) * 100f
        } else 0f
    }

    /**
     * 获取坐姿评分等级
     */
    fun getScoreGrade(): String {
        return when {
            postureScore >= 90 -> "优秀"
            postureScore >= 80 -> "良好"
            postureScore >= 70 -> "一般"
            postureScore >= 60 -> "较差"
            else -> "需要改进"
        }
    }
}

/**
 * 提醒记录
 */
data class ReminderRecord(
    val timestamp: Long = System.currentTimeMillis(),
    val type: ReminderType = ReminderType.MANUAL,
    val message: String = "",
    val postureData: PostureData? = null
)

/**
 * 提醒类型
 */
enum class ReminderType {
    MANUAL,     // 手动提醒
    AUTO,       // 自动提醒
    SCHEDULED   // 定时提醒
}

/**
 * 坐姿质量枚举
 */
enum class PostureQuality(val displayName: String) {
    EXCELLENT("优秀"),
    GOOD("良好"),
    FAIR("一般"),
    POOR("较差"),
    VERY_POOR("很差")
}
