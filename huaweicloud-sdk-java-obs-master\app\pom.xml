<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.huaweicloud</groupId>
	<artifactId>esdk-obs-java-android</artifactId>
	<version>3.21.12</version>

	<name>OBS SDK for Java/Android</name>
	<description>The OBS SDK for Android used for accessing Object Storage Service</description>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<checkStyleFile>${project.basedir}/../../CI/checkstyle_ruleset_minimal_IT_product_line.xml</checkStyleFile>
	</properties>

	<dependencies>

		<dependency>
			<groupId>com.jamesmurty.utils</groupId>
			<artifactId>java-xmlbuilder</artifactId>
			<version>1.3</version>
			<exclusions>
				<exclusion>
					<groupId>net.iharder</groupId>
					<artifactId>base64</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
		    <groupId>com.squareup.okhttp3</groupId>
		    <artifactId>okhttp</artifactId>
		    <version>4.9.1</version>
		</dependency>
		<dependency>
			<groupId>com.squareup.okio</groupId>
			<artifactId>okio</artifactId>
			<version>2.7.0</version>
		</dependency>
		
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>2.12.5</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.12.5</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>2.12.5</version>
		</dependency>

		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>2.17.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>2.17.0</version>
		</dependency>
	</dependencies>

	<build>
		<sourceDirectory>src/main/java</sourceDirectory>
		<testSourceDirectory>src/test/java</testSourceDirectory>
		<plugins>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>exec-maven-plugin</artifactId>
				<version>1.2</version>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>${basedir}/gradlew.bat</executable>
							<arguments>
								<argument>build</argument>
							</arguments>
						</configuration>
					</execution>
				</executions>
			</plugin>
			
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-checkstyle-plugin</artifactId>
				<configuration>
					<configLocation>${checkStyleFile}</configLocation>
				</configuration>
				<version>2.9.1</version>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-clean-plugin</artifactId>
				<configuration>
					<filesets>
						<fileset>
							<directory>logs</directory>
						</fileset>
					</filesets>
				</configuration>
			</plugin>

			<!-- compile plugin -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<encoding>UTF-8</encoding>
				</configuration>
				<executions>
					<execution>
						<phase>compile</phase>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>3.0.0-M1</version>
				<configuration>
					<source>1.8</source>
					<docencoding>UTF-8</docencoding>
					<charset>UTF-8</charset>
					<excludePackageNames>*.internal.*:*.log:*.proxy:okio</excludePackageNames>
					<destDir>${project.build.directory}/doc</destDir>
				</configuration>
				<executions>
					<execution>
						<id>attach-javadocs</id>
						<phase>install</phase>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-assembly-plugin</artifactId>
				<executions>
					<execution>
						<phase>install</phase>
						<goals>
							<goal>single</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<appendAssemblyId>false</appendAssemblyId>
					<descriptors>
						<descriptor>assembly-android.xml</descriptor>
					</descriptors>
				</configuration>
			</plugin>
		</plugins>
	</build>


</project>
