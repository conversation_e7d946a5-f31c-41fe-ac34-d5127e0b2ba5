{"logs": [{"outputFile": "com.example.iotandroidv20.app-mergeDebugResources-2:/values-cs/values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\2c60f935d2795008c652ca71073c0e75\\transformed\\media3-ui-1.2.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,610,924,1005,1085,1163,1265,1363,1441,1505,1594,1686,1756,1822,1887,1959,2072,2187,2310,2384,2464,2536,2617,2711,2806,2873,2938,2991,3049,3097,3158,3224,3291,3354,3421,3486,3545,3610,3674,3740,3792,3855,3932,4009", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,80,79,77,101,97,77,63,88,91,69,65,64,71,112,114,122,73,79,71,80,93,94,66,64,52,57,47,60,65,66,62,66,64,58,64,63,65,51,62,76,76,53", "endOffsets": "282,605,919,1000,1080,1158,1260,1358,1436,1500,1589,1681,1751,1817,1882,1954,2067,2182,2305,2379,2459,2531,2612,2706,2801,2868,2933,2986,3044,3092,3153,3219,3286,3349,3416,3481,3540,3605,3669,3735,3787,3850,3927,4004,4058"}, "to": {"startLines": "2,11,17,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,660,2317,2398,2478,2556,2658,2756,2834,2898,2987,3079,3149,3215,3280,3352,3465,3580,3703,3777,3857,3929,4010,4104,4199,4266,4990,5043,5101,5149,5210,5276,5343,5406,5473,5538,5597,5662,5726,5792,5844,5907,5984,6061", "endLines": "10,16,22,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "endColumns": "17,12,12,80,79,77,101,97,77,63,88,91,69,65,64,71,112,114,122,73,79,71,80,93,94,66,64,52,57,47,60,65,66,62,66,64,58,64,63,65,51,62,76,76,53", "endOffsets": "332,655,969,2393,2473,2551,2653,2751,2829,2893,2982,3074,3144,3210,3275,3347,3460,3575,3698,3772,3852,3924,4005,4099,4194,4261,4326,5038,5096,5144,5205,5271,5338,5401,5468,5533,5592,5657,5721,5787,5839,5902,5979,6056,6110"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\835843a1fca13b839a80c71cfb075e12\\transformed\\media3-session-1.2.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,129,203,276,345,425,509,604", "endColumns": "73,73,72,68,79,83,94,102", "endOffsets": "124,198,271,340,420,504,599,702"}, "to": {"startLines": "23,34,147,148,149,150,151,152", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "974,2049,12482,12555,12624,12704,12788,12883", "endColumns": "73,73,72,68,79,83,94,102", "endOffsets": "1043,2118,12550,12619,12699,12783,12878,12981"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\5dca3f3744b4fa46ea145e6d4fc9225c\\transformed\\media3-exoplayer-1.2.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,193,256,325,402,472,554,634", "endColumns": "75,61,62,68,76,69,81,79,79", "endOffsets": "126,188,251,320,397,467,549,629,709"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4331,4407,4469,4532,4601,4678,4748,4830,4910", "endColumns": "75,61,62,68,76,69,81,79,79", "endOffsets": "4402,4464,4527,4596,4673,4743,4825,4905,4985"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\398f61ea23c76807315a7831064b1215\\transformed\\material3-release\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,290,409,527,628,723,835,969,1085,1224,1309,1409,1502,1599,1715,1837,1942,2075,2205,2347,2510,2638,2755,2879,3000,3091,3188,3308,3423,3521,3624,3732,3864,4005,4115,4214,4298,4392,4487,4599,4691,4777,4890,4970,5056,5157,5260,5357,5458,5546,5652,5751,5854,5973,6053,6157", "endColumns": "117,116,118,117,100,94,111,133,115,138,84,99,92,96,115,121,104,132,129,141,162,127,116,123,120,90,96,119,114,97,102,107,131,140,109,98,83,93,94,111,91,85,112,79,85,100,102,96,100,87,105,98,102,118,79,103,94", "endOffsets": "168,285,404,522,623,718,830,964,1080,1219,1304,1404,1497,1594,1710,1832,1937,2070,2200,2342,2505,2633,2750,2874,2995,3086,3183,3303,3418,3516,3619,3727,3859,4000,4110,4209,4293,4387,4482,4594,4686,4772,4885,4965,5051,5152,5255,5352,5453,5541,5647,5746,5849,5968,6048,6152,6247"}, "to": {"startLines": "90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6285,6403,6520,6639,6757,6858,6953,7065,7199,7315,7454,7539,7639,7732,7829,7945,8067,8172,8305,8435,8577,8740,8868,8985,9109,9230,9321,9418,9538,9653,9751,9854,9962,10094,10235,10345,10444,10528,10622,10717,10829,10921,11007,11120,11200,11286,11387,11490,11587,11688,11776,11882,11981,12084,12203,12283,12387", "endColumns": "117,116,118,117,100,94,111,133,115,138,84,99,92,96,115,121,104,132,129,141,162,127,116,123,120,90,96,119,114,97,102,107,131,140,109,98,83,93,94,111,91,85,112,79,85,100,102,96,100,87,105,98,102,118,79,103,94", "endOffsets": "6398,6515,6634,6752,6853,6948,7060,7194,7310,7449,7534,7634,7727,7824,7940,8062,8167,8300,8430,8572,8735,8863,8980,9104,9225,9316,9413,9533,9648,9746,9849,9957,10089,10230,10340,10439,10523,10617,10712,10824,10916,11002,11115,11195,11281,11382,11485,11582,11683,11771,11877,11976,12079,12198,12278,12382,12477"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\752167a3e67da85263f648420d161268\\transformed\\ui-release\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,375,477,569,647,739,830,911,993,1079,1151,1229,1305,1380,1459,1527", "endColumns": "92,82,93,101,91,77,91,90,80,81,85,71,77,75,74,78,67,119", "endOffsets": "193,276,370,472,564,642,734,825,906,988,1074,1146,1224,1300,1375,1454,1522,1642"}, "to": {"startLines": "31,32,33,35,36,88,89,153,154,155,156,157,158,159,160,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1779,1872,1955,2123,2225,6115,6193,12986,13077,13158,13240,13326,13398,13476,13552,13728,13807,13875", "endColumns": "92,82,93,101,91,77,91,90,80,81,85,71,77,75,74,78,67,119", "endOffsets": "1867,1950,2044,2220,2312,6188,6280,13072,13153,13235,13321,13393,13471,13547,13622,13802,13870,13990"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\01b6e8b8fea8bb45b55852d4590f3437\\transformed\\foundation-release\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,88", "endOffsets": "136,225"}, "to": {"startLines": "165,166", "startColumns": "4,4", "startOffsets": "13995,14081", "endColumns": "85,88", "endOffsets": "14076,14165"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\31c73cb6dfb12faac49eb1bae4282a74\\transformed\\core-1.16.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "24,25,26,27,28,29,30,161", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1048,1146,1248,1349,1448,1553,1660,13627", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "1141,1243,1344,1443,1548,1655,1774,13723"}}]}]}