plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    // kotlin("kapt") // 暂时禁用，解决枚举编译问题
}

android {
    namespace = "com.example.iotandroidv20"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.example.iotandroidv20"
        minSdk = 24
        targetSdk = 35
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
            excludes += "META-INF/DEPENDENCIES"
            excludes += "META-INF/LICENSE"
            excludes += "META-INF/LICENSE.txt"
            excludes += "META-INF/NOTICE"
            excludes += "META-INF/NOTICE.txt"
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
        freeCompilerArgs += listOf(
            "-opt-in=androidx.compose.material3.ExperimentalMaterial3Api",
            "-Xopt-in=kotlin.RequiresOptIn"
        )
    }
    buildFeatures {
        compose = true
    }
    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.3"
    }
}

dependencies {

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.activity.compose)
    implementation(platform(libs.androidx.compose.bom))

    // 华为云API Gateway SDK（完整版本，用于签名）
    implementation(files("../javaApiDemo/src/main/resources/lib/java-sdk-core-3.1.0.jar"))
    implementation(files("../javaApiDemo/src/main/resources/lib/joda-time-2.10.jar"))

    // 使用Android兼容的HTTP客户端（用于实际网络请求）
    implementation("com.squareup.okhttp3:okhttp:4.12.0")
    implementation("com.squareup.okhttp3:logging-interceptor:4.12.0")

    // Apache HttpClient（华为云SDK签名需要，但我们不直接用于网络请求）
    implementation("org.apache.httpcomponents:httpclient:4.5.13") {
        exclude(group = "commons-logging", module = "commons-logging")
        exclude(group = "commons-codec", module = "commons-codec")
    }
    implementation("org.apache.httpcomponents:httpcore:4.4.15")

    // 统一的依赖版本（避免冲突）
    implementation("commons-codec:commons-codec:1.11")
    implementation("commons-logging:commons-logging:1.2")

    // 使用统一的Jackson版本（避免冲突）
    implementation("com.fasterxml.jackson.core:jackson-core:2.8.1")
    implementation("com.fasterxml.jackson.core:jackson-databind:2.8.1")
    implementation("com.fasterxml.jackson.core:jackson-annotations:2.8.1")

    // JSON处理
    implementation("com.google.code.gson:gson:2.10.1")
    implementation(libs.androidx.ui)
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.androidx.material3)

    // Material Icons扩展包
    implementation("androidx.compose.material:material-icons-extended:1.5.8")

    // Compose动画
    implementation("androidx.compose.animation:animation:1.5.8")

    // Compose Foundation
    implementation("androidx.compose.foundation:foundation:1.5.8")

    // Navigation Compose
    implementation("androidx.navigation:navigation-compose:2.7.6")

    // 华为云IoT SDK (使用通用MQTT客户端)
    // implementation("com.huaweicloud.sdk:huaweicloud-sdk-iot:3.1.62")
    // implementation("com.huaweicloud.sdk:huaweicloud-sdk-core:3.1.62")

    // 华为云IoT SDK
    implementation("com.huaweicloud.sdk:huaweicloud-sdk-iotda:3.1.153")
    implementation("com.huaweicloud.sdk:huaweicloud-sdk-core:3.1.153")

    // 华为云SDK依赖的HTTP客户端
    implementation("com.squareup.okhttp3:okhttp:4.12.0")

    // JSON处理
    implementation("com.fasterxml.jackson.core:jackson-core:2.15.2")
    implementation("com.fasterxml.jackson.core:jackson-databind:2.15.2")
    implementation("com.fasterxml.jackson.core:jackson-annotations:2.15.2")

    // 网络请求
    implementation("com.squareup.okhttp3:okhttp:4.12.0")
    implementation("com.squareup.retrofit2:retrofit:2.9.0")
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")

    // JSON解析
    implementation("com.google.code.gson:gson:2.10.1")

    // 协程支持
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")

    // ViewModel和LiveData
    implementation("androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0")
    implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.7.0")

    // 权限处理
    implementation("com.google.accompanist:accompanist-permissions:0.32.0")

    // 华为云OBS SDK - 使用HTTP API实现，不需要额外依赖
    // implementation("com.huaweicloud:esdk-obs-android:3.25.5")

    // ExoPlayer媒体播放器
    implementation("androidx.media3:media3-exoplayer:1.2.1")
    implementation("androidx.media3:media3-ui:1.2.1")
    implementation("androidx.media3:media3-common:1.2.1")
    implementation("androidx.media3:media3-session:1.2.1")

    // 图片加载
    implementation("io.coil-kt:coil-compose:2.5.0")

    // 文件选择器
    implementation("androidx.activity:activity-compose:1.8.2")

    // Room数据库 - 暂时禁用，解决编译问题
    // implementation("androidx.room:room-runtime:2.6.1")
    // implementation("androidx.room:room-ktx:2.6.1")
    // kapt("androidx.room:room-compiler:2.6.1")

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    androidTestImplementation(platform(libs.androidx.compose.bom))
    androidTestImplementation(libs.androidx.ui.test.junit4)
    debugImplementation(libs.androidx.ui.tooling)
    debugImplementation(libs.androidx.ui.test.manifest)
}