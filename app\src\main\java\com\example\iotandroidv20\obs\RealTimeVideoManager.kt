package com.example.iotandroidv20.obs

import android.content.Context
import com.example.iotandroidv20.utils.Logger
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*

/**
 * 实时视频管理器
 * 为家长提供一键即时查看孩子当前状况的功能
 */
class RealTimeVideoManager private constructor() {
    
    private val obsManager = ObsManager.getInstance()
    
    private val _liveStreamStatus = MutableStateFlow(LiveStreamStatus.DISCONNECTED)
    val liveStreamStatus: StateFlow<LiveStreamStatus> = _liveStreamStatus.asStateFlow()
    
    private val _currentStreamUrl = MutableStateFlow<String?>(null)
    val currentStreamUrl: StateFlow<String?> = _currentStreamUrl.asStateFlow()
    
    private val _connectionQuality = MutableStateFlow(ConnectionQuality.UNKNOWN)
    val connectionQuality: StateFlow<ConnectionQuality> = _connectionQuality.asStateFlow()
    
    private val _lastUpdateTime = MutableStateFlow(0L)
    val lastUpdateTime: StateFlow<Long> = _lastUpdateTime.asStateFlow()
    
    private var streamMonitoringJob: Job? = null
    private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    companion object {
        private const val TAG = "RealTimeVideoManager"
        private const val STREAM_CHECK_INTERVAL = 3000L // 3秒检查一次
        private const val CONNECTION_TIMEOUT = 10000L // 10秒连接超时
        
        @Volatile
        private var INSTANCE: RealTimeVideoManager? = null
        
        fun getInstance(): RealTimeVideoManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: RealTimeVideoManager().also { INSTANCE = it }
            }
        }
    }
    
    /**
     * 家长一键查看当前实时画面
     * 提供无延迟的即时监控体验
     */
    suspend fun startInstantLiveView(deviceId: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Logger.d("家长请求查看实时画面: $deviceId", tag = TAG)
                _liveStreamStatus.value = LiveStreamStatus.CONNECTING
                
                // 1. 获取设备当前最新的实时流URL
                val liveStreamUrl = getCurrentLiveStreamUrl(deviceId)
                
                if (liveStreamUrl != null) {
                    _currentStreamUrl.value = liveStreamUrl
                    _liveStreamStatus.value = LiveStreamStatus.CONNECTED
                    _lastUpdateTime.value = System.currentTimeMillis()
                    
                    // 2. 启动流质量监控
                    startStreamQualityMonitoring(deviceId)
                    
                    Logger.d("实时画面连接成功", tag = TAG)
                    true
                } else {
                    _liveStreamStatus.value = LiveStreamStatus.FAILED
                    Logger.e("无法获取实时流URL", tag = TAG)
                    false
                }
                
            } catch (e: Exception) {
                Logger.e("启动实时查看失败: ${e.message}", tag = TAG)
                _liveStreamStatus.value = LiveStreamStatus.FAILED
                false
            }
        }
    }
    
    /**
     * 获取设备当前的实时流URL
     * 优先获取最新的实时流，如果没有则获取最近的录制片段
     */
    private suspend fun getCurrentLiveStreamUrl(deviceId: String): String? {
        return try {
            // 1. 首先尝试获取实时推流URL
            val liveUrl = getLiveStreamUrl(deviceId)
            if (liveUrl != null) {
                Logger.d("获取到实时推流URL", tag = TAG)
                return liveUrl
            }
            
            // 2. 如果没有实时流，获取最新的录制片段
            val latestRecording = getLatestRecordingUrl(deviceId)
            if (latestRecording != null) {
                Logger.d("获取到最新录制片段URL", tag = TAG)
                return latestRecording
            }
            
            Logger.w("未找到可用的视频流", tag = TAG)
            null
            
        } catch (e: Exception) {
            Logger.e("获取视频流URL失败: ${e.message}", tag = TAG)
            null
        }
    }
    
    /**
     * 获取实时推流URL
     */
    private suspend fun getLiveStreamUrl(deviceId: String): String? {
        // 这里应该调用华为云VIS API获取实时推流URL
        // 暂时返回模拟URL
        return "https://live-stream.example.com/device/$deviceId/live.m3u8"
    }
    
    /**
     * 获取最新录制片段URL
     */
    private suspend fun getLatestRecordingUrl(deviceId: String): String? {
        return try {
            val today = java.text.SimpleDateFormat("yyyyMMdd", java.util.Locale.getDefault())
                .format(java.util.Date())
            
            val sessions = obsManager.getVideoSessions(deviceId, today)
            val latestSession = sessions.maxByOrNull { it.lastModified.time }
            
            latestSession?.streamUrl
        } catch (e: Exception) {
            Logger.e("获取最新录制失败: ${e.message}", tag = TAG)
            null
        }
    }
    
    /**
     * 启动流质量监控
     */
    private fun startStreamQualityMonitoring(deviceId: String) {
        streamMonitoringJob?.cancel()
        streamMonitoringJob = coroutineScope.launch {
            while (isActive && _liveStreamStatus.value == LiveStreamStatus.CONNECTED) {
                try {
                    // 检查流质量
                    val quality = checkStreamQuality()
                    _connectionQuality.value = quality
                    
                    // 更新最后更新时间
                    _lastUpdateTime.value = System.currentTimeMillis()
                    
                    // 如果质量太差，尝试重新连接
                    if (quality == ConnectionQuality.POOR) {
                        Logger.w("流质量较差，尝试重新连接", tag = TAG)
                        refreshStream(deviceId)
                    }
                    
                    delay(STREAM_CHECK_INTERVAL)
                    
                } catch (e: Exception) {
                    Logger.e("流质量监控异常: ${e.message}", tag = TAG)
                    _connectionQuality.value = ConnectionQuality.POOR
                    delay(STREAM_CHECK_INTERVAL)
                }
            }
        }
    }
    
    /**
     * 检查流质量
     */
    private suspend fun checkStreamQuality(): ConnectionQuality {
        return try {
            // 这里应该实现实际的流质量检测逻辑
            // 暂时返回模拟结果
            val random = (0..100).random()
            when {
                random > 80 -> ConnectionQuality.EXCELLENT
                random > 60 -> ConnectionQuality.GOOD
                random > 40 -> ConnectionQuality.FAIR
                else -> ConnectionQuality.POOR
            }
        } catch (e: Exception) {
            ConnectionQuality.POOR
        }
    }
    
    /**
     * 刷新视频流
     */
    private suspend fun refreshStream(deviceId: String) {
        try {
            Logger.d("刷新视频流", tag = TAG)
            val newUrl = getCurrentLiveStreamUrl(deviceId)
            if (newUrl != null && newUrl != _currentStreamUrl.value) {
                _currentStreamUrl.value = newUrl
                Logger.d("视频流已刷新", tag = TAG)
            }
        } catch (e: Exception) {
            Logger.e("刷新视频流失败: ${e.message}", tag = TAG)
        }
    }
    
    /**
     * 停止实时查看
     */
    fun stopLiveView() {
        Logger.d("停止实时查看", tag = TAG)
        streamMonitoringJob?.cancel()
        _liveStreamStatus.value = LiveStreamStatus.DISCONNECTED
        _currentStreamUrl.value = null
        _connectionQuality.value = ConnectionQuality.UNKNOWN
    }
    
    /**
     * 获取连接状态描述
     */
    fun getStatusDescription(): String {
        return when (_liveStreamStatus.value) {
            LiveStreamStatus.DISCONNECTED -> "未连接"
            LiveStreamStatus.CONNECTING -> "正在连接..."
            LiveStreamStatus.CONNECTED -> "已连接 - ${getQualityDescription()}"
            LiveStreamStatus.FAILED -> "连接失败"
        }
    }
    
    /**
     * 获取质量描述
     */
    private fun getQualityDescription(): String {
        return when (_connectionQuality.value) {
            ConnectionQuality.EXCELLENT -> "画质优秀"
            ConnectionQuality.GOOD -> "画质良好"
            ConnectionQuality.FAIR -> "画质一般"
            ConnectionQuality.POOR -> "画质较差"
            ConnectionQuality.UNKNOWN -> "检测中"
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        streamMonitoringJob?.cancel()
        coroutineScope.cancel()
    }
}

/**
 * 直播流状态
 */
enum class LiveStreamStatus {
    DISCONNECTED,   // 未连接
    CONNECTING,     // 连接中
    CONNECTED,      // 已连接
    FAILED          // 连接失败
}

/**
 * 连接质量
 */
enum class ConnectionQuality {
    UNKNOWN,        // 未知
    POOR,           // 较差
    FAIR,           // 一般
    GOOD,           // 良好
    EXCELLENT       // 优秀
}
