package com.example.iotandroidv20.obs

import android.content.Context
import android.content.SharedPreferences
import android.util.Log

/**
 * OBS配置管理类
 * 负责管理华为云OBS的配置信息，包括访问密钥、端点等
 */
class ObsConfig private constructor() {
    
    companion object {
        private const val TAG = "ObsConfig"
        private const val PREFS_NAME = "obs_config"
        private const val KEY_ACCESS_KEY = "access_key"
        private const val KEY_SECRET_KEY = "secret_key"
        private const val KEY_ENDPOINT = "endpoint"
        private const val KEY_BUCKET_NAME = "bucket_name"
        private const val KEY_REGION = "region"
        
        // 默认配置
        private const val DEFAULT_ENDPOINT = "https://obs.cn-north-4.myhuaweicloud.com"
        private const val DEFAULT_BUCKET_NAME = "learning-supervision-obs"
        private const val DEFAULT_REGION = "cn-north-4"
        
        @Volatile
        private var INSTANCE: ObsConfig? = null
        
        fun getInstance(): ObsConfig {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ObsConfig().also { INSTANCE = it }
            }
        }
    }
    
    private var sharedPreferences: SharedPreferences? = null
    
    /**
     * 初始化配置
     */
    fun initialize(context: Context) {
        sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        Log.d(TAG, "ObsConfig initialized")
    }
    
    /**
     * 设置访问密钥
     */
    fun setAccessKey(accessKey: String) {
        sharedPreferences?.edit()?.putString(KEY_ACCESS_KEY, accessKey)?.apply()
        Log.d(TAG, "Access key updated")
    }
    
    /**
     * 获取访问密钥
     */
    fun getAccessKey(): String {
        return sharedPreferences?.getString(KEY_ACCESS_KEY, "") ?: ""
    }
    
    /**
     * 设置密钥
     */
    fun setSecretKey(secretKey: String) {
        sharedPreferences?.edit()?.putString(KEY_SECRET_KEY, secretKey)?.apply()
        Log.d(TAG, "Secret key updated")
    }
    
    /**
     * 获取密钥
     */
    fun getSecretKey(): String {
        return sharedPreferences?.getString(KEY_SECRET_KEY, "") ?: ""
    }
    
    /**
     * 设置端点
     */
    fun setEndpoint(endpoint: String) {
        sharedPreferences?.edit()?.putString(KEY_ENDPOINT, endpoint)?.apply()
        Log.d(TAG, "Endpoint updated: $endpoint")
    }
    
    /**
     * 获取端点
     */
    fun getEndpoint(): String {
        return sharedPreferences?.getString(KEY_ENDPOINT, DEFAULT_ENDPOINT) ?: DEFAULT_ENDPOINT
    }
    
    /**
     * 设置存储桶名称
     */
    fun setBucketName(bucketName: String) {
        sharedPreferences?.edit()?.putString(KEY_BUCKET_NAME, bucketName)?.apply()
        Log.d(TAG, "Bucket name updated: $bucketName")
    }
    
    /**
     * 获取存储桶名称
     */
    fun getBucketName(): String {
        return sharedPreferences?.getString(KEY_BUCKET_NAME, DEFAULT_BUCKET_NAME) ?: DEFAULT_BUCKET_NAME
    }
    
    /**
     * 设置区域
     */
    fun setRegion(region: String) {
        sharedPreferences?.edit()?.putString(KEY_REGION, region)?.apply()
        Log.d(TAG, "Region updated: $region")
    }
    
    /**
     * 获取区域
     */
    fun getRegion(): String {
        return sharedPreferences?.getString(KEY_REGION, DEFAULT_REGION) ?: DEFAULT_REGION
    }
    
    /**
     * 检查配置是否完整
     */
    fun isConfigured(): Boolean {
        val accessKey = getAccessKey()
        val secretKey = getSecretKey()
        val endpoint = getEndpoint()
        val bucketName = getBucketName()
        
        val isValid = accessKey.isNotEmpty() && 
                     secretKey.isNotEmpty() && 
                     endpoint.isNotEmpty() && 
                     bucketName.isNotEmpty()
        
        Log.d(TAG, "Configuration check: $isValid")
        return isValid
    }
    
    /**
     * 清除所有配置
     */
    fun clearConfig() {
        sharedPreferences?.edit()?.clear()?.apply()
        Log.d(TAG, "Configuration cleared")
    }
    
    /**
     * 获取配置摘要（用于调试）
     */
    fun getConfigSummary(): String {
        return """
            OBS Configuration:
            - Endpoint: ${getEndpoint()}
            - Bucket: ${getBucketName()}
            - Region: ${getRegion()}
            - Access Key: ${if (getAccessKey().isNotEmpty()) "***${getAccessKey().takeLast(4)}" else "Not set"}
            - Secret Key: ${if (getSecretKey().isNotEmpty()) "***" else "Not set"}
            - Configured: ${isConfigured()}
        """.trimIndent()
    }
    
    /**
     * 设置默认配置（用于开发测试）
     */
    fun setDefaultConfig() {
        setEndpoint(DEFAULT_ENDPOINT)
        setBucketName(DEFAULT_BUCKET_NAME)
        setRegion(DEFAULT_REGION)
        Log.d(TAG, "Default configuration set")
    }
    
    /**
     * 验证访问密钥格式
     */
    fun validateAccessKey(accessKey: String): Boolean {
        // 华为云访问密钥通常是20个字符的字母数字组合
        return accessKey.length == 20 && accessKey.all { it.isLetterOrDigit() }
    }
    
    /**
     * 验证密钥格式
     */
    fun validateSecretKey(secretKey: String): Boolean {
        // 华为云密钥通常是40个字符的字母数字组合
        return secretKey.length == 40 && secretKey.all { it.isLetterOrDigit() || it == '/' || it == '+' }
    }
    
    /**
     * 验证端点格式
     */
    fun validateEndpoint(endpoint: String): Boolean {
        return endpoint.startsWith("https://") && endpoint.contains("obs") && endpoint.contains("myhuaweicloud.com")
    }
    
    /**
     * 验证存储桶名称格式
     */
    fun validateBucketName(bucketName: String): Boolean {
        // 存储桶名称规则：3-63个字符，只能包含小写字母、数字和连字符
        val regex = Regex("^[a-z0-9][a-z0-9-]{1,61}[a-z0-9]$")
        return bucketName.matches(regex)
    }
    
    /**
     * 获取完整的OBS URL
     */
    fun getObsUrl(objectKey: String): String {
        val bucketName = getBucketName()
        val endpoint = getEndpoint()
        return "$endpoint/$bucketName/$objectKey"
    }
    
    /**
     * 获取视频文件的完整路径
     */
    fun getVideoPath(deviceId: String, date: String, fileName: String): String {
        return "video/$deviceId/processed/$date/$fileName"
    }
    
    /**
     * 获取音频文件的完整路径
     */
    fun getAudioPath(deviceId: String, date: String, fileName: String): String {
        return "audio/$deviceId/processed/$date/$fileName"
    }
    
    /**
     * 获取缩略图文件的完整路径
     */
    fun getThumbnailPath(deviceId: String, date: String, fileName: String): String {
        return "video/$deviceId/processed/$date/thumbnails/$fileName"
    }
    
    /**
     * 获取波形图文件的完整路径
     */
    fun getWaveformPath(deviceId: String, date: String, fileName: String): String {
        return "audio/$deviceId/processed/$date/waveforms/$fileName"
    }
}
