# 华为云IAM权限配置指南

## 🎯 配置目标

为学习监督项目配置华为云IAM用户和权限策略，确保设备端和应用端能够安全访问所需的云服务。

## 📋 权限需求分析

### **设备端权限需求**
- OBS对象上传权限 (PutObject)
- IoT设备数据上报权限
- 预签名URL请求权限

### **应用端权限需求**
- OBS对象下载权限 (GetObject)
- OBS桶列表权限 (ListBucket)
- 媒体文件访问权限

### **管理端权限需求**
- 完整的OBS管理权限
- IoT设备管理权限
- MPS媒体处理权限

## 🔧 实施步骤

### **步骤1：创建IAM用户组**

```bash
# 使用华为云CLI创建用户组
hcloud iam create-group \
  --group-name "LearningSupervisionDeviceGroup" \
  --description "学习监督设备端用户组"

hcloud iam create-group \
  --group-name "LearningSupervisionAppGroup" \
  --description "学习监督应用端用户组"

hcloud iam create-group \
  --group-name "LearningSupervisionAdminGroup" \
  --description "学习监督管理员用户组"
```

### **步骤2：创建自定义权限策略**

#### **设备端权限策略**
```json
{
  "Version": "1.1",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "obs:object:PutObject",
        "obs:object:PutObjectAcl"
      ],
      "Resource": [
        "obs:*:*:bucket:learning-supervision-obs",
        "obs:*:*:object:learning-supervision-obs/video/*/raw/*",
        "obs:*:*:object:learning-supervision-obs/audio/*/raw/*"
      ]
    },
    {
      "Effect": "Allow",
      "Action": [
        "iotda:device:reportData",
        "iotda:device:reportEvent"
      ],
      "Resource": [
        "iotda:*:*:device:*"
      ]
    }
  ]
}
```

#### **应用端权限策略**
```json
{
  "Version": "1.1",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "obs:object:GetObject",
        "obs:object:GetObjectAcl",
        "obs:bucket:ListBucket",
        "obs:bucket:GetBucketLocation"
      ],
      "Resource": [
        "obs:*:*:bucket:learning-supervision-obs",
        "obs:*:*:object:learning-supervision-obs/video/*/processed/*",
        "obs:*:*:object:learning-supervision-obs/audio/*/processed/*"
      ]
    }
  ]
}
```

#### **管理员权限策略**
```json
{
  "Version": "1.1",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "obs:*:*",
        "iotda:*:*",
        "mps:*:*"
      ],
      "Resource": ["*"]
    }
  ]
}
```

### **步骤3：创建权限策略**

```bash
# 创建设备端权限策略
hcloud iam create-policy \
  --policy-name "LearningSupervisionDevicePolicy" \
  --policy-document file://device-policy.json \
  --description "学习监督设备端权限策略"

# 创建应用端权限策略
hcloud iam create-policy \
  --policy-name "LearningSupervisionAppPolicy" \
  --policy-document file://app-policy.json \
  --description "学习监督应用端权限策略"

# 创建管理员权限策略
hcloud iam create-policy \
  --policy-name "LearningSupervisionAdminPolicy" \
  --policy-document file://admin-policy.json \
  --description "学习监督管理员权限策略"
```

### **步骤4：将策略附加到用户组**

```bash
# 附加策略到设备端用户组
hcloud iam attach-group-policy \
  --group-name "LearningSupervisionDeviceGroup" \
  --policy-name "LearningSupervisionDevicePolicy"

# 附加策略到应用端用户组
hcloud iam attach-group-policy \
  --group-name "LearningSupervisionAppGroup" \
  --policy-name "LearningSupervisionAppPolicy"

# 附加策略到管理员用户组
hcloud iam attach-group-policy \
  --group-name "LearningSupervisionAdminGroup" \
  --policy-name "LearningSupervisionAdminPolicy"
```

### **步骤5：创建IAM用户**

```bash
# 创建设备端用户
hcloud iam create-user \
  --user-name "learning-supervision-device-user" \
  --description "学习监督设备端服务用户"

# 创建应用端用户
hcloud iam create-user \
  --user-name "learning-supervision-app-user" \
  --description "学习监督应用端服务用户"

# 创建管理员用户
hcloud iam create-user \
  --user-name "learning-supervision-admin-user" \
  --description "学习监督管理员用户"
```

### **步骤6：将用户添加到用户组**

```bash
# 将用户添加到对应用户组
hcloud iam add-user-to-group \
  --group-name "LearningSupervisionDeviceGroup" \
  --user-name "learning-supervision-device-user"

hcloud iam add-user-to-group \
  --group-name "LearningSupervisionAppGroup" \
  --user-name "learning-supervision-app-user"

hcloud iam add-user-to-group \
  --group-name "LearningSupervisionAdminGroup" \
  --user-name "learning-supervision-admin-user"
```

### **步骤7：创建访问密钥**

```bash
# 为设备端用户创建访问密钥
hcloud iam create-access-key \
  --user-name "learning-supervision-device-user"

# 为应用端用户创建访问密钥
hcloud iam create-access-key \
  --user-name "learning-supervision-app-user"

# 为管理员用户创建访问密钥
hcloud iam create-access-key \
  --user-name "learning-supervision-admin-user"
```

## 🔐 安全配置

### **访问密钥管理**
```bash
# 设置密钥轮换策略（建议每90天轮换一次）
# 创建密钥轮换脚本
cat > rotate_access_keys.sh << 'EOF'
#!/bin/bash

USER_NAME=$1
if [ -z "$USER_NAME" ]; then
    echo "Usage: $0 <user-name>"
    exit 1
fi

echo "Rotating access keys for user: $USER_NAME"

# 创建新的访问密钥
NEW_KEY=$(hcloud iam create-access-key --user-name "$USER_NAME" --output json)
NEW_ACCESS_KEY=$(echo "$NEW_KEY" | jq -r '.access_key')
NEW_SECRET_KEY=$(echo "$NEW_KEY" | jq -r '.secret_key')

echo "New Access Key: $NEW_ACCESS_KEY"
echo "New Secret Key: $NEW_SECRET_KEY"

# 等待新密钥生效
sleep 30

# 列出旧的访问密钥
OLD_KEYS=$(hcloud iam list-access-keys --user-name "$USER_NAME" --output json)
echo "$OLD_KEYS" | jq -r '.access_keys[] | select(.access_key != "'$NEW_ACCESS_KEY'") | .access_key' | while read OLD_ACCESS_KEY; do
    echo "Deleting old access key: $OLD_ACCESS_KEY"
    hcloud iam delete-access-key --user-name "$USER_NAME" --access-key "$OLD_ACCESS_KEY"
done

echo "Access key rotation completed for user: $USER_NAME"
EOF

chmod +x rotate_access_keys.sh
```

### **IP白名单配置**
```json
{
  "Version": "1.1",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": ["*"],
      "Resource": ["*"],
      "Condition": {
        "IpAddress": {
          "aws:SourceIp": [
            "***********/24",
            "10.0.0.0/8"
          ]
        }
      }
    }
  ]
}
```

## 📊 权限验证

### **验证脚本**
```bash
#!/bin/bash

echo "=== 华为云IAM权限配置验证 ==="

# 验证设备端权限
echo "1. 验证设备端权限..."
export HUAWEICLOUD_ACCESS_KEY="device-access-key"
export HUAWEICLOUD_SECRET_KEY="device-secret-key"

# 测试OBS上传权限
echo "测试OBS上传权限..."
echo "test content" > test_upload.txt
hcloud obs cp test_upload.txt obs://learning-supervision-obs/test/device_test.txt
if [ $? -eq 0 ]; then
    echo "✅ 设备端OBS上传权限正常"
else
    echo "❌ 设备端OBS上传权限异常"
fi

# 验证应用端权限
echo "2. 验证应用端权限..."
export HUAWEICLOUD_ACCESS_KEY="app-access-key"
export HUAWEICLOUD_SECRET_KEY="app-secret-key"

# 测试OBS下载权限
echo "测试OBS下载权限..."
hcloud obs cp obs://learning-supervision-obs/test/device_test.txt test_download.txt
if [ $? -eq 0 ]; then
    echo "✅ 应用端OBS下载权限正常"
else
    echo "❌ 应用端OBS下载权限异常"
fi

# 清理测试文件
rm -f test_upload.txt test_download.txt
hcloud obs rm obs://learning-supervision-obs/test/device_test.txt

echo "=== 权限验证完成 ==="
```

## 📝 配置记录

### **访问密钥记录表**
| 用户类型 | 用户名 | Access Key | Secret Key | 创建时间 | 状态 |
|---------|--------|------------|------------|----------|------|
| 设备端 | learning-supervision-device-user | AKIA... | *** | 2025-01-01 | 活跃 |
| 应用端 | learning-supervision-app-user | AKIA... | *** | 2025-01-01 | 活跃 |
| 管理员 | learning-supervision-admin-user | AKIA... | *** | 2025-01-01 | 活跃 |

### **权限策略版本记录**
| 策略名称 | 版本 | 更新时间 | 更新内容 |
|---------|------|----------|----------|
| LearningSupervisionDevicePolicy | v1.0 | 2025-01-01 | 初始版本 |
| LearningSupervisionAppPolicy | v1.0 | 2025-01-01 | 初始版本 |
| LearningSupervisionAdminPolicy | v1.0 | 2025-01-01 | 初始版本 |

## ✅ 完成检查清单

- [ ] 创建IAM用户组
- [ ] 创建自定义权限策略
- [ ] 附加策略到用户组
- [ ] 创建IAM用户
- [ ] 将用户添加到用户组
- [ ] 创建访问密钥
- [ ] 配置安全策略
- [ ] 验证权限配置
- [ ] 记录配置信息

---

**注意**: 请妥善保管访问密钥，定期轮换，并遵循最小权限原则。
