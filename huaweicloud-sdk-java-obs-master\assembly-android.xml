<?xml version="1.0" encoding="UTF-8"?>
<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.0.0 http://maven.apache.org/xsd/assembly-2.0.0.xsd">
	<id>assembly_zip</id>
	<formats>
		<format>zip</format>
	</formats>
	<!-- 无需wrapp -->
	<includeBaseDirectory>false</includeBaseDirectory>

	<dependencySets>
		<dependencySet>
			<useProjectArtifact>true</useProjectArtifact>
			<outputDirectory>/libs</outputDirectory>
			<unpack>false</unpack>
			<scope>runtime</scope>
			<excludes>
				<!--<exclude>com.jamesmurty.utils:java-xmlbuilder</exclude> -->
			</excludes>
		</dependencySet>
	</dependencySets>


	<files>
		<file>
			<source>${project.build.directory}/${project.artifactId}-${project.version}-javadoc.jar</source>
			<outputDirectory>/</outputDirectory>
			<destName>esdk-obs-android-${project.version}-javadoc.jar</destName>
		</file>
		<!-- <file> <source>${project.basedir}/app/build/intermediates/bundles/release/classes.jar</source> 
			<outputDirectory>/libs</outputDirectory> <destName>${project.artifactId}-${project.version}.jar</destName> 
			</file> -->
		<file>
			<source>${project.basedir}/LICENSE</source>
			<outputDirectory>/</outputDirectory>
		</file>
		<file>
			<source>${project.basedir}/Notice.MD</source>
			<outputDirectory>/</outputDirectory>
		</file>
		<file>
			<source>${project.basedir}/Help on License</source>
			<outputDirectory>/</outputDirectory>
		</file>
	</files>
	<fileSets>
		<fileSet>
			<directory>${project.basedir}/app/src/test/java/samples_android</directory>
			<outputDirectory>/samples_android</outputDirectory>
		</fileSet>
		<fileSet>
			<directory>${project.basedir}/app/src/third_part</directory>
			<outputDirectory>/third_part</outputDirectory>
		</fileSet>
		<fileSet>
			<directory>${project.build.directory}/doc</directory>
			<outputDirectory>/doc</outputDirectory>
		</fileSet>
		<fileSet>
			<directory>${project.basedir}/app</directory>
			<outputDirectory>/source</outputDirectory>
			<excludes>
				<exclude>src/test/**</exclude>
				<exclude>build/**</exclude>
				<exclude>.gitignore</exclude>
				<exclude>build.gradle</exclude>
				<exclude>proguard-rules.pro</exclude>
			</excludes>
		</fileSet>
	</fileSets>
</assembly>