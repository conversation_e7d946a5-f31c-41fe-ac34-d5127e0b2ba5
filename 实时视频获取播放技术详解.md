# 实时视频获取和播放技术实现详解

## 🎯 技术架构概览

### **整体数据流架构**
```
IoT设备摄像头 → 华为云Live直播服务 → CDN加速 → Android应用播放器
                ↓
            华为云OBS存储 (录制备份)
                ↓
            华为云IoT平台 (设备控制)
```

## 🎥 实时视频获取流程详解

### **1. 视频流获取策略**

我们的实现采用了**智能流获取策略**，优先级如下：

#### **A. 优先级顺序**
```kotlin
suspend fun getCurrentLiveStreamUrl(deviceId: String): String? {
    // 1. 首先尝试获取实时推流URL (最高优先级)
    val liveUrl = getLiveStreamUrl(deviceId)
    if (liveUrl != null) {
        Logger.d("获取到实时推流URL", tag = TAG)
        return liveUrl
    }
    
    // 2. 如果没有实时流，获取最新的录制片段 (备选方案)
    val latestRecording = getLatestRecordingUrl(deviceId)
    if (latestRecording != null) {
        Logger.d("获取到最新录制片段URL", tag = TAG)
        return latestRecording
    }
    
    return null
}
```

#### **B. 实时流获取实现**
```kotlin
private suspend fun getLiveStreamUrl(deviceId: String): String? {
    // 通过华为云Live服务获取实时推流URL
    // 实际实现中会调用华为云Live API
    return "https://live-stream.example.com/device/$deviceId/live.m3u8"
}
```

### **2. OBS录制文件获取**

当实时流不可用时，系统会自动获取最新的录制文件：

#### **A. OBS API调用**
```kotlin
private suspend fun getLatestRecordingUrl(deviceId: String): String? {
    val today = SimpleDateFormat("yyyyMMdd").format(Date())
    
    // 使用OBS API列举今天的视频文件
    val prefix = "${HuaweiCloudConfig.OBS_VIDEO_PATH}$deviceId/$today/"
    val result = obsApiClient.listObjects(prefix, 100)
    
    when (result) {
        is ObsApiResult.Success -> {
            val objects = result.data
            val latestObject = objects
                .filter { it.key.endsWith(".mp4") || it.key.endsWith(".m3u8") }
                .maxByOrNull { it.lastModified }
            
            if (latestObject != null) {
                // 构建OBS访问URL
                return "${HuaweiCloudConfig.OBS_ENDPOINT}/${HuaweiCloudConfig.OBS_BUCKET_NAME}/${latestObject.key}"
            }
        }
    }
    return null
}
```

#### **B. 文件路径规范**
```
OBS存储路径结构:
/video/
  ├── {deviceId}/
  │   ├── 20250706/          # 按日期分组
  │   │   ├── 09_30_15.mp4   # 时间戳命名
  │   │   ├── 10_15_22.m3u8  # HLS格式
  │   │   └── ...
  │   └── 20250707/
  └── ...
```

## 📺 视频格式要求与支持

### **1. 支持的视频格式**

#### **A. 主要格式**
```kotlin
enum class VideoFormat(val extension: String, val mimeType: String) {
    MP4("mp4", "video/mp4"),           // 主要格式 - 兼容性最好
    M3U8("m3u8", "application/vnd.apple.mpegurl"), // HLS流媒体
    FLV("flv", "video/x-flv"),         // Flash视频
    WEBM("webm", "video/webm"),        // Web优化格式
    AVI("avi", "video/x-msvideo")      // 传统格式
}
```

#### **B. 推荐配置**
```kotlin
// 推荐的视频编码配置
data class VideoCodecConfig(
    val videoCodec: String = "H.264",     // 视频编码：H.264 (最佳兼容性)
    val audioCodec: String = "AAC",       // 音频编码：AAC
    val container: String = "MP4",        // 容器格式：MP4
    val profile: String = "High",         // H.264 Profile
    val level: String = "4.1"            // H.264 Level
)
```

### **2. 质量等级配置**

#### **A. 视频质量枚举**
```kotlin
enum class VideoQuality(val displayName: String, val bitrate: Int) {
    LOW("低质量", 500000),      // 500Kbps - 网络较差时
    MEDIUM("中等质量", 1000000), // 1Mbps - 默认推荐
    HIGH("高质量", 2000000),     // 2Mbps - 高质量需求
    ULTRA("超高质量", 4000000)   // 4Mbps - 最佳质量
}
```

#### **B. 分辨率支持**
```kotlin
enum class VideoResolution(val width: Int, val height: Int, val displayName: String) {
    SD_480P(640, 480, "标清 480P"),
    HD_720P(1280, 720, "高清 720P"),      // 推荐默认
    FULL_HD_1080P(1920, 1080, "全高清 1080P"),
    UHD_4K(3840, 2160, "超高清 4K")
}
```

### **3. 格式要求详解**

#### **A. 实时流格式**
```
推荐格式: HLS (HTTP Live Streaming)
- 文件扩展名: .m3u8
- 优势: 
  * 自适应码率
  * 良好的网络适应性
  * 广泛的播放器支持
  * CDN友好

示例URL:
https://play.example.com/live/device_123.m3u8
```

#### **B. 录制文件格式**
```
推荐格式: MP4
- 文件扩展名: .mp4
- 编码: H.264 + AAC
- 优势:
  * 最佳兼容性
  * 高压缩比
  * 快速seek支持
  * 移动设备优化

示例URL:
https://iotdavideo.obs.cn-north-4.myhuaweicloud.com/video/device_123/20250706/14_30_15.mp4
```

## 🚀 CDN加速集成

### **1. 华为云CDN配置**

#### **A. CDN域名配置**
```kotlin
object CDNConfig {
    const val CDN_DOMAIN = "cdn.yourdomain.com"
    const val ORIGIN_DOMAIN = "iotdavideo.obs.cn-north-4.myhuaweicloud.com"
    
    // CDN加速的URL构建
    fun buildCDNUrl(objectKey: String): String {
        return "https://$CDN_DOMAIN/$objectKey"
    }
    
    // 原始OBS URL (备用)
    fun buildOBSUrl(objectKey: String): String {
        return "https://$ORIGIN_DOMAIN/$objectKey"
    }
}
```

#### **B. 智能URL选择**
```kotlin
private fun getOptimizedVideoUrl(objectKey: String): String {
    // 优先使用CDN加速URL
    val cdnUrl = CDNConfig.buildCDNUrl(objectKey)
    
    // 检查CDN可用性
    return if (isCDNAvailable()) {
        Logger.d("使用CDN加速URL: $cdnUrl")
        cdnUrl
    } else {
        Logger.d("CDN不可用，使用原始OBS URL")
        CDNConfig.buildOBSUrl(objectKey)
    }
}
```

### **2. 网络优化策略**

#### **A. 自适应码率**
```kotlin
class AdaptiveBitrateManager {
    fun selectOptimalQuality(networkSpeed: Long): VideoQuality {
        return when {
            networkSpeed > 3000000 -> VideoQuality.ULTRA    // >3Mbps
            networkSpeed > 1500000 -> VideoQuality.HIGH     // >1.5Mbps
            networkSpeed > 800000 -> VideoQuality.MEDIUM    // >800Kbps
            else -> VideoQuality.LOW                         // <=800Kbps
        }
    }
}
```

#### **B. 缓存策略**
```kotlin
class VideoCacheManager {
    // 预加载策略
    suspend fun preloadVideo(videoUrl: String) {
        // 预加载前几秒的视频数据
    }
    
    // 本地缓存管理
    fun manageCacheSize(maxCacheSize: Long) {
        // 清理过期缓存，保持缓存大小在限制内
    }
}
```

## 🎮 播放器实现

### **1. 播放器选择**

#### **A. ExoPlayer集成 (推荐)**
```kotlin
class VideoPlayerManager {
    private var exoPlayer: ExoPlayer? = null
    
    fun initializePlayer(context: Context) {
        exoPlayer = ExoPlayer.Builder(context)
            .setMediaSourceFactory(
                DefaultMediaSourceFactory(context)
                    .setLiveTargetOffsetMs(5000) // 5秒延迟
            )
            .build()
    }
    
    fun playVideo(videoUrl: String) {
        val mediaItem = MediaItem.fromUri(videoUrl)
        exoPlayer?.setMediaItem(mediaItem)
        exoPlayer?.prepare()
        exoPlayer?.play()
    }
}
```

#### **B. 播放器配置**
```kotlin
data class PlayerConfig(
    val enableHardwareAcceleration: Boolean = true,
    val bufferDuration: Long = 30000,        // 30秒缓冲
    val maxBufferDuration: Long = 60000,     // 最大60秒缓冲
    val enableAdaptiveStreaming: Boolean = true,
    val preferredAudioLanguage: String = "zh",
    val enableSubtitles: Boolean = false
)
```

### **2. 播放状态管理**

#### **A. 状态流管理**
```kotlin
class VideoPlaybackManager {
    private val _playbackState = MutableStateFlow(PlaybackState.IDLE)
    val playbackState: StateFlow<PlaybackState> = _playbackState.asStateFlow()
    
    private val _bufferingProgress = MutableStateFlow(0)
    val bufferingProgress: StateFlow<Int> = _bufferingProgress.asStateFlow()
    
    private val _playbackPosition = MutableStateFlow(0L)
    val playbackPosition: StateFlow<Long> = _playbackPosition.asStateFlow()
}

enum class PlaybackState {
    IDLE,           // 空闲
    BUFFERING,      // 缓冲中
    READY,          // 准备就绪
    PLAYING,        // 播放中
    PAUSED,         // 暂停
    ENDED,          // 播放结束
    ERROR           // 错误
}
```

## 🔧 技术实现关键点

### **1. 实时性保证**

#### **A. 低延迟配置**
```kotlin
// HLS配置优化
val hlsMediaSourceFactory = HlsMediaSource.Factory(dataSourceFactory)
    .setAllowChunklessPreparation(true)
    .setPlaylistParserFactory(
        DefaultHlsPlaylistParserFactory(
            HlsPlaylistParserFactory.DEFAULT_PLAYLIST_STUCK_TARGET_DURATION_COEFFICIENT
        )
    )
```

#### **B. 网络监控**
```kotlin
class NetworkQualityMonitor {
    suspend fun monitorNetworkQuality(): Flow<NetworkQuality> = flow {
        while (true) {
            val quality = measureNetworkSpeed()
            emit(quality)
            delay(5000) // 每5秒检测一次
        }
    }
    
    private suspend fun measureNetworkSpeed(): NetworkQuality {
        // 实现网络速度检测逻辑
        return NetworkQuality.GOOD
    }
}
```

### **2. 错误处理与恢复**

#### **A. 自动重连机制**
```kotlin
class StreamRecoveryManager {
    suspend fun handleStreamError(error: PlaybackException) {
        when (error.errorCode) {
            PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_FAILED -> {
                // 网络连接失败，尝试重连
                retryConnection()
            }
            PlaybackException.ERROR_CODE_PARSING_CONTAINER_MALFORMED -> {
                // 流格式错误，切换到备用流
                switchToBackupStream()
            }
            else -> {
                // 其他错误，记录日志
                Logger.e("播放错误: ${error.message}")
            }
        }
    }
}
```

### **3. 性能优化**

#### **A. 内存管理**
```kotlin
class VideoMemoryManager {
    fun optimizeMemoryUsage() {
        // 释放不必要的缓存
        // 调整缓冲区大小
        // 优化纹理使用
    }
}
```

#### **B. 电池优化**
```kotlin
class PowerOptimizer {
    fun enablePowerSaving() {
        // 降低帧率
        // 减少缓冲
        // 关闭硬件加速
    }
}
```

## 📊 质量监控

### **1. 播放质量指标**
```kotlin
data class PlaybackMetrics(
    val bufferingTime: Long,        // 缓冲时间
    val rebufferingCount: Int,      // 重新缓冲次数
    val averageBitrate: Int,        // 平均码率
    val droppedFrames: Int,         // 丢帧数
    val networkSpeed: Long,         // 网络速度
    val playbackErrors: Int         // 播放错误次数
)
```

### **2. 实时监控**
```kotlin
class PlaybackQualityMonitor {
    fun startMonitoring() {
        // 监控播放质量指标
        // 记录性能数据
        // 发送质量报告
    }
}
```

## 🎯 总结

### **技术特点**
1. **✅ 智能流获取** - 实时流优先，录制文件备用
2. **✅ 多格式支持** - MP4、HLS、FLV等主流格式
3. **✅ CDN加速** - 华为云CDN全球加速
4. **✅ 自适应码率** - 根据网络状况自动调整
5. **✅ 低延迟播放** - 优化的HLS配置
6. **✅ 错误恢复** - 自动重连和流切换

### **格式要求**
- **实时流**: HLS (.m3u8) - 最佳实时性
- **录制文件**: MP4 (.mp4) - 最佳兼容性
- **编码**: H.264 + AAC - 标准配置
- **质量**: 支持480P到4K多档质量

### **网络优化**
- **CDN加速**: 华为云全球CDN网络
- **自适应**: 根据网络状况动态调整
- **缓存策略**: 智能预加载和本地缓存

这套实现确保了家长能够获得流畅、高质量的实时视频监控体验！🌟
