/ Header Record For PersistentHashMapValueStorageJ I$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\MainActivity.kt` _$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\analysis\IntelligentAnalysisEngine.ktO N$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\auth\TokenManager.ktR Q$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\camera\CameraManager.ktU T$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\camera\StreamConnection.ktV U$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\config\HuaweiCloudConfig.ktY X$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\eeg\AdvancedFatigueDetector.ktY X$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\eeg\AdvancedFocusCalculator.ktR Q$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\eeg\DeviceEEGManager.ktU T$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\eeg\EEGAssessmentEngine.ktL K$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\eeg\EEGManager.ktT S$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\eeg\EEGSignalProcessor.ktV U$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.ktV U$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\eeg\FocusCalculationDemo.kt] \$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\fusion\RealTimeDataFusionEngine.kt[ Z$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\health\HealthAssessmentSystem.kta `$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\intelligence\LearningGuidanceEngine.ktY X$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\iot\HuaweiCloudOkHttpClient.ktR Q$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\iot\HuaweiIoTManager.ktO N$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\iot\MqttIoTClient.ktQ P$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\iot\TokenIoTManager.ktT S$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\live\HuaweiLiveManager.ktO N$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\model\AppSettings.ktK J$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\model\EEGData.ktY X$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\model\LearningGuidanceModel.kt\ [$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\model\LearningSupervisionModel.ktW V$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\model\MultiModalDataModel.ktO N$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\model\PostureData.ktP O$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\model\UserSettings.ktK J$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\model\VISData.ktM L$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\model\VideoData.ktM L$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\model\VoiceData.ktV U$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\navigation\AppNavigation.ktT S$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\obs\HuaweiObsApiClient.ktM L$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\obs\MediaModels.ktR Q$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\obs\ObsApiTestHelper.kt_ ^$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\obs\ParentVoiceInteractionManager.ktV U$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\obs\RealTimeVideoManager.ktW V$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\obs\RealTimeVideoVerifier.kt\ [$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\obs\VideoMonitoringDiagnostics.ktW V$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\player\AudioPlayerManager.ktW V$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\player\VideoPlayerManager.kt] \$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\recording\VideoRecordingManager.ktX W$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\repository\MediaRepository.kta `$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\repository\MultiModalDataRepository.kt^ ]$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\repository\PostureDataRepository.ktZ Y$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\settings\UserSettingsManager.ktd c$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\supervision\LearningSupervisionManager.ktX W$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\test\VoiceModuleTestHelper.ktb a$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\AdvancedFatigueDisplay.kt` _$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\AdvancedFocusDisplay.kth g$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\ComprehensiveHealthDashboard.kt] \$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\EEGBrainwaveChart.ktY X$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\EEGFocusGauge.kt] \$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\EEGMonitoringCard.kt_ ^$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\EEGSpectrumAnalyzer.kt^ ]$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\EEGWaveformDisplay.ktY X$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\ErrorHandling.ktf e$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\ParentVoiceInteractionCard.kta `$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\PermissionRequestCard.kt^ ]$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\PostureDisplayCard.kt_ ^$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\PostureHistoryChart.kt\ [$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\PostureScoreCard.ktb a$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\PostureVisualization3D.kti h$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\RealTimeVideoVerificationCard.kt` _$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\SimplePermissionCard.ktU T$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\StatsCard.kt^ ]$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\VISVideoPlayerCard.kt` _$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\VideoPlayerComponent.kt\ [$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\VideoSurfaceView.kt` _$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\VoiceInteractionCard.kt_ ^$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\VoiceModuleTestCard.ktZ Y$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\media\MediaItemComponents.kt_ ^$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\media\MediaThumbnailComponents.ktY X$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\player\AudioPlayerScreen.kt\ [$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\player\AudioPlayerViewModel.ktY X$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\player\VideoPlayerScreen.kt\ [$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\player\VideoPlayerViewModel.kt^ ]$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\screen\AdvancedSettingsScreen.kta `$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\screen\LearningSupervisionScreen.ktR Q$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\screen\MainScreen.ktV U$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\screen\SettingsScreen.ktL K$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\theme\Color.ktL K$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\theme\Theme.ktK J$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\theme\Type.ktJ I$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\utils\Logger.ktT S$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\utils\PermissionHelper.ktZ Y$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\utils\PostureScoreCalculator.ktU T$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\viewmodel\MainViewModel.ktR Q$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\vis\HuaweiVISManager.ktW V$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\vis\VoiceRecordingManager.kt\ [$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\vision\PostureDetectionManager.ktV U$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\voice\DeviceVoiceManager.kt] \$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\voice\SmartVoiceReminderManager.ktZ Y$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\voice\VoiceCommandController.ktP O$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\voice\VoiceManager.kt` _$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\ui\components\SimplePermissionCard.ktT S$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\utils\PermissionHelper.ktV U$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\config\HuaweiCloudConfig.ktT S$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\obs\HuaweiObsApiClient.kt_ ^$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\obs\ParentVoiceInteractionManager.ktT S$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\obs\HuaweiObsApiClient.ktT S$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\obs\HuaweiObsApiClient.ktT S$PROJECT_DIR$\app\src\main\java\com\example\iotandroidv20\obs\HuaweiObsApiClient.kt