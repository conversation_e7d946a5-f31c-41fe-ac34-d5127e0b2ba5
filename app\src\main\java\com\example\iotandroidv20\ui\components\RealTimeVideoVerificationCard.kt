package com.example.iotandroidv20.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.iotandroidv20.obs.VerificationStatus
import com.example.iotandroidv20.obs.VerificationStep

/**
 * 实时视频监控验证卡片
 * 显示验证过程和结果
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RealTimeVideoVerificationCard(
    verificationStatus: VerificationStatus,
    verificationProgress: Float,
    verificationMessage: String,
    verificationResults: List<VerificationStep>,
    onStartVerification: () -> Unit,
    onStartRealTimeVideo: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "实时视频监控验证",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Icon(
                    imageVector = when (verificationStatus) {
                        VerificationStatus.SUCCESS -> Icons.Default.CheckCircle
                        VerificationStatus.FAILED -> Icons.Default.Error
                        VerificationStatus.RUNNING -> Icons.Default.Refresh
                        VerificationStatus.PARTIAL_SUCCESS -> Icons.Default.Warning
                        else -> Icons.Default.PlayArrow
                    },
                    contentDescription = null,
                    tint = when (verificationStatus) {
                        VerificationStatus.SUCCESS -> Color.Green
                        VerificationStatus.FAILED -> Color.Red
                        VerificationStatus.RUNNING -> Color.Blue
                        VerificationStatus.PARTIAL_SUCCESS -> Color(0xFFFF9800)
                        else -> MaterialTheme.colorScheme.onSurface
                    }
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 状态信息
            Text(
                text = verificationMessage,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 进度条
            if (verificationStatus == VerificationStatus.RUNNING) {
                LinearProgressIndicator(
                    progress = verificationProgress,
                    modifier = Modifier.fillMaxWidth()
                )
                Spacer(modifier = Modifier.height(8.dp))
            }
            
            // 验证结果列表
            if (verificationResults.isNotEmpty()) {
                Text(
                    text = "验证结果:",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                LazyColumn(
                    modifier = Modifier.heightIn(max = 200.dp)
                ) {
                    items(verificationResults) { step ->
                        VerificationStepItem(step = step)
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp))
            }
            
            // 操作按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = onStartVerification,
                    enabled = verificationStatus != VerificationStatus.RUNNING,
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(
                        imageVector = Icons.Default.PlayArrow,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("开始验证")
                }
                
                if (verificationStatus == VerificationStatus.SUCCESS) {
                    Button(
                        onClick = onStartRealTimeVideo,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color.Green
                        )
                    ) {
                        Icon(
                            imageVector = Icons.Default.Videocam,
                            contentDescription = null,
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("启动监控")
                    }
                }
            }
        }
    }
}

/**
 * 验证步骤项目
 */
@Composable
private fun VerificationStepItem(
    step: VerificationStep,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (step.success) {
                Color.Green.copy(alpha = 0.1f)
            } else {
                Color.Red.copy(alpha = 0.1f)
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = if (step.success) Icons.Default.CheckCircle else Icons.Default.Error,
                contentDescription = null,
                tint = if (step.success) Color.Green else Color.Red,
                modifier = Modifier.size(20.dp)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = step.name,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
                
                Text(
                    text = step.message,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                if (step.details.isNotEmpty()) {
                    Text(
                        text = step.details,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                    )
                }
            }
        }
    }
}

/**
 * 实时视频状态显示卡片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RealTimeVideoStatusCard(
    statusDetails: String,
    onRefreshStatus: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "实时视频状态",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium
                )
                
                IconButton(onClick = onRefreshStatus) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "刷新状态"
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = statusDetails,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}
