package com.example.iotandroidv20.utils

import android.content.Context
import android.content.pm.PackageManager
import androidx.core.content.ContextCompat

/**
 * 权限检查助手
 * 提供简单的权限检查功能
 */
object PermissionHelper {
    
    private const val TAG = "PermissionHelper"
    
    /**
     * 检查录音权限
     */
    fun hasRecordAudioPermission(context: Context): <PERSON><PERSON>an {
        val granted = ContextCompat.checkSelfPermission(
            context, 
            android.Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED
        
        Logger.d("🔐 录音权限检查: $granted", tag = TAG)
        return granted
    }
    
    /**
     * 检查存储权限
     */
    fun hasStoragePermission(context: Context): Bo<PERSON>an {
        val granted = ContextCompat.checkSelfPermission(
            context, 
            android.Manifest.permission.WRITE_EXTERNAL_STORAGE
        ) == PackageManager.PERMISSION_GRANTED
        
        Logger.d("🔐 存储权限检查: $granted", tag = TAG)
        return granted
    }
    
    /**
     * 检查所有语音功能需要的权限
     */
    fun hasAllVoicePermissions(context: Context): <PERSON><PERSON><PERSON> {
        val recordPermission = hasRecordAudioPermission(context)
        val storagePermission = hasStoragePermission(context)
        val allGranted = recordPermission && storagePermission
        
        Logger.d("🔐 语音功能权限检查 - 录音: $recordPermission, 存储: $storagePermission, 全部: $allGranted", tag = TAG)
        return allGranted
    }
    
    /**
     * 获取权限状态报告
     */
    fun getPermissionReport(context: Context): String {
        val recordPermission = hasRecordAudioPermission(context)
        val storagePermission = hasStoragePermission(context)
        
        return buildString {
            appendLine("=== 权限状态报告 ===")
            appendLine("录音权限 (RECORD_AUDIO): ${if (recordPermission) "✅ 已授予" else "❌ 未授予"}")
            appendLine("存储权限 (WRITE_EXTERNAL_STORAGE): ${if (storagePermission) "✅ 已授予" else "❌ 未授予"}")
            appendLine("语音功能状态: ${if (recordPermission && storagePermission) "✅ 就绪" else "❌ 需要权限"}")
            
            if (!recordPermission || !storagePermission) {
                appendLine()
                appendLine("📋 解决方案:")
                appendLine("1. 打开应用设置")
                appendLine("2. 找到权限管理")
                appendLine("3. 开启麦克风和存储权限")
                appendLine("4. 返回应用重新测试")
            }
        }
    }
    
    /**
     * 需要的权限列表
     */
    val requiredPermissions = arrayOf(
        android.Manifest.permission.RECORD_AUDIO,
        android.Manifest.permission.WRITE_EXTERNAL_STORAGE
    )
    
    /**
     * 权限名称映射
     */
    fun getPermissionDisplayName(permission: String): String {
        return when (permission) {
            android.Manifest.permission.RECORD_AUDIO -> "录音权限"
            android.Manifest.permission.WRITE_EXTERNAL_STORAGE -> "存储权限"
            else -> permission
        }
    }
    
    /**
     * 权限描述映射
     */
    fun getPermissionDescription(permission: String): String {
        return when (permission) {
            android.Manifest.permission.RECORD_AUDIO -> "用于录制家长语音消息"
            android.Manifest.permission.WRITE_EXTERNAL_STORAGE -> "用于保存录音文件到设备存储"
            else -> "应用功能所需权限"
        }
    }
}
