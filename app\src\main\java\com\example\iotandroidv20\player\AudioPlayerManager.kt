package com.example.iotandroidv20.player

import android.content.Context
import android.net.Uri
import android.util.Log
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.datasource.DefaultDataSourceFactory
import androidx.media3.datasource.DefaultHttpDataSource
import com.example.iotandroidv20.obs.AudioSession
import com.example.iotandroidv20.obs.PlaybackState
import com.example.iotandroidv20.obs.PlayerInfo
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 音频播放器管理器
 * 基于ExoPlayer实现音频播放功能，支持本地文件和网络流播放
 */
class AudioPlayerManager(private val context: Context) {
    
    companion object {
        private const val TAG = "AudioPlayerManager"
    }
    
    private var exoPlayer: ExoPlayer? = null
    private var currentAudioSession: AudioSession? = null
    
    // 播放器状态
    private val _playerInfo = MutableStateFlow(
        PlayerInfo(
            state = PlaybackState.IDLE,
            currentPosition = 0L,
            duration = 0L,
            bufferedPosition = 0L
        )
    )
    val playerInfo: StateFlow<PlayerInfo> = _playerInfo.asStateFlow()
    
    // 播放列表
    private val _playlist = MutableStateFlow<List<AudioSession>>(emptyList())
    val playlist: StateFlow<List<AudioSession>> = _playlist.asStateFlow()
    
    private val _currentIndex = MutableStateFlow(0)
    val currentIndex: StateFlow<Int> = _currentIndex.asStateFlow()
    
    // 播放模式
    private val _repeatMode = MutableStateFlow(RepeatMode.OFF)
    val repeatMode: StateFlow<RepeatMode> = _repeatMode.asStateFlow()
    
    private val _shuffleMode = MutableStateFlow(false)
    val shuffleMode: StateFlow<Boolean> = _shuffleMode.asStateFlow()
    
    // 播放器事件监听器
    private val playerEventListeners = mutableListOf<AudioPlayerEventListener>()
    
    /**
     * 播放模式枚举
     */
    enum class RepeatMode {
        OFF,        // 不重复
        ONE,        // 单曲循环
        ALL         // 列表循环
    }
    
    /**
     * 音频播放器事件监听器接口
     */
    interface AudioPlayerEventListener {
        fun onPlayerStateChanged(isPlaying: Boolean, playbackState: Int) {}
        fun onPlayerError(error: Exception) {}
        fun onPositionChanged(position: Long, duration: Long) {}
        fun onTrackChanged(audioSession: AudioSession) {}
        fun onPlaylistChanged(playlist: List<AudioSession>) {}
    }
    
    /**
     * 初始化播放器
     */
    fun initializePlayer() {
        if (exoPlayer == null) {
            Log.d(TAG, "Initializing ExoPlayer for audio")
            
            // 创建数据源工厂
            val dataSourceFactory = DefaultDataSourceFactory(
                context,
                DefaultHttpDataSource.Factory()
                    .setUserAgent("LearningSupervisionApp/1.0")
                    .setConnectTimeoutMs(30000)
                    .setReadTimeoutMs(30000)
            )
            
            // 创建媒体源工厂
            val mediaSourceFactory = DefaultMediaSourceFactory(dataSourceFactory)
            
            // 创建ExoPlayer实例
            exoPlayer = ExoPlayer.Builder(context)
                .setMediaSourceFactory(mediaSourceFactory)
                .build()
                .apply {
                    // 添加播放器监听器
                    addListener(object : Player.Listener {
                        override fun onPlaybackStateChanged(playbackState: Int) {
                            updatePlayerState(playbackState)
                            notifyPlayerStateChanged(isPlaying, playbackState)
                        }
                        
                        override fun onIsPlayingChanged(isPlaying: Boolean) {
                            updatePlayerState(playbackState)
                            notifyPlayerStateChanged(isPlaying, playbackState)
                        }
                        
                        override fun onPlayerError(error: androidx.media3.common.PlaybackException) {
                            Log.e(TAG, "Audio player error: ${error.message}", error)
                            updatePlayerState(Player.STATE_IDLE)
                            notifyPlayerError(Exception(error.message, error))
                        }
                        
                        override fun onMediaItemTransition(mediaItem: androidx.media3.common.MediaItem?, reason: Int) {
                            // 处理曲目切换
                            mediaItem?.let { item ->
                                val sessionId = item.mediaId
                                val session = _playlist.value.find { it.sessionId == sessionId }
                                session?.let { audioSession ->
                                    currentAudioSession = audioSession
                                    val newIndex = _playlist.value.indexOf(audioSession)
                                    _currentIndex.value = newIndex
                                    notifyTrackChanged(audioSession)
                                }
                            }
                        }
                        
                        override fun onPositionDiscontinuity(
                            oldPosition: androidx.media3.common.Player.PositionInfo,
                            newPosition: androidx.media3.common.Player.PositionInfo,
                            reason: Int
                        ) {
                            updatePlayerInfo()
                        }
                    })
                }
            
            Log.d(TAG, "Audio ExoPlayer initialized successfully")
        }
    }
    
    /**
     * 播放单个音频会话
     */
    fun playAudioSession(audioSession: AudioSession) {
        try {
            Log.d(TAG, "Playing audio session: ${audioSession.sessionId}")
            
            initializePlayer()
            currentAudioSession = audioSession
            
            // 设置单曲播放列表
            setPlaylist(listOf(audioSession), 0)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to play audio session", e)
            notifyPlayerError(e)
        }
    }
    
    /**
     * 设置播放列表
     */
    fun setPlaylist(audioSessions: List<AudioSession>, startIndex: Int = 0) {
        try {
            Log.d(TAG, "Setting playlist with ${audioSessions.size} items, starting at index $startIndex")
            
            initializePlayer()
            
            // 更新播放列表状态
            _playlist.value = audioSessions
            _currentIndex.value = startIndex.coerceIn(0, audioSessions.size - 1)
            
            // 创建媒体项目列表
            val mediaItems = audioSessions.map { session ->
                val uri = if (session.isDownloaded() && !session.localFilePath.isNullOrEmpty()) {
                    // 播放本地文件
                    Uri.fromFile(java.io.File(session.localFilePath))
                } else {
                    // 播放网络流
                    Uri.parse(session.streamUrl)
                }
                
                MediaItem.Builder()
                    .setUri(uri)
                    .setMediaId(session.sessionId)
                    .build()
            }
            
            // 设置媒体项目列表并准备播放
            exoPlayer?.apply {
                setMediaItems(mediaItems, _currentIndex.value, 0L)
                prepare()
                playWhenReady = true
            }
            
            // 更新当前音频会话
            if (audioSessions.isNotEmpty()) {
                currentAudioSession = audioSessions[_currentIndex.value]
            }
            
            // 通知播放列表变化
            notifyPlaylistChanged(audioSessions)
            
            Log.d(TAG, "Playlist set successfully")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to set playlist", e)
            notifyPlayerError(e)
        }
    }
    
    /**
     * 播放/暂停
     */
    fun togglePlayPause() {
        exoPlayer?.let { player ->
            if (player.isPlaying) {
                pause()
            } else {
                play()
            }
        }
    }
    
    /**
     * 播放
     */
    fun play() {
        exoPlayer?.playWhenReady = true
        Log.d(TAG, "Audio play requested")
    }
    
    /**
     * 暂停
     */
    fun pause() {
        exoPlayer?.playWhenReady = false
        Log.d(TAG, "Audio pause requested")
    }
    
    /**
     * 停止
     */
    fun stop() {
        exoPlayer?.stop()
        currentAudioSession = null
        Log.d(TAG, "Audio stop requested")
    }
    
    /**
     * 下一首
     */
    fun skipToNext() {
        exoPlayer?.let { player ->
            if (player.hasNextMediaItem()) {
                player.seekToNextMediaItem()
                Log.d(TAG, "Skip to next track")
            } else if (_repeatMode.value == RepeatMode.ALL) {
                // 列表循环，跳到第一首
                player.seekTo(0, 0L)
                Log.d(TAG, "Repeat all: skip to first track")
            }
        }
    }
    
    /**
     * 上一首
     */
    fun skipToPrevious() {
        exoPlayer?.let { player ->
            if (player.hasPreviousMediaItem()) {
                player.seekToPreviousMediaItem()
                Log.d(TAG, "Skip to previous track")
            } else if (_repeatMode.value == RepeatMode.ALL) {
                // 列表循环，跳到最后一首
                player.seekTo(player.mediaItemCount - 1, 0L)
                Log.d(TAG, "Repeat all: skip to last track")
            }
        }
    }
    
    /**
     * 跳转到指定位置
     */
    fun seekTo(positionMs: Long) {
        exoPlayer?.seekTo(positionMs)
        Log.d(TAG, "Audio seek to: ${positionMs}ms")
    }
    
    /**
     * 跳转到指定曲目
     */
    fun seekToTrack(index: Int) {
        val playlist = _playlist.value
        if (index in 0 until playlist.size) {
            exoPlayer?.seekTo(index, 0L)
            _currentIndex.value = index
            currentAudioSession = playlist[index]
            Log.d(TAG, "Seek to track: $index")
        }
    }
    
    /**
     * 设置重复模式
     */
    fun setRepeatMode(mode: RepeatMode) {
        _repeatMode.value = mode
        
        exoPlayer?.repeatMode = when (mode) {
            RepeatMode.OFF -> Player.REPEAT_MODE_OFF
            RepeatMode.ONE -> Player.REPEAT_MODE_ONE
            RepeatMode.ALL -> Player.REPEAT_MODE_ALL
        }
        
        Log.d(TAG, "Repeat mode set to: $mode")
    }
    
    /**
     * 设置随机播放
     */
    fun setShuffleMode(enabled: Boolean) {
        _shuffleMode.value = enabled
        exoPlayer?.shuffleModeEnabled = enabled
        Log.d(TAG, "Shuffle mode set to: $enabled")
    }
    
    /**
     * 设置播放速度
     */
    fun setPlaybackSpeed(speed: Float) {
        exoPlayer?.setPlaybackSpeed(speed)
        updatePlayerInfo()
        Log.d(TAG, "Audio playback speed set to: ${speed}x")
    }
    
    /**
     * 设置音量
     */
    fun setVolume(volume: Float) {
        exoPlayer?.volume = volume.coerceIn(0f, 1f)
        updatePlayerInfo()
        Log.d(TAG, "Audio volume set to: $volume")
    }
    
    /**
     * 获取ExoPlayer实例
     */
    fun getExoPlayer(): ExoPlayer? = exoPlayer
    
    /**
     * 获取当前播放的音频会话
     */
    fun getCurrentAudioSession(): AudioSession? = currentAudioSession
    
    /**
     * 添加播放器事件监听器
     */
    fun addPlayerListener(listener: AudioPlayerEventListener) {
        playerEventListeners.add(listener)
    }
    
    /**
     * 移除播放器事件监听器
     */
    fun removePlayerListener(listener: AudioPlayerEventListener) {
        playerEventListeners.remove(listener)
    }
    
    /**
     * 释放播放器资源
     */
    fun releasePlayer() {
        try {
            Log.d(TAG, "Releasing audio ExoPlayer")
            exoPlayer?.release()
            exoPlayer = null
            currentAudioSession = null
            playerEventListeners.clear()
            
            // 重置状态
            _playerInfo.value = PlayerInfo(
                state = PlaybackState.IDLE,
                currentPosition = 0L,
                duration = 0L,
                bufferedPosition = 0L
            )
            _playlist.value = emptyList()
            _currentIndex.value = 0
            
            Log.d(TAG, "Audio ExoPlayer released successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to release audio ExoPlayer", e)
        }
    }
    
    /**
     * 更新播放器状态
     */
    private fun updatePlayerState(exoPlayerState: Int) {
        val playbackState = when (exoPlayerState) {
            Player.STATE_IDLE -> PlaybackState.IDLE
            Player.STATE_BUFFERING -> PlaybackState.PREPARING
            Player.STATE_READY -> if (exoPlayer?.isPlaying == true) PlaybackState.PLAYING else PlaybackState.PAUSED
            Player.STATE_ENDED -> PlaybackState.ENDED
            else -> PlaybackState.IDLE
        }
        
        updatePlayerInfo(playbackState)
    }
    
    /**
     * 更新播放器信息
     */
    private fun updatePlayerInfo(state: PlaybackState? = null) {
        exoPlayer?.let { player ->
            val currentState = state ?: _playerInfo.value.state
            val currentPosition = player.currentPosition
            val duration = player.duration.takeIf { it != androidx.media3.common.C.TIME_UNSET } ?: 0L
            val bufferedPosition = player.bufferedPosition
            val playbackSpeed = player.playbackParameters.speed
            val volume = player.volume
            
            _playerInfo.value = PlayerInfo(
                state = currentState,
                currentPosition = currentPosition,
                duration = duration,
                bufferedPosition = bufferedPosition,
                playbackSpeed = playbackSpeed,
                volume = volume
            )
            
            // 通知位置变化
            notifyPositionChanged(currentPosition, duration)
        }
    }
    
    /**
     * 通知播放器状态变化
     */
    private fun notifyPlayerStateChanged(isPlaying: Boolean, playbackState: Int) {
        playerEventListeners.forEach { listener ->
            try {
                listener.onPlayerStateChanged(isPlaying, playbackState)
            } catch (e: Exception) {
                Log.e(TAG, "Error in audio player state listener", e)
            }
        }
    }
    
    /**
     * 通知播放器错误
     */
    private fun notifyPlayerError(error: Exception) {
        playerEventListeners.forEach { listener ->
            try {
                listener.onPlayerError(error)
            } catch (e: Exception) {
                Log.e(TAG, "Error in audio player error listener", e)
            }
        }
    }
    
    /**
     * 通知位置变化
     */
    private fun notifyPositionChanged(position: Long, duration: Long) {
        playerEventListeners.forEach { listener ->
            try {
                listener.onPositionChanged(position, duration)
            } catch (e: Exception) {
                Log.e(TAG, "Error in audio position change listener", e)
            }
        }
    }
    
    /**
     * 通知曲目变化
     */
    private fun notifyTrackChanged(audioSession: AudioSession) {
        playerEventListeners.forEach { listener ->
            try {
                listener.onTrackChanged(audioSession)
            } catch (e: Exception) {
                Log.e(TAG, "Error in audio track change listener", e)
            }
        }
    }
    
    /**
     * 通知播放列表变化
     */
    private fun notifyPlaylistChanged(playlist: List<AudioSession>) {
        playerEventListeners.forEach { listener ->
            try {
                listener.onPlaylistChanged(playlist)
            } catch (e: Exception) {
                Log.e(TAG, "Error in audio playlist change listener", e)
            }
        }
    }
}
