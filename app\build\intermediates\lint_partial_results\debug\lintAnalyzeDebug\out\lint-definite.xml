<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.0" type="incidents">

    <incident
        id="MissingClass"
        severity="error"
        message="Class referenced in the manifest, `org.eclipse.paho.android.service.MqttService`, was not found in the project or the libraries">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="40"
            column="32"
            startOffset="1662"
            endLine="40"
            endColumn="76"
            endOffset="1706"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt"
            line="233"
            column="24"
            startOffset="7179"
            endLine="233"
            endColumn="59"
            endOffset="7214"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt"
            line="274"
            column="24"
            startOffset="8225"
            endLine="274"
            endColumn="67"
            endOffset="8268"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt"
            line="520"
            column="39"
            startOffset="15156"
            endLine="520"
            endColumn="87"
            endOffset="15204"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt"
            line="521"
            column="39"
            startOffset="15244"
            endLine="521"
            endColumn="83"
            endOffset="15288"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt"
            line="522"
            column="39"
            startOffset="15328"
            endLine="522"
            endColumn="97"
            endOffset="15386"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/AdvancedFocusDisplay.kt"
            line="206"
            column="24"
            startOffset="6140"
            endLine="206"
            endColumn="57"
            endOffset="6173"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/AdvancedFocusDisplay.kt"
            line="400"
            column="37"
            startOffset="11698"
            endLine="400"
            endColumn="83"
            endOffset="11744"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/AdvancedFocusDisplay.kt"
            line="401"
            column="37"
            startOffset="11782"
            endLine="401"
            endColumn="79"
            endOffset="11824"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/player/AudioPlayerScreen.kt"
            line="506"
            column="9"
            startOffset="18187"
            endLine="506"
            endColumn="65"
            endOffset="18243"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/player/AudioPlayerScreen.kt"
            line="508"
            column="9"
            startOffset="18265"
            endLine="508"
            endColumn="53"
            endOffset="18309"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/ComprehensiveHealthDashboard.kt"
            line="416"
            column="32"
            startOffset="11428"
            endLine="416"
            endColumn="68"
            endOffset="11464"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/EEGData.kt"
            line="482"
            column="24"
            startOffset="12595"
            endLine="482"
            endColumn="60"
            endOffset="12631"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/EEGData.kt"
            line="484"
            column="22"
            startOffset="12700"
            endLine="484"
            endColumn="63"
            endOffset="12741"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/EEGData.kt"
            line="485"
            column="31"
            startOffset="12773"
            endLine="485"
            endColumn="68"
            endOffset="12810"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/EEGData.kt"
            line="486"
            column="26"
            startOffset="12837"
            endLine="486"
            endColumn="78"
            endOffset="12889"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/EEGData.kt"
            line="487"
            column="27"
            startOffset="12917"
            endLine="487"
            endColumn="78"
            endOffset="12968"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/EEGData.kt"
            line="488"
            column="24"
            startOffset="12993"
            endLine="488"
            endColumn="77"
            endOffset="13046"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/EEGData.kt"
            line="489"
            column="22"
            startOffset="13069"
            endLine="489"
            endColumn="67"
            endOffset="13114"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/EEGData.kt"
            line="490"
            column="23"
            startOffset="13138"
            endLine="490"
            endColumn="65"
            endOffset="13180"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/EEGData.kt"
            line="491"
            column="23"
            startOffset="13204"
            endLine="491"
            endColumn="64"
            endOffset="13245"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/EEGData.kt"
            line="656"
            column="23"
            startOffset="17290"
            endLine="656"
            endColumn="61"
            endOffset="17328"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/EEGData.kt"
            line="658"
            column="24"
            startOffset="17400"
            endLine="658"
            endColumn="69"
            endOffset="17445"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/EEGData.kt"
            line="659"
            column="22"
            startOffset="17468"
            endLine="659"
            endColumn="63"
            endOffset="17509"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/EEGData.kt"
            line="660"
            column="31"
            startOffset="17541"
            endLine="660"
            endColumn="68"
            endOffset="17578"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/EEGData.kt"
            line="661"
            column="27"
            startOffset="17606"
            endLine="661"
            endColumn="73"
            endOffset="17652"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/EEGData.kt"
            line="662"
            column="27"
            startOffset="17680"
            endLine="662"
            endColumn="73"
            endOffset="17726"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/EEGData.kt"
            line="663"
            column="23"
            startOffset="17750"
            endLine="663"
            endColumn="67"
            endOffset="17794"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/EEGData.kt"
            line="664"
            column="23"
            startOffset="17818"
            endLine="664"
            endColumn="71"
            endOffset="17866"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/EEGData.kt"
            line="666"
            column="23"
            startOffset="17942"
            endLine="666"
            endColumn="65"
            endOffset="17984"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/EEGData.kt"
            line="667"
            column="23"
            startOffset="18008"
            endLine="667"
            endColumn="69"
            endOffset="18054"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/EEGData.kt"
            line="668"
            column="23"
            startOffset="18078"
            endLine="668"
            endColumn="60"
            endOffset="18115"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/EEGSpectrumAnalyzer.kt"
            line="370"
            column="13"
            startOffset="11720"
            endLine="370"
            endColumn="41"
            endOffset="11748"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/EEGSpectrumAnalyzer.kt"
            line="551"
            column="45"
            startOffset="18147"
            endLine="551"
            endColumn="103"
            endOffset="18205"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/EEGSpectrumAnalyzer.kt"
            line="552"
            column="41"
            startOffset="18251"
            endLine="552"
            endColumn="92"
            endOffset="18302"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/EEGSpectrumAnalyzer.kt"
            line="553"
            column="41"
            startOffset="18344"
            endLine="553"
            endColumn="97"
            endOffset="18400"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/EEGWaveformDisplay.kt"
            line="513"
            column="37"
            startOffset="14752"
            endLine="513"
            endColumn="85"
            endOffset="14800"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/EEGWaveformDisplay.kt"
            line="514"
            column="33"
            startOffset="14838"
            endLine="514"
            endColumn="74"
            endOffset="14879"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/EEGWaveformDisplay.kt"
            line="515"
            column="33"
            startOffset="14913"
            endLine="515"
            endColumn="79"
            endOffset="14959"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt"
            line="183"
            column="43"
            startOffset="7727"
            endLine="183"
            endColumn="90"
            endOffset="7774"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt"
            line="184"
            column="43"
            startOffset="7820"
            endLine="184"
            endColumn="90"
            endOffset="7867"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt"
            line="185"
            column="43"
            startOffset="7913"
            endLine="185"
            endColumn="90"
            endOffset="7960"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt"
            line="186"
            column="43"
            startOffset="8006"
            endLine="186"
            endColumn="89"
            endOffset="8052"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt"
            line="187"
            column="43"
            startOffset="8098"
            endLine="187"
            endColumn="90"
            endOffset="8145"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt"
            line="188"
            column="42"
            startOffset="8190"
            endLine="188"
            endColumn="97"
            endOffset="8245"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt"
            line="189"
            column="41"
            startOffset="8291"
            endLine="189"
            endColumn="94"
            endOffset="8344"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt"
            line="193"
            column="42"
            startOffset="8486"
            endLine="193"
            endColumn="102"
            endOffset="8546"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt"
            line="195"
            column="41"
            startOffset="8686"
            endLine="195"
            endColumn="109"
            endOffset="8754"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt"
            line="196"
            column="41"
            startOffset="8799"
            endLine="196"
            endColumn="105"
            endOffset="8863"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt"
            line="201"
            column="42"
            startOffset="9104"
            endLine="201"
            endColumn="100"
            endOffset="9162"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt"
            line="203"
            column="41"
            startOffset="9300"
            endLine="203"
            endColumn="107"
            endOffset="9366"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt"
            line="204"
            column="42"
            startOffset="9412"
            endLine="204"
            endColumn="111"
            endOffset="9481"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt"
            line="205"
            column="42"
            startOffset="9527"
            endLine="205"
            endColumn="102"
            endOffset="9587"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt"
            line="209"
            column="42"
            startOffset="9732"
            endLine="209"
            endColumn="101"
            endOffset="9791"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt"
            line="211"
            column="41"
            startOffset="9930"
            endLine="211"
            endColumn="108"
            endOffset="9997"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt"
            line="212"
            column="42"
            startOffset="10043"
            endLine="212"
            endColumn="108"
            endOffset="10109"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt"
            line="217"
            column="42"
            startOffset="10355"
            endLine="217"
            endColumn="84"
            endOffset="10397"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt"
            line="223"
            column="42"
            startOffset="10787"
            endLine="223"
            endColumn="72"
            endOffset="10817"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt"
            line="237"
            column="43"
            startOffset="11447"
            endLine="237"
            endColumn="80"
            endOffset="11484"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt"
            line="238"
            column="43"
            startOffset="11530"
            endLine="238"
            endColumn="78"
            endOffset="11565"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt"
            line="239"
            column="44"
            startOffset="11612"
            endLine="239"
            endColumn="80"
            endOffset="11648"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt"
            line="240"
            column="36"
            startOffset="11687"
            endLine="240"
            endColumn="88"
            endOffset="11739"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt"
            line="241"
            column="36"
            startOffset="11778"
            endLine="241"
            endColumn="89"
            endOffset="11831"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FocusCalculationDemo.kt"
            line="154"
            column="43"
            startOffset="6339"
            endLine="154"
            endColumn="90"
            endOffset="6386"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FocusCalculationDemo.kt"
            line="155"
            column="43"
            startOffset="6432"
            endLine="155"
            endColumn="90"
            endOffset="6479"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FocusCalculationDemo.kt"
            line="156"
            column="43"
            startOffset="6525"
            endLine="156"
            endColumn="90"
            endOffset="6572"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FocusCalculationDemo.kt"
            line="157"
            column="43"
            startOffset="6618"
            endLine="157"
            endColumn="89"
            endOffset="6664"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FocusCalculationDemo.kt"
            line="158"
            column="43"
            startOffset="6710"
            endLine="158"
            endColumn="90"
            endOffset="6757"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FocusCalculationDemo.kt"
            line="159"
            column="42"
            startOffset="6802"
            endLine="159"
            endColumn="97"
            endOffset="6857"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FocusCalculationDemo.kt"
            line="160"
            column="41"
            startOffset="6903"
            endLine="160"
            endColumn="94"
            endOffset="6956"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FocusCalculationDemo.kt"
            line="164"
            column="43"
            startOffset="7091"
            endLine="164"
            endColumn="95"
            endOffset="7143"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FocusCalculationDemo.kt"
            line="166"
            column="41"
            startOffset="7276"
            endLine="166"
            endColumn="99"
            endOffset="7334"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FocusCalculationDemo.kt"
            line="167"
            column="50"
            startOffset="7388"
            endLine="167"
            endColumn="106"
            endOffset="7444"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FocusCalculationDemo.kt"
            line="168"
            column="45"
            startOffset="7492"
            endLine="168"
            endColumn="114"
            endOffset="7561"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FocusCalculationDemo.kt"
            line="169"
            column="44"
            startOffset="7609"
            endLine="169"
            endColumn="105"
            endOffset="7670"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FocusCalculationDemo.kt"
            line="173"
            column="43"
            startOffset="7807"
            endLine="173"
            endColumn="94"
            endOffset="7858"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FocusCalculationDemo.kt"
            line="175"
            column="41"
            startOffset="7990"
            endLine="175"
            endColumn="98"
            endOffset="8047"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FocusCalculationDemo.kt"
            line="176"
            column="50"
            startOffset="8101"
            endLine="176"
            endColumn="105"
            endOffset="8156"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FocusCalculationDemo.kt"
            line="177"
            column="45"
            startOffset="8204"
            endLine="177"
            endColumn="113"
            endOffset="8272"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FocusCalculationDemo.kt"
            line="178"
            column="44"
            startOffset="8320"
            endLine="178"
            endColumn="104"
            endOffset="8380"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FocusCalculationDemo.kt"
            line="183"
            column="43"
            startOffset="8599"
            endLine="183"
            endColumn="75"
            endOffset="8631"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FocusCalculationDemo.kt"
            line="196"
            column="39"
            startOffset="9163"
            endLine="196"
            endColumn="75"
            endOffset="9199"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FocusCalculationDemo.kt"
            line="197"
            column="40"
            startOffset="9242"
            endLine="197"
            endColumn="75"
            endOffset="9277"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FocusCalculationDemo.kt"
            line="198"
            column="36"
            startOffset="9316"
            endLine="198"
            endColumn="87"
            endOffset="9367"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/iot/HuaweiCloudOkHttpClient.kt"
            line="717"
            column="28"
            startOffset="27073"
            endLine="717"
            endColumn="57"
            endOffset="27102"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/iot/HuaweiCloudOkHttpClient.kt"
            line="718"
            column="28"
            startOffset="27141"
            endLine="718"
            endColumn="57"
            endOffset="27170"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/iot/HuaweiCloudOkHttpClient.kt"
            line="719"
            column="28"
            startOffset="27209"
            endLine="719"
            endColumn="57"
            endOffset="27238"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/screen/MainScreen.kt"
            line="1645"
            column="22"
            startOffset="59913"
            endLine="1645"
            endColumn="88"
            endOffset="59979"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/screen/MainScreen.kt"
            line="1646"
            column="24"
            startOffset="60003"
            endLine="1646"
            endColumn="73"
            endOffset="60052"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/screen/MainScreen.kt"
            line="1647"
            column="17"
            startOffset="60069"
            endLine="1647"
            endColumn="50"
            endOffset="60102"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/obs/MediaModels.kt"
            line="56"
            column="26"
            startOffset="1642"
            endLine="56"
            endColumn="82"
            endOffset="1698"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/obs/MediaModels.kt"
            line="57"
            column="21"
            startOffset="1719"
            endLine="57"
            endColumn="65"
            endOffset="1763"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/obs/MediaModels.kt"
            line="113"
            column="26"
            startOffset="3487"
            endLine="113"
            endColumn="82"
            endOffset="3543"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/obs/MediaModels.kt"
            line="114"
            column="21"
            startOffset="3564"
            endLine="114"
            endColumn="65"
            endOffset="3608"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/media/MediaThumbnailComponents.kt"
            line="429"
            column="9"
            startOffset="14654"
            endLine="429"
            endColumn="65"
            endOffset="14710"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/media/MediaThumbnailComponents.kt"
            line="431"
            column="9"
            startOffset="14732"
            endLine="431"
            endColumn="53"
            endOffset="14776"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/iot/MqttIoTClient.kt"
            line="335"
            column="28"
            startOffset="10534"
            endLine="335"
            endColumn="57"
            endOffset="10563"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/iot/MqttIoTClient.kt"
            line="336"
            column="28"
            startOffset="10602"
            endLine="336"
            endColumn="57"
            endOffset="10631"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/iot/MqttIoTClient.kt"
            line="337"
            column="28"
            startOffset="10670"
            endLine="337"
            endColumn="57"
            endOffset="10699"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/PostureData.kt"
            line="86"
            column="22"
            startOffset="2101"
            endLine="86"
            endColumn="51"
            endOffset="2130"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/PostureData.kt"
            line="86"
            column="59"
            startOffset="2138"
            endLine="86"
            endColumn="88"
            endOffset="2167"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/PostureData.kt"
            line="86"
            column="96"
            startOffset="2175"
            endLine="86"
            endColumn="125"
            endOffset="2204"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/PostureVisualization3D.kt"
            line="262"
            column="23"
            startOffset="7871"
            endLine="262"
            endColumn="51"
            endOffset="7899"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/VideoData.kt"
            line="130"
            column="26"
            startOffset="2954"
            endLine="130"
            endColumn="92"
            endOffset="3020"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/VideoData.kt"
            line="131"
            column="28"
            startOffset="3048"
            endLine="131"
            endColumn="77"
            endOffset="3097"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/VideoData.kt"
            line="132"
            column="21"
            startOffset="3118"
            endLine="132"
            endColumn="54"
            endOffset="3151"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/VideoData.kt"
            line="141"
            column="47"
            startOffset="3316"
            endLine="141"
            endColumn="110"
            endOffset="3379"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/VideoData.kt"
            line="142"
            column="40"
            startOffset="3419"
            endLine="142"
            endColumn="94"
            endOffset="3473"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/model/VideoData.kt"
            line="143"
            column="33"
            startOffset="3506"
            endLine="143"
            endColumn="76"
            endOffset="3549"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/player/VideoPlayerScreen.kt"
            line="416"
            column="9"
            startOffset="14376"
            endLine="416"
            endColumn="65"
            endOffset="14432"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/player/VideoPlayerScreen.kt"
            line="418"
            column="9"
            startOffset="14454"
            endLine="418"
            endColumn="53"
            endOffset="14498"/>
    </incident>

    <incident
        id="OldTargetApi"
        severity="warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details.">
        <fix-replace
            description="Update targetSdkVersion to 36"
            oldString="35"
            replacement="36"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="14"
            column="9"
            startOffset="310"
            endLine="14"
            endColumn="23"
            endOffset="324"/>
    </incident>

    <incident
        id="SimpleDateFormat"
        severity="warning"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/ComprehensiveHealthDashboard.kt"
            line="148"
            column="21"
            startOffset="4825"
            endLine="148"
            endColumn="59"
            endOffset="4863"/>
    </incident>

    <incident
        id="SimpleDateFormat"
        severity="warning"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/EEGWaveformDisplay.kt"
            line="516"
            column="34"
            startOffset="14994"
            endLine="516"
            endColumn="72"
            endOffset="15032"/>
    </incident>

    <incident
        id="SimpleDateFormat"
        severity="warning"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt"
            line="174"
            column="36"
            startOffset="7313"
            endLine="174"
            endColumn="85"
            endOffset="7362"/>
    </incident>

    <incident
        id="SimpleDateFormat"
        severity="warning"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/eeg/FocusCalculationDemo.kt"
            line="145"
            column="36"
            startOffset="5925"
            endLine="145"
            endColumn="85"
            endOffset="5974"/>
    </incident>

    <incident
        id="SimpleDateFormat"
        severity="warning"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/viewmodel/MainViewModel.kt"
            line="1400"
            column="53"
            startOffset="43498"
            endLine="1400"
            endColumn="91"
            endOffset="43536"/>
    </incident>

    <incident
        id="SimpleDateFormat"
        severity="warning"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/viewmodel/MainViewModel.kt"
            line="1418"
            column="41"
            startOffset="44168"
            endLine="1418"
            endColumn="90"
            endOffset="44217"/>
    </incident>

    <incident
        id="SimpleDateFormat"
        severity="warning"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/obs/ObsApiTestHelper.kt"
            line="211"
            column="36"
            startOffset="6880"
            endLine="211"
            endColumn="85"
            endOffset="6929"/>
    </incident>

    <incident
        id="SimpleDateFormat"
        severity="warning"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/obs/RealTimeVideoVerifier.kt"
            line="392"
            column="32"
            startOffset="14926"
            endLine="392"
            endColumn="71"
            endOffset="14965"/>
    </incident>

    <incident
        id="SimpleDateFormat"
        severity="warning"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/test/VoiceModuleTestHelper.kt"
            line="236"
            column="33"
            startOffset="7688"
            endLine="236"
            endColumn="82"
            endOffset="7737"/>
    </incident>

    <incident
        id="RedundantLabel"
        severity="warning"
        message="Redundant label can be removed">
        <fix-attribute
            description="Delete label"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="label"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="30"
            column="13"
            startOffset="1283"
            endLine="30"
            endColumn="45"
            endOffset="1315"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of org.apache.httpcomponents:httpcore than 4.4.15 is available: 4.4.16">
        <fix-replace
            description="Change to 4.4.16"
            family="Update versions"
            oldString="4.4.15"
            replacement="4.4.16"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="80"
            column="20"
            startOffset="2393"
            endLine="80"
            endColumn="63"
            endOffset="2436"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of commons-codec:commons-codec than 1.11 is available: 1.15">
        <fix-replace
            description="Change to 1.15"
            family="Update versions"
            oldString="1.11"
            replacement="1.15"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="83"
            column="20"
            startOffset="2479"
            endLine="83"
            endColumn="54"
            endOffset="2513"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.fasterxml.jackson.core:jackson-core than 2.8.1 is available: 2.15.2">
        <fix-replace
            description="Change to 2.15.2"
            family="Update versions"
            oldString="2.8.1"
            replacement="2.15.2"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="87"
            column="20"
            startOffset="2621"
            endLine="87"
            endColumn="67"
            endOffset="2668"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.fasterxml.jackson.core:jackson-databind than 2.8.1 is available: 2.15.2">
        <fix-replace
            description="Change to 2.15.2"
            family="Update versions"
            oldString="2.8.1"
            replacement="2.15.2"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="88"
            column="20"
            startOffset="2689"
            endLine="88"
            endColumn="71"
            endOffset="2740"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.fasterxml.jackson.core:jackson-annotations than 2.8.1 is available: 2.15.2">
        <fix-replace
            description="Change to 2.15.2"
            family="Update versions"
            oldString="2.8.1"
            replacement="2.15.2"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="89"
            column="20"
            startOffset="2761"
            endLine="89"
            endColumn="74"
            endOffset="2815"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0">
        <fix-replace
            description="Change to 2.11.0"
            family="Update versions"
            oldString="2.10.1"
            replacement="2.11.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="92"
            column="20"
            startOffset="2851"
            endLine="92"
            endColumn="54"
            endOffset="2885"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.compose.material:material-icons-extended than 1.5.8 is available: 1.7.0">
        <fix-replace
            description="Change to 1.7.0"
            family="Update versions"
            oldString="1.5.8"
            replacement="1.7.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="99"
            column="20"
            startOffset="3112"
            endLine="99"
            endColumn="77"
            endOffset="3169"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.compose.animation:animation than 1.5.8 is available: 1.7.8">
        <fix-replace
            description="Change to 1.7.8"
            family="Update versions"
            oldString="1.5.8"
            replacement="1.7.8"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="102"
            column="20"
            startOffset="3208"
            endLine="102"
            endColumn="64"
            endOffset="3252"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="Upgrade `androidx.compose.foundation` for keyboard and mouse support">
        <fix-replace
            description="Change to 1.7.8"
            family="Update versions"
            oldString="1.5.8"
            replacement="1.7.8"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="105"
            column="20"
            startOffset="3300"
            endLine="105"
            endColumn="66"
            endOffset="3346"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0">
        <fix-replace
            description="Change to 2.11.0"
            family="Update versions"
            oldString="2.10.1"
            replacement="2.11.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="132"
            column="20"
            startOffset="4327"
            endLine="132"
            endColumn="54"
            endOffset="4361"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-compose than 2.7.0 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.7.0"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="138"
            column="20"
            startOffset="4499"
            endLine="138"
            endColumn="74"
            endOffset="4553"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-livedata-ktx than 2.7.0 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.7.0"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="139"
            column="20"
            startOffset="4574"
            endLine="139"
            endColumn="69"
            endOffset="4623"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.activity:activity-compose than 1.8.2 is available: 1.10.1">
        <fix-replace
            description="Change to 1.10.1"
            family="Update versions"
            oldString="1.8.2"
            replacement="1.10.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="157"
            column="20"
            startOffset="5174"
            endLine="157"
            endColumn="62"
            endOffset="5216"/>
    </incident>

    <incident
        id="UnsafeOptInUsageError"
        severity="error"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`">
        <fix-alternatives>
            <annotate
                description="Add &apos;@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)&apos; annotation to &apos;initializePlayer&apos;"
                source="@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/AudioPlayerManager.kt"
                    startOffset="2550"
                    endOffset="5727"/>
            </annotate>
            <annotate
                description="Add &apos;@androidx.media3.common.util.UnstableApi&apos; annotation to &apos;initializePlayer&apos;"
                source="@androidx.media3.common.util.UnstableApi"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/AudioPlayerManager.kt"
                    startOffset="2550"
                    endOffset="5727"/>
            </annotate>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/AudioPlayerManager.kt"
            line="88"
            column="17"
            startOffset="2749"
            endLine="88"
            endColumn="34"
            endOffset="2766"/>
    </incident>

    <incident
        id="UnsafeOptInUsageError"
        severity="error"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`">
        <fix-alternatives>
            <annotate
                description="Add &apos;@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)&apos; annotation to &apos;initializePlayer&apos;"
                source="@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/AudioPlayerManager.kt"
                    startOffset="2550"
                    endOffset="5727"/>
            </annotate>
            <annotate
                description="Add &apos;@androidx.media3.common.util.UnstableApi&apos; annotation to &apos;initializePlayer&apos;"
                source="@androidx.media3.common.util.UnstableApi"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/AudioPlayerManager.kt"
                    startOffset="2550"
                    endOffset="5727"/>
            </annotate>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/AudioPlayerManager.kt"
            line="88"
            column="37"
            startOffset="2769"
            endLine="88"
            endColumn="61"
            endOffset="2793"/>
    </incident>

    <incident
        id="UnsafeOptInUsageError"
        severity="error"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`">
        <fix-alternatives>
            <annotate
                description="Add &apos;@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)&apos; annotation to &apos;initializePlayer&apos;"
                source="@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/AudioPlayerManager.kt"
                    startOffset="2550"
                    endOffset="5727"/>
            </annotate>
            <annotate
                description="Add &apos;@androidx.media3.common.util.UnstableApi&apos; annotation to &apos;initializePlayer&apos;"
                source="@androidx.media3.common.util.UnstableApi"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/AudioPlayerManager.kt"
                    startOffset="2550"
                    endOffset="5727"/>
            </annotate>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/AudioPlayerManager.kt"
            line="91"
            column="22"
            startOffset="2889"
            endLine="91"
            endColumn="34"
            endOffset="2901"/>
    </incident>

    <incident
        id="UnsafeOptInUsageError"
        severity="error"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`">
        <fix-alternatives>
            <annotate
                description="Add &apos;@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)&apos; annotation to &apos;initializePlayer&apos;"
                source="@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/AudioPlayerManager.kt"
                    startOffset="2550"
                    endOffset="5727"/>
            </annotate>
            <annotate
                description="Add &apos;@androidx.media3.common.util.UnstableApi&apos; annotation to &apos;initializePlayer&apos;"
                source="@androidx.media3.common.util.UnstableApi"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/AudioPlayerManager.kt"
                    startOffset="2550"
                    endOffset="5727"/>
            </annotate>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/AudioPlayerManager.kt"
            line="92"
            column="22"
            startOffset="2953"
            endLine="92"
            endColumn="41"
            endOffset="2972"/>
    </incident>

    <incident
        id="UnsafeOptInUsageError"
        severity="error"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`">
        <fix-alternatives>
            <annotate
                description="Add &apos;@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)&apos; annotation to &apos;initializePlayer&apos;"
                source="@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/AudioPlayerManager.kt"
                    startOffset="2550"
                    endOffset="5727"/>
            </annotate>
            <annotate
                description="Add &apos;@androidx.media3.common.util.UnstableApi&apos; annotation to &apos;initializePlayer&apos;"
                source="@androidx.media3.common.util.UnstableApi"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/AudioPlayerManager.kt"
                    startOffset="2550"
                    endOffset="5727"/>
            </annotate>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/AudioPlayerManager.kt"
            line="93"
            column="22"
            startOffset="3001"
            endLine="93"
            endColumn="38"
            endOffset="3017"/>
    </incident>

    <incident
        id="UnsafeOptInUsageError"
        severity="error"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`">
        <fix-alternatives>
            <annotate
                description="Add &apos;@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)&apos; annotation to &apos;initializePlayer&apos;"
                source="@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/AudioPlayerManager.kt"
                    startOffset="2550"
                    endOffset="5727"/>
            </annotate>
            <annotate
                description="Add &apos;@androidx.media3.common.util.UnstableApi&apos; annotation to &apos;initializePlayer&apos;"
                source="@androidx.media3.common.util.UnstableApi"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/AudioPlayerManager.kt"
                    startOffset="2550"
                    endOffset="5727"/>
            </annotate>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/AudioPlayerManager.kt"
            line="97"
            column="38"
            startOffset="3112"
            endLine="97"
            endColumn="63"
            endOffset="3137"/>
    </incident>

    <incident
        id="UnsafeOptInUsageError"
        severity="error"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`">
        <fix-alternatives>
            <annotate
                description="Add &apos;@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)&apos; annotation to &apos;initializePlayer&apos;"
                source="@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/VideoPlayerManager.kt"
                    startOffset="1812"
                    endOffset="4124"/>
            </annotate>
            <annotate
                description="Add &apos;@androidx.media3.common.util.UnstableApi&apos; annotation to &apos;initializePlayer&apos;"
                source="@androidx.media3.common.util.UnstableApi"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/VideoPlayerManager.kt"
                    startOffset="1812"
                    endOffset="4124"/>
            </annotate>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/VideoPlayerManager.kt"
            line="65"
            column="17"
            startOffset="2001"
            endLine="65"
            endColumn="34"
            endOffset="2018"/>
    </incident>

    <incident
        id="UnsafeOptInUsageError"
        severity="error"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`">
        <fix-alternatives>
            <annotate
                description="Add &apos;@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)&apos; annotation to &apos;initializePlayer&apos;"
                source="@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/VideoPlayerManager.kt"
                    startOffset="1812"
                    endOffset="4124"/>
            </annotate>
            <annotate
                description="Add &apos;@androidx.media3.common.util.UnstableApi&apos; annotation to &apos;initializePlayer&apos;"
                source="@androidx.media3.common.util.UnstableApi"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/VideoPlayerManager.kt"
                    startOffset="1812"
                    endOffset="4124"/>
            </annotate>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/VideoPlayerManager.kt"
            line="65"
            column="37"
            startOffset="2021"
            endLine="65"
            endColumn="61"
            endOffset="2045"/>
    </incident>

    <incident
        id="UnsafeOptInUsageError"
        severity="error"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`">
        <fix-alternatives>
            <annotate
                description="Add &apos;@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)&apos; annotation to &apos;initializePlayer&apos;"
                source="@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/VideoPlayerManager.kt"
                    startOffset="1812"
                    endOffset="4124"/>
            </annotate>
            <annotate
                description="Add &apos;@androidx.media3.common.util.UnstableApi&apos; annotation to &apos;initializePlayer&apos;"
                source="@androidx.media3.common.util.UnstableApi"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/VideoPlayerManager.kt"
                    startOffset="1812"
                    endOffset="4124"/>
            </annotate>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/VideoPlayerManager.kt"
            line="68"
            column="22"
            startOffset="2141"
            endLine="68"
            endColumn="34"
            endOffset="2153"/>
    </incident>

    <incident
        id="UnsafeOptInUsageError"
        severity="error"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`">
        <fix-alternatives>
            <annotate
                description="Add &apos;@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)&apos; annotation to &apos;initializePlayer&apos;"
                source="@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/VideoPlayerManager.kt"
                    startOffset="1812"
                    endOffset="4124"/>
            </annotate>
            <annotate
                description="Add &apos;@androidx.media3.common.util.UnstableApi&apos; annotation to &apos;initializePlayer&apos;"
                source="@androidx.media3.common.util.UnstableApi"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/VideoPlayerManager.kt"
                    startOffset="1812"
                    endOffset="4124"/>
            </annotate>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/VideoPlayerManager.kt"
            line="69"
            column="22"
            startOffset="2205"
            endLine="69"
            endColumn="41"
            endOffset="2224"/>
    </incident>

    <incident
        id="UnsafeOptInUsageError"
        severity="error"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`">
        <fix-alternatives>
            <annotate
                description="Add &apos;@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)&apos; annotation to &apos;initializePlayer&apos;"
                source="@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/VideoPlayerManager.kt"
                    startOffset="1812"
                    endOffset="4124"/>
            </annotate>
            <annotate
                description="Add &apos;@androidx.media3.common.util.UnstableApi&apos; annotation to &apos;initializePlayer&apos;"
                source="@androidx.media3.common.util.UnstableApi"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/VideoPlayerManager.kt"
                    startOffset="1812"
                    endOffset="4124"/>
            </annotate>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/VideoPlayerManager.kt"
            line="70"
            column="22"
            startOffset="2253"
            endLine="70"
            endColumn="38"
            endOffset="2269"/>
    </incident>

    <incident
        id="UnsafeOptInUsageError"
        severity="error"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`">
        <fix-alternatives>
            <annotate
                description="Add &apos;@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)&apos; annotation to &apos;initializePlayer&apos;"
                source="@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/VideoPlayerManager.kt"
                    startOffset="1812"
                    endOffset="4124"/>
            </annotate>
            <annotate
                description="Add &apos;@androidx.media3.common.util.UnstableApi&apos; annotation to &apos;initializePlayer&apos;"
                source="@androidx.media3.common.util.UnstableApi"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/VideoPlayerManager.kt"
                    startOffset="1812"
                    endOffset="4124"/>
            </annotate>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/VideoPlayerManager.kt"
            line="74"
            column="38"
            startOffset="2364"
            endLine="74"
            endColumn="63"
            endOffset="2389"/>
    </incident>

    <incident
        id="UnsafeOptInUsageError"
        severity="error"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`">
        <fix-alternatives>
            <annotate
                description="Add &apos;@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)&apos; annotation to &apos;VideoPlayerScreen&apos;"
                source="@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/player/VideoPlayerScreen.kt"
                    startOffset="987"
                    endOffset="7025"/>
            </annotate>
            <annotate
                description="Add &apos;@androidx.media3.common.util.UnstableApi&apos; annotation to &apos;VideoPlayerScreen&apos;"
                source="@androidx.media3.common.util.UnstableApi"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/player/VideoPlayerScreen.kt"
                    startOffset="987"
                    endOffset="7025"/>
            </annotate>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/player/VideoPlayerScreen.kt"
            line="82"
            column="25"
            startOffset="2777"
            endLine="82"
            endColumn="41"
            endOffset="2793"/>
    </incident>

    <incident
        id="UnsafeOptInUsageError"
        severity="error"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`">
        <fix-alternatives>
            <annotate
                description="Add &apos;@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)&apos; annotation to &apos;VideoPlayerScreen&apos;"
                source="@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/player/VideoPlayerScreen.kt"
                    startOffset="987"
                    endOffset="7025"/>
            </annotate>
            <annotate
                description="Add &apos;@androidx.media3.common.util.UnstableApi&apos; annotation to &apos;VideoPlayerScreen&apos;"
                source="@androidx.media3.common.util.UnstableApi"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/player/VideoPlayerScreen.kt"
                    startOffset="987"
                    endOffset="7025"/>
            </annotate>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/player/VideoPlayerScreen.kt"
            line="82"
            column="53"
            startOffset="2805"
            endLine="82"
            endColumn="80"
            endOffset="2832"/>
    </incident>

    <incident
        id="ModifierParameter"
        severity="warning"
        message="Modifier parameter should be the first optional parameter">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt"
            line="34"
            column="5"
            startOffset="1138"
            endLine="34"
            endColumn="13"
            endOffset="1146"/>
    </incident>

    <incident
        id="ModifierParameter"
        severity="warning"
        message="Modifier parameter should be the first optional parameter">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/AdvancedFocusDisplay.kt"
            line="34"
            column="5"
            startOffset="1127"
            endLine="34"
            endColumn="13"
            endOffset="1135"/>
    </incident>

    <incident
        id="ModifierParameter"
        severity="warning"
        message="Modifier parameter should be the first optional parameter">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/ComprehensiveHealthDashboard.kt"
            line="36"
            column="5"
            startOffset="1254"
            endLine="36"
            endColumn="13"
            endOffset="1262"/>
    </incident>

    <incident
        id="ModifierParameter"
        severity="warning"
        message="Modifier parameter should be the first optional parameter">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/EEGBrainwaveChart.kt"
            line="31"
            column="5"
            startOffset="1054"
            endLine="31"
            endColumn="13"
            endOffset="1062"/>
    </incident>

    <incident
        id="ModifierParameter"
        severity="warning"
        message="Modifier parameter should be the first optional parameter">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/EEGFocusGauge.kt"
            line="32"
            column="5"
            startOffset="1021"
            endLine="32"
            endColumn="13"
            endOffset="1029"/>
    </incident>

    <incident
        id="ModifierParameter"
        severity="warning"
        message="Modifier parameter should be the first optional parameter">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/EEGSpectrumAnalyzer.kt"
            line="35"
            column="5"
            startOffset="1192"
            endLine="35"
            endColumn="13"
            endOffset="1200"/>
    </incident>

    <incident
        id="ModifierParameter"
        severity="warning"
        message="Modifier parameter should be the first optional parameter">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/EEGWaveformDisplay.kt"
            line="33"
            column="5"
            startOffset="1079"
            endLine="33"
            endColumn="13"
            endOffset="1087"/>
    </incident>

    <incident
        id="ModifierParameter"
        severity="warning"
        message="Modifier parameter should be the first optional parameter">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/ErrorHandling.kt"
            line="144"
            column="5"
            startOffset="4999"
            endLine="144"
            endColumn="13"
            endOffset="5007"/>
    </incident>

    <incident
        id="ModifierParameter"
        severity="warning"
        message="Modifier parameter should be the first optional parameter">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/media/MediaThumbnailComponents.kt"
            line="156"
            column="5"
            startOffset="5474"
            endLine="156"
            endColumn="13"
            endOffset="5482"/>
    </incident>

    <incident
        id="ModifierParameter"
        severity="warning"
        message="Modifier parameter should be the first optional parameter">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/media/MediaThumbnailComponents.kt"
            line="311"
            column="5"
            startOffset="10490"
            endLine="311"
            endColumn="13"
            endOffset="10498"/>
    </incident>

    <incident
        id="ModifierParameter"
        severity="warning"
        message="Modifier parameter should be the first optional parameter">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/StatsCard.kt"
            line="177"
            column="5"
            startOffset="5806"
            endLine="177"
            endColumn="13"
            endOffset="5814"/>
    </incident>

    <incident
        id="ModifierParameter"
        severity="warning"
        message="Modifier parameter should be the first optional parameter">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/VISVideoPlayerCard.kt"
            line="36"
            column="5"
            startOffset="1161"
            endLine="36"
            endColumn="13"
            endOffset="1169"/>
    </incident>

    <incident
        id="TrustAllX509TrustManager"
        severity="warning"
        message="`checkClientTrusted` is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers">
        <location
            file="$GRADLE_USER_HOME/caches/modules-2/files-2.1/com.huaweicloud.sdk/huaweicloud-sdk-core/3.1.153/48a128c5558b727c003a7a7f03337d7154daf6f6/huaweicloud-sdk-core-3.1.153.jar"/>
    </incident>

    <incident
        id="TrustAllX509TrustManager"
        severity="warning"
        message="`checkServerTrusted` is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers">
        <location
            file="$GRADLE_USER_HOME/caches/modules-2/files-2.1/com.huaweicloud.sdk/huaweicloud-sdk-core/3.1.153/48a128c5558b727c003a7a7f03337d7154daf6f6/huaweicloud-sdk-core-3.1.153.jar"/>
    </incident>

    <incident
        id="AutoboxingStateCreation"
        severity="informational"
        message="Prefer `mutableFloatStateOf` instead of `mutableStateOf`">
        <fix-replace
            description="Replace with mutableFloatStateOf"
            replacement="androidx.compose.runtime.mutableFloatStateOf"
            shortenNames="true"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt"
                startOffset="4183"
                endOffset="4197"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt"
            line="133"
            column="44"
            startOffset="4183"
            endLine="133"
            endColumn="58"
            endOffset="4197"/>
    </incident>

    <incident
        id="AutoboxingStateCreation"
        severity="informational"
        message="Prefer `mutableFloatStateOf` instead of `mutableStateOf`">
        <fix-replace
            description="Replace with mutableFloatStateOf"
            replacement="androidx.compose.runtime.mutableFloatStateOf"
            shortenNames="true"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt"
                startOffset="4249"
                endOffset="4263"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt"
            line="134"
            column="46"
            startOffset="4249"
            endLine="134"
            endColumn="60"
            endOffset="4263"/>
    </incident>

    <incident
        id="AutoboxingStateCreation"
        severity="informational"
        message="Prefer `mutableFloatStateOf` instead of `mutableStateOf`">
        <fix-replace
            description="Replace with mutableFloatStateOf"
            replacement="androidx.compose.runtime.mutableFloatStateOf"
            shortenNames="true"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/AdvancedFocusDisplay.kt"
                startOffset="4140"
                endOffset="4154"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/AdvancedFocusDisplay.kt"
            line="133"
            column="42"
            startOffset="4140"
            endLine="133"
            endColumn="56"
            endOffset="4154"/>
    </incident>

    <incident
        id="AutoboxingStateCreation"
        severity="informational"
        message="Prefer `mutableLongStateOf` instead of `mutableStateOf`">
        <fix-replace
            description="Replace with mutableLongStateOf"
            replacement="androidx.compose.runtime.mutableLongStateOf"
            shortenNames="true"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/player/AudioPlayerScreen.kt"
                startOffset="15331"
                endOffset="15345"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/player/AudioPlayerScreen.kt"
            line="415"
            column="44"
            startOffset="15331"
            endLine="415"
            endColumn="58"
            endOffset="15345"/>
    </incident>

    <incident
        id="AutoboxingStateCreation"
        severity="informational"
        message="Prefer `mutableFloatStateOf` instead of `mutableStateOf`">
        <fix-replace
            description="Replace with mutableFloatStateOf"
            replacement="androidx.compose.runtime.mutableFloatStateOf"
            shortenNames="true"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/ComprehensiveHealthDashboard.kt"
                startOffset="10037"
                endOffset="10051"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/ComprehensiveHealthDashboard.kt"
            line="372"
            column="37"
            startOffset="10037"
            endLine="372"
            endColumn="51"
            endOffset="10051"/>
    </incident>

    <incident
        id="AutoboxingStateCreation"
        severity="informational"
        message="Prefer `mutableFloatStateOf` instead of `mutableStateOf`">
        <fix-replace
            description="Replace with mutableFloatStateOf"
            replacement="androidx.compose.runtime.mutableFloatStateOf"
            shortenNames="true"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/EEGFocusGauge.kt"
                startOffset="1091"
                endOffset="1105"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/EEGFocusGauge.kt"
            line="34"
            column="37"
            startOffset="1091"
            endLine="34"
            endColumn="51"
            endOffset="1105"/>
    </incident>

    <incident
        id="AutoboxingStateCreation"
        severity="informational"
        message="Prefer `mutableFloatStateOf` instead of `mutableStateOf`">
        <fix-replace
            description="Replace with mutableFloatStateOf"
            replacement="androidx.compose.runtime.mutableFloatStateOf"
            shortenNames="true"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/EEGWaveformDisplay.kt"
                startOffset="6886"
                endOffset="6900"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/EEGWaveformDisplay.kt"
            line="237"
            column="41"
            startOffset="6886"
            endLine="237"
            endColumn="55"
            endOffset="6900"/>
    </incident>

    <incident
        id="AutoboxingStateCreation"
        severity="informational"
        message="Prefer `mutableIntStateOf` instead of `mutableStateOf`">
        <fix-replace
            description="Replace with mutableIntStateOf"
            replacement="androidx.compose.runtime.mutableIntStateOf"
            shortenNames="true"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/screen/LearningSupervisionScreen.kt"
                startOffset="4124"
                endOffset="4138"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/screen/LearningSupervisionScreen.kt"
            line="119"
            column="38"
            startOffset="4124"
            endLine="119"
            endColumn="52"
            endOffset="4138"/>
    </incident>

    <incident
        id="AutoboxingStateCreation"
        severity="informational"
        message="Prefer `mutableIntStateOf` instead of `mutableStateOf`">
        <fix-replace
            description="Replace with mutableIntStateOf"
            replacement="androidx.compose.runtime.mutableIntStateOf"
            shortenNames="true"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/screen/LearningSupervisionScreen.kt"
                startOffset="24816"
                endOffset="24830"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/screen/LearningSupervisionScreen.kt"
            line="751"
            column="40"
            startOffset="24816"
            endLine="751"
            endColumn="54"
            endOffset="24830"/>
    </incident>

    <incident
        id="AutoboxingStateCreation"
        severity="informational"
        message="Prefer `mutableLongStateOf` instead of `mutableStateOf`">
        <fix-replace
            description="Replace with mutableLongStateOf"
            replacement="androidx.compose.runtime.mutableLongStateOf"
            shortenNames="true"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/player/VideoPlayerScreen.kt"
                startOffset="11326"
                endOffset="11340"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/player/VideoPlayerScreen.kt"
            line="316"
            column="44"
            startOffset="11326"
            endLine="316"
            endColumn="58"
            endOffset="11340"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `String.toUri` instead?">
        <fix-replace
            description="Replace with the toUri extension function"
            family="Replace with the toUri extension function"
            robot="true"
            independent="true"
            oldString="Uri.parse(session.streamUrl)"
            replacement="session.streamUrl.toUri()"
            reformat="value"
            imports="androidx.core.net.toUri"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/AudioPlayerManager.kt"
                startOffset="7048"
                endOffset="7076"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/AudioPlayerManager.kt"
            line="189"
            column="21"
            startOffset="7048"
            endLine="189"
            endColumn="49"
            endOffset="7076"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?">
        <fix-composite
            description="Replace with the edit extension function"
            family="Replace with the edit extension function"
            robot="true"
            independent="true">
            <fix-replace
                description="Replace with edit"
                robot="true"
                independent="true"
                oldString="edit("
                replacement="edit"
                reformat="value"
                imports="androidx.core.content.edit"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/viewmodel/MainViewModel.kt"
                    startOffset="50528"
                    endOffset="50533"/>
            </fix-replace>
            <fix-replace
                description="Replace with  {"
                robot="true"
                independent="true"
                oldString=")"
                replacement=" {"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/viewmodel/MainViewModel.kt"
                    startOffset="50533"
                    endOffset="50534"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;.&quot;"
                robot="true"
                independent="true"
                oldString="."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/viewmodel/MainViewModel.kt"
                    startOffset="50559"
                    endOffset="50560"/>
            </fix-replace>
            <fix-replace
                description="Replace with }"
                robot="true"
                independent="true"
                oldString=".apply()"
                replacement="}"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/viewmodel/MainViewModel.kt"
                    startOffset="50805"
                    endOffset="50813"/>
            </fix-replace>
            <fix-replace
                description="Replace with     "
                robot="true"
                independent="true"
                oldString=""
                replacement="    "
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/viewmodel/MainViewModel.kt"
                    startOffset="50535"
                    endOffset="50535"/>
            </fix-replace>
            <fix-replace
                description="Replace with     "
                robot="true"
                independent="true"
                oldString=""
                replacement="    "
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/viewmodel/MainViewModel.kt"
                    startOffset="50614"
                    endOffset="50614"/>
            </fix-replace>
            <fix-replace
                description="Replace with     "
                robot="true"
                independent="true"
                oldString=""
                replacement="    "
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/viewmodel/MainViewModel.kt"
                    startOffset="50713"
                    endOffset="50713"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/viewmodel/MainViewModel.kt"
            line="1596"
            column="21"
            startOffset="50522"
            endLine="1596"
            endColumn="33"
            endOffset="50534"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?">
        <fix-composite
            description="Replace with the edit extension function"
            family="Replace with the edit extension function"
            robot="true"
            independent="true">
            <fix-replace
                description="Replace with edit"
                robot="true"
                independent="true"
                oldString="edit("
                replacement="edit"
                reformat="value"
                imports="androidx.core.content.edit"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/auth/TokenManager.kt"
                    startOffset="10528"
                    endOffset="10533"/>
            </fix-replace>
            <fix-replace
                description="Replace with  {"
                robot="true"
                independent="true"
                oldString=")"
                replacement=" {"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/auth/TokenManager.kt"
                    startOffset="10533"
                    endOffset="10534"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;.&quot;"
                robot="true"
                independent="true"
                oldString="."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/auth/TokenManager.kt"
                    startOffset="10547"
                    endOffset="10548"/>
            </fix-replace>
            <fix-replace
                description="Replace with }"
                robot="true"
                independent="true"
                oldString=".apply()"
                replacement="}"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/auth/TokenManager.kt"
                    startOffset="10740"
                    endOffset="10748"/>
            </fix-replace>
            <fix-replace
                description="Replace with     "
                robot="true"
                independent="true"
                oldString=""
                replacement="    "
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/auth/TokenManager.kt"
                    startOffset="10535"
                    endOffset="10535"/>
            </fix-replace>
            <fix-replace
                description="Replace with     "
                robot="true"
                independent="true"
                oldString=""
                replacement="    "
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/auth/TokenManager.kt"
                    startOffset="10576"
                    endOffset="10576"/>
            </fix-replace>
            <fix-replace
                description="Replace with     "
                robot="true"
                independent="true"
                oldString=""
                replacement="    "
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/auth/TokenManager.kt"
                    startOffset="10631"
                    endOffset="10631"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/auth/TokenManager.kt"
            line="306"
            column="9"
            startOffset="10522"
            endLine="306"
            endColumn="21"
            endOffset="10534"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?">
        <fix-composite
            description="Replace with the edit extension function"
            family="Replace with the edit extension function"
            robot="true"
            independent="true">
            <fix-replace
                description="Replace with edit"
                robot="true"
                independent="true"
                oldString="edit("
                replacement="edit"
                reformat="value"
                imports="androidx.core.content.edit"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/auth/TokenManager.kt"
                    startOffset="10956"
                    endOffset="10961"/>
            </fix-replace>
            <fix-replace
                description="Replace with  {"
                robot="true"
                independent="true"
                oldString=")"
                replacement=" {"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/auth/TokenManager.kt"
                    startOffset="10961"
                    endOffset="10962"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;.&quot;"
                robot="true"
                independent="true"
                oldString="."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/auth/TokenManager.kt"
                    startOffset="10975"
                    endOffset="10976"/>
            </fix-replace>
            <fix-replace
                description="Replace with }"
                robot="true"
                independent="true"
                oldString=".apply()"
                replacement="}"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/auth/TokenManager.kt"
                    startOffset="11089"
                    endOffset="11097"/>
            </fix-replace>
            <fix-replace
                description="Replace with     "
                robot="true"
                independent="true"
                oldString=""
                replacement="    "
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/auth/TokenManager.kt"
                    startOffset="10963"
                    endOffset="10963"/>
            </fix-replace>
            <fix-replace
                description="Replace with     "
                robot="true"
                independent="true"
                oldString=""
                replacement="    "
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/auth/TokenManager.kt"
                    startOffset="10994"
                    endOffset="10994"/>
            </fix-replace>
            <fix-replace
                description="Replace with     "
                robot="true"
                independent="true"
                oldString=""
                replacement="    "
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/auth/TokenManager.kt"
                    startOffset="11035"
                    endOffset="11035"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/auth/TokenManager.kt"
            line="319"
            column="9"
            startOffset="10950"
            endLine="319"
            endColumn="21"
            endOffset="10962"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?">
        <fix-composite
            description="Replace with the edit extension function"
            family="Replace with the edit extension function"
            robot="true"
            independent="true">
            <fix-replace
                description="Replace with edit"
                robot="true"
                independent="true"
                oldString="edit("
                replacement="edit"
                reformat="value"
                imports="androidx.core.content.edit"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/settings/UserSettingsManager.kt"
                    startOffset="1545"
                    endOffset="1550"/>
            </fix-replace>
            <fix-replace
                description="Replace with  {"
                robot="true"
                independent="true"
                oldString=")"
                replacement=" {"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/settings/UserSettingsManager.kt"
                    startOffset="1550"
                    endOffset="1551"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;.&quot;"
                robot="true"
                independent="true"
                oldString="."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/settings/UserSettingsManager.kt"
                    startOffset="1568"
                    endOffset="1569"/>
            </fix-replace>
            <fix-replace
                description="Replace with }"
                robot="true"
                independent="true"
                oldString=".apply()"
                replacement="}"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/settings/UserSettingsManager.kt"
                    startOffset="1628"
                    endOffset="1636"/>
            </fix-replace>
            <fix-replace
                description="Replace with     "
                robot="true"
                independent="true"
                oldString=""
                replacement="    "
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/settings/UserSettingsManager.kt"
                    startOffset="1552"
                    endOffset="1552"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/settings/UserSettingsManager.kt"
            line="51"
            column="13"
            startOffset="1527"
            endLine="51"
            endColumn="37"
            endOffset="1551"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `String.toUri` instead?">
        <fix-replace
            description="Replace with the toUri extension function"
            family="Replace with the toUri extension function"
            robot="true"
            independent="true"
            oldString="Uri.parse(url)"
            replacement="url.toUri()"
            reformat="value"
            imports="androidx.core.net.toUri"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/VISVideoPlayerCard.kt"
                startOffset="3100"
                endOffset="3114"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/ui/components/VISVideoPlayerCard.kt"
            line="92"
            column="41"
            startOffset="3100"
            endLine="92"
            endColumn="55"
            endOffset="3114"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `String.toUri` instead?">
        <fix-replace
            description="Replace with the toUri extension function"
            family="Replace with the toUri extension function"
            robot="true"
            independent="true"
            oldString="Uri.parse(videoSession.streamUrl)"
            replacement="videoSession.streamUrl.toUri()"
            reformat="value"
            imports="androidx.core.net.toUri"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/VideoPlayerManager.kt"
                startOffset="4852"
                endOffset="4885"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/iotandroidv20/player/VideoPlayerManager.kt"
            line="131"
            column="17"
            startOffset="4852"
            endLine="131"
            endColumn="50"
            endOffset="4885"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for com-squareup-okhttp3-okhttp"
            robot="true">
            <fix-replace
                description="Replace with squareupOkhttpVersion = &quot;4.12.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="squareupOkhttpVersion = &quot;4.12.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="209"
                    endOffset="209"/>
            </fix-replace>
            <fix-replace
                description="Replace with com-squareup-okhttp3-okhttp = { module = &quot;com.squareup.okhttp3:okhttp&quot;, version.ref = &quot;squareupOkhttpVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="com-squareup-okhttp3-okhttp = { module = &quot;com.squareup.okhttp3:okhttp&quot;, version.ref = &quot;squareupOkhttpVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="314"
                    endOffset="314"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.com.squareup.okhttp3.okhttp"
                robot="true"
                replacement="libs.com.squareup.okhttp3.okhttp"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2003"
                    endOffset="2039"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="72"
            column="20"
            startOffset="2003"
            endLine="72"
            endColumn="56"
            endOffset="2039"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for okhttp3-logging-interceptor"
            robot="true">
            <fix-replace
                description="Replace with loggingInterceptorVersion = &quot;4.12.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="loggingInterceptorVersion = &quot;4.12.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="209"
                    endOffset="209"/>
            </fix-replace>
            <fix-replace
                description="Replace with okhttp3-logging-interceptor = { module = &quot;com.squareup.okhttp3:logging-interceptor&quot;, version.ref = &quot;loggingInterceptorVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="okhttp3-logging-interceptor = { module = &quot;com.squareup.okhttp3:logging-interceptor&quot;, version.ref = &quot;loggingInterceptorVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="1517"
                    endOffset="1517"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.okhttp3.logging.interceptor"
                robot="true"
                replacement="libs.okhttp3.logging.interceptor"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2060"
                    endOffset="2109"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="73"
            column="20"
            startOffset="2060"
            endLine="73"
            endColumn="69"
            endOffset="2109"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for httpcomponents-httpcore"
            robot="true">
            <fix-replace
                description="Replace with httpcoreVersion = &quot;4.4.15&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="httpcoreVersion = &quot;4.4.15&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with httpcomponents-httpcore = { module = &quot;org.apache.httpcomponents:httpcore&quot;, version.ref = &quot;httpcoreVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="httpcomponents-httpcore = { module = &quot;org.apache.httpcomponents:httpcore&quot;, version.ref = &quot;httpcoreVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="314"
                    endOffset="314"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.httpcomponents.httpcore"
                robot="true"
                replacement="libs.httpcomponents.httpcore"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2393"
                    endOffset="2436"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="80"
            column="20"
            startOffset="2393"
            endLine="80"
            endColumn="63"
            endOffset="2436"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for commons-codec-commons-codec"
            robot="true">
            <fix-replace
                description="Replace with commonsCodecVersion = &quot;1.11&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="commonsCodecVersion = &quot;1.11&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with commons-codec-commons-codec = { module = &quot;commons-codec:commons-codec&quot;, version.ref = &quot;commonsCodecVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="commons-codec-commons-codec = { module = &quot;commons-codec:commons-codec&quot;, version.ref = &quot;commonsCodecVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="314"
                    endOffset="314"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.commons.codec.commons.codec"
                robot="true"
                replacement="libs.commons.codec.commons.codec"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2479"
                    endOffset="2513"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="83"
            column="20"
            startOffset="2479"
            endLine="83"
            endColumn="54"
            endOffset="2513"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for commons-logging-commons-logging"
            robot="true">
            <fix-replace
                description="Replace with commonsLoggingVersion = &quot;1.2&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="commonsLoggingVersion = &quot;1.2&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with commons-logging-commons-logging = { module = &quot;commons-logging:commons-logging&quot;, version.ref = &quot;commonsLoggingVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="commons-logging-commons-logging = { module = &quot;commons-logging:commons-logging&quot;, version.ref = &quot;commonsLoggingVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="314"
                    endOffset="314"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.commons.logging.commons.logging"
                robot="true"
                replacement="libs.commons.logging.commons.logging"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2534"
                    endOffset="2571"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="84"
            column="20"
            startOffset="2534"
            endLine="84"
            endColumn="57"
            endOffset="2571"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for fasterxml-jackson-core"
            robot="true">
            <fix-replace
                description="Replace with fasterxmlJacksonCore = &quot;2.8.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="fasterxmlJacksonCore = &quot;2.8.1&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with fasterxml-jackson-core = { module = &quot;com.fasterxml.jackson.core:jackson-core&quot;, version.ref = &quot;fasterxmlJacksonCore&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="fasterxml-jackson-core = { module = &quot;com.fasterxml.jackson.core:jackson-core&quot;, version.ref = &quot;fasterxmlJacksonCore&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="314"
                    endOffset="314"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.fasterxml.jackson.core"
                robot="true"
                replacement="libs.fasterxml.jackson.core"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2621"
                    endOffset="2668"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="87"
            column="20"
            startOffset="2621"
            endLine="87"
            endColumn="67"
            endOffset="2668"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for fasterxml-jackson-databind"
            robot="true">
            <fix-replace
                description="Replace with fasterxmlJacksonDatabind = &quot;2.8.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="fasterxmlJacksonDatabind = &quot;2.8.1&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with fasterxml-jackson-databind = { module = &quot;com.fasterxml.jackson.core:jackson-databind&quot;, version.ref = &quot;fasterxmlJacksonDatabind&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="fasterxml-jackson-databind = { module = &quot;com.fasterxml.jackson.core:jackson-databind&quot;, version.ref = &quot;fasterxmlJacksonDatabind&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="314"
                    endOffset="314"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.fasterxml.jackson.databind"
                robot="true"
                replacement="libs.fasterxml.jackson.databind"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2689"
                    endOffset="2740"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="88"
            column="20"
            startOffset="2689"
            endLine="88"
            endColumn="71"
            endOffset="2740"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for fasterxml-jackson-annotations"
            robot="true">
            <fix-replace
                description="Replace with fasterxmlJacksonAnnotations = &quot;2.8.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="fasterxmlJacksonAnnotations = &quot;2.8.1&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with fasterxml-jackson-annotations = { module = &quot;com.fasterxml.jackson.core:jackson-annotations&quot;, version.ref = &quot;fasterxmlJacksonAnnotations&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="fasterxml-jackson-annotations = { module = &quot;com.fasterxml.jackson.core:jackson-annotations&quot;, version.ref = &quot;fasterxmlJacksonAnnotations&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="314"
                    endOffset="314"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.fasterxml.jackson.annotations"
                robot="true"
                replacement="libs.fasterxml.jackson.annotations"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2761"
                    endOffset="2815"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="89"
            column="20"
            startOffset="2761"
            endLine="89"
            endColumn="74"
            endOffset="2815"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for com-google-code-gson-gson"
            robot="true">
            <fix-replace
                description="Replace with googleGson = &quot;2.10.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="googleGson = &quot;2.10.1&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with com-google-code-gson-gson = { module = &quot;com.google.code.gson:gson&quot;, version.ref = &quot;googleGson&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="com-google-code-gson-gson = { module = &quot;com.google.code.gson:gson&quot;, version.ref = &quot;googleGson&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="314"
                    endOffset="314"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.com.google.code.gson.gson"
                robot="true"
                replacement="libs.com.google.code.gson.gson"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2851"
                    endOffset="2885"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="92"
            column="20"
            startOffset="2851"
            endLine="92"
            endColumn="54"
            endOffset="2885"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for material-icons-extended"
            robot="true">
            <fix-replace
                description="Replace with materialIconsExtendedVersion = &quot;1.5.8&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="materialIconsExtendedVersion = &quot;1.5.8&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="209"
                    endOffset="209"/>
            </fix-replace>
            <fix-replace
                description="Replace with material-icons-extended = { module = &quot;androidx.compose.material:material-icons-extended&quot;, version.ref = &quot;materialIconsExtendedVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="material-icons-extended = { module = &quot;androidx.compose.material:material-icons-extended&quot;, version.ref = &quot;materialIconsExtendedVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="1517"
                    endOffset="1517"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.material.icons.extended"
                robot="true"
                replacement="libs.material.icons.extended"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="3112"
                    endOffset="3169"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="99"
            column="20"
            startOffset="3112"
            endLine="99"
            endColumn="77"
            endOffset="3169"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for animation"
            robot="true">
            <fix-replace
                description="Replace with animationVersion = &quot;1.5.8&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="animationVersion = &quot;1.5.8&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with animation = { module = &quot;androidx.compose.animation:animation&quot;, version.ref = &quot;animationVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="animation = { module = &quot;androidx.compose.animation:animation&quot;, version.ref = &quot;animationVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="314"
                    endOffset="314"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.animation"
                robot="true"
                replacement="libs.animation"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="3208"
                    endOffset="3252"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="102"
            column="20"
            startOffset="3208"
            endLine="102"
            endColumn="64"
            endOffset="3252"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for foundation"
            robot="true">
            <fix-replace
                description="Replace with foundationVersion = &quot;1.5.8&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="foundationVersion = &quot;1.5.8&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with foundation = { module = &quot;androidx.compose.foundation:foundation&quot;, version.ref = &quot;foundationVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="foundation = { module = &quot;androidx.compose.foundation:foundation&quot;, version.ref = &quot;foundationVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="314"
                    endOffset="314"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.foundation"
                robot="true"
                replacement="libs.foundation"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="3300"
                    endOffset="3346"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="105"
            column="20"
            startOffset="3300"
            endLine="105"
            endColumn="66"
            endOffset="3346"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for navigation-compose"
            robot="true">
            <fix-replace
                description="Replace with navigationComposeVersion = &quot;2.7.6&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="navigationComposeVersion = &quot;2.7.6&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="209"
                    endOffset="209"/>
            </fix-replace>
            <fix-replace
                description="Replace with navigation-compose = { module = &quot;androidx.navigation:navigation-compose&quot;, version.ref = &quot;navigationComposeVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="navigation-compose = { module = &quot;androidx.navigation:navigation-compose&quot;, version.ref = &quot;navigationComposeVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="1517"
                    endOffset="1517"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.navigation.compose"
                robot="true"
                replacement="libs.navigation.compose"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="3394"
                    endOffset="3440"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="108"
            column="20"
            startOffset="3394"
            endLine="108"
            endColumn="66"
            endOffset="3440"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for sdk-huaweicloud-sdk-iotda"
            robot="true">
            <fix-replace
                description="Replace with huaweicloudSdkIotdaVersion = &quot;3.1.153&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="huaweicloudSdkIotdaVersion = &quot;3.1.153&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with sdk-huaweicloud-sdk-iotda = { module = &quot;com.huaweicloud.sdk:huaweicloud-sdk-iotda&quot;, version.ref = &quot;huaweicloudSdkIotdaVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="sdk-huaweicloud-sdk-iotda = { module = &quot;com.huaweicloud.sdk:huaweicloud-sdk-iotda&quot;, version.ref = &quot;huaweicloudSdkIotdaVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="1517"
                    endOffset="1517"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.sdk.huaweicloud.sdk.iotda"
                robot="true"
                replacement="libs.sdk.huaweicloud.sdk.iotda"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="3658"
                    endOffset="3709"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="115"
            column="20"
            startOffset="3658"
            endLine="115"
            endColumn="71"
            endOffset="3709"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for sdk-huaweicloud-sdk-core"
            robot="true">
            <fix-replace
                description="Replace with huaweicloudSdkCoreVersion = &quot;3.1.153&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="huaweicloudSdkCoreVersion = &quot;3.1.153&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with sdk-huaweicloud-sdk-core = { module = &quot;com.huaweicloud.sdk:huaweicloud-sdk-core&quot;, version.ref = &quot;huaweicloudSdkCoreVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="sdk-huaweicloud-sdk-core = { module = &quot;com.huaweicloud.sdk:huaweicloud-sdk-core&quot;, version.ref = &quot;huaweicloudSdkCoreVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="1517"
                    endOffset="1517"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.sdk.huaweicloud.sdk.core"
                robot="true"
                replacement="libs.sdk.huaweicloud.sdk.core"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="3730"
                    endOffset="3780"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="116"
            column="20"
            startOffset="3730"
            endLine="116"
            endColumn="70"
            endOffset="3780"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for com-squareup-okhttp3-okhttp2"
            robot="true">
            <fix-replace
                description="Replace with comSquareupOkhttp3Okhttp = &quot;4.12.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="comSquareupOkhttp3Okhttp = &quot;4.12.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with com-squareup-okhttp3-okhttp2 = { module = &quot;com.squareup.okhttp3:okhttp&quot;, version.ref = &quot;comSquareupOkhttp3Okhttp&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="com-squareup-okhttp3-okhttp2 = { module = &quot;com.squareup.okhttp3:okhttp&quot;, version.ref = &quot;comSquareupOkhttp3Okhttp&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="314"
                    endOffset="314"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.com.squareup.okhttp3.okhttp2"
                robot="true"
                replacement="libs.com.squareup.okhttp3.okhttp2"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="3826"
                    endOffset="3862"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="119"
            column="20"
            startOffset="3826"
            endLine="119"
            endColumn="56"
            endOffset="3862"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for com-fasterxml-jackson-core-jackson-core"
            robot="true">
            <fix-replace
                description="Replace with fasterxmlJacksonCoreVersion = &quot;2.15.2&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="fasterxmlJacksonCoreVersion = &quot;2.15.2&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with com-fasterxml-jackson-core-jackson-core = { module = &quot;com.fasterxml.jackson.core:jackson-core&quot;, version.ref = &quot;fasterxmlJacksonCoreVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="com-fasterxml-jackson-core-jackson-core = { module = &quot;com.fasterxml.jackson.core:jackson-core&quot;, version.ref = &quot;fasterxmlJacksonCoreVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="314"
                    endOffset="314"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.com.fasterxml.jackson.core.jackson.core"
                robot="true"
                replacement="libs.com.fasterxml.jackson.core.jackson.core"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="3898"
                    endOffset="3946"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="122"
            column="20"
            startOffset="3898"
            endLine="122"
            endColumn="68"
            endOffset="3946"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for com-fasterxml-jackson-core-jackson-databind"
            robot="true">
            <fix-replace
                description="Replace with fasterxmlJacksonDatabindVersion = &quot;2.15.2&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="fasterxmlJacksonDatabindVersion = &quot;2.15.2&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with com-fasterxml-jackson-core-jackson-databind = { module = &quot;com.fasterxml.jackson.core:jackson-databind&quot;, version.ref = &quot;fasterxmlJacksonDatabindVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="com-fasterxml-jackson-core-jackson-databind = { module = &quot;com.fasterxml.jackson.core:jackson-databind&quot;, version.ref = &quot;fasterxmlJacksonDatabindVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="314"
                    endOffset="314"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.com.fasterxml.jackson.core.jackson.databind"
                robot="true"
                replacement="libs.com.fasterxml.jackson.core.jackson.databind"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="3967"
                    endOffset="4019"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="123"
            column="20"
            startOffset="3967"
            endLine="123"
            endColumn="72"
            endOffset="4019"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for com-fasterxml-jackson-core-jackson-annotations"
            robot="true">
            <fix-replace
                description="Replace with fasterxmlJacksonAnnotationsVersion = &quot;2.15.2&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="fasterxmlJacksonAnnotationsVersion = &quot;2.15.2&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with com-fasterxml-jackson-core-jackson-annotations = { module = &quot;com.fasterxml.jackson.core:jackson-annotations&quot;, version.ref = &quot;fasterxmlJacksonAnnotationsVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="com-fasterxml-jackson-core-jackson-annotations = { module = &quot;com.fasterxml.jackson.core:jackson-annotations&quot;, version.ref = &quot;fasterxmlJacksonAnnotationsVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="314"
                    endOffset="314"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.com.fasterxml.jackson.core.jackson.annotations"
                robot="true"
                replacement="libs.com.fasterxml.jackson.core.jackson.annotations"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="4040"
                    endOffset="4095"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="124"
            column="20"
            startOffset="4040"
            endLine="124"
            endColumn="75"
            endOffset="4095"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for com-squareup-okhttp3-okhttp3"
            robot="true">
            <fix-replace
                description="Replace with comSquareupOkhttp3Okhttp2 = &quot;4.12.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="comSquareupOkhttp3Okhttp2 = &quot;4.12.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with com-squareup-okhttp3-okhttp3 = { module = &quot;com.squareup.okhttp3:okhttp&quot;, version.ref = &quot;comSquareupOkhttp3Okhttp2&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="com-squareup-okhttp3-okhttp3 = { module = &quot;com.squareup.okhttp3:okhttp&quot;, version.ref = &quot;comSquareupOkhttp3Okhttp2&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="314"
                    endOffset="314"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.com.squareup.okhttp3.okhttp3"
                robot="true"
                replacement="libs.com.squareup.okhttp3.okhttp3"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="4129"
                    endOffset="4165"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="127"
            column="20"
            startOffset="4129"
            endLine="127"
            endColumn="56"
            endOffset="4165"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for retrofit2-retrofit"
            robot="true">
            <fix-replace
                description="Replace with retrofitVersion = &quot;2.9.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="retrofitVersion = &quot;2.9.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="209"
                    endOffset="209"/>
            </fix-replace>
            <fix-replace
                description="Replace with retrofit2-retrofit = { module = &quot;com.squareup.retrofit2:retrofit&quot;, version.ref = &quot;retrofitVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="retrofit2-retrofit = { module = &quot;com.squareup.retrofit2:retrofit&quot;, version.ref = &quot;retrofitVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="1517"
                    endOffset="1517"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.retrofit2.retrofit"
                robot="true"
                replacement="libs.retrofit2.retrofit"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="4186"
                    endOffset="4225"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="128"
            column="20"
            startOffset="4186"
            endLine="128"
            endColumn="59"
            endOffset="4225"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for retrofit2-converter-gson"
            robot="true">
            <fix-replace
                description="Replace with converterGsonVersion = &quot;2.9.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="converterGsonVersion = &quot;2.9.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with retrofit2-converter-gson = { module = &quot;com.squareup.retrofit2:converter-gson&quot;, version.ref = &quot;converterGsonVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="retrofit2-converter-gson = { module = &quot;com.squareup.retrofit2:converter-gson&quot;, version.ref = &quot;converterGsonVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="1517"
                    endOffset="1517"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.retrofit2.converter.gson"
                robot="true"
                replacement="libs.retrofit2.converter.gson"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="4246"
                    endOffset="4291"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="129"
            column="20"
            startOffset="4246"
            endLine="129"
            endColumn="65"
            endOffset="4291"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for com-google-code-gson-gson2"
            robot="true">
            <fix-replace
                description="Replace with googleGsonVersion = &quot;2.10.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="googleGsonVersion = &quot;2.10.1&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with com-google-code-gson-gson2 = { module = &quot;com.google.code.gson:gson&quot;, version.ref = &quot;googleGsonVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="com-google-code-gson-gson2 = { module = &quot;com.google.code.gson:gson&quot;, version.ref = &quot;googleGsonVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="314"
                    endOffset="314"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.com.google.code.gson.gson2"
                robot="true"
                replacement="libs.com.google.code.gson.gson2"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="4327"
                    endOffset="4361"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="132"
            column="20"
            startOffset="4327"
            endLine="132"
            endColumn="54"
            endOffset="4361"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for jetbrains-kotlinx-coroutines-android"
            robot="true">
            <fix-replace
                description="Replace with kotlinxCoroutinesAndroidVersion = &quot;1.7.3&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="kotlinxCoroutinesAndroidVersion = &quot;1.7.3&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="126"
                    endOffset="126"/>
            </fix-replace>
            <fix-replace
                description="Replace with jetbrains-kotlinx-coroutines-android = { module = &quot;org.jetbrains.kotlinx:kotlinx-coroutines-android&quot;, version.ref = &quot;kotlinxCoroutinesAndroidVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="jetbrains-kotlinx-coroutines-android = { module = &quot;org.jetbrains.kotlinx:kotlinx-coroutines-android&quot;, version.ref = &quot;kotlinxCoroutinesAndroidVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="314"
                    endOffset="314"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.jetbrains.kotlinx.coroutines.android"
                robot="true"
                replacement="libs.jetbrains.kotlinx.coroutines.android"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="4395"
                    endOffset="4451"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="135"
            column="20"
            startOffset="4395"
            endLine="135"
            endColumn="76"
            endOffset="4451"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for lifecycle-viewmodel-compose"
            robot="true">
            <fix-replace
                description="Replace with lifecycleViewmodelComposeVersion = &quot;2.7.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="lifecycleViewmodelComposeVersion = &quot;2.7.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="209"
                    endOffset="209"/>
            </fix-replace>
            <fix-replace
                description="Replace with lifecycle-viewmodel-compose = { module = &quot;androidx.lifecycle:lifecycle-viewmodel-compose&quot;, version.ref = &quot;lifecycleViewmodelComposeVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="lifecycle-viewmodel-compose = { module = &quot;androidx.lifecycle:lifecycle-viewmodel-compose&quot;, version.ref = &quot;lifecycleViewmodelComposeVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="1517"
                    endOffset="1517"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.lifecycle.viewmodel.compose"
                robot="true"
                replacement="libs.lifecycle.viewmodel.compose"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="4499"
                    endOffset="4553"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="138"
            column="20"
            startOffset="4499"
            endLine="138"
            endColumn="74"
            endOffset="4553"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for lifecycle-livedata-ktx"
            robot="true">
            <fix-replace
                description="Replace with lifecycleLivedataKtxVersion = &quot;2.7.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="lifecycleLivedataKtxVersion = &quot;2.7.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="126"
                    endOffset="126"/>
            </fix-replace>
            <fix-replace
                description="Replace with lifecycle-livedata-ktx = { module = &quot;androidx.lifecycle:lifecycle-livedata-ktx&quot;, version.ref = &quot;lifecycleLivedataKtxVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="lifecycle-livedata-ktx = { module = &quot;androidx.lifecycle:lifecycle-livedata-ktx&quot;, version.ref = &quot;lifecycleLivedataKtxVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="1517"
                    endOffset="1517"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.lifecycle.livedata.ktx"
                robot="true"
                replacement="libs.lifecycle.livedata.ktx"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="4574"
                    endOffset="4623"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="139"
            column="20"
            startOffset="4574"
            endLine="139"
            endColumn="69"
            endOffset="4623"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for google-accompanist-permissions"
            robot="true">
            <fix-replace
                description="Replace with accompanistPermissionsVersion = &quot;0.32.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="accompanistPermissionsVersion = &quot;0.32.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="11"
                    endOffset="11"/>
            </fix-replace>
            <fix-replace
                description="Replace with google-accompanist-permissions = { module = &quot;com.google.accompanist:accompanist-permissions&quot;, version.ref = &quot;accompanistPermissionsVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="google-accompanist-permissions = { module = &quot;com.google.accompanist:accompanist-permissions&quot;, version.ref = &quot;accompanistPermissionsVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="314"
                    endOffset="314"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.google.accompanist.permissions"
                robot="true"
                replacement="libs.google.accompanist.permissions"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="4657"
                    endOffset="4712"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="142"
            column="20"
            startOffset="4657"
            endLine="142"
            endColumn="75"
            endOffset="4712"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for media3-exoplayer"
            robot="true">
            <fix-replace
                description="Replace with media3ExoplayerVersion = &quot;1.2.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="media3ExoplayerVersion = &quot;1.2.1&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="209"
                    endOffset="209"/>
            </fix-replace>
            <fix-replace
                description="Replace with media3-exoplayer = { module = &quot;androidx.media3:media3-exoplayer&quot;, version.ref = &quot;media3ExoplayerVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="media3-exoplayer = { module = &quot;androidx.media3:media3-exoplayer&quot;, version.ref = &quot;media3ExoplayerVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="1517"
                    endOffset="1517"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.media3.exoplayer"
                robot="true"
                replacement="libs.media3.exoplayer"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="4863"
                    endOffset="4903"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="148"
            column="20"
            startOffset="4863"
            endLine="148"
            endColumn="60"
            endOffset="4903"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for media3-ui"
            robot="true">
            <fix-replace
                description="Replace with media3UiVersion = &quot;1.2.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="media3UiVersion = &quot;1.2.1&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="209"
                    endOffset="209"/>
            </fix-replace>
            <fix-replace
                description="Replace with media3-ui = { module = &quot;androidx.media3:media3-ui&quot;, version.ref = &quot;media3UiVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="media3-ui = { module = &quot;androidx.media3:media3-ui&quot;, version.ref = &quot;media3UiVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="1517"
                    endOffset="1517"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.media3.ui"
                robot="true"
                replacement="libs.media3.ui"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="4924"
                    endOffset="4957"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="149"
            column="20"
            startOffset="4924"
            endLine="149"
            endColumn="53"
            endOffset="4957"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for media3-common"
            robot="true">
            <fix-replace
                description="Replace with media3CommonVersion = &quot;1.2.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="media3CommonVersion = &quot;1.2.1&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="209"
                    endOffset="209"/>
            </fix-replace>
            <fix-replace
                description="Replace with media3-common = { module = &quot;androidx.media3:media3-common&quot;, version.ref = &quot;media3CommonVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="media3-common = { module = &quot;androidx.media3:media3-common&quot;, version.ref = &quot;media3CommonVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="1517"
                    endOffset="1517"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.media3.common"
                robot="true"
                replacement="libs.media3.common"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="4978"
                    endOffset="5015"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="150"
            column="20"
            startOffset="4978"
            endLine="150"
            endColumn="57"
            endOffset="5015"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for media3-session"
            robot="true">
            <fix-replace
                description="Replace with media3SessionVersion = &quot;1.2.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="media3SessionVersion = &quot;1.2.1&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="209"
                    endOffset="209"/>
            </fix-replace>
            <fix-replace
                description="Replace with media3-session = { module = &quot;androidx.media3:media3-session&quot;, version.ref = &quot;media3SessionVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="media3-session = { module = &quot;androidx.media3:media3-session&quot;, version.ref = &quot;media3SessionVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="1517"
                    endOffset="1517"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.media3.session"
                robot="true"
                replacement="libs.media3.session"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="5036"
                    endOffset="5074"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="151"
            column="20"
            startOffset="5036"
            endLine="151"
            endColumn="58"
            endOffset="5074"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for coil-kt-coil-compose"
            robot="true">
            <fix-replace
                description="Replace with coilComposeVersion = &quot;2.5.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="coilComposeVersion = &quot;2.5.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with coil-kt-coil-compose = { module = &quot;io.coil-kt:coil-compose&quot;, version.ref = &quot;coilComposeVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="coil-kt-coil-compose = { module = &quot;io.coil-kt:coil-compose&quot;, version.ref = &quot;coilComposeVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                    startOffset="314"
                    endOffset="314"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.coil.kt.coil.compose"
                robot="true"
                replacement="libs.coil.kt.coil.compose"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="5108"
                    endOffset="5139"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="154"
            column="20"
            startOffset="5108"
            endLine="154"
            endColumn="51"
            endOffset="5139"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead (androidx.activity:activity-compose is already available as `androidx-activity-compose`, but using version 1.10.1 instead)">
        <fix-alternatives>
            <fix-composite
                description="Replace with new library catalog declaration for activity-compose-v182"
                robot="true"
                independent="true">
                <fix-replace
                    description="Replace with androidxActivityCompose = &quot;1.8.2&quot;..."
                    robot="true"
                    independent="true"
                    oldString="_lint_insert_begin_"
                    replacement="androidxActivityCompose = &quot;1.8.2&quot;&#xA;"
                    priority="0">
                    <range
                        file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                        startOffset="26"
                        endOffset="26"/>
                </fix-replace>
                <fix-replace
                    description="Replace with activity-compose-v182 = { module = &quot;androidx.activity:activity-compose&quot;, version.ref = &quot;androidxActivityCompose&quot; }..."
                    robot="true"
                    independent="true"
                    oldString="_lint_insert_begin_"
                    replacement="activity-compose-v182 = { module = &quot;androidx.activity:activity-compose&quot;, version.ref = &quot;androidxActivityCompose&quot; }&#xA;"
                    priority="0">
                    <range
                        file="$HOME/AndroidStudioProjects/IotAndroidV20/gradle/libs.versions.toml"
                        startOffset="222"
                        endOffset="222"/>
                </fix-replace>
                <fix-replace
                    description="Replace with libs.activity.compose.v182"
                    robot="true"
                    independent="true"
                    replacement="libs.activity.compose.v182"
                    priority="0">
                    <range
                        file="${:app*projectDir}/build.gradle.kts"
                        startOffset="5174"
                        endOffset="5216"/>
                </fix-replace>
            </fix-composite>
            <fix-replace
                description="Replace with existing version catalog reference `androidx-activity-compose` (version 1.10.1)"
                replacement="libs.androidx.activity.compose"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="5174"
                    endOffset="5216"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="157"
            column="20"
            startOffset="5174"
            endLine="157"
            endColumn="62"
            endOffset="5216"/>
    </incident>

</incidents>
