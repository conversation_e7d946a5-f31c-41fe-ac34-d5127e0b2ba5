<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>Lint Report</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
 <link rel="stylesheet" href="https://code.getmdl.io/1.2.1/material.blue-indigo.min.css" />
<link rel="stylesheet" href="http://fonts.googleapis.com/css?family=Roboto:300,400,500,700" type="text/css">
<script defer src="https://code.getmdl.io/1.2.0/material.min.js"></script>
<style>
section.section--center {
    max-width: 860px;
}
.mdl-card__supporting-text + .mdl-card__actions {
    border-top: 1px solid rgba(0, 0, 0, 0.12);
}
main > .mdl-layout__tab-panel {
  padding: 8px;
  padding-top: 48px;
}

.mdl-card__actions {
    margin: 0;
    padding: 4px 40px;
    color: inherit;
}
.mdl-card > * {
    height: auto;
}
.mdl-card__actions a {
    color: #00BCD4;
    margin: 0;
}
.error-icon {
    color: #bb7777;
    vertical-align: bottom;
}
.warning-icon {
    vertical-align: bottom;
}
.mdl-layout__content section:not(:last-of-type) {
  position: relative;
  margin-bottom: 48px;
}

.mdl-card .mdl-card__supporting-text {
  margin: 40px;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  padding: 0;
  color: inherit;
  width: calc(100% - 80px);
}
div.mdl-layout__drawer-button .material-icons {
    line-height: 48px;
}
.mdl-card .mdl-card__supporting-text {
    margin-top: 0px;
}
.chips {
    float: right;
    vertical-align: middle;
}

pre.errorlines {
    background-color: white;
    font-family: monospace;
    border: 1px solid #e0e0e0;
    line-height: 0.9rem;
    font-size: 0.9rem;    padding: 1px 0px 1px; 1px;
    overflow: scroll;
}
.prefix {
    color: #660e7a;
    font-weight: bold;
}
.attribute {
    color: #0000ff;
    font-weight: bold;
}
.value {
    color: #008000;
    font-weight: bold;
}
.tag {
    color: #000080;
    font-weight: bold;
}
.comment {
    color: #808080;
    font-style: italic;
}
.javadoc {
    color: #808080;
    font-style: italic;
}
.annotation {
    color: #808000;
}
.string {
    color: #008000;
    font-weight: bold;
}
.number {
    color: #0000ff;
}
.keyword {
    color: #000080;
    font-weight: bold;
}
.caretline {
    background-color: #fffae3;
}
.lineno {
    color: #999999;
    background-color: #f0f0f0;
}
.error {
    display: inline-block;
    position:relative;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AwCFR4T/3uLMgAAADxJREFUCNdNyLERQEAABMCjL4lQwIzcjErpguAL+C9AvgKJDbeD/PRpLdm35Hm+MU+cB+tCKaJW4L4YBy+CAiLJrFs9mgAAAABJRU5ErkJggg==) bottom repeat-x;
}
.warning {
    text-decoration: none;
    background-color: #f6ebbc;
}
.overview {
    padding: 10pt;
    width: 100%;
    overflow: auto;
    border-collapse:collapse;
}
.overview tr {
    border-bottom: solid 1px #eeeeee;
}
.categoryColumn a {
     text-decoration: none;
     color: inherit;
}
.countColumn {
    text-align: right;
    padding-right: 20px;
    width: 50px;
}
.issueColumn {
   padding-left: 16px;
}
.categoryColumn {
   position: relative;
   left: -50px;
   padding-top: 20px;
   padding-bottom: 5px;
}
.options {
   padding-left: 16px;
}
</style>
<script language="javascript" type="text/javascript">
<!--
function reveal(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'block';
document.getElementById(id+'Link').style.display = 'none';
}
}
function hideid(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'none';
}
}
//-->
</script>
</head>
<body class="mdl-color--grey-100 mdl-color-text--grey-700 mdl-base">
<div class="mdl-layout mdl-js-layout mdl-layout--fixed-header">
  <header class="mdl-layout__header">
    <div class="mdl-layout__header-row">
      <span class="mdl-layout-title">Lint Report: 15 errors, 195 warnings and 10 hints</span>
      <div class="mdl-layout-spacer"></div>
      <nav class="mdl-navigation mdl-layout--large-screen-only">Check performed at Sat Jul 05 17:54:48 CST 2025 by AGP (8.10.0)</nav>
    </div>
  </header>
  <div class="mdl-layout__drawer">
    <span class="mdl-layout-title">Issue Types</span>
    <nav class="mdl-navigation">
      <a class="mdl-navigation__link" href="#overview"><i class="material-icons">dashboard</i>Overview</a>
      <a class="mdl-navigation__link" href="#MissingClass"><i class="material-icons error-icon">error</i>Missing registered class (1)</a>
      <a class="mdl-navigation__link" href="#DefaultLocale"><i class="material-icons warning-icon">warning</i>Implied default locale in case conversion (110)</a>
      <a class="mdl-navigation__link" href="#OldTargetApi"><i class="material-icons warning-icon">warning</i>Target SDK attribute is not targeting latest version (1)</a>
      <a class="mdl-navigation__link" href="#SimpleDateFormat"><i class="material-icons warning-icon">warning</i>Implied locale in date format (9)</a>
      <a class="mdl-navigation__link" href="#RedundantLabel"><i class="material-icons warning-icon">warning</i>Redundant label on activity (1)</a>
      <a class="mdl-navigation__link" href="#GradleDependency"><i class="material-icons warning-icon">warning</i>Obsolete Gradle Dependency (13)</a>
      <a class="mdl-navigation__link" href="#UnsafeOptInUsageError"><i class="material-icons error-icon">error</i>Unsafe opt-in usage intended to be error-level severity (14)</a>
      <a class="mdl-navigation__link" href="#ModifierParameter"><i class="material-icons warning-icon">warning</i>Guidelines for Modifier parameters in a Composable function (12)</a>
      <a class="mdl-navigation__link" href="#TrustAllX509TrustManager"><i class="material-icons warning-icon">warning</i>Insecure TLS/SSL trust manager (2)</a>
      <a class="mdl-navigation__link" href="#AutoboxingStateCreation"><i class="material-icons warning-icon">warning</i><code>State&lt;T></code> will autobox values assigned to this state. Use a specialized state type instead. (10)</a>
      <a class="mdl-navigation__link" href="#UnusedResources"><i class="material-icons warning-icon">warning</i>Unused resources (7)</a>
      <a class="mdl-navigation__link" href="#UseKtx"><i class="material-icons warning-icon">warning</i>Use KTX extension function (7)</a>
      <a class="mdl-navigation__link" href="#UseTomlInstead"><i class="material-icons warning-icon">warning</i>Use TOML Version Catalog Instead (33)</a>
    </nav>
  </div>
  <main class="mdl-layout__content">
    <div class="mdl-layout__tab-panel is-active">
<a name="overview"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OverviewCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Overview</h2>
  </div>
              <div class="mdl-card__supporting-text">
<table class="overview">
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Correctness">Correctness</a>
</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#MissingClass">MissingClass</a>: Missing registered class</td></tr>
<tr>
<td class="countColumn">110</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#DefaultLocale">DefaultLocale</a>: Implied default locale in case conversion</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#OldTargetApi">OldTargetApi</a>: Target SDK attribute is not targeting latest version</td></tr>
<tr>
<td class="countColumn">9</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#SimpleDateFormat">SimpleDateFormat</a>: Implied locale in date format</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#RedundantLabel">RedundantLabel</a>: Redundant label on activity</td></tr>
<tr>
<td class="countColumn">13</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#GradleDependency">GradleDependency</a>: Obsolete Gradle Dependency</td></tr>
<tr>
<td class="countColumn">14</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#UnsafeOptInUsageError">UnsafeOptInUsageError</a>: Unsafe opt-in usage intended to be error-level severity</td></tr>
<tr>
<td class="countColumn">12</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ModifierParameter">ModifierParameter</a>: Guidelines for Modifier parameters in a Composable function</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Security">Security</a>
</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#TrustAllX509TrustManager">TrustAllX509TrustManager</a>: Insecure TLS/SSL trust manager</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Performance">Performance</a>
</td></tr>
<tr>
<td class="countColumn">10</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#AutoboxingStateCreation">AutoboxingStateCreation</a>: <code>State&lt;T></code> will autobox values assigned to this state. Use a specialized state type instead.</td></tr>
<tr>
<td class="countColumn">7</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UnusedResources">UnusedResources</a>: Unused resources</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Productivity">Productivity</a>
</td></tr>
<tr>
<td class="countColumn">7</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UseKtx">UseKtx</a>: Use KTX extension function</td></tr>
<tr>
<td class="countColumn">33</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UseTomlInstead">UseTomlInstead</a>: Use TOML Version Catalog Instead</td></tr>
<tr><td></td><td class="categoryColumn"><a href="#ExtraIssues">Included Additional Checks (63)</a>
</td></tr>
<tr><td></td><td class="categoryColumn"><a href="#MissingIssues">Disabled Checks (41)</a>
</td></tr>
</table>
<br/>              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OverviewCardLink" onclick="hideid('OverviewCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Correctness"></a>
<a name="MissingClass"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="MissingClassCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Missing registered class</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:40</span>: <span class="message">Class referenced in the manifest, <code>org.eclipse.paho.android.service.MqttService</code>, was not found in the project or the libraries</span><br /><pre class="errorlines">
<span class="lineno"> 37 </span>        <span class="tag">&lt;/activity></span>
<span class="lineno"> 38 </span>
<span class="lineno"> 39 </span>        <span class="comment">&lt;!-- MQTT服务 --></span>
<span class="caretline"><span class="lineno"> 40 </span>        <span class="tag">&lt;service</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"</span><span class="error"><span class="value">org.eclipse.paho.android.service.MqttService</span></span><span class="value">"</span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 41 </span>    <span class="tag">&lt;/application></span>
<span class="lineno"> 42 </span>
<span class="lineno"> 43 </span><span class="tag">&lt;/manifest></span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationMissingClass" style="display: none;">
If a class is referenced in the manifest or in a layout file, it must also exist in the project (or in one of the libraries included by the project. This check helps uncover typos in registration names, or attempts to rename or move classes without updating the XML references properly.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/manifest/manifest-intro.html">https://developer.android.com/guide/topics/manifest/manifest-intro.html</a>
</div>To suppress this error, use the issue id "MissingClass" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">MissingClass</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 8/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationMissingClassLink" onclick="reveal('explanationMissingClass');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="MissingClassCardLink" onclick="hideid('MissingClassCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="DefaultLocale"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="DefaultLocaleCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Implied default locale in case conversion</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt">../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt</a>:233</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 230 </span>            horizontalAlignment = Alignment.CenterHorizontally
<span class="lineno"> 231 </span>        ) {
<span class="lineno"> 232 </span>            Text(
<span class="caretline"><span class="lineno"> 233 </span>                text = <span class="warning">String.format(<span class="string">"%.1f"</span>, fatigueIndex)</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 234 </span>                fontSize = <span class="number">20.</span>sp,
<span class="lineno"> 235 </span>                fontWeight = FontWeight.Bold,
<span class="lineno"> 236 </span>                color = getFatigueTypeColor(fatigueType)
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt">../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt</a>:274</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 271 </span>            horizontalAlignment = Alignment.CenterHorizontally
<span class="lineno"> 272 </span>        ) {
<span class="lineno"> 273 </span>            Text(
<span class="caretline"><span class="lineno"> 274 </span>                text = <span class="warning">String.format(<span class="string">"%.0f"</span>, alertnessLevel * <span class="number">100</span>)</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 275 </span>                fontSize = <span class="number">20.</span>sp,
<span class="lineno"> 276 </span>                fontWeight = FontWeight.Bold,
<span class="lineno"> 277 </span>                color = getAlertnessColor(alertnessLevel)
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt">../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt</a>:520</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 517 </span>      modifier = Modifier.fillMaxWidth(),
<span class="lineno"> 518 </span>      horizontalArrangement = Arrangement.SpaceEvenly
<span class="lineno"> 519 </span>  ) {
<span class="caretline"><span class="lineno"> 520 </span>      StatisticItem(<span class="string">"平均疲劳"</span>, <span class="warning">String.format(<span class="string">"%.1f"</span>, statistics.averageFatigue)</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 521 </span>      StatisticItem(<span class="string">"最高疲劳"</span>, String.format(<span class="string">"%.1f"</span>, statistics.maxFatigue))
<span class="lineno"> 522 </span>      StatisticItem(<span class="string">"平均警觉"</span>, String.format(<span class="string">"%.0f%%"</span>, statistics.averageAlertness * <span class="number">100</span>))
<span class="lineno"> 523 </span>      StatisticItem(<span class="string">"趋势"</span>, statistics.getTrendDescription())
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt">../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt</a>:521</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 518 </span>      horizontalArrangement = Arrangement.SpaceEvenly
<span class="lineno"> 519 </span>  ) {
<span class="lineno"> 520 </span>      StatisticItem(<span class="string">"平均疲劳"</span>, String.format(<span class="string">"%.1f"</span>, statistics.averageFatigue))
<span class="caretline"><span class="lineno"> 521 </span>      StatisticItem(<span class="string">"最高疲劳"</span>, <span class="warning">String.format(<span class="string">"%.1f"</span>, statistics.maxFatigue)</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 522 </span>      StatisticItem(<span class="string">"平均警觉"</span>, String.format(<span class="string">"%.0f%%"</span>, statistics.averageAlertness * <span class="number">100</span>))
<span class="lineno"> 523 </span>      StatisticItem(<span class="string">"趋势"</span>, statistics.getTrendDescription())
<span class="lineno"> 524 </span>  }
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt">../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt</a>:522</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 519 </span>  ) {
<span class="lineno"> 520 </span>      StatisticItem(<span class="string">"平均疲劳"</span>, String.format(<span class="string">"%.1f"</span>, statistics.averageFatigue))
<span class="lineno"> 521 </span>      StatisticItem(<span class="string">"最高疲劳"</span>, String.format(<span class="string">"%.1f"</span>, statistics.maxFatigue))
<span class="caretline"><span class="lineno"> 522 </span>      StatisticItem(<span class="string">"平均警觉"</span>, <span class="warning">String.format(<span class="string">"%.0f%%"</span>, statistics.averageAlertness * <span class="number">100</span>)</span>)&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 523 </span>      StatisticItem(<span class="string">"趋势"</span>, statistics.getTrendDescription())
<span class="lineno"> 524 </span>  }
<span class="lineno"> 525 </span>  
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="DefaultLocaleDivLink" onclick="reveal('DefaultLocaleDiv');" />+ 105 More Occurrences...</button>
<div id="DefaultLocaleDiv" style="display: none">
<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFocusDisplay.kt">../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFocusDisplay.kt</a>:206</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 203 </span>            horizontalAlignment = Alignment.CenterHorizontally
<span class="lineno"> 204 </span>        ) {
<span class="lineno"> 205 </span>            Text(
<span class="caretline"><span class="lineno"> 206 </span>                text = <span class="warning">String.format(<span class="string">"%.1f"</span>, focusIndex)</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 207 </span>                fontSize = <span class="number">24.</span>sp,
<span class="lineno"> 208 </span>                fontWeight = FontWeight.Bold,
<span class="lineno"> 209 </span>                color = getFocusTypeColor(focusType)
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFocusDisplay.kt">../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFocusDisplay.kt</a>:400</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 397 </span>                modifier = Modifier.fillMaxWidth(),
<span class="lineno"> 398 </span>                horizontalArrangement = Arrangement.SpaceEvenly
<span class="lineno"> 399 </span>            ) {
<span class="caretline"><span class="lineno"> 400 </span>                StatisticItem(<span class="string">"平均"</span>, <span class="warning">String.format(<span class="string">"%.1f"</span>, statistics.averageFocus)</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 401 </span>                StatisticItem(<span class="string">"最高"</span>, String.format(<span class="string">"%.1f"</span>, statistics.maxFocus))
<span class="lineno"> 402 </span>                StatisticItem(<span class="string">"稳定性"</span>, statistics.getStabilityDescription())
<span class="lineno"> 403 </span>                StatisticItem(<span class="string">"趋势"</span>, statistics.getTrendDescription())
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFocusDisplay.kt">../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFocusDisplay.kt</a>:401</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 398 </span>                horizontalArrangement = Arrangement.SpaceEvenly
<span class="lineno"> 399 </span>            ) {
<span class="lineno"> 400 </span>                StatisticItem(<span class="string">"平均"</span>, String.format(<span class="string">"%.1f"</span>, statistics.averageFocus))
<span class="caretline"><span class="lineno"> 401 </span>                StatisticItem(<span class="string">"最高"</span>, <span class="warning">String.format(<span class="string">"%.1f"</span>, statistics.maxFocus)</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 402 </span>                StatisticItem(<span class="string">"稳定性"</span>, statistics.getStabilityDescription())
<span class="lineno"> 403 </span>                StatisticItem(<span class="string">"趋势"</span>, statistics.getTrendDescription())
<span class="lineno"> 404 </span>            }
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/player/AudioPlayerScreen.kt">../../src/main/java/com/example/iotandroidv20/ui/player/AudioPlayerScreen.kt</a>:506</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 503 </span>    <span class="keyword">val</span> seconds = totalSeconds % <span class="number">60</span>
<span class="lineno"> 504 </span>    
<span class="lineno"> 505 </span>    <span class="keyword">return</span> <span class="keyword">if</span> (hours > <span class="number">0</span>) {
<span class="caretline"><span class="lineno"> 506 </span>        <span class="warning">String.format(<span class="string">"%02d:%02d:%02d"</span>, hours, minutes, seconds)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 507 </span>    } <span class="keyword">else</span> {
<span class="lineno"> 508 </span>        String.format(<span class="string">"%02d:%02d"</span>, minutes, seconds)
<span class="lineno"> 509 </span>    }
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/player/AudioPlayerScreen.kt">../../src/main/java/com/example/iotandroidv20/ui/player/AudioPlayerScreen.kt</a>:508</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 505 </span>    <span class="keyword">return</span> <span class="keyword">if</span> (hours > <span class="number">0</span>) {
<span class="lineno"> 506 </span>        String.format(<span class="string">"%02d:%02d:%02d"</span>, hours, minutes, seconds)
<span class="lineno"> 507 </span>    } <span class="keyword">else</span> {
<span class="caretline"><span class="lineno"> 508 </span>        <span class="warning">String.format(<span class="string">"%02d:%02d"</span>, minutes, seconds)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 509 </span>    }
<span class="lineno"> 510 </span>}
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/ComprehensiveHealthDashboard.kt">../../src/main/java/com/example/iotandroidv20/ui/components/ComprehensiveHealthDashboard.kt</a>:416</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 413 </span>                    horizontalAlignment = Alignment.CenterHorizontally
<span class="lineno"> 414 </span>                ) {
<span class="lineno"> 415 </span>                    Text(
<span class="caretline"><span class="lineno"> 416 </span>                        text = <span class="warning">String.format(<span class="string">"%.0f"</span>, animatedScore)</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 417 </span>                        fontSize = <span class="number">28.</span>sp,
<span class="lineno"> 418 </span>                        fontWeight = FontWeight.Bold,
<span class="lineno"> 419 </span>                        color = getHealthLevelColor(healthLevel)
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/model/EEGData.kt">../../src/main/java/com/example/iotandroidv20/model/EEGData.kt</a>:482</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 479 </span><span class="javadoc">     */</span>
<span class="lineno"> 480 </span>    <span class="keyword">fun</span> getDetailedReport(): Map&lt;String, String> {
<span class="lineno"> 481 </span>        <span class="keyword">return</span> mapOf(
<span class="caretline"><span class="lineno"> 482 </span>            <span class="string">"专注度指数"</span> to <span class="warning">String.format(<span class="string">"%.1f/10"</span>, focusIndex)</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 483 </span>            <span class="string">"专注度类型"</span> to focusType.displayName,
<span class="lineno"> 484 </span>            <span class="string">"置信度"</span> to String.format(<span class="string">"%.1f%%"</span>, confidence * <span class="number">100</span>),
<span class="lineno"> 485 </span>            <span class="string">"Beta/Alpha比值"</span> to String.format(<span class="string">"%.2f"</span>, betaAlphaRatio),
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/model/EEGData.kt">../../src/main/java/com/example/iotandroidv20/model/EEGData.kt</a>:484</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 481 </span>        <span class="keyword">return</span> mapOf(
<span class="lineno"> 482 </span>            <span class="string">"专注度指数"</span> to String.format(<span class="string">"%.1f/10"</span>, focusIndex),
<span class="lineno"> 483 </span>            <span class="string">"专注度类型"</span> to focusType.displayName,
<span class="caretline"><span class="lineno"> 484 </span>            <span class="string">"置信度"</span> to <span class="warning">String.format(<span class="string">"%.1f%%"</span>, confidence * <span class="number">100</span>)</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 485 </span>            <span class="string">"Beta/Alpha比值"</span> to String.format(<span class="string">"%.2f"</span>, betaAlphaRatio),
<span class="lineno"> 486 </span>            <span class="string">"Theta抑制"</span> to String.format(<span class="string">"%.1f%%"</span>, thetaSuppressionIndex * <span class="number">100</span>),
<span class="lineno"> 487 </span>            <span class="string">"Gamma参与度"</span> to String.format(<span class="string">"%.1f%%"</span>, gammaEngagementIndex * <span class="number">100</span>),
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/model/EEGData.kt">../../src/main/java/com/example/iotandroidv20/model/EEGData.kt</a>:485</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 482 </span>            <span class="string">"专注度指数"</span> to String.format(<span class="string">"%.1f/10"</span>, focusIndex),
<span class="lineno"> 483 </span>            <span class="string">"专注度类型"</span> to focusType.displayName,
<span class="lineno"> 484 </span>            <span class="string">"置信度"</span> to String.format(<span class="string">"%.1f%%"</span>, confidence * <span class="number">100</span>),
<span class="caretline"><span class="lineno"> 485 </span>            <span class="string">"Beta/Alpha比值"</span> to <span class="warning">String.format(<span class="string">"%.2f"</span>, betaAlphaRatio)</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 486 </span>            <span class="string">"Theta抑制"</span> to String.format(<span class="string">"%.1f%%"</span>, thetaSuppressionIndex * <span class="number">100</span>),
<span class="lineno"> 487 </span>            <span class="string">"Gamma参与度"</span> to String.format(<span class="string">"%.1f%%"</span>, gammaEngagementIndex * <span class="number">100</span>),
<span class="lineno"> 488 </span>            <span class="string">"频谱一致性"</span> to String.format(<span class="string">"%.1f%%"</span>, spectralCoherenceIndex * <span class="number">100</span>),
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/model/EEGData.kt">../../src/main/java/com/example/iotandroidv20/model/EEGData.kt</a>:486</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 483 </span>            <span class="string">"专注度类型"</span> to focusType.displayName,
<span class="lineno"> 484 </span>            <span class="string">"置信度"</span> to String.format(<span class="string">"%.1f%%"</span>, confidence * <span class="number">100</span>),
<span class="lineno"> 485 </span>            <span class="string">"Beta/Alpha比值"</span> to String.format(<span class="string">"%.2f"</span>, betaAlphaRatio),
<span class="caretline"><span class="lineno"> 486 </span>            <span class="string">"Theta抑制"</span> to <span class="warning">String.format(<span class="string">"%.1f%%"</span>, thetaSuppressionIndex * <span class="number">100</span>)</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 487 </span>            <span class="string">"Gamma参与度"</span> to String.format(<span class="string">"%.1f%%"</span>, gammaEngagementIndex * <span class="number">100</span>),
<span class="lineno"> 488 </span>            <span class="string">"频谱一致性"</span> to String.format(<span class="string">"%.1f%%"</span>, spectralCoherenceIndex * <span class="number">100</span>),
<span class="lineno"> 489 </span>            <span class="string">"稳定性"</span> to String.format(<span class="string">"%.1f%%"</span>, stabilityIndex * <span class="number">100</span>),
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/model/EEGData.kt">../../src/main/java/com/example/iotandroidv20/model/EEGData.kt</a>:487</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 484 </span>            <span class="string">"置信度"</span> to String.format(<span class="string">"%.1f%%"</span>, confidence * <span class="number">100</span>),
<span class="lineno"> 485 </span>            <span class="string">"Beta/Alpha比值"</span> to String.format(<span class="string">"%.2f"</span>, betaAlphaRatio),
<span class="lineno"> 486 </span>            <span class="string">"Theta抑制"</span> to String.format(<span class="string">"%.1f%%"</span>, thetaSuppressionIndex * <span class="number">100</span>),
<span class="caretline"><span class="lineno"> 487 </span>            <span class="string">"Gamma参与度"</span> to <span class="warning">String.format(<span class="string">"%.1f%%"</span>, gammaEngagementIndex * <span class="number">100</span>)</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 488 </span>            <span class="string">"频谱一致性"</span> to String.format(<span class="string">"%.1f%%"</span>, spectralCoherenceIndex * <span class="number">100</span>),
<span class="lineno"> 489 </span>            <span class="string">"稳定性"</span> to String.format(<span class="string">"%.1f%%"</span>, stabilityIndex * <span class="number">100</span>),
<span class="lineno"> 490 </span>            <span class="string">"年龄调整"</span> to String.format(<span class="string">"%.2f"</span>, ageAdjustmentFactor),
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/model/EEGData.kt">../../src/main/java/com/example/iotandroidv20/model/EEGData.kt</a>:488</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 485 </span>            <span class="string">"Beta/Alpha比值"</span> to String.format(<span class="string">"%.2f"</span>, betaAlphaRatio),
<span class="lineno"> 486 </span>            <span class="string">"Theta抑制"</span> to String.format(<span class="string">"%.1f%%"</span>, thetaSuppressionIndex * <span class="number">100</span>),
<span class="lineno"> 487 </span>            <span class="string">"Gamma参与度"</span> to String.format(<span class="string">"%.1f%%"</span>, gammaEngagementIndex * <span class="number">100</span>),
<span class="caretline"><span class="lineno"> 488 </span>            <span class="string">"频谱一致性"</span> to <span class="warning">String.format(<span class="string">"%.1f%%"</span>, spectralCoherenceIndex * <span class="number">100</span>)</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 489 </span>            <span class="string">"稳定性"</span> to String.format(<span class="string">"%.1f%%"</span>, stabilityIndex * <span class="number">100</span>),
<span class="lineno"> 490 </span>            <span class="string">"年龄调整"</span> to String.format(<span class="string">"%.2f"</span>, ageAdjustmentFactor),
<span class="lineno"> 491 </span>            <span class="string">"时长调整"</span> to String.format(<span class="string">"%.2f"</span>, durationAdjustment)
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/model/EEGData.kt">../../src/main/java/com/example/iotandroidv20/model/EEGData.kt</a>:489</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 486 </span>            <span class="string">"Theta抑制"</span> to String.format(<span class="string">"%.1f%%"</span>, thetaSuppressionIndex * <span class="number">100</span>),
<span class="lineno"> 487 </span>            <span class="string">"Gamma参与度"</span> to String.format(<span class="string">"%.1f%%"</span>, gammaEngagementIndex * <span class="number">100</span>),
<span class="lineno"> 488 </span>            <span class="string">"频谱一致性"</span> to String.format(<span class="string">"%.1f%%"</span>, spectralCoherenceIndex * <span class="number">100</span>),
<span class="caretline"><span class="lineno"> 489 </span>            <span class="string">"稳定性"</span> to <span class="warning">String.format(<span class="string">"%.1f%%"</span>, stabilityIndex * <span class="number">100</span>)</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 490 </span>            <span class="string">"年龄调整"</span> to String.format(<span class="string">"%.2f"</span>, ageAdjustmentFactor),
<span class="lineno"> 491 </span>            <span class="string">"时长调整"</span> to String.format(<span class="string">"%.2f"</span>, durationAdjustment)
<span class="lineno"> 492 </span>        )
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/model/EEGData.kt">../../src/main/java/com/example/iotandroidv20/model/EEGData.kt</a>:490</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 487 </span>            <span class="string">"Gamma参与度"</span> to String.format(<span class="string">"%.1f%%"</span>, gammaEngagementIndex * <span class="number">100</span>),
<span class="lineno"> 488 </span>            <span class="string">"频谱一致性"</span> to String.format(<span class="string">"%.1f%%"</span>, spectralCoherenceIndex * <span class="number">100</span>),
<span class="lineno"> 489 </span>            <span class="string">"稳定性"</span> to String.format(<span class="string">"%.1f%%"</span>, stabilityIndex * <span class="number">100</span>),
<span class="caretline"><span class="lineno"> 490 </span>            <span class="string">"年龄调整"</span> to <span class="warning">String.format(<span class="string">"%.2f"</span>, ageAdjustmentFactor)</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 491 </span>            <span class="string">"时长调整"</span> to String.format(<span class="string">"%.2f"</span>, durationAdjustment)
<span class="lineno"> 492 </span>        )
<span class="lineno"> 493 </span>    }
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/model/EEGData.kt">../../src/main/java/com/example/iotandroidv20/model/EEGData.kt</a>:491</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 488 </span>            <span class="string">"频谱一致性"</span> to String.format(<span class="string">"%.1f%%"</span>, spectralCoherenceIndex * <span class="number">100</span>),
<span class="lineno"> 489 </span>            <span class="string">"稳定性"</span> to String.format(<span class="string">"%.1f%%"</span>, stabilityIndex * <span class="number">100</span>),
<span class="lineno"> 490 </span>            <span class="string">"年龄调整"</span> to String.format(<span class="string">"%.2f"</span>, ageAdjustmentFactor),
<span class="caretline"><span class="lineno"> 491 </span>            <span class="string">"时长调整"</span> to <span class="warning">String.format(<span class="string">"%.2f"</span>, durationAdjustment)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 492 </span>        )
<span class="lineno"> 493 </span>    }
<span class="lineno"> 494 </span>}
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/model/EEGData.kt">../../src/main/java/com/example/iotandroidv20/model/EEGData.kt</a>:656</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 653 </span><span class="javadoc">     */</span>
<span class="lineno"> 654 </span>    <span class="keyword">fun</span> getDetailedReport(): Map&lt;String, String> {
<span class="lineno"> 655 </span>        <span class="keyword">return</span> mapOf(
<span class="caretline"><span class="lineno"> 656 </span>            <span class="string">"疲劳指数"</span> to <span class="warning">String.format(<span class="string">"%.1f/10"</span>, fatigueIndex)</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 657 </span>            <span class="string">"疲劳类型"</span> to fatigueType.displayName,
<span class="lineno"> 658 </span>            <span class="string">"警觉性水平"</span> to String.format(<span class="string">"%.1f%%"</span>, alertnessLevel * <span class="number">100</span>),
<span class="lineno"> 659 </span>            <span class="string">"置信度"</span> to String.format(<span class="string">"%.1f%%"</span>, confidence * <span class="number">100</span>),
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/model/EEGData.kt">../../src/main/java/com/example/iotandroidv20/model/EEGData.kt</a>:658</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 655 </span>        <span class="keyword">return</span> mapOf(
<span class="lineno"> 656 </span>            <span class="string">"疲劳指数"</span> to String.format(<span class="string">"%.1f/10"</span>, fatigueIndex),
<span class="lineno"> 657 </span>            <span class="string">"疲劳类型"</span> to fatigueType.displayName,
<span class="caretline"><span class="lineno"> 658 </span>            <span class="string">"警觉性水平"</span> to <span class="warning">String.format(<span class="string">"%.1f%%"</span>, alertnessLevel * <span class="number">100</span>)</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 659 </span>            <span class="string">"置信度"</span> to String.format(<span class="string">"%.1f%%"</span>, confidence * <span class="number">100</span>),
<span class="lineno"> 660 </span>            <span class="string">"Theta/Beta比值"</span> to String.format(<span class="string">"%.2f"</span>, thetaBetaRatio),
<span class="lineno"> 661 </span>            <span class="string">"Alpha波功率"</span> to String.format(<span class="string">"%.1f%%"</span>, alphaPowerIndex * <span class="number">100</span>),
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/model/EEGData.kt">../../src/main/java/com/example/iotandroidv20/model/EEGData.kt</a>:659</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 656 </span>            <span class="string">"疲劳指数"</span> to String.format(<span class="string">"%.1f/10"</span>, fatigueIndex),
<span class="lineno"> 657 </span>            <span class="string">"疲劳类型"</span> to fatigueType.displayName,
<span class="lineno"> 658 </span>            <span class="string">"警觉性水平"</span> to String.format(<span class="string">"%.1f%%"</span>, alertnessLevel * <span class="number">100</span>),
<span class="caretline"><span class="lineno"> 659 </span>            <span class="string">"置信度"</span> to <span class="warning">String.format(<span class="string">"%.1f%%"</span>, confidence * <span class="number">100</span>)</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 660 </span>            <span class="string">"Theta/Beta比值"</span> to String.format(<span class="string">"%.2f"</span>, thetaBetaRatio),
<span class="lineno"> 661 </span>            <span class="string">"Alpha波功率"</span> to String.format(<span class="string">"%.1f%%"</span>, alphaPowerIndex * <span class="number">100</span>),
<span class="lineno"> 662 </span>            <span class="string">"Delta波功率"</span> to String.format(<span class="string">"%.1f%%"</span>, deltaPowerIndex * <span class="number">100</span>),
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/model/EEGData.kt">../../src/main/java/com/example/iotandroidv20/model/EEGData.kt</a>:660</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 657 </span>            <span class="string">"疲劳类型"</span> to fatigueType.displayName,
<span class="lineno"> 658 </span>            <span class="string">"警觉性水平"</span> to String.format(<span class="string">"%.1f%%"</span>, alertnessLevel * <span class="number">100</span>),
<span class="lineno"> 659 </span>            <span class="string">"置信度"</span> to String.format(<span class="string">"%.1f%%"</span>, confidence * <span class="number">100</span>),
<span class="caretline"><span class="lineno"> 660 </span>            <span class="string">"Theta/Beta比值"</span> to <span class="warning">String.format(<span class="string">"%.2f"</span>, thetaBetaRatio)</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 661 </span>            <span class="string">"Alpha波功率"</span> to String.format(<span class="string">"%.1f%%"</span>, alphaPowerIndex * <span class="number">100</span>),
<span class="lineno"> 662 </span>            <span class="string">"Delta波功率"</span> to String.format(<span class="string">"%.1f%%"</span>, deltaPowerIndex * <span class="number">100</span>),
<span class="lineno"> 663 </span>            <span class="string">"眨眼频率"</span> to String.format(<span class="string">"%.1f%%"</span>, eyeBlinkIndex * <span class="number">100</span>),
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/model/EEGData.kt">../../src/main/java/com/example/iotandroidv20/model/EEGData.kt</a>:661</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 658 </span>            <span class="string">"警觉性水平"</span> to String.format(<span class="string">"%.1f%%"</span>, alertnessLevel * <span class="number">100</span>),
<span class="lineno"> 659 </span>            <span class="string">"置信度"</span> to String.format(<span class="string">"%.1f%%"</span>, confidence * <span class="number">100</span>),
<span class="lineno"> 660 </span>            <span class="string">"Theta/Beta比值"</span> to String.format(<span class="string">"%.2f"</span>, thetaBetaRatio),
<span class="caretline"><span class="lineno"> 661 </span>            <span class="string">"Alpha波功率"</span> to <span class="warning">String.format(<span class="string">"%.1f%%"</span>, alphaPowerIndex * <span class="number">100</span>)</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 662 </span>            <span class="string">"Delta波功率"</span> to String.format(<span class="string">"%.1f%%"</span>, deltaPowerIndex * <span class="number">100</span>),
<span class="lineno"> 663 </span>            <span class="string">"眨眼频率"</span> to String.format(<span class="string">"%.1f%%"</span>, eyeBlinkIndex * <span class="number">100</span>),
<span class="lineno"> 664 </span>            <span class="string">"反应时间"</span> to String.format(<span class="string">"%.1f%%"</span>, reactionTimeIndex * <span class="number">100</span>),
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/model/EEGData.kt">../../src/main/java/com/example/iotandroidv20/model/EEGData.kt</a>:662</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 659 </span>            <span class="string">"置信度"</span> to String.format(<span class="string">"%.1f%%"</span>, confidence * <span class="number">100</span>),
<span class="lineno"> 660 </span>            <span class="string">"Theta/Beta比值"</span> to String.format(<span class="string">"%.2f"</span>, thetaBetaRatio),
<span class="lineno"> 661 </span>            <span class="string">"Alpha波功率"</span> to String.format(<span class="string">"%.1f%%"</span>, alphaPowerIndex * <span class="number">100</span>),
<span class="caretline"><span class="lineno"> 662 </span>            <span class="string">"Delta波功率"</span> to <span class="warning">String.format(<span class="string">"%.1f%%"</span>, deltaPowerIndex * <span class="number">100</span>)</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 663 </span>            <span class="string">"眨眼频率"</span> to String.format(<span class="string">"%.1f%%"</span>, eyeBlinkIndex * <span class="number">100</span>),
<span class="lineno"> 664 </span>            <span class="string">"反应时间"</span> to String.format(<span class="string">"%.1f%%"</span>, reactionTimeIndex * <span class="number">100</span>),
<span class="lineno"> 665 </span>            <span class="string">"恢复时间"</span> to getRecoveryTimeDescription(),
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/model/EEGData.kt">../../src/main/java/com/example/iotandroidv20/model/EEGData.kt</a>:663</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 660 </span>            <span class="string">"Theta/Beta比值"</span> to String.format(<span class="string">"%.2f"</span>, thetaBetaRatio),
<span class="lineno"> 661 </span>            <span class="string">"Alpha波功率"</span> to String.format(<span class="string">"%.1f%%"</span>, alphaPowerIndex * <span class="number">100</span>),
<span class="lineno"> 662 </span>            <span class="string">"Delta波功率"</span> to String.format(<span class="string">"%.1f%%"</span>, deltaPowerIndex * <span class="number">100</span>),
<span class="caretline"><span class="lineno"> 663 </span>            <span class="string">"眨眼频率"</span> to <span class="warning">String.format(<span class="string">"%.1f%%"</span>, eyeBlinkIndex * <span class="number">100</span>)</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 664 </span>            <span class="string">"反应时间"</span> to String.format(<span class="string">"%.1f%%"</span>, reactionTimeIndex * <span class="number">100</span>),
<span class="lineno"> 665 </span>            <span class="string">"恢复时间"</span> to getRecoveryTimeDescription(),
<span class="lineno"> 666 </span>            <span class="string">"年龄调整"</span> to String.format(<span class="string">"%.2f"</span>, ageAdjustmentFactor),
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/model/EEGData.kt">../../src/main/java/com/example/iotandroidv20/model/EEGData.kt</a>:664</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 661 </span>            <span class="string">"Alpha波功率"</span> to String.format(<span class="string">"%.1f%%"</span>, alphaPowerIndex * <span class="number">100</span>),
<span class="lineno"> 662 </span>            <span class="string">"Delta波功率"</span> to String.format(<span class="string">"%.1f%%"</span>, deltaPowerIndex * <span class="number">100</span>),
<span class="lineno"> 663 </span>            <span class="string">"眨眼频率"</span> to String.format(<span class="string">"%.1f%%"</span>, eyeBlinkIndex * <span class="number">100</span>),
<span class="caretline"><span class="lineno"> 664 </span>            <span class="string">"反应时间"</span> to <span class="warning">String.format(<span class="string">"%.1f%%"</span>, reactionTimeIndex * <span class="number">100</span>)</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 665 </span>            <span class="string">"恢复时间"</span> to getRecoveryTimeDescription(),
<span class="lineno"> 666 </span>            <span class="string">"年龄调整"</span> to String.format(<span class="string">"%.2f"</span>, ageAdjustmentFactor),
<span class="lineno"> 667 </span>            <span class="string">"环境调整"</span> to String.format(<span class="string">"%.2f"</span>, environmentalAdjustment),
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/model/EEGData.kt">../../src/main/java/com/example/iotandroidv20/model/EEGData.kt</a>:666</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 663 </span>            <span class="string">"眨眼频率"</span> to String.format(<span class="string">"%.1f%%"</span>, eyeBlinkIndex * <span class="number">100</span>),
<span class="lineno"> 664 </span>            <span class="string">"反应时间"</span> to String.format(<span class="string">"%.1f%%"</span>, reactionTimeIndex * <span class="number">100</span>),
<span class="lineno"> 665 </span>            <span class="string">"恢复时间"</span> to getRecoveryTimeDescription(),
<span class="caretline"><span class="lineno"> 666 </span>            <span class="string">"年龄调整"</span> to <span class="warning">String.format(<span class="string">"%.2f"</span>, ageAdjustmentFactor)</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 667 </span>            <span class="string">"环境调整"</span> to String.format(<span class="string">"%.2f"</span>, environmentalAdjustment),
<span class="lineno"> 668 </span>            <span class="string">"时间调整"</span> to String.format(<span class="string">"%.2f"</span>, timeAdjustment)
<span class="lineno"> 669 </span>        )
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/model/EEGData.kt">../../src/main/java/com/example/iotandroidv20/model/EEGData.kt</a>:667</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 664 </span>            <span class="string">"反应时间"</span> to String.format(<span class="string">"%.1f%%"</span>, reactionTimeIndex * <span class="number">100</span>),
<span class="lineno"> 665 </span>            <span class="string">"恢复时间"</span> to getRecoveryTimeDescription(),
<span class="lineno"> 666 </span>            <span class="string">"年龄调整"</span> to String.format(<span class="string">"%.2f"</span>, ageAdjustmentFactor),
<span class="caretline"><span class="lineno"> 667 </span>            <span class="string">"环境调整"</span> to <span class="warning">String.format(<span class="string">"%.2f"</span>, environmentalAdjustment)</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 668 </span>            <span class="string">"时间调整"</span> to String.format(<span class="string">"%.2f"</span>, timeAdjustment)
<span class="lineno"> 669 </span>        )
<span class="lineno"> 670 </span>    }
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/model/EEGData.kt">../../src/main/java/com/example/iotandroidv20/model/EEGData.kt</a>:668</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 665 </span>            <span class="string">"恢复时间"</span> to getRecoveryTimeDescription(),
<span class="lineno"> 666 </span>            <span class="string">"年龄调整"</span> to String.format(<span class="string">"%.2f"</span>, ageAdjustmentFactor),
<span class="lineno"> 667 </span>            <span class="string">"环境调整"</span> to String.format(<span class="string">"%.2f"</span>, environmentalAdjustment),
<span class="caretline"><span class="lineno"> 668 </span>            <span class="string">"时间调整"</span> to <span class="warning">String.format(<span class="string">"%.2f"</span>, timeAdjustment)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 669 </span>        )
<span class="lineno"> 670 </span>    }
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/EEGSpectrumAnalyzer.kt">../../src/main/java/com/example/iotandroidv20/ui/components/EEGSpectrumAnalyzer.kt</a>:370</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 367 </span>        
<span class="lineno"> 368 </span>        <span class="comment">// 绘制数值标签</span>
<span class="lineno"> 369 </span>        drawContext.canvas.nativeCanvas.drawText(
<span class="caretline"><span class="lineno"> 370 </span>            <span class="warning">String.format(<span class="string">"%.1f"</span>, value)</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 371 </span>            x + barWidth / <span class="number">2</span>,
<span class="lineno"> 372 </span>            y - <span class="number">10.d</span>p.toPx(),
<span class="lineno"> 373 </span>            android.graphics.Paint().apply {
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/EEGSpectrumAnalyzer.kt">../../src/main/java/com/example/iotandroidv20/ui/components/EEGSpectrumAnalyzer.kt</a>:551</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 548 </span>      modifier = Modifier.fillMaxWidth(),
<span class="lineno"> 549 </span>      horizontalArrangement = Arrangement.SpaceEvenly
<span class="lineno"> 550 </span>  ) {
<span class="caretline"><span class="lineno"> 551 </span>      SpectrumStatItem(<span class="string">"主导频率"</span>, <span class="string">"${</span><span class="warning">String.format(<span class="string">"%.1f"</span>, frequencyAnalysis.dominantFrequency)</span><span class="string">}Hz"</span>)</span>
<span class="lineno"> 552 </span>      SpectrumStatItem(<span class="string">"总功率"</span>, String.format(<span class="string">"%.1f"</span>, frequencyAnalysis.totalPower))
<span class="lineno"> 553 </span>      SpectrumStatItem(<span class="string">"频谱熵"</span>, String.format(<span class="string">"%.3f"</span>, frequencyAnalysis.spectralEntropy))
<span class="lineno"> 554 </span>      SpectrumStatItem(<span class="string">"最强频段"</span>, frequencyAnalysis.getDominantWaveType().displayName)
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/EEGSpectrumAnalyzer.kt">../../src/main/java/com/example/iotandroidv20/ui/components/EEGSpectrumAnalyzer.kt</a>:552</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 549 </span>      horizontalArrangement = Arrangement.SpaceEvenly
<span class="lineno"> 550 </span>  ) {
<span class="lineno"> 551 </span>      SpectrumStatItem(<span class="string">"主导频率"</span>, <span class="string">"${</span>String.format(<span class="string">"%.1f"</span>, frequencyAnalysis.dominantFrequency)<span class="string">}Hz"</span>)
<span class="caretline"><span class="lineno"> 552 </span>      SpectrumStatItem(<span class="string">"总功率"</span>, <span class="warning">String.format(<span class="string">"%.1f"</span>, frequencyAnalysis.totalPower)</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 553 </span>      SpectrumStatItem(<span class="string">"频谱熵"</span>, String.format(<span class="string">"%.3f"</span>, frequencyAnalysis.spectralEntropy))
<span class="lineno"> 554 </span>      SpectrumStatItem(<span class="string">"最强频段"</span>, frequencyAnalysis.getDominantWaveType().displayName)
<span class="lineno"> 555 </span>  }
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/EEGSpectrumAnalyzer.kt">../../src/main/java/com/example/iotandroidv20/ui/components/EEGSpectrumAnalyzer.kt</a>:553</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 550 </span>      ) {
<span class="lineno"> 551 </span>          SpectrumStatItem(<span class="string">"主导频率"</span>, <span class="string">"${</span>String.format(<span class="string">"%.1f"</span>, frequencyAnalysis.dominantFrequency)<span class="string">}Hz"</span>)
<span class="lineno"> 552 </span>          SpectrumStatItem(<span class="string">"总功率"</span>, String.format(<span class="string">"%.1f"</span>, frequencyAnalysis.totalPower))
<span class="caretline"><span class="lineno"> 553 </span>          SpectrumStatItem(<span class="string">"频谱熵"</span>, <span class="warning">String.format(<span class="string">"%.3f"</span>, frequencyAnalysis.spectralEntropy)</span>)&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 554 </span>          SpectrumStatItem(<span class="string">"最强频段"</span>, frequencyAnalysis.getDominantWaveType().displayName)
<span class="lineno"> 555 </span>      }
<span class="lineno"> 556 </span>  }
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/EEGWaveformDisplay.kt">../../src/main/java/com/example/iotandroidv20/ui/components/EEGWaveformDisplay.kt</a>:513</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 510 </span>      modifier = Modifier.fillMaxWidth(),
<span class="lineno"> 511 </span>      horizontalArrangement = Arrangement.SpaceEvenly
<span class="lineno"> 512 </span>  ) {
<span class="caretline"><span class="lineno"> 513 </span>      StatItem(<span class="string">"主导频率"</span>, <span class="string">"${</span><span class="warning">String.format(<span class="string">"%.1f"</span>, eegData.dominantFrequency)</span><span class="string">}Hz"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 514 </span>      StatItem(<span class="string">"总功率"</span>, String.format(<span class="string">"%.1f"</span>, eegData.totalPower))
<span class="lineno"> 515 </span>      StatItem(<span class="string">"频谱熵"</span>, String.format(<span class="string">"%.3f"</span>, eegData.spectralEntropy))
<span class="lineno"> 516 </span>      StatItem(<span class="string">"采样时间"</span>, java.text.SimpleDateFormat(<span class="string">"HH:mm:ss"</span>).format(java.util.Date(eegData.timestamp)))
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/EEGWaveformDisplay.kt">../../src/main/java/com/example/iotandroidv20/ui/components/EEGWaveformDisplay.kt</a>:514</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 511 </span>      horizontalArrangement = Arrangement.SpaceEvenly
<span class="lineno"> 512 </span>  ) {
<span class="lineno"> 513 </span>      StatItem(<span class="string">"主导频率"</span>, <span class="string">"${</span>String.format(<span class="string">"%.1f"</span>, eegData.dominantFrequency)<span class="string">}Hz"</span>)
<span class="caretline"><span class="lineno"> 514 </span>      StatItem(<span class="string">"总功率"</span>, <span class="warning">String.format(<span class="string">"%.1f"</span>, eegData.totalPower)</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 515 </span>      StatItem(<span class="string">"频谱熵"</span>, String.format(<span class="string">"%.3f"</span>, eegData.spectralEntropy))
<span class="lineno"> 516 </span>      StatItem(<span class="string">"采样时间"</span>, java.text.SimpleDateFormat(<span class="string">"HH:mm:ss"</span>).format(java.util.Date(eegData.timestamp)))
<span class="lineno"> 517 </span>  }
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/EEGWaveformDisplay.kt">../../src/main/java/com/example/iotandroidv20/ui/components/EEGWaveformDisplay.kt</a>:515</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 512 </span>      ) {
<span class="lineno"> 513 </span>          StatItem(<span class="string">"主导频率"</span>, <span class="string">"${</span>String.format(<span class="string">"%.1f"</span>, eegData.dominantFrequency)<span class="string">}Hz"</span>)
<span class="lineno"> 514 </span>          StatItem(<span class="string">"总功率"</span>, String.format(<span class="string">"%.1f"</span>, eegData.totalPower))
<span class="caretline"><span class="lineno"> 515 </span>          StatItem(<span class="string">"频谱熵"</span>, <span class="warning">String.format(<span class="string">"%.3f"</span>, eegData.spectralEntropy)</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 516 </span>          StatItem(<span class="string">"采样时间"</span>, java.text.SimpleDateFormat(<span class="string">"HH:mm:ss"</span>).format(java.util.Date(eegData.timestamp)))
<span class="lineno"> 517 </span>      }
<span class="lineno"> 518 </span>  }
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt">../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt</a>:183</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 180 </span>      report.appendLine()
<span class="lineno"> 181 </span>      
<span class="lineno"> 182 </span>      report.appendLine(<span class="string">"EEG数据:"</span>)
<span class="caretline"><span class="lineno"> 183 </span>      report.appendLine(<span class="string">"  Delta: ${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, result.eegData.deltaWave)</span><span class="string">}"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 184 </span>      report.appendLine(<span class="string">"  Theta: ${</span>String.format(<span class="string">"%.2f"</span>, result.eegData.thetaWave)<span class="string">}"</span>)
<span class="lineno"> 185 </span>      report.appendLine(<span class="string">"  Alpha: ${</span>String.format(<span class="string">"%.2f"</span>, result.eegData.alphaWave)<span class="string">}"</span>)
<span class="lineno"> 186 </span>      report.appendLine(<span class="string">"  Beta:  ${</span>String.format(<span class="string">"%.2f"</span>, result.eegData.betaWave)<span class="string">}"</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt">../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt</a>:184</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 181 </span>      
<span class="lineno"> 182 </span>      report.appendLine(<span class="string">"EEG数据:"</span>)
<span class="lineno"> 183 </span>      report.appendLine(<span class="string">"  Delta: ${</span>String.format(<span class="string">"%.2f"</span>, result.eegData.deltaWave)<span class="string">}"</span>)
<span class="caretline"><span class="lineno"> 184 </span>      report.appendLine(<span class="string">"  Theta: ${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, result.eegData.thetaWave)</span><span class="string">}"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 185 </span>      report.appendLine(<span class="string">"  Alpha: ${</span>String.format(<span class="string">"%.2f"</span>, result.eegData.alphaWave)<span class="string">}"</span>)
<span class="lineno"> 186 </span>      report.appendLine(<span class="string">"  Beta:  ${</span>String.format(<span class="string">"%.2f"</span>, result.eegData.betaWave)<span class="string">}"</span>)
<span class="lineno"> 187 </span>      report.appendLine(<span class="string">"  Gamma: ${</span>String.format(<span class="string">"%.2f"</span>, result.eegData.gammaWave)<span class="string">}"</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt">../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt</a>:185</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 182 </span>  report.appendLine(<span class="string">"EEG数据:"</span>)
<span class="lineno"> 183 </span>  report.appendLine(<span class="string">"  Delta: ${</span>String.format(<span class="string">"%.2f"</span>, result.eegData.deltaWave)<span class="string">}"</span>)
<span class="lineno"> 184 </span>  report.appendLine(<span class="string">"  Theta: ${</span>String.format(<span class="string">"%.2f"</span>, result.eegData.thetaWave)<span class="string">}"</span>)
<span class="caretline"><span class="lineno"> 185 </span>  report.appendLine(<span class="string">"  Alpha: ${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, result.eegData.alphaWave)</span><span class="string">}"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 186 </span>  report.appendLine(<span class="string">"  Beta:  ${</span>String.format(<span class="string">"%.2f"</span>, result.eegData.betaWave)<span class="string">}"</span>)
<span class="lineno"> 187 </span>  report.appendLine(<span class="string">"  Gamma: ${</span>String.format(<span class="string">"%.2f"</span>, result.eegData.gammaWave)<span class="string">}"</span>)
<span class="lineno"> 188 </span>  report.appendLine(<span class="string">"  主导频率: ${</span>String.format(<span class="string">"%.1f"</span>, result.eegData.dominantFrequency)<span class="string">}Hz"</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt">../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt</a>:186</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 183 </span>  report.appendLine(<span class="string">"  Delta: ${</span>String.format(<span class="string">"%.2f"</span>, result.eegData.deltaWave)<span class="string">}"</span>)
<span class="lineno"> 184 </span>  report.appendLine(<span class="string">"  Theta: ${</span>String.format(<span class="string">"%.2f"</span>, result.eegData.thetaWave)<span class="string">}"</span>)
<span class="lineno"> 185 </span>  report.appendLine(<span class="string">"  Alpha: ${</span>String.format(<span class="string">"%.2f"</span>, result.eegData.alphaWave)<span class="string">}"</span>)
<span class="caretline"><span class="lineno"> 186 </span>  report.appendLine(<span class="string">"  Beta:  ${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, result.eegData.betaWave)</span><span class="string">}"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 187 </span>  report.appendLine(<span class="string">"  Gamma: ${</span>String.format(<span class="string">"%.2f"</span>, result.eegData.gammaWave)<span class="string">}"</span>)
<span class="lineno"> 188 </span>  report.appendLine(<span class="string">"  主导频率: ${</span>String.format(<span class="string">"%.1f"</span>, result.eegData.dominantFrequency)<span class="string">}Hz"</span>)
<span class="lineno"> 189 </span>  report.appendLine(<span class="string">"  频谱熵: ${</span>String.format(<span class="string">"%.3f"</span>, result.eegData.spectralEntropy)<span class="string">}"</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt">../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt</a>:187</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 184 </span>  report.appendLine(<span class="string">"  Theta: ${</span>String.format(<span class="string">"%.2f"</span>, result.eegData.thetaWave)<span class="string">}"</span>)
<span class="lineno"> 185 </span>  report.appendLine(<span class="string">"  Alpha: ${</span>String.format(<span class="string">"%.2f"</span>, result.eegData.alphaWave)<span class="string">}"</span>)
<span class="lineno"> 186 </span>  report.appendLine(<span class="string">"  Beta:  ${</span>String.format(<span class="string">"%.2f"</span>, result.eegData.betaWave)<span class="string">}"</span>)
<span class="caretline"><span class="lineno"> 187 </span>  report.appendLine(<span class="string">"  Gamma: ${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, result.eegData.gammaWave)</span><span class="string">}"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 188 </span>  report.appendLine(<span class="string">"  主导频率: ${</span>String.format(<span class="string">"%.1f"</span>, result.eegData.dominantFrequency)<span class="string">}Hz"</span>)
<span class="lineno"> 189 </span>  report.appendLine(<span class="string">"  频谱熵: ${</span>String.format(<span class="string">"%.3f"</span>, result.eegData.spectralEntropy)<span class="string">}"</span>)
<span class="lineno"> 190 </span>  report.appendLine()
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt">../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt</a>:188</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 185 </span>  report.appendLine(<span class="string">"  Alpha: ${</span>String.format(<span class="string">"%.2f"</span>, result.eegData.alphaWave)<span class="string">}"</span>)
<span class="lineno"> 186 </span>  report.appendLine(<span class="string">"  Beta:  ${</span>String.format(<span class="string">"%.2f"</span>, result.eegData.betaWave)<span class="string">}"</span>)
<span class="lineno"> 187 </span>  report.appendLine(<span class="string">"  Gamma: ${</span>String.format(<span class="string">"%.2f"</span>, result.eegData.gammaWave)<span class="string">}"</span>)
<span class="caretline"><span class="lineno"> 188 </span>  report.appendLine(<span class="string">"  主导频率: ${</span><span class="warning">String.format(<span class="string">"%.1f"</span>, result.eegData.dominantFrequency)</span><span class="string">}Hz"</span>)</span>
<span class="lineno"> 189 </span>  report.appendLine(<span class="string">"  频谱熵: ${</span>String.format(<span class="string">"%.3f"</span>, result.eegData.spectralEntropy)<span class="string">}"</span>)
<span class="lineno"> 190 </span>  report.appendLine()
<span class="lineno"> 191 </span>  
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt">../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt</a>:189</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 186 </span>  report.appendLine(<span class="string">"  Beta:  ${</span>String.format(<span class="string">"%.2f"</span>, result.eegData.betaWave)<span class="string">}"</span>)
<span class="lineno"> 187 </span>  report.appendLine(<span class="string">"  Gamma: ${</span>String.format(<span class="string">"%.2f"</span>, result.eegData.gammaWave)<span class="string">}"</span>)
<span class="lineno"> 188 </span>  report.appendLine(<span class="string">"  主导频率: ${</span>String.format(<span class="string">"%.1f"</span>, result.eegData.dominantFrequency)<span class="string">}Hz"</span>)
<span class="caretline"><span class="lineno"> 189 </span>  report.appendLine(<span class="string">"  频谱熵: ${</span><span class="warning">String.format(<span class="string">"%.3f"</span>, result.eegData.spectralEntropy)</span><span class="string">}"</span>)&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 190 </span>  report.appendLine()
<span class="lineno"> 191 </span>  
<span class="lineno"> 192 </span>  report.appendLine(<span class="string">"儿童正常环境 (8岁, 30分钟):"</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt">../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt</a>:193</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 190 </span>  report.appendLine()
<span class="lineno"> 191 </span>  
<span class="lineno"> 192 </span>  report.appendLine(<span class="string">"儿童正常环境 (8岁, 30分钟):"</span>)
<span class="caretline"><span class="lineno"> 193 </span>  report.appendLine(<span class="string">"  疲劳指数: ${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, result.childNormalResult.fatigueIndex)</span><span class="string">}/10"</span>)</span>
<span class="lineno"> 194 </span>  report.appendLine(<span class="string">"  疲劳类型: ${</span>result.childNormalResult.fatigueType.displayName<span class="string">}"</span>)
<span class="lineno"> 195 </span>  report.appendLine(<span class="string">"  警觉性: ${</span>String.format(<span class="string">"%.1f"</span>, result.childNormalResult.alertnessLevel * <span class="number">100</span>)<span class="string">}%"</span>)
<span class="lineno"> 196 </span>  report.appendLine(<span class="string">"  置信度: ${</span>String.format(<span class="string">"%.1f"</span>, result.childNormalResult.confidence * <span class="number">100</span>)<span class="string">}%"</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt">../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt</a>:195</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 192 </span>  report.appendLine(<span class="string">"儿童正常环境 (8岁, 30分钟):"</span>)
<span class="lineno"> 193 </span>  report.appendLine(<span class="string">"  疲劳指数: ${</span>String.format(<span class="string">"%.2f"</span>, result.childNormalResult.fatigueIndex)<span class="string">}/10"</span>)
<span class="lineno"> 194 </span>  report.appendLine(<span class="string">"  疲劳类型: ${</span>result.childNormalResult.fatigueType.displayName<span class="string">}"</span>)
<span class="caretline"><span class="lineno"> 195 </span>  report.appendLine(<span class="string">"  警觉性: ${</span><span class="warning">String.format(<span class="string">"%.1f"</span>, result.childNormalResult.alertnessLevel * <span class="number">100</span>)</span><span class="string">}%"</span>)</span>
<span class="lineno"> 196 </span>  report.appendLine(<span class="string">"  置信度: ${</span>String.format(<span class="string">"%.1f"</span>, result.childNormalResult.confidence * <span class="number">100</span>)<span class="string">}%"</span>)
<span class="lineno"> 197 </span>  report.appendLine(<span class="string">"  恢复时间: ${</span>result.childNormalResult.getRecoveryTimeDescription()<span class="string">}"</span>)
<span class="lineno"> 198 </span>  report.appendLine()
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt">../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt</a>:196</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 193 </span>  report.appendLine(<span class="string">"  疲劳指数: ${</span>String.format(<span class="string">"%.2f"</span>, result.childNormalResult.fatigueIndex)<span class="string">}/10"</span>)
<span class="lineno"> 194 </span>  report.appendLine(<span class="string">"  疲劳类型: ${</span>result.childNormalResult.fatigueType.displayName<span class="string">}"</span>)
<span class="lineno"> 195 </span>  report.appendLine(<span class="string">"  警觉性: ${</span>String.format(<span class="string">"%.1f"</span>, result.childNormalResult.alertnessLevel * <span class="number">100</span>)<span class="string">}%"</span>)
<span class="caretline"><span class="lineno"> 196 </span>  report.appendLine(<span class="string">"  置信度: ${</span><span class="warning">String.format(<span class="string">"%.1f"</span>, result.childNormalResult.confidence * <span class="number">100</span>)</span><span class="string">}%"</span>)</span>
<span class="lineno"> 197 </span>  report.appendLine(<span class="string">"  恢复时间: ${</span>result.childNormalResult.getRecoveryTimeDescription()<span class="string">}"</span>)
<span class="lineno"> 198 </span>  report.appendLine()
<span class="lineno"> 199 </span>  
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt">../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt</a>:201</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 198 </span>  report.appendLine()
<span class="lineno"> 199 </span>  
<span class="lineno"> 200 </span>  report.appendLine(<span class="string">"儿童恶劣环境 (8岁, 60分钟):"</span>)
<span class="caretline"><span class="lineno"> 201 </span>  report.appendLine(<span class="string">"  疲劳指数: ${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, result.childPoorResult.fatigueIndex)</span><span class="string">}/10"</span>)</span>
<span class="lineno"> 202 </span>  report.appendLine(<span class="string">"  疲劳类型: ${</span>result.childPoorResult.fatigueType.displayName<span class="string">}"</span>)
<span class="lineno"> 203 </span>  report.appendLine(<span class="string">"  警觉性: ${</span>String.format(<span class="string">"%.1f"</span>, result.childPoorResult.alertnessLevel * <span class="number">100</span>)<span class="string">}%"</span>)
<span class="lineno"> 204 </span>  report.appendLine(<span class="string">"  环境影响: ${</span>String.format(<span class="string">"%.2f"</span>, result.childPoorResult.environmentalAdjustment)<span class="string">}倍"</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt">../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt</a>:203</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 200 </span>  report.appendLine(<span class="string">"儿童恶劣环境 (8岁, 60分钟):"</span>)
<span class="lineno"> 201 </span>  report.appendLine(<span class="string">"  疲劳指数: ${</span>String.format(<span class="string">"%.2f"</span>, result.childPoorResult.fatigueIndex)<span class="string">}/10"</span>)
<span class="lineno"> 202 </span>  report.appendLine(<span class="string">"  疲劳类型: ${</span>result.childPoorResult.fatigueType.displayName<span class="string">}"</span>)
<span class="caretline"><span class="lineno"> 203 </span>  report.appendLine(<span class="string">"  警觉性: ${</span><span class="warning">String.format(<span class="string">"%.1f"</span>, result.childPoorResult.alertnessLevel * <span class="number">100</span>)</span><span class="string">}%"</span>)</span>
<span class="lineno"> 204 </span>  report.appendLine(<span class="string">"  环境影响: ${</span>String.format(<span class="string">"%.2f"</span>, result.childPoorResult.environmentalAdjustment)<span class="string">}倍"</span>)
<span class="lineno"> 205 </span>  report.appendLine(<span class="string">"  时间影响: ${</span>String.format(<span class="string">"%.2f"</span>, result.childPoorResult.timeAdjustment)<span class="string">}倍"</span>)
<span class="lineno"> 206 </span>  report.appendLine()
</pre>

<br/><b>NOTE: 60 results omitted.</b><br/><br/></div>
</div>
<div class="metadata"><div class="explanation" id="explanationDefaultLocale" style="display: none;">
Calling <code>String#toLowerCase()</code> or <code>#toUpperCase()</code> <b>without specifying an explicit locale</b> is a common source of bugs. The reason for that is that those methods will use the current locale on the user's device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for <code>i</code> is <b>not</b> <code>I</code>.<br/>
<br/>
If you want the methods to just perform ASCII replacement, for example to convert an enum name, call <code>String#toUpperCase(Locale.ROOT)</code> instead. If you really want to use the current locale, call <code>String#toUpperCase(Locale.getDefault())</code> instead.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/java/util/Locale.html#default_locale">https://developer.android.com/reference/java/util/Locale.html#default_locale</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "DefaultLocale" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">DefaultLocale</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationDefaultLocaleLink" onclick="reveal('explanationDefaultLocale');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="DefaultLocaleCardLink" onclick="hideid('DefaultLocaleCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="OldTargetApi"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OldTargetApiCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Target SDK attribute is not targeting latest version</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:14</span>: <span class="message">Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the <code>android.os.Build.VERSION_CODES</code> javadoc for details.</span><br /><pre class="errorlines">
<span class="lineno">  11 </span>    defaultConfig {
<span class="lineno">  12 </span>        applicationId = "com.example.iotandroidv20"
<span class="lineno">  13 </span>        minSdk = 24
<span class="caretline"><span class="lineno">  14 </span>        <span class="warning">targetSdk = 35</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  15 </span>        versionCode = 1
<span class="lineno">  16 </span>        versionName = "1.0"
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationOldTargetApi" style="display: none;">
When your application or sdk runs on a version of Android that is more recent than your <code>targetSdkVersion</code> specifies that it has been tested with, various compatibility modes kick in. This ensures that your application continues to work, but it may look out of place. For example, if the <code>targetSdkVersion</code> is less than 14, your app may get an option button in the UI.<br/>
<br/>
To fix this issue, set the <code>targetSdkVersion</code> to the highest available value. Then test your app to make sure everything works correctly. You may want to consult the compatibility notes to see what changes apply to each version you are adding support for: <a href="https://developer.android.com/reference/android/os/Build.VERSION_CODES.html">https://developer.android.com/reference/android/os/Build.VERSION_CODES.html</a> as well as follow this guide:<br/>
<a href="https://developer.android.com/distribute/best-practices/develop/target-sdk.html">https://developer.android.com/distribute/best-practices/develop/target-sdk.html</a><br/><div class="moreinfo">More info: <ul><li><a href="https://developer.android.com/distribute/best-practices/develop/target-sdk.html">https://developer.android.com/distribute/best-practices/develop/target-sdk.html</a>
<li><a href="https://developer.android.com/reference/android/os/Build.VERSION_CODES.html">https://developer.android.com/reference/android/os/Build.VERSION_CODES.html</a>
</ul></div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "OldTargetApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">OldTargetApi</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationOldTargetApiLink" onclick="reveal('explanationOldTargetApi');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OldTargetApiCardLink" onclick="hideid('OldTargetApiCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="SimpleDateFormat"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SimpleDateFormatCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Implied locale in date format</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/ComprehensiveHealthDashboard.kt">../../src/main/java/com/example/iotandroidv20/ui/components/ComprehensiveHealthDashboard.kt</a>:148</span>: <span class="message">To get local formatting use <code>getDateInstance()</code>, <code>getDateTimeInstance()</code>, or <code>getTimeInstance()</code>, or use <code>new SimpleDateFormat(String template, Locale locale)</code> with for example <code>Locale.US</code> for ASCII dates.</span><br /><pre class="errorlines">
<span class="lineno"> 145 </span>     )
<span class="lineno"> 146 </span>     Text(
<span class="lineno"> 147 </span>         text = <span class="keyword">if</span> (lastUpdate > <span class="number">0</span>) {
<span class="caretline"><span class="lineno"> 148 </span>             <span class="warning">java.text.SimpleDateFormat(<span class="string">"HH:mm:ss"</span>)</span>.format(java.util.Date(lastUpdate))&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 149 </span>         } <span class="keyword">else</span> {
<span class="lineno"> 150 </span>             <span class="string">"--:--:--"</span>
<span class="lineno"> 151 </span>         },
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/EEGWaveformDisplay.kt">../../src/main/java/com/example/iotandroidv20/ui/components/EEGWaveformDisplay.kt</a>:516</span>: <span class="message">To get local formatting use <code>getDateInstance()</code>, <code>getDateTimeInstance()</code>, or <code>getTimeInstance()</code>, or use <code>new SimpleDateFormat(String template, Locale locale)</code> with for example <code>Locale.US</code> for ASCII dates.</span><br /><pre class="errorlines">
<span class="lineno"> 513 </span>              StatItem(<span class="string">"主导频率"</span>, <span class="string">"${</span>String.format(<span class="string">"%.1f"</span>, eegData.dominantFrequency)<span class="string">}Hz"</span>)
<span class="lineno"> 514 </span>              StatItem(<span class="string">"总功率"</span>, String.format(<span class="string">"%.1f"</span>, eegData.totalPower))
<span class="lineno"> 515 </span>              StatItem(<span class="string">"频谱熵"</span>, String.format(<span class="string">"%.3f"</span>, eegData.spectralEntropy))
<span class="caretline"><span class="lineno"> 516 </span>              StatItem(<span class="string">"采样时间"</span>, <span class="warning">java.text.SimpleDateFormat(<span class="string">"HH:mm:ss"</span>)</span>.format(java.util.Date(eegData.timestamp)))</span>
<span class="lineno"> 517 </span>          }
<span class="lineno"> 518 </span>      }
<span class="lineno"> 519 </span>  }
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt">../../src/main/java/com/example/iotandroidv20/eeg/FatigueDetectionDemo.kt</a>:174</span>: <span class="message">To get local formatting use <code>getDateInstance()</code>, <code>getDateTimeInstance()</code>, or <code>getTimeInstance()</code>, or use <code>new SimpleDateFormat(String template, Locale locale)</code> with for example <code>Locale.US</code> for ASCII dates.</span><br /><pre class="errorlines">
<span class="lineno"> 171 </span>  <span class="keyword">val</span> report = StringBuilder()
<span class="lineno"> 172 </span>  
<span class="lineno"> 173 </span>  report.appendLine(<span class="string">"=== 疲劳检测演示报告 ==="</span>)
<span class="caretline"><span class="lineno"> 174 </span>  report.appendLine(<span class="string">"生成时间: ${</span><span class="warning">java.text.SimpleDateFormat(<span class="string">"yyyy-MM-dd HH:mm:ss"</span>)</span>.format(java.util.Date())<span class="string">}"</span>)</span>
<span class="lineno"> 175 </span>  report.appendLine()
<span class="lineno"> 176 </span>  
<span class="lineno"> 177 </span>  results.forEach { result ->
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/eeg/FocusCalculationDemo.kt">../../src/main/java/com/example/iotandroidv20/eeg/FocusCalculationDemo.kt</a>:145</span>: <span class="message">To get local formatting use <code>getDateInstance()</code>, <code>getDateTimeInstance()</code>, or <code>getTimeInstance()</code>, or use <code>new SimpleDateFormat(String template, Locale locale)</code> with for example <code>Locale.US</code> for ASCII dates.</span><br /><pre class="errorlines">
<span class="lineno"> 142 </span>  <span class="keyword">val</span> report = StringBuilder()
<span class="lineno"> 143 </span>  
<span class="lineno"> 144 </span>  report.appendLine(<span class="string">"=== 专注度计算演示报告 ==="</span>)
<span class="caretline"><span class="lineno"> 145 </span>  report.appendLine(<span class="string">"生成时间: ${</span><span class="warning">java.text.SimpleDateFormat(<span class="string">"yyyy-MM-dd HH:mm:ss"</span>)</span>.format(java.util.Date())<span class="string">}"</span>)</span>
<span class="lineno"> 146 </span>  report.appendLine()
<span class="lineno"> 147 </span>  
<span class="lineno"> 148 </span>  results.forEach { result ->
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/viewmodel/MainViewModel.kt">../../src/main/java/com/example/iotandroidv20/viewmodel/MainViewModel.kt</a>:1400</span>: <span class="message">To get local formatting use <code>getDateInstance()</code>, <code>getDateTimeInstance()</code>, or <code>getTimeInstance()</code>, or use <code>new SimpleDateFormat(String template, Locale locale)</code> with for example <code>Locale.US</code> for ASCII dates.</span><br /><pre class="errorlines">
<span class="lineno"> 1397 </span>          appendLine(<span class="string">"连接状态: $status"</span>)
<span class="lineno"> 1398 </span>          appendLine(<span class="string">"流URL: ${</span>url ?: <span class="string">"无"}"</span>)
<span class="lineno"> 1399 </span>          appendLine(<span class="string">"连接质量: $quality"</span>)
<span class="caretline"><span class="lineno"> 1400 </span>          appendLine(<span class="string">"最后更新: ${</span><span class="keyword">if</span> (lastUpdate > <span class="number">0</span>) <span class="warning">java.text.SimpleDateFormat(<span class="string">"HH:mm:ss"</span>)</span>.format(java.util.Date(lastUpdate)) <span class="keyword">else</span> <span class="string">"无"}"</span>)</span>
<span class="lineno"> 1401 </span>      }
<span class="lineno"> 1402 </span>  }
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="SimpleDateFormatDivLink" onclick="reveal('SimpleDateFormatDiv');" />+ 4 More Occurrences...</button>
<div id="SimpleDateFormatDiv" style="display: none">
<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/viewmodel/MainViewModel.kt">../../src/main/java/com/example/iotandroidv20/viewmodel/MainViewModel.kt</a>:1418</span>: <span class="message">To get local formatting use <code>getDateInstance()</code>, <code>getDateTimeInstance()</code>, or <code>getTimeInstance()</code>, or use <code>new SimpleDateFormat(String template, Locale locale)</code> with for example <code>Locale.US</code> for ASCII dates.</span><br /><pre class="errorlines">
<span class="lineno"> 1415 </span>  <span class="keyword">val</span> reportText = buildString {
<span class="lineno"> 1416 </span>      appendLine(<span class="string">"=== 视频监控诊断报告 ==="</span>)
<span class="lineno"> 1417 </span>      appendLine(<span class="string">"设备ID: ${</span>diagnosticReport.deviceId<span class="string">}"</span>)
<span class="caretline"><span class="lineno"> 1418 </span>      appendLine(<span class="string">"诊断时间: ${</span><span class="warning">java.text.SimpleDateFormat(<span class="string">"yyyy-MM-dd HH:mm:ss"</span>)</span>.format(java.util.Date(diagnosticReport.timestamp))<span class="string">}"</span>)</span>
<span class="lineno"> 1419 </span>      appendLine()
<span class="lineno"> 1420 </span>
<span class="lineno"> 1421 </span>      diagnosticReport.obsConnectionDiagnostic?.let { item ->
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/obs/ObsApiTestHelper.kt">../../src/main/java/com/example/iotandroidv20/obs/ObsApiTestHelper.kt</a>:211</span>: <span class="message">To get local formatting use <code>getDateInstance()</code>, <code>getDateTimeInstance()</code>, or <code>getTimeInstance()</code>, or use <code>new SimpleDateFormat(String template, Locale locale)</code> with for example <code>Locale.US</code> for ASCII dates.</span><br /><pre class="errorlines">
<span class="lineno"> 208 </span>  <span class="keyword">fun</span> generateTestReport(results: TestResults): String {
<span class="lineno"> 209 </span>      <span class="keyword">val</span> report = StringBuilder()
<span class="lineno"> 210 </span>      report.appendLine(<span class="string">"=== 华为云OBS API测试报告 ==="</span>)
<span class="caretline"><span class="lineno"> 211 </span>      report.appendLine(<span class="string">"测试时间: ${</span><span class="warning">java.text.SimpleDateFormat(<span class="string">"yyyy-MM-dd HH:mm:ss"</span>)</span>.format(java.util.Date())<span class="string">}"</span>)</span>
<span class="lineno"> 212 </span>      report.appendLine()
<span class="lineno"> 213 </span>      
<span class="lineno"> 214 </span>      report.appendLine(<span class="string">"1. 列举对象测试: ${</span><span class="keyword">if</span> (results.listObjectsTest.success) <span class="string">"✅ 通过"</span> <span class="keyword">else</span> <span class="string">"❌ 失败"}"</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/obs/RealTimeVideoVerifier.kt">../../src/main/java/com/example/iotandroidv20/obs/RealTimeVideoVerifier.kt</a>:392</span>: <span class="message">To get local formatting use <code>getDateInstance()</code>, <code>getDateTimeInstance()</code>, or <code>getTimeInstance()</code>, or use <code>new SimpleDateFormat(String template, Locale locale)</code> with for example <code>Locale.US</code> for ASCII dates.</span><br /><pre class="errorlines">
<span class="lineno"> 389 </span>  <span class="keyword">fun</span> generateDetailedReport(report: VerificationReport): String {
<span class="lineno"> 390 </span>      <span class="keyword">val</span> sb = StringBuilder()
<span class="lineno"> 391 </span>      sb.appendLine(<span class="string">"=== 实时视频监控功能验证报告 ==="</span>)
<span class="caretline"><span class="lineno"> 392 </span>      sb.appendLine(<span class="string">"验证时间: ${</span><span class="warning">SimpleDateFormat(<span class="string">"yyyy-MM-dd HH:mm:ss"</span>)</span>.format(Date(report.timestamp))<span class="string">}"</span>)</span>
<span class="lineno"> 393 </span>      sb.appendLine(<span class="string">"设备ID: $TEST_DEVICE_ID"</span>)
<span class="lineno"> 394 </span>      sb.appendLine()
<span class="lineno"> 395 </span>      
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/test/VoiceModuleTestHelper.kt">../../src/main/java/com/example/iotandroidv20/test/VoiceModuleTestHelper.kt</a>:236</span>: <span class="message">To get local formatting use <code>getDateInstance()</code>, <code>getDateTimeInstance()</code>, or <code>getTimeInstance()</code>, or use <code>new SimpleDateFormat(String template, Locale locale)</code> with for example <code>Locale.US</code> for ASCII dates.</span><br /><pre class="errorlines">
<span class="lineno"> 233 </span>  <span class="keyword">fun</span> generateTestReport(report: VoiceTestReport): String {
<span class="lineno"> 234 </span>      <span class="keyword">return</span> buildString {
<span class="lineno"> 235 </span>          appendLine(<span class="string">"=== 语音模块测试报告 ==="</span>)
<span class="caretline"><span class="lineno"> 236 </span>          appendLine(<span class="string">"测试时间: ${</span><span class="warning">java.text.SimpleDateFormat(<span class="string">"yyyy-MM-dd HH:mm:ss"</span>)</span>.format(java.util.Date())<span class="string">}"</span>)</span>
<span class="lineno"> 237 </span>          appendLine()
<span class="lineno"> 238 </span>          
<span class="lineno"> 239 </span>          appendLine(<span class="string">"📊 测试结果概览:"</span>)
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationSimpleDateFormat" style="display: none;">
Almost all callers should use <code>getDateInstance()</code>, <code>getDateTimeInstance()</code>, or <code>getTimeInstance()</code> to get a ready-made instance of SimpleDateFormat suitable for the user's locale. The main reason you'd create an instance this class directly is because you need to format/parse a specific machine-readable format, in which case you almost certainly want to explicitly ask for US to ensure that you get ASCII digits (rather than, say, Arabic digits).<br/>
<br/>
Therefore, you should either use the form of the SimpleDateFormat constructor where you pass in an explicit locale, such as Locale.US, or use one of the get instance methods, or suppress this error if really know what you are doing.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/java/text/SimpleDateFormat.html">https://developer.android.com/reference/java/text/SimpleDateFormat.html</a>
</div>To suppress this error, use the issue id "SimpleDateFormat" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">SimpleDateFormat</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationSimpleDateFormatLink" onclick="reveal('explanationSimpleDateFormat');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SimpleDateFormatCardLink" onclick="hideid('SimpleDateFormatCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="RedundantLabel"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="RedundantLabelCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Redundant label on activity</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:30</span>: <span class="message">Redundant label can be removed</span><br /><pre class="errorlines">
<span class="lineno"> 27 </span>        <span class="tag">&lt;activity</span><span class="attribute">
</span><span class="lineno"> 28 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">".MainActivity"</span>
<span class="lineno"> 29 </span>            <span class="prefix">android:</span><span class="attribute">exported</span>=<span class="value">"true"</span>
<span class="caretline"><span class="lineno"> 30 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">label</span>=<span class="value">"@string/app_name"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 31 </span>            <span class="prefix">android:</span><span class="attribute">theme</span>=<span class="value">"@style/Theme.IotAndroidV20"</span>>
<span class="lineno"> 32 </span>            <span class="tag">&lt;intent-filter></span>
<span class="lineno"> 33 </span>                <span class="tag">&lt;action</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.intent.action.MAIN"</span> />
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationRedundantLabel" style="display: none;">
When an activity does not have a label attribute, it will use the one from the application tag. Since the application has already specified the same label, the label on this activity can be omitted.<br/>To suppress this error, use the issue id "RedundantLabel" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">RedundantLabel</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationRedundantLabelLink" onclick="reveal('explanationRedundantLabel');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="RedundantLabelCardLink" onclick="hideid('RedundantLabelCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="GradleDependency"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="GradleDependencyCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Obsolete Gradle Dependency</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:80</span>: <span class="message">A newer version of org.apache.httpcomponents:httpcore than 4.4.15 is available: 4.4.16</span><br /><pre class="errorlines">
<span class="lineno">  77 </span>        exclude(group = "commons-logging", module = "commons-logging")
<span class="lineno">  78 </span>        exclude(group = "commons-codec", module = "commons-codec")
<span class="lineno">  79 </span>    }
<span class="caretline"><span class="lineno">  80 </span>    implementation(<span class="warning">"org.apache.httpcomponents:httpcore:4.4.15"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  81 </span>
<span class="lineno">  82 </span>    // 统一的依赖版本（避免冲突）
<span class="lineno">  83 </span>    implementation("commons-codec:commons-codec:1.11")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:83</span>: <span class="message">A newer version of commons-codec:commons-codec than 1.11 is available: 1.15</span><br /><pre class="errorlines">
<span class="lineno">  80 </span>    implementation("org.apache.httpcomponents:httpcore:4.4.15")
<span class="lineno">  81 </span>
<span class="lineno">  82 </span>    // 统一的依赖版本（避免冲突）
<span class="caretline"><span class="lineno">  83 </span>    implementation(<span class="warning">"commons-codec:commons-codec:1.11"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  84 </span>    implementation("commons-logging:commons-logging:1.2")
<span class="lineno">  85 </span>
<span class="lineno">  86 </span>    // 使用统一的Jackson版本（避免冲突）
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:87</span>: <span class="message">A newer version of com.fasterxml.jackson.core:jackson-core than 2.8.1 is available: 2.15.2</span><br /><pre class="errorlines">
<span class="lineno">  84 </span>    implementation("commons-logging:commons-logging:1.2")
<span class="lineno">  85 </span>
<span class="lineno">  86 </span>    // 使用统一的Jackson版本（避免冲突）
<span class="caretline"><span class="lineno">  87 </span>    implementation(<span class="warning">"com.fasterxml.jackson.core:jackson-core:2.8.1"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  88 </span>    implementation("com.fasterxml.jackson.core:jackson-databind:2.8.1")
<span class="lineno">  89 </span>    implementation("com.fasterxml.jackson.core:jackson-annotations:2.8.1")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:88</span>: <span class="message">A newer version of com.fasterxml.jackson.core:jackson-databind than 2.8.1 is available: 2.15.2</span><br /><pre class="errorlines">
<span class="lineno">  85 </span>
<span class="lineno">  86 </span>    // 使用统一的Jackson版本（避免冲突）
<span class="lineno">  87 </span>    implementation("com.fasterxml.jackson.core:jackson-core:2.8.1")
<span class="caretline"><span class="lineno">  88 </span>    implementation(<span class="warning">"com.fasterxml.jackson.core:jackson-databind:2.8.1"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  89 </span>    implementation("com.fasterxml.jackson.core:jackson-annotations:2.8.1")
<span class="lineno">  90 </span>
<span class="lineno">  91 </span>    // JSON处理
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:89</span>: <span class="message">A newer version of com.fasterxml.jackson.core:jackson-annotations than 2.8.1 is available: 2.15.2</span><br /><pre class="errorlines">
<span class="lineno">  86 </span>    // 使用统一的Jackson版本（避免冲突）
<span class="lineno">  87 </span>    implementation("com.fasterxml.jackson.core:jackson-core:2.8.1")
<span class="lineno">  88 </span>    implementation("com.fasterxml.jackson.core:jackson-databind:2.8.1")
<span class="caretline"><span class="lineno">  89 </span>    implementation(<span class="warning">"com.fasterxml.jackson.core:jackson-annotations:2.8.1"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  90 </span>
<span class="lineno">  91 </span>    // JSON处理
<span class="lineno">  92 </span>    implementation("com.google.code.gson:gson:2.10.1")
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="GradleDependencyDivLink" onclick="reveal('GradleDependencyDiv');" />+ 8 More Occurrences...</button>
<div id="GradleDependencyDiv" style="display: none">
<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:92</span>: <span class="message">A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0</span><br /><pre class="errorlines">
<span class="lineno">  89 </span>    implementation("com.fasterxml.jackson.core:jackson-annotations:2.8.1")
<span class="lineno">  90 </span>
<span class="lineno">  91 </span>    // JSON处理
<span class="caretline"><span class="lineno">  92 </span>    implementation(<span class="warning">"com.google.code.gson:gson:2.10.1"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  93 </span>    implementation(libs.androidx.ui)
<span class="lineno">  94 </span>    implementation(libs.androidx.ui.graphics)
<span class="lineno">  95 </span>    implementation(libs.androidx.ui.tooling.preview)
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:99</span>: <span class="message">A newer version of androidx.compose.material:material-icons-extended than 1.5.8 is available: 1.7.0</span><br /><pre class="errorlines">
<span class="lineno">  96 </span>    implementation(libs.androidx.material3)
<span class="lineno">  97 </span>
<span class="lineno">  98 </span>    // Material Icons扩展包
<span class="caretline"><span class="lineno">  99 </span>    implementation(<span class="warning">"androidx.compose.material:material-icons-extended:1.5.8"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 100 </span>
<span class="lineno"> 101 </span>    // Compose动画
<span class="lineno"> 102 </span>    implementation("androidx.compose.animation:animation:1.5.8")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:102</span>: <span class="message">A newer version of androidx.compose.animation:animation than 1.5.8 is available: 1.7.8</span><br /><pre class="errorlines">
<span class="lineno">  99 </span>    implementation("androidx.compose.material:material-icons-extended:1.5.8")
<span class="lineno"> 100 </span>
<span class="lineno"> 101 </span>    // Compose动画
<span class="caretline"><span class="lineno"> 102 </span>    implementation(<span class="warning">"androidx.compose.animation:animation:1.5.8"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 103 </span>
<span class="lineno"> 104 </span>    // Compose Foundation
<span class="lineno"> 105 </span>    implementation("androidx.compose.foundation:foundation:1.5.8")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:105</span>: <span class="message">Upgrade <code>androidx.compose.foundation</code> for keyboard and mouse support</span><br /><pre class="errorlines">
<span class="lineno"> 102 </span>    implementation("androidx.compose.animation:animation:1.5.8")
<span class="lineno"> 103 </span>
<span class="lineno"> 104 </span>    // Compose Foundation
<span class="caretline"><span class="lineno"> 105 </span>    implementation(<span class="warning">"androidx.compose.foundation:foundation:1.5.8"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 106 </span>
<span class="lineno"> 107 </span>    // Navigation Compose
<span class="lineno"> 108 </span>    implementation("androidx.navigation:navigation-compose:2.7.6")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:132</span>: <span class="message">A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0</span><br /><pre class="errorlines">
<span class="lineno"> 129 </span>    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
<span class="lineno"> 130 </span>
<span class="lineno"> 131 </span>    // JSON解析
<span class="caretline"><span class="lineno"> 132 </span>    implementation(<span class="warning">"com.google.code.gson:gson:2.10.1"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 133 </span>
<span class="lineno"> 134 </span>    // 协程支持
<span class="lineno"> 135 </span>    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:138</span>: <span class="message">A newer version of androidx.lifecycle:lifecycle-viewmodel-compose than 2.7.0 is available: 2.9.0</span><br /><pre class="errorlines">
<span class="lineno"> 135 </span>    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")
<span class="lineno"> 136 </span>
<span class="lineno"> 137 </span>    // ViewModel和LiveData
<span class="caretline"><span class="lineno"> 138 </span>    implementation(<span class="warning">"androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 139 </span>    implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.7.0")
<span class="lineno"> 140 </span>
<span class="lineno"> 141 </span>    // 权限处理
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:139</span>: <span class="message">A newer version of androidx.lifecycle:lifecycle-livedata-ktx than 2.7.0 is available: 2.9.0</span><br /><pre class="errorlines">
<span class="lineno"> 136 </span>
<span class="lineno"> 137 </span>    // ViewModel和LiveData
<span class="lineno"> 138 </span>    implementation("androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0")
<span class="caretline"><span class="lineno"> 139 </span>    implementation(<span class="warning">"androidx.lifecycle:lifecycle-livedata-ktx:2.7.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 140 </span>
<span class="lineno"> 141 </span>    // 权限处理
<span class="lineno"> 142 </span>    implementation("com.google.accompanist:accompanist-permissions:0.32.0")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:157</span>: <span class="message">A newer version of androidx.activity:activity-compose than 1.8.2 is available: 1.10.1</span><br /><pre class="errorlines">
<span class="lineno"> 154 </span>    implementation("io.coil-kt:coil-compose:2.5.0")
<span class="lineno"> 155 </span>
<span class="lineno"> 156 </span>    // 文件选择器
<span class="caretline"><span class="lineno"> 157 </span>    implementation(<span class="warning">"androidx.activity:activity-compose:1.8.2"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 158 </span>
<span class="lineno"> 159 </span>    // Room数据库 - 暂时禁用，解决编译问题
<span class="lineno"> 160 </span>    // implementation("androidx.room:room-runtime:2.6.1")
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationGradleDependency" style="display: none;">
This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "GradleDependency" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">GradleDependency</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationGradleDependencyLink" onclick="reveal('explanationGradleDependency');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="GradleDependencyCardLink" onclick="hideid('GradleDependencyCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UnsafeOptInUsageError"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UnsafeOptInUsageErrorCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Unsafe opt-in usage intended to be error-level severity</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/player/AudioPlayerManager.kt">../../src/main/java/com/example/iotandroidv20/player/AudioPlayerManager.kt</a>:88</span>: <span class="message">This declaration is opt-in and its usage should be marked with <code>@androidx.media3.common.util.UnstableApi</code> or <code>@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)</code></span><br /><pre class="errorlines">
<span class="lineno">  85 </span>            Log.d(TAG, <span class="string">"Initializing ExoPlayer for audio"</span>)
<span class="lineno">  86 </span>            
<span class="lineno">  87 </span>            <span class="comment">// 创建数据源工厂</span>
<span class="caretline"><span class="lineno">  88 </span>            <span class="keyword">val</span> <span class="error">dataSourceFactory</span> = DefaultDataSourceFactory(&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  89 </span>                context,
<span class="lineno">  90 </span>                DefaultHttpDataSource.Factory()
<span class="lineno">  91 </span>                    .setUserAgent(<span class="string">"LearningSupervisionApp/1.0"</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/player/AudioPlayerManager.kt">../../src/main/java/com/example/iotandroidv20/player/AudioPlayerManager.kt</a>:88</span>: <span class="message">This declaration is opt-in and its usage should be marked with <code>@androidx.media3.common.util.UnstableApi</code> or <code>@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)</code></span><br /><pre class="errorlines">
<span class="lineno">  85 </span>            Log.d(TAG, <span class="string">"Initializing ExoPlayer for audio"</span>)
<span class="lineno">  86 </span>            
<span class="lineno">  87 </span>            <span class="comment">// 创建数据源工厂</span>
<span class="caretline"><span class="lineno">  88 </span>            <span class="keyword">val</span> dataSourceFactory = <span class="error">DefaultDataSourceFactory</span>(&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  89 </span>                context,
<span class="lineno">  90 </span>                DefaultHttpDataSource.Factory()
<span class="lineno">  91 </span>                    .setUserAgent(<span class="string">"LearningSupervisionApp/1.0"</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/player/AudioPlayerManager.kt">../../src/main/java/com/example/iotandroidv20/player/AudioPlayerManager.kt</a>:91</span>: <span class="message">This declaration is opt-in and its usage should be marked with <code>@androidx.media3.common.util.UnstableApi</code> or <code>@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)</code></span><br /><pre class="errorlines">
<span class="lineno">  88 </span>            <span class="keyword">val</span> dataSourceFactory = DefaultDataSourceFactory(
<span class="lineno">  89 </span>                context,
<span class="lineno">  90 </span>                DefaultHttpDataSource.Factory()
<span class="caretline"><span class="lineno">  91 </span>                    .<span class="error">setUserAgent</span>(<span class="string">"LearningSupervisionApp/1.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  92 </span>                    .setConnectTimeoutMs(<span class="number">30000</span>)
<span class="lineno">  93 </span>                    .setReadTimeoutMs(<span class="number">30000</span>)
<span class="lineno">  94 </span>            )
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/player/AudioPlayerManager.kt">../../src/main/java/com/example/iotandroidv20/player/AudioPlayerManager.kt</a>:92</span>: <span class="message">This declaration is opt-in and its usage should be marked with <code>@androidx.media3.common.util.UnstableApi</code> or <code>@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)</code></span><br /><pre class="errorlines">
<span class="lineno">  89 </span>                context,
<span class="lineno">  90 </span>                DefaultHttpDataSource.Factory()
<span class="lineno">  91 </span>                    .setUserAgent(<span class="string">"LearningSupervisionApp/1.0"</span>)
<span class="caretline"><span class="lineno">  92 </span>                    .<span class="error">setConnectTimeoutMs</span>(<span class="number">30000</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  93 </span>                    .setReadTimeoutMs(<span class="number">30000</span>)
<span class="lineno">  94 </span>            )
<span class="lineno">  95 </span>            
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/player/AudioPlayerManager.kt">../../src/main/java/com/example/iotandroidv20/player/AudioPlayerManager.kt</a>:93</span>: <span class="message">This declaration is opt-in and its usage should be marked with <code>@androidx.media3.common.util.UnstableApi</code> or <code>@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)</code></span><br /><pre class="errorlines">
<span class="lineno">  90 </span>                DefaultHttpDataSource.Factory()
<span class="lineno">  91 </span>                    .setUserAgent(<span class="string">"LearningSupervisionApp/1.0"</span>)
<span class="lineno">  92 </span>                    .setConnectTimeoutMs(<span class="number">30000</span>)
<span class="caretline"><span class="lineno">  93 </span>                    .<span class="error">setReadTimeoutMs</span>(<span class="number">30000</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  94 </span>            )
<span class="lineno">  95 </span>            
<span class="lineno">  96 </span>            <span class="comment">// 创建媒体源工厂</span></pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="UnsafeOptInUsageErrorDivLink" onclick="reveal('UnsafeOptInUsageErrorDiv');" />+ 9 More Occurrences...</button>
<div id="UnsafeOptInUsageErrorDiv" style="display: none">
<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/player/AudioPlayerManager.kt">../../src/main/java/com/example/iotandroidv20/player/AudioPlayerManager.kt</a>:97</span>: <span class="message">This declaration is opt-in and its usage should be marked with <code>@androidx.media3.common.util.UnstableApi</code> or <code>@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)</code></span><br /><pre class="errorlines">
<span class="lineno">  94 </span>            )
<span class="lineno">  95 </span>            
<span class="lineno">  96 </span>            <span class="comment">// 创建媒体源工厂</span>
<span class="caretline"><span class="lineno">  97 </span>            <span class="keyword">val</span> mediaSourceFactory = <span class="error">DefaultMediaSourceFactory</span>(dataSourceFactory)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  98 </span>            
<span class="lineno">  99 </span>            <span class="comment">// 创建ExoPlayer实例</span>
<span class="lineno"> 100 </span>            exoPlayer = ExoPlayer.Builder(context)
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/player/VideoPlayerManager.kt">../../src/main/java/com/example/iotandroidv20/player/VideoPlayerManager.kt</a>:65</span>: <span class="message">This declaration is opt-in and its usage should be marked with <code>@androidx.media3.common.util.UnstableApi</code> or <code>@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)</code></span><br /><pre class="errorlines">
<span class="lineno">  62 </span>            Log.d(TAG, <span class="string">"Initializing ExoPlayer"</span>)
<span class="lineno">  63 </span>            
<span class="lineno">  64 </span>            <span class="comment">// 创建数据源工厂</span>
<span class="caretline"><span class="lineno">  65 </span>            <span class="keyword">val</span> <span class="error">dataSourceFactory</span> = DefaultDataSourceFactory(&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  66 </span>                context,
<span class="lineno">  67 </span>                DefaultHttpDataSource.Factory()
<span class="lineno">  68 </span>                    .setUserAgent(<span class="string">"LearningSupervisionApp/1.0"</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/player/VideoPlayerManager.kt">../../src/main/java/com/example/iotandroidv20/player/VideoPlayerManager.kt</a>:65</span>: <span class="message">This declaration is opt-in and its usage should be marked with <code>@androidx.media3.common.util.UnstableApi</code> or <code>@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)</code></span><br /><pre class="errorlines">
<span class="lineno">  62 </span>            Log.d(TAG, <span class="string">"Initializing ExoPlayer"</span>)
<span class="lineno">  63 </span>            
<span class="lineno">  64 </span>            <span class="comment">// 创建数据源工厂</span>
<span class="caretline"><span class="lineno">  65 </span>            <span class="keyword">val</span> dataSourceFactory = <span class="error">DefaultDataSourceFactory</span>(&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  66 </span>                context,
<span class="lineno">  67 </span>                DefaultHttpDataSource.Factory()
<span class="lineno">  68 </span>                    .setUserAgent(<span class="string">"LearningSupervisionApp/1.0"</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/player/VideoPlayerManager.kt">../../src/main/java/com/example/iotandroidv20/player/VideoPlayerManager.kt</a>:68</span>: <span class="message">This declaration is opt-in and its usage should be marked with <code>@androidx.media3.common.util.UnstableApi</code> or <code>@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)</code></span><br /><pre class="errorlines">
<span class="lineno">  65 </span>            <span class="keyword">val</span> dataSourceFactory = DefaultDataSourceFactory(
<span class="lineno">  66 </span>                context,
<span class="lineno">  67 </span>                DefaultHttpDataSource.Factory()
<span class="caretline"><span class="lineno">  68 </span>                    .<span class="error">setUserAgent</span>(<span class="string">"LearningSupervisionApp/1.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  69 </span>                    .setConnectTimeoutMs(<span class="number">30000</span>)
<span class="lineno">  70 </span>                    .setReadTimeoutMs(<span class="number">30000</span>)
<span class="lineno">  71 </span>            )
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/player/VideoPlayerManager.kt">../../src/main/java/com/example/iotandroidv20/player/VideoPlayerManager.kt</a>:69</span>: <span class="message">This declaration is opt-in and its usage should be marked with <code>@androidx.media3.common.util.UnstableApi</code> or <code>@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)</code></span><br /><pre class="errorlines">
<span class="lineno">  66 </span>                context,
<span class="lineno">  67 </span>                DefaultHttpDataSource.Factory()
<span class="lineno">  68 </span>                    .setUserAgent(<span class="string">"LearningSupervisionApp/1.0"</span>)
<span class="caretline"><span class="lineno">  69 </span>                    .<span class="error">setConnectTimeoutMs</span>(<span class="number">30000</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  70 </span>                    .setReadTimeoutMs(<span class="number">30000</span>)
<span class="lineno">  71 </span>            )
<span class="lineno">  72 </span>            
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/player/VideoPlayerManager.kt">../../src/main/java/com/example/iotandroidv20/player/VideoPlayerManager.kt</a>:70</span>: <span class="message">This declaration is opt-in and its usage should be marked with <code>@androidx.media3.common.util.UnstableApi</code> or <code>@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)</code></span><br /><pre class="errorlines">
<span class="lineno">  67 </span>                DefaultHttpDataSource.Factory()
<span class="lineno">  68 </span>                    .setUserAgent(<span class="string">"LearningSupervisionApp/1.0"</span>)
<span class="lineno">  69 </span>                    .setConnectTimeoutMs(<span class="number">30000</span>)
<span class="caretline"><span class="lineno">  70 </span>                    .<span class="error">setReadTimeoutMs</span>(<span class="number">30000</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  71 </span>            )
<span class="lineno">  72 </span>            
<span class="lineno">  73 </span>            <span class="comment">// 创建媒体源工厂</span></pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/player/VideoPlayerManager.kt">../../src/main/java/com/example/iotandroidv20/player/VideoPlayerManager.kt</a>:74</span>: <span class="message">This declaration is opt-in and its usage should be marked with <code>@androidx.media3.common.util.UnstableApi</code> or <code>@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)</code></span><br /><pre class="errorlines">
<span class="lineno">  71 </span>            )
<span class="lineno">  72 </span>            
<span class="lineno">  73 </span>            <span class="comment">// 创建媒体源工厂</span>
<span class="caretline"><span class="lineno">  74 </span>            <span class="keyword">val</span> mediaSourceFactory = <span class="error">DefaultMediaSourceFactory</span>(dataSourceFactory)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  75 </span>            
<span class="lineno">  76 </span>            <span class="comment">// 创建ExoPlayer实例</span>
<span class="lineno">  77 </span>            exoPlayer = ExoPlayer.Builder(context)
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/player/VideoPlayerScreen.kt">../../src/main/java/com/example/iotandroidv20/ui/player/VideoPlayerScreen.kt</a>:82</span>: <span class="message">This declaration is opt-in and its usage should be marked with <code>@androidx.media3.common.util.UnstableApi</code> or <code>@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)</code></span><br /><pre class="errorlines">
<span class="lineno">  79 </span>                    PlayerView(context).apply {
<span class="lineno">  80 </span>                        player = viewModel.getExoPlayer()
<span class="lineno">  81 </span>                        useController = <span class="keyword">false</span> <span class="comment">// 使用自定义控制器</span>
<span class="caretline"><span class="lineno">  82 </span>                        <span class="error">setShowBuffering</span>(PlayerView.SHOW_BUFFERING_WHEN_PLAYING)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  83 </span>                    }
<span class="lineno">  84 </span>                },
<span class="lineno">  85 </span>                modifier = Modifier.fillMaxSize(),
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/player/VideoPlayerScreen.kt">../../src/main/java/com/example/iotandroidv20/ui/player/VideoPlayerScreen.kt</a>:82</span>: <span class="message">This declaration is opt-in and its usage should be marked with <code>@androidx.media3.common.util.UnstableApi</code> or <code>@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)</code></span><br /><pre class="errorlines">
<span class="lineno">  79 </span>                    PlayerView(context).apply {
<span class="lineno">  80 </span>                        player = viewModel.getExoPlayer()
<span class="lineno">  81 </span>                        useController = <span class="keyword">false</span> <span class="comment">// 使用自定义控制器</span>
<span class="caretline"><span class="lineno">  82 </span>                        setShowBuffering(PlayerView.<span class="error">SHOW_BUFFERING_WHEN_PLAYING</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  83 </span>                    }
<span class="lineno">  84 </span>                },
<span class="lineno">  85 </span>                modifier = Modifier.fillMaxSize(),
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationUnsafeOptInUsageError" style="display: none;">
This API has been flagged as opt-in with error-level severity.<br/>
<br/>
Any declaration annotated with this marker is considered part of an unstable or<br/>
otherwise non-standard API surface and its call sites should accept the opt-in<br/>
aspect of it by using the <code>@OptIn</code> annotation, using the marker annotation --<br/>
effectively causing further propagation of the opt-in aspect -- or configuring<br/>
the <code>UnsafeOptInUsageError</code> check's options for project-wide opt-in.<br/>
<br/>
To configure project-wide opt-in, specify the <code>opt-in</code> option value in <code>lint.xml</code><br/>
as a comma-delimited list of opted-in annotations:<br/>

<pre>
&lt;lint>
    &lt;issue id="UnsafeOptInUsageError">
        &lt;option name="opt-in" value="com.foo.ExperimentalBarAnnotation" />
    &lt;/issue>
&lt;/lint>
</pre>
<br/>To suppress this error, use the issue id "UnsafeOptInUsageError" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UnsafeOptInUsageError</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUnsafeOptInUsageErrorLink" onclick="reveal('explanationUnsafeOptInUsageError');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UnsafeOptInUsageErrorCardLink" onclick="hideid('UnsafeOptInUsageErrorCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="ModifierParameter"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ModifierParameterCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Guidelines for Modifier parameters in a Composable function</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt">../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt</a>:34</span>: <span class="message">Modifier parameter should be the first optional parameter</span><br /><pre class="errorlines">
<span class="lineno">  31 </span>    fatigueResult: FatigueIndexResult?,
<span class="lineno">  32 </span>    fatigueStatistics: FatigueStatistics?,
<span class="lineno">  33 </span>    isAnimated: Boolean = <span class="keyword">true</span>,
<span class="caretline"><span class="lineno">  34 </span>    <span class="warning">modifier</span>: Modifier = Modifier&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  35 </span>) {
<span class="lineno">  36 </span>    <span class="keyword">var</span> showDetails by remember { mutableStateOf(<span class="keyword">false</span>) }
<span class="lineno">  37 </span>    
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFocusDisplay.kt">../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFocusDisplay.kt</a>:34</span>: <span class="message">Modifier parameter should be the first optional parameter</span><br /><pre class="errorlines">
<span class="lineno">  31 </span>    focusResult: FocusIndexResult?,
<span class="lineno">  32 </span>    focusStatistics: FocusStatistics?,
<span class="lineno">  33 </span>    isAnimated: Boolean = <span class="keyword">true</span>,
<span class="caretline"><span class="lineno">  34 </span>    <span class="warning">modifier</span>: Modifier = Modifier&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  35 </span>) {
<span class="lineno">  36 </span>    <span class="keyword">var</span> showDetails by remember { mutableStateOf(<span class="keyword">false</span>) }
<span class="lineno">  37 </span>    
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/ComprehensiveHealthDashboard.kt">../../src/main/java/com/example/iotandroidv20/ui/components/ComprehensiveHealthDashboard.kt</a>:36</span>: <span class="message">Modifier parameter should be the first optional parameter</span><br /><pre class="errorlines">
<span class="lineno">  33 </span>    healthAssessment: HealthAssessmentResult?,
<span class="lineno">  34 </span>    analysisResult: IntelligentAnalysisResult?,
<span class="lineno">  35 </span>    isRealTime: Boolean = <span class="keyword">true</span>,
<span class="caretline"><span class="lineno">  36 </span>    <span class="warning">modifier</span>: Modifier = Modifier&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  37 </span>) {
<span class="lineno">  38 </span>    <span class="keyword">var</span> selectedTab by remember { mutableStateOf(DashboardTab.OVERVIEW) }
<span class="lineno">  39 </span>    
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/EEGBrainwaveChart.kt">../../src/main/java/com/example/iotandroidv20/ui/components/EEGBrainwaveChart.kt</a>:31</span>: <span class="message">Modifier parameter should be the first optional parameter</span><br /><pre class="errorlines">
<span class="lineno">  28 </span><span class="keyword">fun</span> EEGBrainwaveChart(
<span class="lineno">  29 </span>    frequencyAnalysis: EEGFrequencyAnalysis?,
<span class="lineno">  30 </span>    isAnimated: Boolean = <span class="keyword">true</span>,
<span class="caretline"><span class="lineno">  31 </span>    <span class="warning">modifier</span>: Modifier = Modifier&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  32 </span>) {
<span class="lineno">  33 </span>    <span class="keyword">var</span> animatedValues by remember { mutableStateOf(BrainwaveValues()) }
<span class="lineno">  34 </span>    
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/EEGFocusGauge.kt">../../src/main/java/com/example/iotandroidv20/ui/components/EEGFocusGauge.kt</a>:32</span>: <span class="message">Modifier parameter should be the first optional parameter</span><br /><pre class="errorlines">
<span class="lineno">  29 </span>    focusScore: Float,
<span class="lineno">  30 </span>    focusLevel: FocusLevel,
<span class="lineno">  31 </span>    isAnimated: Boolean = <span class="keyword">true</span>,
<span class="caretline"><span class="lineno">  32 </span>    <span class="warning">modifier</span>: Modifier = Modifier&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  33 </span>) {
<span class="lineno">  34 </span>    <span class="keyword">var</span> animatedScore by remember { mutableStateOf(<span class="number">0f</span>) }
<span class="lineno">  35 </span>    
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="ModifierParameterDivLink" onclick="reveal('ModifierParameterDiv');" />+ 7 More Occurrences...</button>
<div id="ModifierParameterDiv" style="display: none">
<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/EEGSpectrumAnalyzer.kt">../../src/main/java/com/example/iotandroidv20/ui/components/EEGSpectrumAnalyzer.kt</a>:35</span>: <span class="message">Modifier parameter should be the first optional parameter</span><br /><pre class="errorlines">
<span class="lineno">  32 </span>    analysisHistory: List&lt;EEGFrequencyAnalysis> = emptyList(),
<span class="lineno">  33 </span>    showHistory: Boolean = <span class="keyword">false</span>,
<span class="lineno">  34 </span>    isAnimated: Boolean = <span class="keyword">true</span>,
<span class="caretline"><span class="lineno">  35 </span>    <span class="warning">modifier</span>: Modifier = Modifier&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  36 </span>) {
<span class="lineno">  37 </span>    <span class="keyword">var</span> viewMode by remember { mutableStateOf(SpectrumViewMode.CURRENT) }
<span class="lineno">  38 </span>    <span class="keyword">var</span> selectedBand by remember { mutableStateOf(FrequencyBand.ALL) }
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/EEGWaveformDisplay.kt">../../src/main/java/com/example/iotandroidv20/ui/components/EEGWaveformDisplay.kt</a>:33</span>: <span class="message">Modifier parameter should be the first optional parameter</span><br /><pre class="errorlines">
<span class="lineno">  30 </span>    eegData: List&lt;EEGFrequencyAnalysis>,
<span class="lineno">  31 </span>    isRealTime: Boolean = <span class="keyword">true</span>,
<span class="lineno">  32 </span>    showGrid: Boolean = <span class="keyword">true</span>,
<span class="caretline"><span class="lineno">  33 </span>    <span class="warning">modifier</span>: Modifier = Modifier&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  34 </span>) {
<span class="lineno">  35 </span>    <span class="keyword">var</span> selectedChannel by remember { mutableStateOf(EEGChannel.ALL) }
<span class="lineno">  36 </span>    <span class="keyword">var</span> timeScale by remember { mutableStateOf(TimeScale.SECONDS_10) }
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/ErrorHandling.kt">../../src/main/java/com/example/iotandroidv20/ui/components/ErrorHandling.kt</a>:144</span>: <span class="message">Modifier parameter should be the first optional parameter</span><br /><pre class="errorlines">
<span class="lineno"> 141 </span><span class="keyword">fun</span> LoadingIndicator(
<span class="lineno"> 142 </span>    isLoading: Boolean,
<span class="lineno"> 143 </span>    message: String = <span class="string">"加载中..."</span>,
<span class="caretline"><span class="lineno"> 144 </span>    <span class="warning">modifier</span>: Modifier = Modifier&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 145 </span>) {
<span class="lineno"> 146 </span>    <span class="keyword">if</span> (isLoading) {
<span class="lineno"> 147 </span>        Card(
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/media/MediaThumbnailComponents.kt">../../src/main/java/com/example/iotandroidv20/ui/media/MediaThumbnailComponents.kt</a>:156</span>: <span class="message">Modifier parameter should be the first optional parameter</span><br /><pre class="errorlines">
<span class="lineno"> 153 </span>    audioSession: AudioSession,
<span class="lineno"> 154 </span>    onClick: () -> Unit,
<span class="lineno"> 155 </span>    compact: Boolean = <span class="keyword">false</span>,
<span class="caretline"><span class="lineno"> 156 </span>    <span class="warning">modifier</span>: Modifier = Modifier&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 157 </span>) {
<span class="lineno"> 158 </span>    Card(
<span class="lineno"> 159 </span>        modifier = modifier
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/media/MediaThumbnailComponents.kt">../../src/main/java/com/example/iotandroidv20/ui/media/MediaThumbnailComponents.kt</a>:311</span>: <span class="message">Modifier parameter should be the first optional parameter</span><br /><pre class="errorlines">
<span class="lineno"> 308 </span>    downloadStatus: DownloadStatus = DownloadStatus.NOT_DOWNLOADED,
<span class="lineno"> 309 </span>    isVideo: Boolean = <span class="keyword">true</span>,
<span class="lineno"> 310 </span>    onClick: () -> Unit,
<span class="caretline"><span class="lineno"> 311 </span>    <span class="warning">modifier</span>: Modifier = Modifier&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 312 </span>) {
<span class="lineno"> 313 </span>    Card(
<span class="lineno"> 314 </span>        modifier = modifier
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/StatsCard.kt">../../src/main/java/com/example/iotandroidv20/ui/components/StatsCard.kt</a>:177</span>: <span class="message">Modifier parameter should be the first optional parameter</span><br /><pre class="errorlines">
<span class="lineno"> 174 </span>    label: String,
<span class="lineno"> 175 </span>    value: String,
<span class="lineno"> 176 </span>    color: Color = MaterialTheme.colorScheme.primary,
<span class="caretline"><span class="lineno"> 177 </span>    <span class="warning">modifier</span>: Modifier = Modifier&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 178 </span>) {
<span class="lineno"> 179 </span>    Card(
<span class="lineno"> 180 </span>        modifier = modifier,
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/VISVideoPlayerCard.kt">../../src/main/java/com/example/iotandroidv20/ui/components/VISVideoPlayerCard.kt</a>:36</span>: <span class="message">Modifier parameter should be the first optional parameter</span><br /><pre class="errorlines">
<span class="lineno">  33 </span>    onStartVoiceRecording: () -> Unit,
<span class="lineno">  34 </span>    onStopVoiceRecording: () -> Unit,
<span class="lineno">  35 </span>    isVoiceRecording: Boolean = <span class="keyword">false</span>,
<span class="caretline"><span class="lineno">  36 </span>    <span class="warning">modifier</span>: Modifier = Modifier&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  37 </span>) {
<span class="lineno">  38 </span>    <span class="keyword">var</span> isPlaying by remember { mutableStateOf(<span class="keyword">false</span>) }
<span class="lineno">  39 </span>    <span class="keyword">var</span> videoView: VideoView? by remember { mutableStateOf(<span class="keyword">null</span>) }
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationModifierParameter" style="display: none;">
The first (or only) Modifier parameter in a Composable function should follow the following rules:<br/>
- Be named <code>modifier</code><br/>
- Have a type of <code>Modifier</code><br/>
- Either have no default value, or have a default value of <code>Modifier</code><br/>
- If optional, be the first optional parameter in the parameter list<br/>To suppress this error, use the issue id "ModifierParameter" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ModifierParameter</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationModifierParameterLink" onclick="reveal('explanationModifierParameter');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ModifierParameterCardLink" onclick="hideid('ModifierParameterCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Security"></a>
<a name="TrustAllX509TrustManager"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="TrustAllX509TrustManagerCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Insecure TLS/SSL trust manager</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../../../../.gradle/caches/modules-2/files-2.1/com.huaweicloud.sdk/huaweicloud-sdk-core/3.1.153/48a128c5558b727c003a7a7f03337d7154daf6f6/huaweicloud-sdk-core-3.1.153.jar">../../../../../.gradle/caches/modules-2/files-2.1/com.huaweicloud.sdk/huaweicloud-sdk-core/3.1.153/48a128c5558b727c003a7a7f03337d7154daf6f6/huaweicloud-sdk-core-3.1.153.jar</a></span>: <span class="message"><code>checkClientTrusted</code> is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers</span><br />
<span class="location"><a href="../../../../../.gradle/caches/modules-2/files-2.1/com.huaweicloud.sdk/huaweicloud-sdk-core/3.1.153/48a128c5558b727c003a7a7f03337d7154daf6f6/huaweicloud-sdk-core-3.1.153.jar">../../../../../.gradle/caches/modules-2/files-2.1/com.huaweicloud.sdk/huaweicloud-sdk-core/3.1.153/48a128c5558b727c003a7a7f03337d7154daf6f6/huaweicloud-sdk-core-3.1.153.jar</a></span>: <span class="message"><code>checkServerTrusted</code> is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers</span><br />
</div>
<div class="metadata"><div class="explanation" id="explanationTrustAllX509TrustManager" style="display: none;">
This check looks for X509TrustManager implementations whose <code>checkServerTrusted</code> or <code>checkClientTrusted</code> methods do nothing (thus trusting any certificate chain) which could result in insecure network traffic caused by trusting arbitrary TLS/SSL certificates presented by peers.<br/><div class="moreinfo">More info: <a href="https://goo.gle/TrustAllX509TrustManager">https://goo.gle/TrustAllX509TrustManager</a>
</div>To suppress this error, use the issue id "TrustAllX509TrustManager" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">TrustAllX509TrustManager</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Security</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationTrustAllX509TrustManagerLink" onclick="reveal('explanationTrustAllX509TrustManager');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="TrustAllX509TrustManagerCardLink" onclick="hideid('TrustAllX509TrustManagerCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Performance"></a>
<a name="AutoboxingStateCreation"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="AutoboxingStateCreationCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">State&lt;T> will autobox values assigned to this state. Use a specialized state type instead.</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt">../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt</a>:133</span>: <span class="message">Prefer <code>mutableFloatStateOf</code> instead of <code>mutableStateOf</code></span><br /><pre class="errorlines">
<span class="lineno"> 130 </span><span class="javadoc"> */</span>
<span class="lineno"> 131 </span><span class="annotation">@Composable</span>
<span class="lineno"> 132 </span>private <span class="keyword">fun</span> MainFatigueView(fatigueResult: FatigueIndexResult, isAnimated: Boolean) {
<span class="caretline"><span class="lineno"> 133 </span>    <span class="keyword">var</span> animatedFatigueIndex by remember { <span class="warning">mutableStateOf</span>(<span class="number">0f</span>) }&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 134 </span>    <span class="keyword">var</span> animatedAlertnessLevel by remember { mutableStateOf(<span class="number">0f</span>) }
<span class="lineno"> 135 </span>    
<span class="lineno"> 136 </span>    LaunchedEffect(fatigueResult.fatigueIndex, fatigueResult.alertnessLevel) {
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt">../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFatigueDisplay.kt</a>:134</span>: <span class="message">Prefer <code>mutableFloatStateOf</code> instead of <code>mutableStateOf</code></span><br /><pre class="errorlines">
<span class="lineno"> 131 </span><span class="annotation">@Composable</span>
<span class="lineno"> 132 </span>private <span class="keyword">fun</span> MainFatigueView(fatigueResult: FatigueIndexResult, isAnimated: Boolean) {
<span class="lineno"> 133 </span>    <span class="keyword">var</span> animatedFatigueIndex by remember { mutableStateOf(<span class="number">0f</span>) }
<span class="caretline"><span class="lineno"> 134 </span>    <span class="keyword">var</span> animatedAlertnessLevel by remember { <span class="warning">mutableStateOf</span>(<span class="number">0f</span>) }&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 135 </span>    
<span class="lineno"> 136 </span>    LaunchedEffect(fatigueResult.fatigueIndex, fatigueResult.alertnessLevel) {
<span class="lineno"> 137 </span>        <span class="keyword">if</span> (isAnimated) {
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFocusDisplay.kt">../../src/main/java/com/example/iotandroidv20/ui/components/AdvancedFocusDisplay.kt</a>:133</span>: <span class="message">Prefer <code>mutableFloatStateOf</code> instead of <code>mutableStateOf</code></span><br /><pre class="errorlines">
<span class="lineno"> 130 </span><span class="javadoc"> */</span>
<span class="lineno"> 131 </span><span class="annotation">@Composable</span>
<span class="lineno"> 132 </span>private <span class="keyword">fun</span> MainFocusView(focusResult: FocusIndexResult, isAnimated: Boolean) {
<span class="caretline"><span class="lineno"> 133 </span>    <span class="keyword">var</span> animatedFocusIndex by remember { <span class="warning">mutableStateOf</span>(<span class="number">0f</span>) }&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 134 </span>    
<span class="lineno"> 135 </span>    LaunchedEffect(focusResult.focusIndex) {
<span class="lineno"> 136 </span>        <span class="keyword">if</span> (isAnimated) {
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/player/AudioPlayerScreen.kt">../../src/main/java/com/example/iotandroidv20/ui/player/AudioPlayerScreen.kt</a>:415</span>: <span class="message">Prefer <code>mutableLongStateOf</code> instead of <code>mutableStateOf</code></span><br /><pre class="errorlines">
<span class="lineno"> 412 </span>    <span class="comment">// 进度条</span>
<span class="lineno"> 413 </span>    <span class="keyword">if</span> (duration > <span class="number">0</span>) {
<span class="lineno"> 414 </span>        <span class="keyword">var</span> isDragging by remember { mutableStateOf(<span class="keyword">false</span>) }
<span class="caretline"><span class="lineno"> 415 </span>        <span class="keyword">var</span> dragPosition by remember { <span class="warning">mutableStateOf</span>(currentPosition) }&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 416 </span>        
<span class="lineno"> 417 </span>        Slider(
<span class="lineno"> 418 </span>            value = <span class="keyword">if</span> (isDragging) dragPosition.toFloat() <span class="keyword">else</span> currentPosition.toFloat(),
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/ComprehensiveHealthDashboard.kt">../../src/main/java/com/example/iotandroidv20/ui/components/ComprehensiveHealthDashboard.kt</a>:372</span>: <span class="message">Prefer <code>mutableFloatStateOf</code> instead of <code>mutableStateOf</code></span><br /><pre class="errorlines">
<span class="lineno"> 369 </span>    healthLevel: HealthLevel,
<span class="lineno"> 370 </span>    confidence: Float
<span class="lineno"> 371 </span>) {
<span class="caretline"><span class="lineno"> 372 </span>    <span class="keyword">var</span> animatedScore by remember { <span class="warning">mutableStateOf</span>(<span class="number">0f</span>) }&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 373 </span>    
<span class="lineno"> 374 </span>    LaunchedEffect(score) {
<span class="lineno"> 375 </span>        animate(
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="AutoboxingStateCreationDivLink" onclick="reveal('AutoboxingStateCreationDiv');" />+ 5 More Occurrences...</button>
<div id="AutoboxingStateCreationDiv" style="display: none">
<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/EEGFocusGauge.kt">../../src/main/java/com/example/iotandroidv20/ui/components/EEGFocusGauge.kt</a>:34</span>: <span class="message">Prefer <code>mutableFloatStateOf</code> instead of <code>mutableStateOf</code></span><br /><pre class="errorlines">
<span class="lineno">  31 </span>    isAnimated: Boolean = <span class="keyword">true</span>,
<span class="lineno">  32 </span>    modifier: Modifier = Modifier
<span class="lineno">  33 </span>) {
<span class="caretline"><span class="lineno">  34 </span>    <span class="keyword">var</span> animatedScore by remember { <span class="warning">mutableStateOf</span>(<span class="number">0f</span>) }&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  35 </span>    
<span class="lineno">  36 </span>    <span class="comment">// 动画效果</span>
<span class="lineno">  37 </span>    <span class="keyword">val</span> animationSpec = <span class="keyword">if</span> (isAnimated) {
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/EEGWaveformDisplay.kt">../../src/main/java/com/example/iotandroidv20/ui/components/EEGWaveformDisplay.kt</a>:237</span>: <span class="message">Prefer <code>mutableFloatStateOf</code> instead of <code>mutableStateOf</code></span><br /><pre class="errorlines">
<span class="lineno"> 234 </span>    showGrid: Boolean,
<span class="lineno"> 235 </span>    isRealTime: Boolean
<span class="lineno"> 236 </span>) {
<span class="caretline"><span class="lineno"> 237 </span>    <span class="keyword">var</span> animationProgress by remember { <span class="warning">mutableStateOf</span>(<span class="number">0f</span>) }&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 238 </span>    
<span class="lineno"> 239 </span>    <span class="comment">// 实时动画</span>
<span class="lineno"> 240 </span>    LaunchedEffect(isRealTime) {
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/screen/LearningSupervisionScreen.kt">../../src/main/java/com/example/iotandroidv20/ui/screen/LearningSupervisionScreen.kt</a>:119</span>: <span class="message">Prefer <code>mutableIntStateOf</code> instead of <code>mutableStateOf</code></span><br /><pre class="errorlines">
<span class="lineno"> 116 </span>    onStartSession: (LearningSessionType, Int) -> Unit
<span class="lineno"> 117 </span>) {
<span class="lineno"> 118 </span>    <span class="keyword">var</span> selectedSessionType by remember { mutableStateOf(LearningSessionType.HOMEWORK) }
<span class="caretline"><span class="lineno"> 119 </span>    <span class="keyword">var</span> targetDuration by remember { <span class="warning">mutableStateOf</span>(<span class="number">45</span>) }&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 120 </span>    <span class="keyword">var</span> showDurationDialog by remember { mutableStateOf(<span class="keyword">false</span>) }
<span class="lineno"> 121 </span>    
<span class="lineno"> 122 </span>    Card(
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/screen/LearningSupervisionScreen.kt">../../src/main/java/com/example/iotandroidv20/ui/screen/LearningSupervisionScreen.kt</a>:751</span>: <span class="message">Prefer <code>mutableIntStateOf</code> instead of <code>mutableStateOf</code></span><br /><pre class="errorlines">
<span class="lineno"> 748 </span>    onDurationSelected: (Int) -> Unit,
<span class="lineno"> 749 </span>    onDismiss: () -> Unit
<span class="lineno"> 750 </span>) {
<span class="caretline"><span class="lineno"> 751 </span>    <span class="keyword">var</span> selectedDuration by remember { <span class="warning">mutableStateOf</span>(currentDuration) }&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 752 </span>    
<span class="lineno"> 753 </span>    AlertDialog(
<span class="lineno"> 754 </span>        onDismissRequest = onDismiss,
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/player/VideoPlayerScreen.kt">../../src/main/java/com/example/iotandroidv20/ui/player/VideoPlayerScreen.kt</a>:316</span>: <span class="message">Prefer <code>mutableLongStateOf</code> instead of <code>mutableStateOf</code></span><br /><pre class="errorlines">
<span class="lineno"> 313 </span>    <span class="comment">// 可拖拽的进度条</span>
<span class="lineno"> 314 </span>    <span class="keyword">if</span> (duration > <span class="number">0</span>) {
<span class="lineno"> 315 </span>        <span class="keyword">var</span> isDragging by remember { mutableStateOf(<span class="keyword">false</span>) }
<span class="caretline"><span class="lineno"> 316 </span>        <span class="keyword">var</span> dragPosition by remember { <span class="warning">mutableStateOf</span>(currentPosition) }&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 317 </span>        
<span class="lineno"> 318 </span>        Slider(
<span class="lineno"> 319 </span>            value = <span class="keyword">if</span> (isDragging) dragPosition.toFloat() <span class="keyword">else</span> currentPosition.toFloat(),
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationAutoboxingStateCreation" style="display: none;">
Calling <code>mutableStateOf&lt;T>()</code> when <code>T</code> is either backed by a primitive type on the JVM or is a value class results in a state implementation that requires all state values to be boxed. This usually causes an additional allocation for each state write, and adds some additional work to auto-unbox values when reading the value of the state. Instead, prefer to use a specialized primitive state implementation for <code>Int</code>, <code>Long</code>, <code>Float</code>, and <code>Double</code> when the state does not need to track null values and does not override the default <code>SnapshotMutationPolicy</code>. See <code>mutableIntStateOf()</code>, <code>mutableLongStateOf()</code>, <code>mutableFloatStateOf()</code>, and <code>mutableDoubleStateOf()</code> for more information.<br/>To suppress this error, use the issue id "AutoboxingStateCreation" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">AutoboxingStateCreation</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Hint</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationAutoboxingStateCreationLink" onclick="reveal('explanationAutoboxingStateCreation');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="AutoboxingStateCreationCardLink" onclick="hideid('AutoboxingStateCreationCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UnusedResources"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UnusedResourcesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Unused resources</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:3</span>: <span class="message">The resource <code>R.color.purple_200</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="lineno">  2 </span><span class="tag">&lt;resources></span>
<span class="caretline"><span class="lineno">  3 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"purple_200"</span></span>>#FFBB86FC<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  4 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_500"</span>>#FF6200EE<span class="tag">&lt;/color></span>
<span class="lineno">  5 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_700"</span>>#FF3700B3<span class="tag">&lt;/color></span>
<span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_200"</span>>#FF03DAC5<span class="tag">&lt;/color></span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:4</span>: <span class="message">The resource <code>R.color.purple_500</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="lineno">  2 </span><span class="tag">&lt;resources></span>
<span class="lineno">  3 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_200"</span>>#FFBB86FC<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno">  4 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"purple_500"</span></span>>#FF6200EE<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  5 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_700"</span>>#FF3700B3<span class="tag">&lt;/color></span>
<span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_200"</span>>#FF03DAC5<span class="tag">&lt;/color></span>
<span class="lineno">  7 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_700"</span>>#FF018786<span class="tag">&lt;/color></span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:5</span>: <span class="message">The resource <code>R.color.purple_700</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  2 </span><span class="tag">&lt;resources></span>
<span class="lineno">  3 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_200"</span>>#FFBB86FC<span class="tag">&lt;/color></span>
<span class="lineno">  4 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_500"</span>>#FF6200EE<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno">  5 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"purple_700"</span></span>>#FF3700B3<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_200"</span>>#FF03DAC5<span class="tag">&lt;/color></span>
<span class="lineno">  7 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_700"</span>>#FF018786<span class="tag">&lt;/color></span>
<span class="lineno">  8 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"black"</span>>#FF000000<span class="tag">&lt;/color></span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:6</span>: <span class="message">The resource <code>R.color.teal_200</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  3 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_200"</span>>#FFBB86FC<span class="tag">&lt;/color></span>
<span class="lineno">  4 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_500"</span>>#FF6200EE<span class="tag">&lt;/color></span>
<span class="lineno">  5 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_700"</span>>#FF3700B3<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"teal_200"</span></span>>#FF03DAC5<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  7 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_700"</span>>#FF018786<span class="tag">&lt;/color></span>
<span class="lineno">  8 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"black"</span>>#FF000000<span class="tag">&lt;/color></span>
<span class="lineno">  9 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"white"</span>>#FFFFFFFF<span class="tag">&lt;/color></span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:7</span>: <span class="message">The resource <code>R.color.teal_700</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  4 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_500"</span>>#FF6200EE<span class="tag">&lt;/color></span>
<span class="lineno">  5 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_700"</span>>#FF3700B3<span class="tag">&lt;/color></span>
<span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_200"</span>>#FF03DAC5<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno">  7 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"teal_700"</span></span>>#FF018786<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  8 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"black"</span>>#FF000000<span class="tag">&lt;/color></span>
<span class="lineno">  9 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"white"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 10 </span><span class="tag">&lt;/resources></span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:8</span>: <span class="message">The resource <code>R.color.black</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  5 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_700"</span>>#FF3700B3<span class="tag">&lt;/color></span>
<span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_200"</span>>#FF03DAC5<span class="tag">&lt;/color></span>
<span class="lineno">  7 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_700"</span>>#FF018786<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno">  8 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"black"</span></span>>#FF000000<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  9 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"white"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 10 </span><span class="tag">&lt;/resources></span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:9</span>: <span class="message">The resource <code>R.color.white</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_200"</span>>#FF03DAC5<span class="tag">&lt;/color></span>
<span class="lineno">  7 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_700"</span>>#FF018786<span class="tag">&lt;/color></span>
<span class="lineno">  8 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"black"</span>>#FF000000<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno">  9 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"white"</span></span>>#FFFFFFFF<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 10 </span><span class="tag">&lt;/resources></span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationUnusedResources" style="display: none;">
Unused resources make applications larger and slow down builds.<br/>
<br/>
<br/>
The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.<br/>
<br/>
You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.<br/>
,<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>skip-libraries</b> (default is true):<br/>
Whether the unused resource check should skip reporting unused resources in libraries.<br/>
<br/>
Many libraries will declare resources that are part of the library surface; other modules depending on the library will also reference the resources. To avoid reporting all these resources as unused (in the context of a library), the unused resource check normally skips reporting unused resources in libraries. Instead, run the unused resource check on the consuming app module (along with <code>checkDependencies=true</code>).<br/>
<br/>
However, there are cases where you want to check that all the resources declared in a library are used; in that case, you can disable the skip option.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UnusedResources"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"skip-libraries"</span> <span class="attribute">value</span>=<span class="value">"true"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "UnusedResources" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UnusedResources</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUnusedResourcesLink" onclick="reveal('explanationUnusedResources');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UnusedResourcesCardLink" onclick="hideid('UnusedResourcesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Productivity"></a>
<a name="UseKtx"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UseKtxCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Use KTX extension function</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/player/AudioPlayerManager.kt">../../src/main/java/com/example/iotandroidv20/player/AudioPlayerManager.kt</a>:189</span>: <span class="message">Use the KTX extension function <code>String.toUri</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 186 </span>                    Uri.fromFile(java.io.File(session.localFilePath))
<span class="lineno"> 187 </span>                } <span class="keyword">else</span> {
<span class="lineno"> 188 </span>                    <span class="comment">// 播放网络流</span>
<span class="caretline"><span class="lineno"> 189 </span>                    <span class="warning">Uri.parse(session.streamUrl)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 190 </span>                }
<span class="lineno"> 191 </span>                
<span class="lineno"> 192 </span>                MediaItem.Builder()
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/viewmodel/MainViewModel.kt">../../src/main/java/com/example/iotandroidv20/viewmodel/MainViewModel.kt</a>:1596</span>: <span class="message">Use the KTX extension function <code>SharedPreferences.edit</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 1593 </span>  <span class="keyword">val</span> prefs = getApplication&lt;Application>()
<span class="lineno"> 1594 </span>      .getSharedPreferences(<span class="string">"voice_interaction"</span>, Context.MODE_PRIVATE)
<span class="lineno"> 1595 </span>
<span class="caretline"><span class="lineno"> 1596 </span>  <span class="warning">prefs.edit()</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 1597 </span>      .putLong(<span class="string">"last_sent_time"</span>, System.currentTimeMillis())
<span class="lineno"> 1598 </span>      .putInt(<span class="string">"total_messages_sent"</span>, prefs.getInt(<span class="string">"total_messages_sent"</span>, <span class="number">0</span>) + <span class="number">1</span>)
<span class="lineno"> 1599 </span>      .putString(<span class="string">"device_response_status"</span>, <span class="string">"已接收"</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/auth/TokenManager.kt">../../src/main/java/com/example/iotandroidv20/auth/TokenManager.kt</a>:306</span>: <span class="message">Use the KTX extension function <code>SharedPreferences.edit</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 303 </span><span class="javadoc">   */</span>
<span class="lineno"> 304 </span>  private <span class="keyword">fun</span> cacheToken(token: String) {
<span class="lineno"> 305 </span>      <span class="keyword">val</span> currentTime = System.currentTimeMillis()
<span class="caretline"><span class="lineno"> 306 </span>      <span class="warning">prefs.edit()</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 307 </span>          .putString(KEY_TOKEN, token)
<span class="lineno"> 308 </span>          .putLong(KEY_TOKEN_TIMESTAMP, currentTime)
<span class="lineno"> 309 </span>          .putLong(KEY_TOKEN_EXPIRES_AT, currentTime + HuaweiCloudConfig.TOKEN_CACHE_DURATION)
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/auth/TokenManager.kt">../../src/main/java/com/example/iotandroidv20/auth/TokenManager.kt</a>:319</span>: <span class="message">Use the KTX extension function <code>SharedPreferences.edit</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 316 </span><span class="javadoc">     * 清除缓存的Token
</span><span class="lineno"> 317 </span><span class="javadoc">     */</span>
<span class="lineno"> 318 </span>    private <span class="keyword">fun</span> clearCachedToken() {
<span class="caretline"><span class="lineno"> 319 </span>        <span class="warning">prefs.edit()</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 320 </span>            .remove(KEY_TOKEN)
<span class="lineno"> 321 </span>            .remove(KEY_TOKEN_TIMESTAMP)
<span class="lineno"> 322 </span>            .remove(KEY_TOKEN_EXPIRES_AT)
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/settings/UserSettingsManager.kt">../../src/main/java/com/example/iotandroidv20/settings/UserSettingsManager.kt</a>:51</span>: <span class="message">Use the KTX extension function <code>SharedPreferences.edit</code> instead?</span><br /><pre class="errorlines">
<span class="lineno">  48 </span>    <span class="keyword">fun</span> saveSettings(settings: UserSettings) {
<span class="lineno">  49 </span>        <span class="keyword">try</span> {
<span class="lineno">  50 </span>            <span class="keyword">val</span> settingsJson = gson.toJson(settings)
<span class="caretline"><span class="lineno">  51 </span>            <span class="warning">sharedPreferences.edit()</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  52 </span>                .putString(KEY_USER_SETTINGS, settingsJson)
<span class="lineno">  53 </span>                .apply()
<span class="lineno">  54 </span>            
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/ui/components/VISVideoPlayerCard.kt">../../src/main/java/com/example/iotandroidv20/ui/components/VISVideoPlayerCard.kt</a>:92</span>: <span class="message">Use the KTX extension function <code>String.toUri</code> instead?</span><br /><pre class="errorlines">
<span class="lineno">  89 </span>                    <span class="comment">// 开始播放视频</span>
<span class="lineno">  90 </span>                    playbackUrl?.let { url ->
<span class="lineno">  91 </span>                        videoView?.apply {
<span class="caretline"><span class="lineno">  92 </span>                            setVideoURI(<span class="warning">Uri.parse(url)</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  93 </span>                            start()
<span class="lineno">  94 </span>                            isPlaying = <span class="keyword">true</span>
<span class="lineno">  95 </span>                        }
</pre>

<span class="location"><a href="../../src/main/java/com/example/iotandroidv20/player/VideoPlayerManager.kt">../../src/main/java/com/example/iotandroidv20/player/VideoPlayerManager.kt</a>:131</span>: <span class="message">Use the KTX extension function <code>String.toUri</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 128 </span>            } <span class="keyword">else</span> {
<span class="lineno"> 129 </span>                <span class="comment">// 播放网络流</span>
<span class="lineno"> 130 </span>                Log.d(TAG, <span class="string">"Playing network stream: ${</span>videoSession.streamUrl<span class="string">}"</span>)
<span class="caretline"><span class="lineno"> 131 </span>                <span class="warning">Uri.parse(videoSession.streamUrl)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 132 </span>            }
<span class="lineno"> 133 </span>            
<span class="lineno"> 134 </span>            <span class="comment">// 创建媒体项目</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationUseKtx" style="display: none;">
The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>remove-defaults</b> (default is true):<br/>
Whether to skip arguments that match the defaults provided by the extension.<br/>
<br/>
Extensions often provide default values for some of the parameters. For example:
<pre>
fun Path.readLines(charset: Charset = Charsets.UTF_8): List&lt;String> { return Files.readAllLines(this, charset) }
</pre>
This lint check will by default automatically omit parameters that match the default, so if your code was calling<br/>

<pre>
Files.readAllLines(file, Charset.UTF_8)
</pre>
lint would replace this with
<pre>
file.readLines()
</pre>
rather than<br/>

<pre>
file.readLines(Charset.UTF_8
</pre>
You can turn this behavior off using this option.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UseKtx"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"remove-defaults"</span> <span class="attribute">value</span>=<span class="value">"true"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
<b>require-present</b> (default is true):<br/>
Whether to only offer extensions already available.<br/>
<br/>
This option lets you only have lint suggest extension replacements if those extensions are already available on the class path (in other words, you're already depending on the library containing the extension method.)<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UseKtx"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"require-present"</span> <span class="attribute">value</span>=<span class="value">"true"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div>To suppress this error, use the issue id "UseKtx" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UseKtx</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Productivity</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUseKtxLink" onclick="reveal('explanationUseKtx');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UseKtxCardLink" onclick="hideid('UseKtxCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UseTomlInstead"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UseTomlInsteadCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Use TOML Version Catalog Instead</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:72</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno">  69 </span>    implementation(files("../javaApiDemo/src/main/resources/lib/joda-time-2.10.jar"))
<span class="lineno">  70 </span>
<span class="lineno">  71 </span>    // 使用Android兼容的HTTP客户端（用于实际网络请求）
<span class="caretline"><span class="lineno">  72 </span>    implementation(<span class="warning">"com.squareup.okhttp3:okhttp:4.12.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  73 </span>    implementation("com.squareup.okhttp3:logging-interceptor:4.12.0")
<span class="lineno">  74 </span>
<span class="lineno">  75 </span>    // Apache HttpClient（华为云SDK签名需要，但我们不直接用于网络请求）
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:73</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno">  70 </span>
<span class="lineno">  71 </span>    // 使用Android兼容的HTTP客户端（用于实际网络请求）
<span class="lineno">  72 </span>    implementation("com.squareup.okhttp3:okhttp:4.12.0")
<span class="caretline"><span class="lineno">  73 </span>    implementation(<span class="warning">"com.squareup.okhttp3:logging-interceptor:4.12.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  74 </span>
<span class="lineno">  75 </span>    // Apache HttpClient（华为云SDK签名需要，但我们不直接用于网络请求）
<span class="lineno">  76 </span>    implementation("org.apache.httpcomponents:httpclient:4.5.13") {
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:80</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno">  77 </span>        exclude(group = "commons-logging", module = "commons-logging")
<span class="lineno">  78 </span>        exclude(group = "commons-codec", module = "commons-codec")
<span class="lineno">  79 </span>    }
<span class="caretline"><span class="lineno">  80 </span>    implementation(<span class="warning">"org.apache.httpcomponents:httpcore:4.4.15"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  81 </span>
<span class="lineno">  82 </span>    // 统一的依赖版本（避免冲突）
<span class="lineno">  83 </span>    implementation("commons-codec:commons-codec:1.11")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:83</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno">  80 </span>    implementation("org.apache.httpcomponents:httpcore:4.4.15")
<span class="lineno">  81 </span>
<span class="lineno">  82 </span>    // 统一的依赖版本（避免冲突）
<span class="caretline"><span class="lineno">  83 </span>    implementation(<span class="warning">"commons-codec:commons-codec:1.11"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  84 </span>    implementation("commons-logging:commons-logging:1.2")
<span class="lineno">  85 </span>
<span class="lineno">  86 </span>    // 使用统一的Jackson版本（避免冲突）
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:84</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno">  81 </span>
<span class="lineno">  82 </span>    // 统一的依赖版本（避免冲突）
<span class="lineno">  83 </span>    implementation("commons-codec:commons-codec:1.11")
<span class="caretline"><span class="lineno">  84 </span>    implementation(<span class="warning">"commons-logging:commons-logging:1.2"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  85 </span>
<span class="lineno">  86 </span>    // 使用统一的Jackson版本（避免冲突）
<span class="lineno">  87 </span>    implementation("com.fasterxml.jackson.core:jackson-core:2.8.1")
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="UseTomlInsteadDivLink" onclick="reveal('UseTomlInsteadDiv');" />+ 28 More Occurrences...</button>
<div id="UseTomlInsteadDiv" style="display: none">
<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:87</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno">  84 </span>    implementation("commons-logging:commons-logging:1.2")
<span class="lineno">  85 </span>
<span class="lineno">  86 </span>    // 使用统一的Jackson版本（避免冲突）
<span class="caretline"><span class="lineno">  87 </span>    implementation(<span class="warning">"com.fasterxml.jackson.core:jackson-core:2.8.1"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  88 </span>    implementation("com.fasterxml.jackson.core:jackson-databind:2.8.1")
<span class="lineno">  89 </span>    implementation("com.fasterxml.jackson.core:jackson-annotations:2.8.1")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:88</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno">  85 </span>
<span class="lineno">  86 </span>    // 使用统一的Jackson版本（避免冲突）
<span class="lineno">  87 </span>    implementation("com.fasterxml.jackson.core:jackson-core:2.8.1")
<span class="caretline"><span class="lineno">  88 </span>    implementation(<span class="warning">"com.fasterxml.jackson.core:jackson-databind:2.8.1"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  89 </span>    implementation("com.fasterxml.jackson.core:jackson-annotations:2.8.1")
<span class="lineno">  90 </span>
<span class="lineno">  91 </span>    // JSON处理
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:89</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno">  86 </span>    // 使用统一的Jackson版本（避免冲突）
<span class="lineno">  87 </span>    implementation("com.fasterxml.jackson.core:jackson-core:2.8.1")
<span class="lineno">  88 </span>    implementation("com.fasterxml.jackson.core:jackson-databind:2.8.1")
<span class="caretline"><span class="lineno">  89 </span>    implementation(<span class="warning">"com.fasterxml.jackson.core:jackson-annotations:2.8.1"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  90 </span>
<span class="lineno">  91 </span>    // JSON处理
<span class="lineno">  92 </span>    implementation("com.google.code.gson:gson:2.10.1")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:92</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno">  89 </span>    implementation("com.fasterxml.jackson.core:jackson-annotations:2.8.1")
<span class="lineno">  90 </span>
<span class="lineno">  91 </span>    // JSON处理
<span class="caretline"><span class="lineno">  92 </span>    implementation(<span class="warning">"com.google.code.gson:gson:2.10.1"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  93 </span>    implementation(libs.androidx.ui)
<span class="lineno">  94 </span>    implementation(libs.androidx.ui.graphics)
<span class="lineno">  95 </span>    implementation(libs.androidx.ui.tooling.preview)
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:99</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno">  96 </span>    implementation(libs.androidx.material3)
<span class="lineno">  97 </span>
<span class="lineno">  98 </span>    // Material Icons扩展包
<span class="caretline"><span class="lineno">  99 </span>    implementation(<span class="warning">"androidx.compose.material:material-icons-extended:1.5.8"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 100 </span>
<span class="lineno"> 101 </span>    // Compose动画
<span class="lineno"> 102 </span>    implementation("androidx.compose.animation:animation:1.5.8")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:102</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno">  99 </span>    implementation("androidx.compose.material:material-icons-extended:1.5.8")
<span class="lineno"> 100 </span>
<span class="lineno"> 101 </span>    // Compose动画
<span class="caretline"><span class="lineno"> 102 </span>    implementation(<span class="warning">"androidx.compose.animation:animation:1.5.8"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 103 </span>
<span class="lineno"> 104 </span>    // Compose Foundation
<span class="lineno"> 105 </span>    implementation("androidx.compose.foundation:foundation:1.5.8")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:105</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 102 </span>    implementation("androidx.compose.animation:animation:1.5.8")
<span class="lineno"> 103 </span>
<span class="lineno"> 104 </span>    // Compose Foundation
<span class="caretline"><span class="lineno"> 105 </span>    implementation(<span class="warning">"androidx.compose.foundation:foundation:1.5.8"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 106 </span>
<span class="lineno"> 107 </span>    // Navigation Compose
<span class="lineno"> 108 </span>    implementation("androidx.navigation:navigation-compose:2.7.6")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:108</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 105 </span>    implementation("androidx.compose.foundation:foundation:1.5.8")
<span class="lineno"> 106 </span>
<span class="lineno"> 107 </span>    // Navigation Compose
<span class="caretline"><span class="lineno"> 108 </span>    implementation(<span class="warning">"androidx.navigation:navigation-compose:2.7.6"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 109 </span>
<span class="lineno"> 110 </span>    // 华为云IoT SDK (使用通用MQTT客户端)
<span class="lineno"> 111 </span>    // implementation("com.huaweicloud.sdk:huaweicloud-sdk-iot:3.1.62")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:115</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 112 </span>    // implementation("com.huaweicloud.sdk:huaweicloud-sdk-core:3.1.62")
<span class="lineno"> 113 </span>
<span class="lineno"> 114 </span>    // 华为云IoT SDK
<span class="caretline"><span class="lineno"> 115 </span>    implementation(<span class="warning">"com.huaweicloud.sdk:huaweicloud-sdk-iotda:3.1.153"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 116 </span>    implementation("com.huaweicloud.sdk:huaweicloud-sdk-core:3.1.153")
<span class="lineno"> 117 </span>
<span class="lineno"> 118 </span>    // 华为云SDK依赖的HTTP客户端
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:116</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 113 </span>
<span class="lineno"> 114 </span>    // 华为云IoT SDK
<span class="lineno"> 115 </span>    implementation("com.huaweicloud.sdk:huaweicloud-sdk-iotda:3.1.153")
<span class="caretline"><span class="lineno"> 116 </span>    implementation(<span class="warning">"com.huaweicloud.sdk:huaweicloud-sdk-core:3.1.153"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 117 </span>
<span class="lineno"> 118 </span>    // 华为云SDK依赖的HTTP客户端
<span class="lineno"> 119 </span>    implementation("com.squareup.okhttp3:okhttp:4.12.0")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:119</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 116 </span>    implementation("com.huaweicloud.sdk:huaweicloud-sdk-core:3.1.153")
<span class="lineno"> 117 </span>
<span class="lineno"> 118 </span>    // 华为云SDK依赖的HTTP客户端
<span class="caretline"><span class="lineno"> 119 </span>    implementation(<span class="warning">"com.squareup.okhttp3:okhttp:4.12.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 120 </span>
<span class="lineno"> 121 </span>    // JSON处理
<span class="lineno"> 122 </span>    implementation("com.fasterxml.jackson.core:jackson-core:2.15.2")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:122</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 119 </span>    implementation("com.squareup.okhttp3:okhttp:4.12.0")
<span class="lineno"> 120 </span>
<span class="lineno"> 121 </span>    // JSON处理
<span class="caretline"><span class="lineno"> 122 </span>    implementation(<span class="warning">"com.fasterxml.jackson.core:jackson-core:2.15.2"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 123 </span>    implementation("com.fasterxml.jackson.core:jackson-databind:2.15.2")
<span class="lineno"> 124 </span>    implementation("com.fasterxml.jackson.core:jackson-annotations:2.15.2")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:123</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 120 </span>
<span class="lineno"> 121 </span>    // JSON处理
<span class="lineno"> 122 </span>    implementation("com.fasterxml.jackson.core:jackson-core:2.15.2")
<span class="caretline"><span class="lineno"> 123 </span>    implementation(<span class="warning">"com.fasterxml.jackson.core:jackson-databind:2.15.2"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 124 </span>    implementation("com.fasterxml.jackson.core:jackson-annotations:2.15.2")
<span class="lineno"> 125 </span>
<span class="lineno"> 126 </span>    // 网络请求
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:124</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 121 </span>    // JSON处理
<span class="lineno"> 122 </span>    implementation("com.fasterxml.jackson.core:jackson-core:2.15.2")
<span class="lineno"> 123 </span>    implementation("com.fasterxml.jackson.core:jackson-databind:2.15.2")
<span class="caretline"><span class="lineno"> 124 </span>    implementation(<span class="warning">"com.fasterxml.jackson.core:jackson-annotations:2.15.2"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 125 </span>
<span class="lineno"> 126 </span>    // 网络请求
<span class="lineno"> 127 </span>    implementation("com.squareup.okhttp3:okhttp:4.12.0")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:127</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 124 </span>    implementation("com.fasterxml.jackson.core:jackson-annotations:2.15.2")
<span class="lineno"> 125 </span>
<span class="lineno"> 126 </span>    // 网络请求
<span class="caretline"><span class="lineno"> 127 </span>    implementation(<span class="warning">"com.squareup.okhttp3:okhttp:4.12.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 128 </span>    implementation("com.squareup.retrofit2:retrofit:2.9.0")
<span class="lineno"> 129 </span>    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:128</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 125 </span>
<span class="lineno"> 126 </span>    // 网络请求
<span class="lineno"> 127 </span>    implementation("com.squareup.okhttp3:okhttp:4.12.0")
<span class="caretline"><span class="lineno"> 128 </span>    implementation(<span class="warning">"com.squareup.retrofit2:retrofit:2.9.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 129 </span>    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
<span class="lineno"> 130 </span>
<span class="lineno"> 131 </span>    // JSON解析
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:129</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 126 </span>    // 网络请求
<span class="lineno"> 127 </span>    implementation("com.squareup.okhttp3:okhttp:4.12.0")
<span class="lineno"> 128 </span>    implementation("com.squareup.retrofit2:retrofit:2.9.0")
<span class="caretline"><span class="lineno"> 129 </span>    implementation(<span class="warning">"com.squareup.retrofit2:converter-gson:2.9.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 130 </span>
<span class="lineno"> 131 </span>    // JSON解析
<span class="lineno"> 132 </span>    implementation("com.google.code.gson:gson:2.10.1")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:132</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 129 </span>    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
<span class="lineno"> 130 </span>
<span class="lineno"> 131 </span>    // JSON解析
<span class="caretline"><span class="lineno"> 132 </span>    implementation(<span class="warning">"com.google.code.gson:gson:2.10.1"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 133 </span>
<span class="lineno"> 134 </span>    // 协程支持
<span class="lineno"> 135 </span>    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:135</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 132 </span>    implementation("com.google.code.gson:gson:2.10.1")
<span class="lineno"> 133 </span>
<span class="lineno"> 134 </span>    // 协程支持
<span class="caretline"><span class="lineno"> 135 </span>    implementation(<span class="warning">"org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 136 </span>
<span class="lineno"> 137 </span>    // ViewModel和LiveData
<span class="lineno"> 138 </span>    implementation("androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:138</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 135 </span>    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")
<span class="lineno"> 136 </span>
<span class="lineno"> 137 </span>    // ViewModel和LiveData
<span class="caretline"><span class="lineno"> 138 </span>    implementation(<span class="warning">"androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 139 </span>    implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.7.0")
<span class="lineno"> 140 </span>
<span class="lineno"> 141 </span>    // 权限处理
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:139</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 136 </span>
<span class="lineno"> 137 </span>    // ViewModel和LiveData
<span class="lineno"> 138 </span>    implementation("androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0")
<span class="caretline"><span class="lineno"> 139 </span>    implementation(<span class="warning">"androidx.lifecycle:lifecycle-livedata-ktx:2.7.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 140 </span>
<span class="lineno"> 141 </span>    // 权限处理
<span class="lineno"> 142 </span>    implementation("com.google.accompanist:accompanist-permissions:0.32.0")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:142</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 139 </span>    implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.7.0")
<span class="lineno"> 140 </span>
<span class="lineno"> 141 </span>    // 权限处理
<span class="caretline"><span class="lineno"> 142 </span>    implementation(<span class="warning">"com.google.accompanist:accompanist-permissions:0.32.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 143 </span>
<span class="lineno"> 144 </span>    // 华为云OBS SDK - 使用HTTP API实现，不需要额外依赖
<span class="lineno"> 145 </span>    // implementation("com.huaweicloud:esdk-obs-android:3.25.5")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:148</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 145 </span>    // implementation("com.huaweicloud:esdk-obs-android:3.25.5")
<span class="lineno"> 146 </span>
<span class="lineno"> 147 </span>    // ExoPlayer媒体播放器
<span class="caretline"><span class="lineno"> 148 </span>    implementation(<span class="warning">"androidx.media3:media3-exoplayer:1.2.1"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 149 </span>    implementation("androidx.media3:media3-ui:1.2.1")
<span class="lineno"> 150 </span>    implementation("androidx.media3:media3-common:1.2.1")
<span class="lineno"> 151 </span>    implementation("androidx.media3:media3-session:1.2.1")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:149</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 146 </span>
<span class="lineno"> 147 </span>    // ExoPlayer媒体播放器
<span class="lineno"> 148 </span>    implementation("androidx.media3:media3-exoplayer:1.2.1")
<span class="caretline"><span class="lineno"> 149 </span>    implementation(<span class="warning">"androidx.media3:media3-ui:1.2.1"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 150 </span>    implementation("androidx.media3:media3-common:1.2.1")
<span class="lineno"> 151 </span>    implementation("androidx.media3:media3-session:1.2.1")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:150</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 147 </span>    // ExoPlayer媒体播放器
<span class="lineno"> 148 </span>    implementation("androidx.media3:media3-exoplayer:1.2.1")
<span class="lineno"> 149 </span>    implementation("androidx.media3:media3-ui:1.2.1")
<span class="caretline"><span class="lineno"> 150 </span>    implementation(<span class="warning">"androidx.media3:media3-common:1.2.1"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 151 </span>    implementation("androidx.media3:media3-session:1.2.1")
<span class="lineno"> 152 </span>
<span class="lineno"> 153 </span>    // 图片加载
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:151</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 148 </span>    implementation("androidx.media3:media3-exoplayer:1.2.1")
<span class="lineno"> 149 </span>    implementation("androidx.media3:media3-ui:1.2.1")
<span class="lineno"> 150 </span>    implementation("androidx.media3:media3-common:1.2.1")
<span class="caretline"><span class="lineno"> 151 </span>    implementation(<span class="warning">"androidx.media3:media3-session:1.2.1"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 152 </span>
<span class="lineno"> 153 </span>    // 图片加载
<span class="lineno"> 154 </span>    implementation("io.coil-kt:coil-compose:2.5.0")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:154</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 151 </span>    implementation("androidx.media3:media3-session:1.2.1")
<span class="lineno"> 152 </span>
<span class="lineno"> 153 </span>    // 图片加载
<span class="caretline"><span class="lineno"> 154 </span>    implementation(<span class="warning">"io.coil-kt:coil-compose:2.5.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 155 </span>
<span class="lineno"> 156 </span>    // 文件选择器
<span class="lineno"> 157 </span>    implementation("androidx.activity:activity-compose:1.8.2")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:157</span>: <span class="message">Use version catalog instead (androidx.activity:activity-compose is already available as <code>androidx-activity-compose</code>, but using version 1.10.1 instead)</span><br /><pre class="errorlines">
<span class="lineno"> 154 </span>    implementation("io.coil-kt:coil-compose:2.5.0")
<span class="lineno"> 155 </span>
<span class="lineno"> 156 </span>    // 文件选择器
<span class="caretline"><span class="lineno"> 157 </span>    implementation(<span class="warning">"androidx.activity:activity-compose:1.8.2"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 158 </span>
<span class="lineno"> 159 </span>    // Room数据库 - 暂时禁用，解决编译问题
<span class="lineno"> 160 </span>    // implementation("androidx.room:room-runtime:2.6.1")
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationUseTomlInstead" style="display: none;">
If your project is using a <code>libs.versions.toml</code> file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically).<br/>To suppress this error, use the issue id "UseTomlInstead" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UseTomlInstead</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Productivity</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUseTomlInsteadLink" onclick="reveal('explanationUseTomlInstead');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UseTomlInsteadCardLink" onclick="hideid('UseTomlInsteadCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="ExtraIssues"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ExtraIssuesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Included Additional Checks</h2>
  </div>
              <div class="mdl-card__supporting-text">
This card lists all the extra checks run by lint, provided from libraries,
build configuration and extra flags. This is included to help you verify
whether a particular check is included in analysis when configuring builds.
(Note that the list does not include the hundreds of built-in checks into lint,
only additional ones.)
<div id="IncludedIssues" style="display: none;"><br/><br/><div class="issue">
<div class="id">ArcAnimationSpecTypeIssue<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
ArcAnimationSpec is designed for 2D values. Particularly, for positional values such as Offset.<br/>
Trying to use it for values of different dimensions (Float, Size, Color, etc.) will result in unpredictable animation behavior.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.animation.core<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AutoboxingStateCreation<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Calling <code>mutableStateOf&lt;T>()</code> when <code>T</code> is either backed by a primitive type on the JVM or is a value class results in a state implementation that requires all state values to be boxed. This usually causes an additional allocation for each state write, and adds some additional work to auto-unbox values when reading the value of the state. Instead, prefer to use a specialized primitive state implementation for <code>Int</code>, <code>Long</code>, <code>Float</code>, and <code>Double</code> when the state does not need to track null values and does not override the default <code>SnapshotMutationPolicy</code>. See <code>mutableIntStateOf()</code>, <code>mutableLongStateOf()</code>, <code>mutableFloatStateOf()</code>, and <code>mutableDoubleStateOf()</code> for more information.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AutoboxingStateValueProperty<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Avoid using the generic <code>value</code> property when using a specialized State type. Reading or writing to the state's generic <code>value</code> property will result in an unnecessary autoboxing operation. Prefer the specialized value property (e.g. <code>intValue</code> for <code>MutableIntState</code>), or use property delegation to avoid unnecessary allocations.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ComposableDestinationInComposeScope<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Composable destinations should only be constructed directly within a NavGraphBuilder scope. Composable destinations cannot not be nested, and you should use the <code>navigation</code> function to create a nested graph instead.<br/><div class="vendor">
Vendor: Jetpack Navigation Compose<br/>
Identifier: androidx.navigation.compose<br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ComposableNaming<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
@Composable functions without a return type should use similar naming to classes, starting with an uppercase letter and ending with a noun. @Composable functions with a return type should be treated as normal Kotlin functions, starting with a lowercase letter.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ComposableNavGraphInComposeScope<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Composable destinations should only be constructed directly within a NavGraphBuilder scope.<br/><div class="vendor">
Vendor: Jetpack Navigation Compose<br/>
Identifier: androidx.navigation.compose<br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">CompositionLocalNaming<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
CompositionLocal properties should be prefixed with <code>Local</code>. This helps make it clear at their use site that these values are local to the current composition. Typically the full name will be <code>Local</code> + the type of the CompositionLocal, for example val LocalFoo = compositionLocalOf { Foo() }.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ConflictingOnColor<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
In the Material color system background colors have a corresponding 'on' color which is used for the content color inside a component. For example, a button colored <code>primary</code> will have <code>onPrimary</code> text. Because of this, it is important that there is only one possible <code>onColor</code> for a given color value, otherwise there is no way to know which 'on' color should be used inside a component. To fix this either use the same 'on' color for identical background colors, or use a different background color for each 'on' color.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.material<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ContextCastToActivity<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Casting Context to Activity is an error as Contexts are not always Activities. Use LocalActivity instead<br/><div class="vendor">
Vendor: Jetpack Activity Compose<br/>
Identifier: androidx.activity.compose<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">CoroutineCreationDuringComposition<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Creating a coroutine with <code>async</code> or <code>launch</code> during composition is often incorrect - this means that a coroutine will be created even if the composition fails / is rolled back, and it also means that multiple coroutines could end up mutating the same state, causing inconsistent results. Instead, use <code>LaunchedEffect</code> and create coroutines inside the suspending block. The block will only run after a successful composition, and will cancel existing coroutines when <code>key</code> changes, allowing correct cleanup.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DeepLinkInActivityDestination<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Attaching a &lt;deeplink> to an &lt;activity> destination will never give                 the right behavior when using an implicit deep link on another app's task                 (where the system back should immediately take the user back to the app that                 triggered the deep link). Instead, attach the deep link directly to                 the second activity (either by manually writing the appropriate &lt;intent-filter>                 or by adding the &lt;deeplink> to the start destination of a nav host in that second                 activity).<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EmptyNavDeepLink<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Attempting to create an empty NavDeepLink will result in an IllegalStateException at runtime. You may set these arguments within the lambda of the call to navDeepLink.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.common<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EnsureInitializerMetadata<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When a library defines a Initializer, it needs to be accompanied by a corresponding &lt;meta-data> entry in the AndroidManifest.xml file.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.startup<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=823348">https://issuetracker.google.com/issues/new?component=823348</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EnsureInitializerNoArgConstr<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Every <code>Initializer</code> must have a no argument constructor.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.startup<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=823348">https://issuetracker.google.com/issues/new?component=823348</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExperimentalAnnotationRetention<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Experimental annotations defined in Java source should use default (<code>CLASS</code>) retention, while Kotlin-sourced annotations should use <code>BINARY</code> retention.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FlowOperatorInvokedInComposition<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Calling a Flow operator function within composition will result in a new Flow being created every recomposition, which will reset collectAsState() and cause other related problems. Instead Flow operators should be called inside <code>remember</code>, or a side effect such as LaunchedEffect.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FrequentlyChangedStateReadInComposition<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This property is observable and is updated after every scroll or remeasure. If you use it in the composable function directly, it will be recomposed on every change, causing potential performance issues including infinity recomposition loops. Prefer wrapping it with derivedStateOf to use calculation based on this property in composition or collect changes inside LaunchedEffect instead.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.foundation<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidColorHexValue<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Creating a Color with a hex value requires a 32 bit value (such as 0xFF000000), with 8 bits being used per channel (ARGB). Not passing a full 32 bit value will result in channels being undefined / incorrect.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui.graphics<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidFragmentVersionForActivityResult<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
In order to use the ActivityResult APIs you must upgrade your                 Fragment version to 1.3.0. Previous versions of FragmentActivity                 failed to call super.onRequestPermissionsResult() and used invalid request codes<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/permissions/requesting#make-the-request">https://developer.android.com/training/permissions/requesting#make-the-request</a>
</div><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.activity<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=527362">https://issuetracker.google.com/issues/new?component=527362</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidLanguageTagDelimiter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
A language tag must be compliant with IETF BCP47, specifically a sequence of subtags must be separated by hyphens (-) instead of underscores (_)<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui.text<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=779818">https://issuetracker.google.com/issues/new?component=779818</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidSetHasFixedSize<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When a RecyclerView uses <code>setHasFixedSize(...)</code> you cannot use <code>wrap_content</code> for  size in the scrolling direction.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.recyclerview<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460887">https://issuetracker.google.com/issues/new?component=460887</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidUseOfOnBackPressed<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
You should not used OnBackPressedCallback for non-UI cases. If you<br/>
                |add a callback, you have to handle back completely in the callback.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/navigation/custom-back/predictive-back-gesture#ui-logic">https://developer.android.com/guide/navigation/custom-back/predictive-back-gesture#ui-logic</a>
</div><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.activity<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=527362">https://issuetracker.google.com/issues/new?component=527362</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LaunchDuringComposition<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Calling <code>launch</code> during composition is incorrect. Doing so will cause launch to be called multiple times resulting in a RuntimeException. Instead, use <code>SideEffect</code> and <code>launch</code> inside of the suspending block. The block will only run after a successful composition.<br/><div class="vendor">
Vendor: Jetpack Activity Compose<br/>
Identifier: androidx.activity.compose<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LifecycleCurrentStateInComposition<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Calling Lifecycle.currentState within composition will not observe changes to the Lifecycle, so changes might not be reflected within the composition. Instead you should use lifecycle.currentStateAsState() to observe changes to the Lifecycle, and recompose when it changes.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingColorAlphaChannel<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Creating a Color with a hex value requires a 32 bit value (such as 0xFF000000), with 8 bits being used per channel (ARGB). Not passing a full 32 bit value will result in channels being undefined. For example, passing 0xFF0000 will result in a missing alpha channel, so the color will not appear visible.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui.graphics<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModifierFactoryExtensionFunction<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Modifier factory functions should be defined as extension functions on Modifier to allow modifiers to be fluently chained.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModifierFactoryReturnType<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Modifier factory functions should return Modifier as their type, and not a subtype of Modifier (such as Modifier.Element).<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModifierFactoryUnreferencedReceiver<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Modifier factory functions are fluently chained to construct a chain of Modifier objects that will be applied to a layout. As a result, each factory function <i>must</i> use the receiver <code>Modifier</code> parameter, to ensure that the function is returning a chain that includes previous items in the chain. Make sure the returned chain either explicitly includes <code>this</code>, such as <code>return this.then(MyModifier)</code> or implicitly by returning a chain that starts with an implicit call to another factory function, such as <code>return myModifier()</code>, where <code>myModifier</code> is defined as <code>fun Modifier.myModifier(): Modifier</code>.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModifierNodeInspectableProperties<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
ModifierNodeElements may override inspectableProperties() to provide information about the modifier in the layout inspector. The default implementation attempts to read all of the properties on the class reflectively, which may not comprehensively or effectively describe the modifier.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModifierParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The first (or only) Modifier parameter in a Composable function should follow the following rules:<br/>
- Be named <code>modifier</code><br/>
- Have a type of <code>Modifier</code><br/>
- Either have no default value, or have a default value of <code>Modifier</code><br/>
- If optional, be the first optional parameter in the parameter list<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MultipleAwaitPointerEventScopes<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Pointer Input events are queued inside awaitPointerEventScope. Multiple calls to awaitPointerEventScope may exit the scope. During this time there is no guarantee that the events will be queued and some events may be dropped. It is recommended to use a single top-level block and perform the pointer events processing within such block.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MutableCollectionMutableState<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Writes to mutable collections inside a MutableState will not cause a recomposition - only writes to the MutableState itself will. In most cases you should either use a read-only collection (such as List or Map) and assign a new instance to the MutableState when your data changes, or you can use an snapshot-backed collection such as SnapshotStateList or SnapshotStateMap which will correctly cause a recomposition when their contents are modified.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoCollectCallFound<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
You must call collect on the progress in the onBack function. The collect call is what properly splits the callback so it knows what to do when the back gestures is started vs when it is completed. Failing to call collect will cause all code in the block to run when the gesture is started.<br/><div class="vendor">
Vendor: Jetpack Activity Compose<br/>
Identifier: androidx.activity.compose<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NullSafeMutableLiveData<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This check ensures that LiveData values are not null when explicitly                 declared as non-nullable.<br/>
<br/>
                Kotlin interoperability does not support enforcing explicit null-safety when using                 generic Java type parameters. Since LiveData is a Java class its value can always                 be null even when its type is explicitly declared as non-nullable. This can lead                 to runtime exceptions from reading a null LiveData value that is assumed to be                 non-nullable.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">OpaqueUnitKey<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Certain Compose functions including <code>remember</code>, <code>LaunchedEffect</code>, and <code>DisposableEffect</code> declare (and sometimes require) one or more key parameters. When a key parameter changes, it is a signal that the previous invocation is now invalid. In certain cases, it may be required to pass <code>Unit</code> as a key to one of these functions, indicating that the invocation never becomes invalid. Using <code>Unit</code> as a key should be done infrequently, and should always be done explicitly by passing the <code>Unit</code> literal. This inspection checks for invocations where <code>Unit</code> is being passed as a key argument in any form other than the <code>Unit</code> literal. This is usually done by mistake, and can harm readability. If a Unit expression is being passed as a key, it is always equivalent to move the expression before the function invocation and pass the <code>Unit</code> literal instead.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PermissionLaunchedDuringComposition<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Calls to <code>launchPermissionRequest</code> or <code>launchMultiplePermissionRequest</code> in the Composition throw a runtime exception. Please call them inside a regular lambda or in a side-effect.<br/><div class="vendor">
Vendor: Accompanist Permissions<br/>
Identifier: com.google.accompanist.permissions<br/>
Feedback: <a href="https://github.com/google/accompanist/issues/new/choose">https://github.com/google/accompanist/issues/new/choose</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ProduceStateDoesNotAssignValue<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
produceState returns an observable State using values assigned inside the producer lambda. If the lambda never assigns (i.e <code>value = foo</code>), then the State will never change. Make sure to assign a value when the source you are producing values from changes / emits a new value. For sample usage see the produceState documentation.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RememberReturnType<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
A call to <code>remember</code> that returns <code>Unit</code> is always an error. This typically happens when using <code>remember</code> to mutate variables on an object. <code>remember</code> is executed during the composition, which means that if the composition fails or is happening on a separate thread, the mutated variables may not reflect the true state of the composition. Instead, use <code>SideEffect</code> to make deferred changes once the composition succeeds, or mutate <code>MutableState</code> backed variables directly, as these will handle composition failure for you.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RememberSaveableSaverParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The first parameter to <code>rememberSaveable</code> is a vararg parameter for inputs that when changed will cause the state to reset. Passing a <code>Saver</code> object to this parameter is an error, as the intention is to pass the <code>Saver</code> object to the saver parameter. Since the saver parameter is not the first parameter, it must be explicitly named.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime.saveable<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RepeatOnLifecycleWrongUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The repeatOnLifecycle APIs should be used when the View is created,                 that is in the <code>onCreate</code> lifecycle method for Activities, or <code>onViewCreated</code> in                 case you're using Fragments.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ReturnFromAwaitPointerEventScope<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Pointer Input events are queued inside awaitPointerEventScope. By using the return value of awaitPointerEventScope one might unexpectedly lose events. If another awaitPointerEventScope is restarted there is no guarantee that the events will persist between those calls. In this case you should keep all events inside the awaitPointerEventScope block<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StateFlowValueCalledInComposition<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Calling StateFlow.value within composition will not observe changes to the StateFlow, so changes might not be reflected within the composition. Instead you should use stateFlow.collectAsState() to observe changes to the StateFlow, and recompose when it changes.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SuspiciousCompositionLocalModifierRead<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Jetpack Compose is unable to send updated values of a CompositionLocal when it's read in a Modifier.Node's initializer and onAttach() or onDetach() callbacks. Modifier.Node's callbacks are not aware of snapshot reads, and their lifecycle callbacks are not invoked on every recomposition. If you read a CompositionLocal in onAttach() or onDetach(), you will only get the CompositionLocal's value once at the moment of the read, which may lead to unexpected behaviors. We recommend instead reading CompositionLocals at time-of-use in callbacks that apply your Modifier's behavior, like measure() for LayoutModifierNode, draw() for DrawModifierNode, and so on. To observe the value of the CompositionLocal manually, extend from the ObserverNode interface and place the read inside an observeReads {} block within the onObservedReadsChanged() callback.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SuspiciousModifierThen<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Calling a Modifier factory function with an implicit receiver inside Modifier.then will result in the receiver (<code>this</code>) being added twice to the chain. For example, fun Modifier.myModifier() = this.then(otherModifier()) - the implementation of factory functions such as Modifier.otherModifier() will internally call this.then(...) to chain the provided modifier with their implementation. When you expand this.then(otherModifier()), it becomes: this.then(this.then(OtherModifierImplementation)) - so you can see that <code>this</code> is included twice in the chain, which results in modifiers such as padding being applied twice, for example. Instead, you should either remove the then() and directly chain the factory function on the receiver, this.otherModifier(), or add the empty Modifier as the receiver for the factory, such as this.then(Modifier.otherModifier())<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TestManifestGradleConfiguration<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The androidx.compose.ui:ui-test-manifest dependency is needed for launching a Compose host, such as with createComposeRule. However, it only needs to be present in testing configurations therefore use this dependency with the debugImplementation configuration<br/><div class="moreinfo">More info: <a href="https://developer.android.com/jetpack/compose/testing#setup">https://developer.android.com/jetpack/compose/testing#setup</a>
</div><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.compose.ui.test.manifest<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=741505">https://issuetracker.google.com/issues/new?component=741505</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnnecessaryComposedModifier<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>Modifier.composed</code> allows invoking @Composable functions when creating a <code>Modifier</code> instance - for example, using <code>remember</code> to have instance-specific state, allowing the same <code>Modifier</code> object to be safely used in multiple places. Using <code>Modifier.composed</code> without calling any @Composable functions inside is unnecessary, and since the Modifier is no longer skippable, this can cause a lot of extra work inside the composed body, leading to worse performance.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnrememberedAnimatable<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Animatable instances created during composition need to be `remember`ed, otherwise they will be recreated during recomposition, and lose their state. Either hoist the Animatable to an object that is not created during composition, or wrap the Animatable in a call to <code>remember</code>.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.animation.core<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnrememberedGetBackStackEntry<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Backstack entries retrieved during composition need to be `remember`ed, otherwise they will be retrieved from the navController again, and be changed. You also need to pass in a key of a NavBackStackEntry to the remember call or they will not be updated properly. If this is in a <code>NavGraphBuilder.composable</code> scope, you should pass in the lambda's given entry as the key. Either hoist the state to an object that is not created during composition, or wrap the state in a call to <code>remember</code> with a <code>NavBackStackEntry</code> as a key.<br/><div class="vendor">
Vendor: Jetpack Navigation Compose<br/>
Identifier: androidx.navigation.compose<br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnrememberedMutableInteractionSource<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
MutableInteractionSource instances created during composition need to be `remember`ed, otherwise they will be recreated during recomposition, and lose their state. Either hoist the MutableInteractionSource to an object that is not created during composition, or wrap the MutableInteractionSource in a call to <code>remember</code>.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.foundation<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnrememberedMutableState<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
State objects created during composition need to be `remember`ed, otherwise they will be recreated during recomposition, and lose their state. Either hoist the state to an object that is not created during composition, or wrap the state in a call to <code>remember</code>.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeLifecycleWhenUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
If the <code>Lifecycle</code> is destroyed within the block of                     <code>Lifecycle.whenStarted</code> or any similar <code>Lifecycle.when</code> method is suspended,                     the block will be cancelled, which will also cancel any child coroutine                     launched inside the block. As as a result, If you have a try finally block                     in your code, the finally might run after the Lifecycle moves outside                     the desired state. It is recommended to check the <code>Lifecycle.isAtLeast</code>                     before accessing UI in finally block. Similarly,                     if you have a catch statement that might catch <code>CancellationException</code>,                     you should check the <code>Lifecycle.isAtLeast</code> before accessing the UI. See                     documentation of <code>Lifecycle.whenStateAtLeast</code> for more details<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeOptInUsageError<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This API has been flagged as opt-in with error-level severity.<br/>
<br/>
Any declaration annotated with this marker is considered part of an unstable or<br/>
otherwise non-standard API surface and its call sites should accept the opt-in<br/>
aspect of it by using the <code>@OptIn</code> annotation, using the marker annotation --<br/>
effectively causing further propagation of the opt-in aspect -- or configuring<br/>
the <code>UnsafeOptInUsageError</code> check's options for project-wide opt-in.<br/>
<br/>
To configure project-wide opt-in, specify the <code>opt-in</code> option value in <code>lint.xml</code><br/>
as a comma-delimited list of opted-in annotations:<br/>

<pre>
&lt;lint>
    &lt;issue id="UnsafeOptInUsageError">
        &lt;option name="opt-in" value="com.foo.ExperimentalBarAnnotation" />
    &lt;/issue>
&lt;/lint>
</pre>
<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeOptInUsageWarning<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This API has been flagged as opt-in with warning-level severity.<br/>
<br/>
Any declaration annotated with this marker is considered part of an unstable or<br/>
otherwise non-standard API surface and its call sites should accept the opt-in<br/>
aspect of it by using the <code>@OptIn</code> annotation, using the marker annotation --<br/>
effectively causing further propagation of the opt-in aspect -- or configuring<br/>
the <code>UnsafeOptInUsageWarning</code> check's options for project-wide opt-in.<br/>
<br/>
To configure project-wide opt-in, specify the <code>opt-in</code> option value in <code>lint.xml</code><br/>
as a comma-delimited list of opted-in annotations:<br/>

<pre>
&lt;lint>
    &lt;issue id="UnsafeOptInUsageWarning">
        &lt;option name="opt-in" value="com.foo.ExperimentalBarAnnotation" />
    &lt;/issue>
&lt;/lint>
</pre>
<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedBoxWithConstraintsScope<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The <code>content</code> lambda in BoxWithConstraints has a scope which will include the incoming constraints. If this scope is ignored, then the cost of subcomposition is being wasted and this BoxWithConstraints should be replaced with a Box.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.foundation<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedContentLambdaTargetStateParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>content</code> lambda in AnimatedContent works as a lookup function that returns the corresponding content based on the parameter (a state of type <code>T</code>). It is important for this lambda to return content <i>specific</i> to the input parameter, so that the different contents can be properly animated. Not using the input parameter to the content lambda will result in the same content for different input (i.e. target state) and therefore an erroneous transition between the exact same content.`<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.animation<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedCrossfadeTargetStateParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>content</code> lambda in Crossfade works as a lookup function that returns the corresponding content based on the parameter (a state of type <code>T</code>). It is important for this lambda to return content <i>specific</i> to the input parameter, so that the different contents can be properly crossfaded. Not using the input parameter to the content lambda will result in the same content for different input (i.e. target state) and therefore an erroneous crossfade between the exact same content.`<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.animation<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedMaterial3ScaffoldPaddingParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The <code>content</code> lambda in Scaffold has a padding parameter which will include any inner padding for the content due to app bars. If this parameter is ignored, then content may be obscured by the app bars resulting in visual issues or elements that can't be interacted with.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.material3<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedMaterialScaffoldPaddingParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The <code>content</code> lambda in Scaffold has a padding parameter which will include any inner padding for the content due to app bars. If this parameter is ignored, then content may be obscured by the app bars resulting in visual issues or elements that can't be interacted with.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.material<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedTransitionTargetStateParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Transition.animate* functions provide a target state parameter in the lambda that will be used to calculate the value for a given state. This target state parameter in the lambda may or may not be the same as the actual state, as the animation system occasionally needs to look up target values for other states to do proper seeking/tooling preview. Relying on other state than the provided <code>targetState</code> could also result in unnecessary recompositions. Therefore, it is generally considered an error if this <code>targetState</code> parameter is not used.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.animation.core<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseOfNonLambdaOffsetOverload<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>Modifier.offset()</code> is recommended to be used with static arguments only to avoid unnecessary recompositions. <code>Modifier.offset{ }</code> is preferred in the cases where the arguments are backed by a <code>State</code>.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.foundation<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UsingMaterialAndMaterial3Libraries<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
material and material3 are separate design system libraries that are incompatible with each other, as they have their own distinct theming systems. Using components from both libraries concurrently can cause issues: for example material components will not pick up the correct content color from a material3 container, and vice versa.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.material3<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ViewModelConstructorInComposable<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
View models should not be constructed directly inside composable functions. Instead you should use the lifecycle viewmodel extensionfunctions e.g. viewModel&lt;MyViewModel>()<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongRequiresOptIn<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Experimental features defined in Kotlin source code must be annotated with the Kotlin<br/>
<code>@RequiresOptIn</code> annotation. Using <code>androidx.annotation.RequiresOptIn</code> will prevent the<br/>
Kotlin compiler from enforcing its opt-in policies.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="IncludedIssuesLink" onclick="reveal('IncludedIssues');">
List Issues</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ExtraIssuesCardLink" onclick="hideid('ExtraIssuesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="MissingIssues"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="MissingIssuesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Disabled Checks</h2>
  </div>
              <div class="mdl-card__supporting-text">
One or more issues were not run by lint, either
because the check is not enabled by default, or because
it was disabled with a command line flag or via one or
more <code>lint.xml</code> configuration files in the project directories.
<div id="SuppressedIssues" style="display: none;"><br/><br/><div class="issue">
<div class="id">AppCompatMethod<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
When using the appcompat library, there are some methods you should be calling instead of the normal ones; for example, <code>getSupportActionBar()</code> instead of <code>getActionBar()</code>. This lint check looks for calls to the wrong method.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/libraries/support-library/">https://developer.android.com/topic/libraries/support-library/</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AppLinksAutoVerify<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that app links are correctly set and associated with website.<br/><div class="moreinfo">More info: <a href="https://g.co/appindexing/applinks">https://g.co/appindexing/applinks</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">BackButton<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
According to the Android Design Guide,<br/>
<br/>
"Other platforms use an explicit back button with label to allow the user to navigate up the application's hierarchy. Instead, Android uses the main action bar's app icon for hierarchical navigation and the navigation bar's back button for temporal navigation."<br/>
<br/>
This check is not very sophisticated (it just looks for buttons with the label "Back"), so it is disabled by default to not trigger on common scenarios like pairs of Back/Next buttons to paginate through screens.<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/studio-ui/designer/material/design">https://d.android.com/r/studio-ui/designer/material/design</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ComposableLambdaParameterNaming<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Composable functions with only one composable lambda parameter should use the name <code>content</code> for the parameter.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ComposableLambdaParameterPosition<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Composable functions with only one composable lambda parameter should place the parameter at the end of the parameter list, so it can be used as a trailing lambda.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ConvertToWebp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The WebP format is typically more compact than PNG and JPEG. As of Android 4.2.1 it supports transparency and lossless conversion as well. Note that there is a quickfix in the IDE which lets you perform conversion.<br/>
<br/>
Previously, launcher icons were required to be in the PNG format but that restriction is no longer there, so lint now flags these.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DalvikOverride<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The Dalvik virtual machine will treat a package private method in one class as overriding a package private method in its super class, even if they are in separate packages.<br/>
<br/>
If you really did intend for this method to override the other, make the method <code>protected</code> instead.<br/>
<br/>
If you did <b>not</b> intend the override, consider making the method private, or changing its name or signature.<br/>
<br/>
Note that this check is disabled be default, because ART (the successor to Dalvik) no longer has this behavior.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DefaultEncoding<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Some APIs will implicitly use the default system character encoding instead of UTF-8 when converting to or from bytes, such as when creating a default <code>FileReader</code>.<br/>
<br/>
This is <i>usually</i> not correct; you only want to do this if you need to read files created by other programs where they have deliberately written in the same encoding. The default encoding varies from platform to platform and can vary from locale to locale, so this makes it difficult to interpret files containing non-ASCII characters.<br/>
<br/>
We recommend using UTF-8 everywhere.<br/>
<br/>
Note that on Android, the default file encoding is always UTF-8 (see <a href="https://developer.android.com/reference/java/nio/charset/Charset#defaultCharset(">https://developer.android.com/reference/java/nio/charset/Charset#defaultCharset(</a>) for more), so this lint check deliberately does not flag any problems in Android code, since it is always safe to rely on the default character encoding there.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DuplicateStrings<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Duplicate strings can make applications larger unnecessarily.<br/>
<br/>
This lint check looks for duplicate strings, including differences for strings where the only difference is in capitalization. Title casing and all uppercase can all be adjusted in the layout or in code.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType">https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EasterEgg<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
An "easter egg" is code deliberately hidden in the code, both from potential users and even from other developers. This lint check looks for code which looks like it may be hidden from sight.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExpensiveAssertion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
In Kotlin, assertions are not handled the same way as from the Java programming language. In particular, they're just implemented as a library call, and inside the library call the error is only thrown if assertions are enabled.<br/>
<br/>
This means that the arguments to the <code>assert</code> call will <b>always</b> be evaluated. If you're doing any computation in the expression being asserted, that computation will unconditionally be performed whether or not assertions are turned on. This typically turns into wasted work in release builds.<br/>
<br/>
This check looks for cases where the assertion condition is nontrivial, e.g. it is performing method calls or doing more work than simple comparisons on local variables or fields.<br/>
<br/>
You can work around this by writing your own inline assert method instead:<br/>

<pre>
@Suppress("INVISIBLE_REFERENCE", "INVISIBLE_MEMBER")
inline fun assert(condition: () -> Boolean) {
    if (_Assertions.ENABLED &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>
In Android, because assertions are not enforced at runtime, instead use this:<br/>

<pre>
inline fun assert(condition: () -> Boolean) {
    if (BuildConfig.DEBUG &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IconExpectedSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
There are predefined sizes (for each density) for launcher icons. You should follow these conventions to make sure your icons fit in with the overall look of the platform.<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/studio-ui/designer/material/iconography">https://d.android.com/r/studio-ui/designer/material/iconography</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidPackage<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This check scans through libraries looking for calls to APIs that are not included in Android.<br/>
<br/>
When you create Android projects, the classpath is set up such that you can only access classes in the API packages that are included in Android. However, if you add other projects to your libs/ folder, there is no guarantee that those .jar files were built with an Android specific classpath, and in particular, they could be accessing unsupported APIs such as java.applet.<br/>
<br/>
This check scans through library jars and looks for references to API packages that are not included in Android and flags these. This is only an error if your code calls one of the library classes which wind up referencing the unsupported package.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KotlinPropertyAccess<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
For a method to be represented as a property in Kotlin, strict &#8220;bean&#8221;-style prefixing must be used.<br/>
<br/>
Accessor methods require a <code>get</code> prefix or for boolean-returning methods an <code>is</code> prefix can be used.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#property-prefixes">https://android.github.io/kotlin-guides/interop.html#property-prefixes</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KotlincFE10<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
K2, the new version of Kotlin compiler, which encompasses the new frontend, is coming. Try to avoid using internal APIs from the old frontend if possible.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LambdaLast<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve calling this code from Kotlin, parameter types eligible for SAM conversion should be last.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last">https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintDocExample<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Lint's tool for generating documentation for each issue has special support for including a code example which shows how to trigger the report. It will pick the first unit test it can find and pick out the source file referenced from the error message, but you can instead designate a unit test to be the documentation example, and in that case, all the files are included.<br/>
<br/>
To designate a unit test as the documentation example for an issue, name the test <code>testDocumentationExample</code>, or if your detector reports multiple issues, <code>testDocumentationExample</code>&lt;Id>, such as <code>testDocumentationExampleMyId</code>.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplPsiEquals<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
You should never compare two PSI elements for equality with <code>equals</code>; use <code>PsiEquivalenceUtil.areElementsEquivalent(PsiElement, PsiElement)</code> instead.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplUnexpectedDomain<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This checks flags URLs to domains that have not been explicitly allowed for use as a documentation source.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LogConditional<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>BuildConfig</code> class provides a constant, <code>DEBUG</code>, which indicates whether the code is being built in release mode or in debug mode. In release mode, you typically want to strip out all the logging calls. Since the compiler will automatically remove all code which is inside a <code>if (false)</code> check, surrounding your logging calls with a check for <code>BuildConfig.DEBUG</code> is a good idea.<br/>
<br/>
If you <b>really</b> intend for the logging to be present in release mode, you can suppress this warning with a <code>@SuppressLint</code> annotation for the intentional logging calls.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MangledCRLF<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
On Windows, line endings are typically recorded as carriage return plus newline: \r\n.<br/>
<br/>
This detector looks for invalid line endings with repeated carriage return characters (without newlines). Previous versions of the ADT plugin could accidentally introduce these into the file, and when editing the file, the editor could produce confusing visual artifacts.<br/><div class="moreinfo">More info: <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421">https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MinSdkTooLow<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The value of the <code>minSdkVersion</code> property is too low and can be incremented without noticeably reducing the number of supported devices.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NegativeMargin<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Margin values should be positive. Negative values are generally a sign that you are making assumptions about views surrounding the current one, or may be tempted to turn off child clipping to allow a view to escape its parent. Turning off child clipping to do this not only leads to poor graphical performance, it also results in wrong touch event handling since touch events are based strictly on a chain of parent-rect hit tests. Finally, making assumptions about the size of strings can lead to localization problems.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NewerVersionAvailable<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This detector checks with a central repository to see if there are newer versions available for the dependencies used by this project. This is similar to the <code>GradleDependency</code> check, which checks for newer versions available in the Android SDK tools and libraries, but this works with any MavenCentral dependency, and connects to the library every time, which makes it more flexible but also <b>much</b> slower.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoHardKeywords<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Do not use Kotlin&#8217;s hard keywords as the name of methods or fields. These require the use of backticks to escape when calling from Kotlin. Soft keywords, modifier keywords, and special identifiers are allowed.<br/>
<br/>
For example, ActionEvent's <code>getWhen()</code> method requires backticks when used from Kotlin:
<pre>
val timestamp = event.`when`
</pre>
<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#no-hard-keywords">https://android.github.io/kotlin-guides/interop.html#no-hard-keywords</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoOp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This check looks for code which looks like it's a no-op -- usually leftover expressions from interactive debugging, but in some cases bugs where you had intended to do something with the expression such as assign it to a field.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>pure-getters</b> (default is false):<br/>
Whether to assume methods with getter-names have no side effects.<br/>
<br/>
Getter methods (where names start with <code>get</code> or <code>is</code>, and have non-void return types, and no arguments) should not have side effects. With this option turned on, lint will assume that is the case and will list any getter calls whose results are ignored as suspicious code.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"NoOp"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"pure-getters"</span> <span class="attribute">value</span>=<span class="value">"false"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PermissionNamingConvention<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Permissions should be prefixed with an app's package name, using reverse-domain-style naming. This prefix should be followed by <code>.permission.</code>, and then a description of the capability that the permission represents, in upper SNAKE_CASE. For example, <code>com.example.myapp.permission.ENGAGE_HYPERSPACE</code>.<br/>
<br/>
Following this recommendation avoids naming collisions, and helps clearly identify the owner and intention of a custom permission.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PrivacySandboxBlockedCall<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Many APIs are unavailable in the Privacy Sandbox, depending on the <code>targetSdk</code>.<br/>
<br/>
If your code is designed to run in the sandbox (and never outside the sandbox) then you should remove the blocked calls to avoid exceptions at runtime.<br/>
<br/>
If your code is part of a library that can be executed both inside and outside the sandbox, surround the code with <code>if (!Process.isSdkSandbox()) { ... }</code> (or use your own field or method annotated with <code>@ChecksRestrictedEnvironment</code>) to avoid executing blocked calls when in the sandbox. Or, add the <code>@RestrictedForEnvironment</code> annotation to the containing method if the entire method should not be called when in the sandbox.<br/>
<br/>
This check is disabled by default, and should only be enabled in modules that may execute in the Privacy Sandbox.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Registered<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Activities, services and content providers should be registered in the <code>AndroidManifest.xml</code> file using <code>&lt;activity></code>, <code>&lt;service></code> and <code>&lt;provider></code> tags.<br/>
<br/>
If your activity is simply a parent class intended to be subclassed by other "real" activities, make it an abstract class.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/manifest/manifest-intro.html">https://developer.android.com/guide/topics/manifest/manifest-intro.html</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RequiredSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
All views must specify an explicit <code>layout_width</code> and <code>layout_height</code> attribute. There is a runtime check for this, so if you fail to specify a size, an exception is thrown at runtime.<br/>
<br/>
It's possible to specify these widths via styles as well. GridLayout, as a special case, does not require you to specify a size.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SelectableText<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
If a <code>&lt;TextView></code> is used to display data, the user might want to copy that data and paste it elsewhere. To allow this, the <code>&lt;TextView></code> should specify <code>android:textIsSelectable="true"</code>.<br/>
<br/>
This lint check looks for TextViews which are likely to be displaying data: views whose text is set dynamically.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StopShip<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Using the comment <code>// STOPSHIP</code> can be used to flag code that is incomplete but checked in. This comment marker can be used to indicate that the code should not be shipped until the issue is addressed, and lint will look for these. In Gradle projects, this is only checked for non-debug (release) builds.<br/>
<br/>
In Kotlin, the <code>TODO()</code> method is also treated as a stop ship marker; you can use it to make incomplete code compile, but it will throw an exception at runtime and therefore should be fixed before shipping releases.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StringFormatTrivial<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Every call to <code>String.format</code> creates a new <code>Formatter</code> instance, which will decrease the performance of your app. <code>String.format</code> should only be used when necessary--if the formatted string contains only trivial conversions (e.g. <code>b</code>, <code>s</code>, <code>c</code>) and there are no translation concerns, it will be more efficient to replace them and concatenate with <code>+</code>.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SyntheticAccessor<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
A private inner class which is accessed from the outer class will force the compiler to insert a synthetic accessor; this means that you are causing extra overhead. This is not important in small projects, but is important for large apps running up against the 64K method handle limit, and especially for <b>libraries</b> where you want to make sure your library is as small as possible for the cases where your library is used in an app running up against the 64K limit.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TypographyQuotes<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Straight single quotes and double quotes, when used as a pair, can be replaced by "curvy quotes" (or directional quotes). Use the right single quotation mark for apostrophes. Never use generic quotes ", ' or free-standing accents `, ´ for quotation marks, apostrophes, or primes. This can make the text more readable.<br/><div class="moreinfo">More info: <a href="https://en.wikipedia.org/wiki/Quotation_mark">https://en.wikipedia.org/wiki/Quotation_mark</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnknownNullness<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve referencing this code from Kotlin, consider adding explicit nullness information here with either <code>@NonNull</code> or <code>@Nullable</code>.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>ignore-deprecated</b> (default is false):<br/>
Whether to ignore classes and members that have been annotated with <code>@Deprecated</code>.<br/>
<br/>
Normally this lint check will flag all unannotated elements, but by setting this option to <code>true</code> it will skip any deprecated elements.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UnknownNullness"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"ignore-deprecated"</span> <span class="attribute">value</span>=<span class="value">"false"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div><div class="moreinfo">More info: <a href="https://developer.android.com/kotlin/interop#nullability_annotations">https://developer.android.com/kotlin/interop#nullability_annotations</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsupportedChromeOsHardware<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>&lt;uses-feature></code> element should not require this unsupported large screen hardware feature. Any &lt;uses-feature> not explicitly marked with <code>required="false"</code> is necessary on the device to be installed on. Ensure that any features that might prevent it from being installed on a ChromeOS, large screen, or foldable device are reviewed and marked as not required in the manifest.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/arc/manifest.html#incompat-entries">https://developer.android.com/topic/arc/manifest.html#incompat-entries</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedIds<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This resource id definition appears not to be needed since it is not referenced from anywhere. Having id definitions, even if unused, is not necessarily a bad idea since they make working on layouts and menus easier, so there is not a strong reason to delete these.<br/>
<br/>
<br/>
The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.<br/>
<br/>
You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.<br/>
<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ValidActionsXml<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that an actions XML file is properly formed<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">VulnerableCordovaVersion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The version of Cordova used in the app is vulnerable to security issues. Please update to the latest Apache Cordova version.<br/><div class="moreinfo">More info: <a href="https://cordova.apache.org/announcements/2015/11/20/security.html">https://cordova.apache.org/announcements/2015/11/20/security.html</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongThreadInterprocedural<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Searches for interprocedural call paths that violate thread annotations in the program. Tracks the flow of instantiated types and lambda expressions to increase accuracy across method boundaries.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/components/processes-and-threads.html#Threads">https://developer.android.com/guide/components/processes-and-threads.html#Threads</a>
</div><br/>
<br/></div>
</div>
</div>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SuppressedIssuesLink" onclick="reveal('SuppressedIssues');">
List Missing Issues</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="MissingIssuesCardLink" onclick="hideid('MissingIssuesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="SuppressInfo"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SuppressCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Suppressing Warnings and Errors</h2>
  </div>
              <div class="mdl-card__supporting-text">
Lint errors can be suppressed in a variety of ways:<br/>
<br/>
1. With a <code>@SuppressLint</code> annotation in the Java code<br/>
2. With a <code>tools:ignore</code> attribute in the XML file<br/>
3. With a //noinspection comment in the source code<br/>
4. With ignore flags specified in the <code>build.gradle</code> file, as explained below<br/>
5. With a <code>lint.xml</code> configuration file in the project<br/>
6. With a <code>lint.xml</code> configuration file passed to lint via the --config flag<br/>
7. With the --ignore flag passed to lint.<br/>
<br/>
To suppress a lint warning with an annotation, add a <code>@SuppressLint("id")</code> annotation on the class, method or variable declaration closest to the warning instance you want to disable. The id can be one or more issue id's, such as <code>"UnusedResources"</code> or <code>{"UnusedResources","UnusedIds"}</code>, or it can be <code>"all"</code> to suppress all lint warnings in the given scope.<br/>
<br/>
To suppress a lint warning with a comment, add a <code>//noinspection id</code> comment on the line before the statement with the error.<br/>
<br/>
To suppress a lint warning in an XML file, add a <code>tools:ignore="id"</code> attribute on the element containing the error, or one of its surrounding elements. You also need to define the namespace for the tools prefix on the root element in your document, next to the <code>xmlns:android</code> declaration:<br/>
<code>xmlns:tools="http://schemas.android.com/tools"</code><br/>
<br/>
To suppress a lint warning in a <code>build.gradle</code> file, add a section like this:<br/>

<pre>
android {
    lintOptions {
        disable 'TypographyFractions','TypographyQuotes'
    }
}
</pre>
<br/>
Here we specify a comma separated list of issue id's after the disable command. You can also use <code>warning</code> or <code>error</code> instead of <code>disable</code> to change the severity of issues.<br/>
<br/>
To suppress lint warnings with a configuration XML file, create a file named <code>lint.xml</code> and place it at the root directory of the module in which it applies.<br/>
<br/>
The format of the <code>lint.xml</code> file is something like the following:<br/>

<pre>
&lt;?xml version="1.0" encoding="UTF-8"?>
&lt;lint>
    &lt;!-- Ignore everything in the test source set -->
    &lt;issue id="all">
        &lt;ignore path="\*/test/\*" />
    &lt;/issue>

    &lt;!-- Disable this given check in this project -->
    &lt;issue id="IconMissingDensityFolder" severity="ignore" />

    &lt;!-- Ignore the ObsoleteLayoutParam issue in the given files -->
    &lt;issue id="ObsoleteLayoutParam">
        &lt;ignore path="res/layout/activation.xml" />
        &lt;ignore path="res/layout-xlarge/activation.xml" />
        &lt;ignore regexp="(foo|bar)\.java" />
    &lt;/issue>

    &lt;!-- Ignore the UselessLeaf issue in the given file -->
    &lt;issue id="UselessLeaf">
        &lt;ignore path="res/layout/main.xml" />
    &lt;/issue>

    &lt;!-- Change the severity of hardcoded strings to "error" -->
    &lt;issue id="HardcodedText" severity="error" />
&lt;/lint>
</pre>
<br/>
To suppress lint checks from the command line, pass the --ignore flag with a comma separated list of ids to be suppressed, such as:<br/>
<code>$ lint --ignore UnusedResources,UselessLeaf /my/project/path</code><br/>
<br/>
For more information, see <a href="https://developer.android.com/studio/write/lint.html#config">https://developer.android.com/studio/write/lint.html#config</a><br/>

            </div>
            </div>
          </section>    </div>
  </main>
</div>
</body>
</html>