/**
 * Copyright 2019 Huawei Technologies Co.,Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License.  You may obtain a copy of the
 * License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed
 * under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
 * CONDITIONS OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 **/

package com.obs.services.model.fusion;

public enum RedundancyTypeEnum {
    /*
    基础桶 [1az, 3az]
     */
    CLASSIC("classic"),
    /*
    融合桶（由多个基础桶组合而成）
     */
    FUSION("fusion");
    private String code;

    RedundancyTypeEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}
