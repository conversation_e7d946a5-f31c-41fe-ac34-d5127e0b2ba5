# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.9.10"
  }
  digests {
    sha256: "\244\307M\224\326L\341\253\3457`\376\003\211\335\224\037o\305X\320\332\263^G\300\205\241\036\310\017("
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.0.21"
  }
  digests {
    sha256: "\363\034\305?\020Z~H\300\223h;\275T7V\035\0223\222\005\023wKG\b\005d\033\355\274\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.9.10"
  }
  digests {
    sha256: "\254ca\277\232\321\3558,!\003\331q,G\315\354\026b2\264\220>\325\226\350\207k\006\201\311\267"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "2.0.21"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.16.0"
  }
  digests {
    sha256: "\027f\333\330/d\241-\3155\236\313o\025\363\3175\333Mf\322)a\247\305\033/\374dh1L"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.9.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.9.1"
  }
  digests {
    sha256: "\03649\027\353\362{\251o\344\334R\261\312\327\3752\2678\373\3065[\266\315[;0]r\022\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.16.0"
  }
  digests {
    sha256: "k\360=9\333\343tJ\314\342\'\323\266\2277L6%\252\341\002_\276\310\255\237\327\275X\274\3441"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.4"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.4"
  }
  digests {
    sha256: "S\232CB\215\370\2437b/\267\201@\037_\f\334\325-\2136\025\360\bZ\365\034\220\000/\3633"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.4.4"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-viewtree"
    version: "1.0.0"
  }
  digests {
    sha256: "\334\033g\215X\353\317+\372\025\207\276h\377\202f\224\316=\"\022Q\271\3570\324\324\263b\227\346\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\324\":\346\250Dvw&}\377\"\206;mi\210\312h\264\322|\246\327K\201\004k\004yBW"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-jvm"
    version: "2.9.0"
  }
  digests {
    sha256: "7\330\233!\001\360t\254l&\t\027\332\273\030V\ad^\342\000\2520\030\307\305\275\347\016\334\361\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jspecify"
    artifactId: "jspecify"
    version: "1.0.0"
  }
  digests {
    sha256: "\037\255nk\347Uw\201\344\3237)\324\232\341\315\310\375\332o\344w\273\f\306\214\343Q\352\375\373\253"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.9.0"
  }
  digests {
    sha256: "I}\330M\351\362\375F2O%\371\243\257\003-:#\320\207\350z\365\025\304\261\2245\366\326\246Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.9.0"
  }
  digests {
    sha256: "F8-\337\2724\215\271xF\317\250\035g\327#\004\334\025k\271b$5\213B\237\347H\274j\264"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.9.0"
  }
  digests {
    sha256: "d\371kR\307\b\225\200\1770 \342\350\372\021\363\354-\251\301W\366*\256!\242\311\323\264\204\247G"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.9.0"
  }
  digests {
    sha256: "\362e\246e\201\236Q2+>\352*o\362\253\220\030l\255\361xz\201\335\213\357\f\351<J`\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-ktx"
    version: "2.9.0"
  }
  digests {
    sha256: "W\207 \336m:\237K\256\002#f\261\213\272\332Co}\314\024\347  \f\357\242I\214\036=\356"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx-android"
    version: "2.9.0"
  }
  digests {
    sha256: "3?\242\316m\267\n\303r\267\221\337\353\251\234\002\300\331{\032\213J\264\250MGbFm\022\262\a"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\372\235u\021\250\376\r^\334S\t\227\006\326O7b\035\317\353\237,Usi\355\203d_\310z#"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-compose"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-compose-android"
    version: "2.9.0"
  }
  digests {
    sha256: "}\177\374\f\307l\225\201A<\250\251\256?ginI\354\250\v6C\313\255\222B\207\221\037N\200"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\fNcf\243\f\361\364d`\0324\236\016,\336\333p\337Sr\362\332\205\227\346\020\207\251\020\250\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable-android"
    version: "1.7.8"
  }
  digests {
    sha256: "b\202\364\256s\224\215T\320H\034Z|\317_\'\017\331\356tq\231\361Nc\023\363\314\3209\266\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\325O\261z\207\344\004\360`,I\365\312H\377\n\356@UE\034\232Z<\217\024B\016\aS\321\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.10.1"
  }
  digests {
    sha256: "\363\226\365\215\275w,\006[\2076\226\267J=M\buT\263vb\200\346;I\262]\273S\253\026"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.10.1"
  }
  digests {
    sha256: "\266+R\214\221}\351\276I~\266\370\2100\031| \351\322\022g\303\221la4\222\345\356\203}M"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\3336x8qnU\206h#\306\336\355b\374\354#\374\344\207\277*\217\245e\243o\024\232\263\321\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.3.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-android"
    version: "1.3.0"
  }
  digests {
    sha256: "!\vi\t\273\325\025\333\364\025\220\\\273\"\017\312\223\250\177\245F\203\217u@w\353\310\340H\205\220"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\360\255\336E\206ADGS\205\317J\247\340\267\376\262\177a\374\371G&e\355\230\314\227\033\006\261\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.3.0"
  }
  digests {
    sha256: "E\305\366A(@\365\r\347\277}\233\034J\214\201\352#h2Hz\0218\3132\v\214\231t\026\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.9.0"
  }
  digests {
    sha256: "\257B\375\204\364\345+D\334\253\257\236\317\224\022\005S\222\233\301\240Y\276\262$\n\236\301\333\356\201O"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose-android"
    version: "2.9.0"
  }
  digests {
    sha256: "_h\274\352\f\361\332\254S \242\375W\025r7\343\tE\317~e\206q\177\274qK5\263.o"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.9.0"
  }
  digests {
    sha256: "\301\\\351r\314(\223jYj\234\322V\263/\"v\212a\325\335\201\017\245k\251ER*\016\347\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\002\024\037\314\351\002C\017b\266t\303*\354\320\367Rb\347\356,\321\\t\255\266\027\315\023\023\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-compose"
    version: "1.10.1"
  }
  digests {
    sha256: "\370\232\361\262l\314\203F48|\205|-\324\364eM7e\b\220\003\234R|/\355\a\333ja"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.autofill"
    artifactId: "autofill"
    version: "1.0.0"
  }
  digests {
    sha256: "\311F\217V\340P\006\352\025\032BlT\225|\320y\233\213\203\245y\322\204m\322 a\363>^\315"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\210T\226v=\214\213\243/>jv[\315\265\243\236d\v\323W\177\305E!\030\321\'>,\nJ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util-android"
    version: "1.7.8"
  }
  digests {
    sha256: "V\206\252\257\374k\355\036\223\325\355\267\f\026\315*\024)o\261G\fR\366\305\246\'\261\030\214#\\"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\353Hb\3169\227\351\334\\2-\245\332kA^\342\220\177@\200wf\362\245cR\233\272\324i\005"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\2347\361e\2579{l\350\362\271\220\022n\210\254\332\201\341X0|\367\3403L\306\272]A\355I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text-android"
    version: "1.7.8"
  }
  digests {
    sha256: "_wW\255\027k\360\200z?\230\324\341\315\220\004\356\005\234\277\005\2422\006-\223\344\240P\327\337\272"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\341va\250\a\341\037\355.\274o\222\373Y\021\263\202N\346\241W\220\243\201\026r\b\325\267\327\376f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.graphics"
    artifactId: "graphics-path"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\244\003+my\263Q\360\265\232\324\265\200\355\333\271B>\026R\367\311X\203\006\207\361\356\342\354\003"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\204E0\036\326N:\243\025\v\'\365r\205\\s#\213x$\252\301du\212\207&\267\257\337\364\201"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\251E\327{U:u\216O\376\234\223\257\342\365$5\344\227\351\262\030\256\213\342\301\353\244\177>%\234"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\2027H\210p\307>\233f\367\354\253^\301\205[\f\257\300#\260\2517\363\367\306V\002\342\310\307\262"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\25023\347h\343\n\253\207\016fg\'~\311\035\324\n\333Vc\323/6\337\245\333\2556}\265a"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose"
    artifactId: "compose-bom"
    version: "2024.09.00"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-extended"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-extended-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\361\326\\c\353 %c\370\236\\\\\241yM\005~\037B7@\330P\362^Y\004\020.\322\301\344"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\222]$c\310\vG\263\360L\253oI8\025>\017]\225\217\020\255\371B(\327\036\340n\037\304\017"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3"
    version: "1.3.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3-android"
    version: "1.3.0"
  }
  digests {
    sha256: "\363\033\333\202\005\207\300\231\271Fb\346\352\315B\217B\311!\036\326\fE\261\027uc\016\311\356\247\221"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\247W8\270\3379\204\343\376\036\216I\304\265\373:\002\'\372\006 \323T\016\335\217jZw\231\307\300"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.12.0"
  }
  digests {
    sha256: "\261\005\000\201\261K\267\243\247\345ZM>\360\033]\317\253\304S\264W:O\300\031vq\221\325\364\340"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.6.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.6.0"
  }
  digests {
    sha256: "gT?\a6\374B*\351\'\355\016PK\230\274^&\237\332\r5\000W\2237\313q=\242\204\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "logging-interceptor"
    version: "4.12.0"
  }
  digests {
    sha256: "\363\350\325\360\220<%\f+U\322\364\177\317\340\b\350\00648]\2508Qa\307\246:\256\320\307L"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.apache.httpcomponents"
    artifactId: "httpclient"
    version: "4.5.13"
  }
  digests {
    sha256: "o\351\002jVljP\001`\214\363\3742\031fA\366\301\345\341\230m\0207\314\333\325\363\036\367C"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.apache.httpcomponents"
    artifactId: "httpcore"
    version: "4.4.15"
  }
  digests {
    sha256: "<\272\355\b\214I\232\020\371m\336X\363\235\300\347\230Qq\253\330\2018\312\026U\250r\001\033\261B"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "commons-codec"
    artifactId: "commons-codec"
    version: "1.11"
  }
  digests {
    sha256: "\345\231\3251\216\227\252H\364!6\242\222~m\372N\210\201\337\360\346\310\343\020\235\333\277\365\035{}"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "commons-logging"
    artifactId: "commons-logging"
    version: "1.2"
  }
  digests {
    sha256: "\332\335\352\036\240\276\017V\227\212\263\000k\212\311(4\257\356\373\331\267\344\3461o\312W\337\017\2466"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.fasterxml.jackson.core"
    artifactId: "jackson-core"
    version: "2.15.2"
  }
  digests {
    sha256: "0<\231\350+\037\252\221\240\272\345\330\373\353V\367\342\255\371\265&\251\000\335r;\361@\326+\324\264"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.fasterxml.jackson"
    artifactId: "jackson-bom"
    version: "2.15.2"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.fasterxml.jackson.core"
    artifactId: "jackson-annotations"
    version: "2.15.2"
  }
  digests {
    sha256: "\004\342\037\224\334\376\344\260x\372Z_S\004{xZ\253\246\235\031\3369/anz\177\345\323\210/"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.fasterxml.jackson.core"
    artifactId: "jackson-databind"
    version: "2.15.2"
  }
  digests {
    sha256: "\016\262\375\255n@\253\2102\247\214\233\"\365\201\226\335\227\005\224\350\323\325\242n\255\207\204|O:\226"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.fasterxml.jackson.datatype"
    artifactId: "jackson-datatype-jdk8"
    version: "2.15.2"
  }
  digests {
    sha256: "[\346\342\005\004\261\352\267\244\017\230\304\371\213\331.\221w\326\346\342\017\0309(\347\322\a\374f\313x"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.fasterxml.jackson.datatype"
    artifactId: "jackson-datatype-jsr310"
    version: "2.15.2"
  }
  digests {
    sha256: "ut\310\032\325pGn\366\252\322oA\222\210\375Fg3\3631[\3560\022\362\362\234\235\300\b\310"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.10.1"
  }
  digests {
    sha256: "BA\301Jw\'\303O\356\246P~\310\0011\212=J\220\360p\344RV\201\a\237\271N\344\305\223"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-compose"
    version: "2.7.6"
  }
  digests {
    sha256: "\344\331\325\253S\207\022l\227\004\326\326\352\347\257\342\037^\307;>\376\235\240\326\205_4-\a\300\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime-ktx"
    version: "2.7.6"
  }
  digests {
    sha256: "\231o\242es\367\205\025\250\322\365.\272\037\262G\031\a\bd\2471[\347\022b\032WDU\347V"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common-ktx"
    version: "2.7.6"
  }
  digests {
    sha256: "\311\325\267\331+ \030\252\351\205\027\266\003\244E\034\347\225\221B\\\020\227\376\024+\301,\352\vS\274"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common"
    version: "2.7.6"
  }
  digests {
    sha256: "3\315U\3019Qr$\2376\255\231\037s\337x\213B\016\374\302\250/\022\310\244\027\365\317\364\b,"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime"
    version: "2.7.6"
  }
  digests {
    sha256: "\333\255-K\312\257\260\r\r\346\274o\315\n\030\207\025j{\257\343<\242\313\345\203A\355\300\324:\373"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.huaweicloud.sdk"
    artifactId: "huaweicloud-sdk-iotda"
    version: "3.1.153"
  }
  digests {
    sha256: "\300vM\033\360\0165\322K\326\267\001\356j?*\037\323\374\246p\t\331\223=\367\335W*\006\242\002"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.huaweicloud.sdk"
    artifactId: "huaweicloud-sdk-core"
    version: "3.1.153"
  }
  digests {
    sha256: "\035\037\360\2023x\250\302\247\266\200\214\310\004N=h\303\030)\217\247\207S\314\350\017}\317W8E"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.yaml"
    artifactId: "snakeyaml"
    version: "2.0"
  }
  digests {
    sha256: "\210\f\235\211nKt\240lT\234\025\312IdP\026]i\t\372\025\327\346b\276\350\366\246mz\372"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.openeuler"
    artifactId: "bgmprovider"
    version: "1.1.2"
  }
  digests {
    sha256: "4u\366\344\362\241\365B\272\230\226\266o\002\bpo\241\377I\3340\'2\267%\313_\b\346\261{"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.openeuler"
    artifactId: "jca"
    version: "1.1.2"
  }
  digests {
    sha256: "\2113\254q\261E\r1T\"ru\231\314\376\234[\3406\022#\206Z>=i\033\372a]\247N"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.bouncycastle"
    artifactId: "bcprov-jdk18on"
    version: "1.75"
  }
  digests {
    sha256: "\177$\001\216\222\022\333\332a\306\222\022\370\327\261RL(\357\271x\361\r\365\220\337;L\312\304{\325"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.openeuler"
    artifactId: "jsse"
    version: "1.1.2"
  }
  digests {
    sha256: "\357\375Y\307\207o\030\315\350\026T\231M\023\237y\244\373I\225\374\352\000\215\320\373\220(\255\3164\240"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.slf4j"
    artifactId: "slf4j-api"
    version: "1.7.36"
  }
  digests {
    sha256: "\323\357W^>Iyg\215\300\033\361\334\316Q\002\024\223\264\321\037\267\361\276\212\331\202\207|\026\241\300"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "retrofit"
    version: "2.9.0"
  }
  digests {
    sha256: "\346\352\031)\304hR\365\276\306j\2635}\243\203Gl\357N\215\035\356\375\277\031[y\314Me\201"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "converter-gson"
    version: "2.9.0"
  }
  digests {
    sha256: "2\252 k\232)\311\337^\332\223\240\222\317\263\260\271\023>#,\006+\252\210/\003\031\360\347\237\016"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.accompanist"
    artifactId: "accompanist-permissions"
    version: "0.32.0"
  }
  digests {
    sha256: "\264\324\r\243\371\331\267\221h\271\317\r\252\a2\000\3509\356|\215x\213\351\233<\370u\356\255\302D"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.github.aakira"
    artifactId: "napier"
    version: "1.4.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.github.aakira"
    artifactId: "napier-android"
    version: "1.4.1"
  }
  digests {
    sha256: "&\234\307\264:.K]7\337)8\322u-)F\276\251\271\217\373\200\3540\257\025\003\200E\037L"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-exoplayer"
    version: "1.2.1"
  }
  digests {
    sha256: "\205 L/\274\264\352h\375\021\271\304]\241ki\030\230\024z!\343\351\331W\226\255[E\304\0370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.3.6"
  }
  digests {
    sha256: "\030\004\020^\236\005\375\330\367`A;\255]\344\230\303\201\2522\237O\235\224\310Q\274\211\032\306T\306"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-common"
    version: "1.2.1"
  }
  digests {
    sha256: "=\3353\350\301\024\266\022v\255\304\352\260\342\310xT\t?\306x\350\273\2140\276;\251o\323\267\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "31.1-android"
  }
  digests {
    sha256: "2\254.\327\t\331m\'\213].>\\\352\027\217\244\223\2319\305%\373du2\360\0230\215\263\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.1"
  }
  digests {
    sha256: "\241q\356LsM\322\332\203~K\026\276\235\364f\032\372\267*A\255\2571\353\204\337\332\3716\312&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-container"
    version: "1.2.1"
  }
  digests {
    sha256: "\346}\213P\375\261t\252##\217y\263X:H\\\254\251e\205$\265\006\265\352\326\351\304}\033d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-database"
    version: "1.2.1"
  }
  digests {
    sha256: "%M`\b\031\352\003\354L!\350<:\277\357\322,\035\030d\aN\\\252\247S\030\217lE\021\225"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-datasource"
    version: "1.2.1"
  }
  digests {
    sha256: "m\240\366\210\\\205:\037\371\342\207O\216\317hM{\215\030\324]\214\030\274\033|I_\005\245\264\325"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-decoder"
    version: "1.2.1"
  }
  digests {
    sha256: "\216D\212\203\257\364|~\255\264\333\333\374\344\200\206\255\315\370P\276\3103E\n\346\222\006\370>\'m"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-extractor"
    version: "1.2.1"
  }
  digests {
    sha256: "f\225[\177[\315\t\323I\231r;w\314\307\'\253\315\271o\332\221M\302\346c\302\202q\217\356\355"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-session"
    version: "1.2.1"
  }
  digests {
    sha256: "\254\366\257w\024\373j\005Yf\204[\346~<nC(K\242\357w\343BI\313\247\b\v\343\a\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media"
    artifactId: "media"
    version: "1.6.0"
  }
  digests {
    sha256: "\376b\210G\330\371\t\312\217#\326\205\006\037\232v\204\252\f\233\311\301\262\001\304\377n\267\2637(("
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-ui"
    version: "1.2.1"
  }
  digests {
    sha256: "\322\f\f\373)\271\344\022\341\224\037\302m\205c\030\321\257\221(H\343\336\271r\205iP\366a\312\337"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.3.0"
  }
  digests {
    sha256: "\326Y(\240\017cX\232I\342\031%A.\017H\205/\211%K\a\260<\003\rV\017\221\357\374\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.0.0"
  }
  digests {
    sha256: " \345\270\366Rj4YZ`OVq\215\250\021g\300\264\nz\224\245}\2525Vc\362YM\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.coil-kt"
    artifactId: "coil-compose"
    version: "2.5.0"
  }
  digests {
    sha256: "\221\177@G\\\033 la\244\2115X3\341\021\346\375\347)\354\v\216<\\\312\271#\262\203d/"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.coil-kt"
    artifactId: "coil-compose-base"
    version: "2.5.0"
  }
  digests {
    sha256: "\020h\no\000\253c\006Sz&\207\"*\260\377b\260\255\203\355}\345\360)G\322\331\325\302\004\v"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.accompanist"
    artifactId: "accompanist-drawablepainter"
    version: "0.32.0"
  }
  digests {
    sha256: "h\r\'\225\017\221\272\030j\241E\"\255\001\235\255\222\307\002$\215\021\016\275\326\304\227sJ\275}\240"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.coil-kt"
    artifactId: "coil-base"
    version: "2.5.0"
  }
  digests {
    sha256: "\2739\237\2009\350\317\177\025\221\002\310[D(\033\342\377~\274X!@V\370`\034\244?\276\253\305"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.6.1"
  }
  digests {
    sha256: "\333\221]\277I5xc\336\026i\377\237\335\216\220\b\326_\343W\257l\316\232\3460C\255_f\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.coil-kt"
    artifactId: "coil"
    version: "2.5.0"
  }
  digests {
    sha256: "\304\243\306^\301\275T0~V\356-D\177[kH\214V\361\373\373Z\020\316\301\320\3443\f\3413"
  }
  repo_index {
    value: 1
  }
}
library {
  digests {
    sha256: "\214\365V\2550tP\243\227@\246Q9\355\226\356\210&\020\024Gj\347\245\344\277\3122\260\267\310/"
  }
}
library {
  digests {
    sha256: "\304\325\r\256MX\303\003\024u\326J\345\352\372\336P\361\206\034\241U2R\252\177\321v\325nN\354"
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 3
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 0
  library_dep_index: 4
}
library_dependencies {
  library_index: 3
  library_dep_index: 1
}
library_dependencies {
  library_index: 4
  library_dep_index: 1
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 1
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 7
  library_dep_index: 1
}
library_dependencies {
  library_index: 8
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 60
  library_dep_index: 87
  library_dep_index: 1
  library_dep_index: 27
  library_dep_index: 5
  library_dep_index: 1
}
library_dependencies {
  library_index: 9
  library_dep_index: 1
}
library_dependencies {
  library_index: 10
  library_dep_index: 11
}
library_dependencies {
  library_index: 11
  library_dep_index: 6
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 12
}
library_dependencies {
  library_index: 12
  library_dep_index: 10
  library_dep_index: 10
}
library_dependencies {
  library_index: 13
  library_dep_index: 6
  library_dep_index: 14
}
library_dependencies {
  library_index: 15
  library_dep_index: 1
  library_dep_index: 1
}
library_dependencies {
  library_index: 16
  library_dep_index: 6
}
library_dependencies {
  library_index: 17
  library_dep_index: 18
}
library_dependencies {
  library_index: 18
  library_dep_index: 6
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 15
  library_dep_index: 21
  library_dep_index: 61
  library_dep_index: 1
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 55
  library_dep_index: 47
  library_dep_index: 1
  library_dep_index: 30
  library_dep_index: 56
  library_dep_index: 58
}
library_dependencies {
  library_index: 19
  library_dep_index: 6
}
library_dependencies {
  library_index: 20
  library_dep_index: 6
  library_dep_index: 19
}
library_dependencies {
  library_index: 21
  library_dep_index: 22
}
library_dependencies {
  library_index: 22
  library_dep_index: 6
  library_dep_index: 1
  library_dep_index: 23
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 55
  library_dep_index: 47
  library_dep_index: 1
  library_dep_index: 30
  library_dep_index: 56
  library_dep_index: 58
}
library_dependencies {
  library_index: 23
  library_dep_index: 24
}
library_dependencies {
  library_index: 24
  library_dep_index: 2
  library_dep_index: 25
  library_dep_index: 4
  library_dep_index: 0
}
library_dependencies {
  library_index: 25
  library_dep_index: 26
  library_dep_index: 24
  library_dep_index: 23
}
library_dependencies {
  library_index: 26
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 0
}
library_dependencies {
  library_index: 28
  library_dep_index: 6
  library_dep_index: 21
  library_dep_index: 21
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 55
  library_dep_index: 47
  library_dep_index: 56
  library_dep_index: 58
}
library_dependencies {
  library_index: 29
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 1
  library_dep_index: 23
  library_dep_index: 27
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 55
  library_dep_index: 47
  library_dep_index: 1
  library_dep_index: 56
  library_dep_index: 58
}
library_dependencies {
  library_index: 30
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 1
  library_dep_index: 27
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 55
  library_dep_index: 47
  library_dep_index: 1
  library_dep_index: 56
  library_dep_index: 58
}
library_dependencies {
  library_index: 31
  library_dep_index: 30
  library_dep_index: 1
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 32
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 55
  library_dep_index: 47
  library_dep_index: 1
  library_dep_index: 56
  library_dep_index: 58
}
library_dependencies {
  library_index: 32
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 1
  library_dep_index: 23
  library_dep_index: 21
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 55
  library_dep_index: 1
  library_dep_index: 47
  library_dep_index: 28
  library_dep_index: 30
  library_dep_index: 56
  library_dep_index: 58
}
library_dependencies {
  library_index: 33
  library_dep_index: 34
}
library_dependencies {
  library_index: 34
  library_dep_index: 6
  library_dep_index: 17
  library_dep_index: 1
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 32
  library_dep_index: 17
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 1
  library_dep_index: 55
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 47
  library_dep_index: 28
  library_dep_index: 30
  library_dep_index: 56
  library_dep_index: 58
}
library_dependencies {
  library_index: 35
  library_dep_index: 36
}
library_dependencies {
  library_index: 36
  library_dep_index: 6
  library_dep_index: 15
  library_dep_index: 1
  library_dep_index: 26
  library_dep_index: 23
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 37
  library_dep_index: 55
  library_dep_index: 47
  library_dep_index: 1
  library_dep_index: 56
  library_dep_index: 58
}
library_dependencies {
  library_index: 37
  library_dep_index: 38
}
library_dependencies {
  library_index: 38
  library_dep_index: 6
  library_dep_index: 39
  library_dep_index: 43
  library_dep_index: 21
  library_dep_index: 35
  library_dep_index: 47
  library_dep_index: 1
  library_dep_index: 51
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 55
  library_dep_index: 47
  library_dep_index: 1
  library_dep_index: 56
  library_dep_index: 58
}
library_dependencies {
  library_index: 39
  library_dep_index: 40
}
library_dependencies {
  library_index: 40
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 1
  library_dep_index: 26
  library_dep_index: 23
  library_dep_index: 41
}
library_dependencies {
  library_index: 41
  library_dep_index: 42
}
library_dependencies {
  library_index: 42
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 39
  library_dep_index: 1
  library_dep_index: 39
}
library_dependencies {
  library_index: 43
  library_dep_index: 44
}
library_dependencies {
  library_index: 44
  library_dep_index: 45
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 63
  library_dep_index: 10
  library_dep_index: 10
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 64
  library_dep_index: 68
  library_dep_index: 72
  library_dep_index: 70
  library_dep_index: 66
  library_dep_index: 8
  library_dep_index: 78
  library_dep_index: 74
  library_dep_index: 56
  library_dep_index: 35
  library_dep_index: 61
  library_dep_index: 54
  library_dep_index: 1
  library_dep_index: 26
  library_dep_index: 23
  library_dep_index: 64
  library_dep_index: 68
  library_dep_index: 72
  library_dep_index: 75
  library_dep_index: 70
  library_dep_index: 66
  library_dep_index: 79
}
library_dependencies {
  library_index: 45
  library_dep_index: 46
  library_dep_index: 33
  library_dep_index: 55
  library_dep_index: 54
  library_dep_index: 46
  library_dep_index: 62
}
library_dependencies {
  library_index: 46
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 15
  library_dep_index: 21
  library_dep_index: 17
  library_dep_index: 35
  library_dep_index: 47
  library_dep_index: 61
  library_dep_index: 49
  library_dep_index: 60
  library_dep_index: 1
  library_dep_index: 23
  library_dep_index: 62
  library_dep_index: 45
  library_dep_index: 1
}
library_dependencies {
  library_index: 47
  library_dep_index: 48
}
library_dependencies {
  library_index: 48
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 30
  library_dep_index: 35
  library_dep_index: 49
  library_dep_index: 1
  library_dep_index: 26
  library_dep_index: 23
  library_dep_index: 51
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 55
  library_dep_index: 1
  library_dep_index: 56
  library_dep_index: 58
}
library_dependencies {
  library_index: 49
  library_dep_index: 50
}
library_dependencies {
  library_index: 50
  library_dep_index: 6
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 15
  library_dep_index: 21
  library_dep_index: 1
  library_dep_index: 23
  library_dep_index: 51
  library_dep_index: 54
  library_dep_index: 1
}
library_dependencies {
  library_index: 51
  library_dep_index: 52
}
library_dependencies {
  library_index: 52
  library_dep_index: 53
  library_dep_index: 1
}
library_dependencies {
  library_index: 53
  library_dep_index: 52
  library_dep_index: 51
}
library_dependencies {
  library_index: 54
  library_dep_index: 49
  library_dep_index: 1
  library_dep_index: 49
  library_dep_index: 1
}
library_dependencies {
  library_index: 55
  library_dep_index: 35
  library_dep_index: 1
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 47
  library_dep_index: 1
  library_dep_index: 28
  library_dep_index: 30
  library_dep_index: 56
  library_dep_index: 58
}
library_dependencies {
  library_index: 56
  library_dep_index: 57
}
library_dependencies {
  library_index: 57
  library_dep_index: 6
  library_dep_index: 39
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 55
  library_dep_index: 47
  library_dep_index: 58
}
library_dependencies {
  library_index: 58
  library_dep_index: 6
  library_dep_index: 17
  library_dep_index: 59
  library_dep_index: 1
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 17
  library_dep_index: 56
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 55
  library_dep_index: 47
  library_dep_index: 1
}
library_dependencies {
  library_index: 59
  library_dep_index: 6
  library_dep_index: 60
}
library_dependencies {
  library_index: 60
  library_dep_index: 6
}
library_dependencies {
  library_index: 61
  library_dep_index: 6
  library_dep_index: 13
  library_dep_index: 59
  library_dep_index: 14
}
library_dependencies {
  library_index: 62
  library_dep_index: 45
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 5
  library_dep_index: 21
  library_dep_index: 17
  library_dep_index: 35
  library_dep_index: 49
  library_dep_index: 1
  library_dep_index: 23
  library_dep_index: 45
  library_dep_index: 1
  library_dep_index: 46
}
library_dependencies {
  library_index: 63
  library_dep_index: 8
}
library_dependencies {
  library_index: 64
  library_dep_index: 65
}
library_dependencies {
  library_index: 65
  library_dep_index: 6
  library_dep_index: 39
  library_dep_index: 66
  library_dep_index: 1
  library_dep_index: 43
  library_dep_index: 68
  library_dep_index: 72
  library_dep_index: 75
  library_dep_index: 70
  library_dep_index: 66
}
library_dependencies {
  library_index: 66
  library_dep_index: 67
}
library_dependencies {
  library_index: 67
  library_dep_index: 9
  library_dep_index: 1
  library_dep_index: 43
  library_dep_index: 64
  library_dep_index: 68
  library_dep_index: 72
  library_dep_index: 75
  library_dep_index: 70
}
library_dependencies {
  library_index: 68
  library_dep_index: 69
}
library_dependencies {
  library_index: 69
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 39
  library_dep_index: 70
  library_dep_index: 66
  library_dep_index: 8
  library_dep_index: 77
  library_dep_index: 1
  library_dep_index: 43
  library_dep_index: 64
  library_dep_index: 72
  library_dep_index: 75
  library_dep_index: 70
  library_dep_index: 66
}
library_dependencies {
  library_index: 70
  library_dep_index: 71
}
library_dependencies {
  library_index: 71
  library_dep_index: 6
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 12
  library_dep_index: 39
  library_dep_index: 64
  library_dep_index: 66
  library_dep_index: 1
  library_dep_index: 43
  library_dep_index: 64
  library_dep_index: 68
  library_dep_index: 72
  library_dep_index: 75
  library_dep_index: 66
}
library_dependencies {
  library_index: 72
  library_dep_index: 73
}
library_dependencies {
  library_index: 73
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 68
  library_dep_index: 70
  library_dep_index: 66
  library_dep_index: 8
  library_dep_index: 74
  library_dep_index: 1
  library_dep_index: 23
  library_dep_index: 43
  library_dep_index: 64
  library_dep_index: 68
  library_dep_index: 75
  library_dep_index: 70
  library_dep_index: 66
}
library_dependencies {
  library_index: 74
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 58
  library_dep_index: 59
}
library_dependencies {
  library_index: 75
  library_dep_index: 76
}
library_dependencies {
  library_index: 76
  library_dep_index: 6
  library_dep_index: 39
  library_dep_index: 4
  library_dep_index: 43
  library_dep_index: 64
  library_dep_index: 68
  library_dep_index: 72
  library_dep_index: 70
  library_dep_index: 66
}
library_dependencies {
  library_index: 77
  library_dep_index: 8
  library_dep_index: 1
}
library_dependencies {
  library_index: 78
  library_dep_index: 5
  library_dep_index: 1
}
library_dependencies {
  library_index: 79
  library_dep_index: 80
}
library_dependencies {
  library_index: 80
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 81
  library_dep_index: 85
  library_dep_index: 39
  library_dep_index: 43
  library_dep_index: 72
  library_dep_index: 66
  library_dep_index: 8
  library_dep_index: 74
  library_dep_index: 1
  library_dep_index: 85
}
library_dependencies {
  library_index: 81
  library_dep_index: 82
}
library_dependencies {
  library_index: 82
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 83
  library_dep_index: 85
  library_dep_index: 39
  library_dep_index: 43
  library_dep_index: 64
  library_dep_index: 68
  library_dep_index: 66
  library_dep_index: 1
  library_dep_index: 83
}
library_dependencies {
  library_index: 83
  library_dep_index: 84
}
library_dependencies {
  library_index: 84
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 39
  library_dep_index: 43
  library_dep_index: 68
  library_dep_index: 70
  library_dep_index: 66
  library_dep_index: 1
  library_dep_index: 23
  library_dep_index: 81
}
library_dependencies {
  library_index: 85
  library_dep_index: 86
}
library_dependencies {
  library_index: 86
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 83
  library_dep_index: 39
  library_dep_index: 43
  library_dep_index: 70
  library_dep_index: 66
  library_dep_index: 8
  library_dep_index: 1
  library_dep_index: 79
}
library_dependencies {
  library_index: 87
  library_dep_index: 6
  library_dep_index: 10
}
library_dependencies {
  library_index: 88
  library_dep_index: 81
  library_dep_index: 79
  library_dep_index: 89
  library_dep_index: 93
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 68
  library_dep_index: 75
  library_dep_index: 85
  library_dep_index: 90
  library_dep_index: 94
  library_dep_index: 83
  library_dep_index: 64
  library_dep_index: 66
  library_dep_index: 72
  library_dep_index: 91
  library_dep_index: 95
  library_dep_index: 70
  library_dep_index: 92
  library_dep_index: 96
  library_dep_index: 40
  library_dep_index: 44
  library_dep_index: 42
  library_dep_index: 65
  library_dep_index: 69
  library_dep_index: 73
  library_dep_index: 71
  library_dep_index: 67
  library_dep_index: 76
  library_dep_index: 80
  library_dep_index: 82
  library_dep_index: 86
  library_dep_index: 84
}
library_dependencies {
  library_index: 89
  library_dep_index: 90
}
library_dependencies {
  library_index: 90
  library_dep_index: 91
  library_dep_index: 4
}
library_dependencies {
  library_index: 91
  library_dep_index: 92
}
library_dependencies {
  library_index: 92
  library_dep_index: 43
  library_dep_index: 1
  library_dep_index: 4
}
library_dependencies {
  library_index: 93
  library_dep_index: 94
}
library_dependencies {
  library_index: 94
  library_dep_index: 62
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 83
  library_dep_index: 79
  library_dep_index: 85
  library_dep_index: 91
  library_dep_index: 95
  library_dep_index: 39
  library_dep_index: 43
  library_dep_index: 72
  library_dep_index: 66
  library_dep_index: 28
  library_dep_index: 4
}
library_dependencies {
  library_index: 95
  library_dep_index: 96
}
library_dependencies {
  library_index: 96
  library_dep_index: 10
  library_dep_index: 81
  library_dep_index: 79
  library_dep_index: 39
  library_dep_index: 66
  library_dep_index: 4
}
library_dependencies {
  library_index: 97
  library_dep_index: 98
  library_dep_index: 0
}
library_dependencies {
  library_index: 98
  library_dep_index: 99
}
library_dependencies {
  library_index: 99
  library_dep_index: 0
  library_dep_index: 4
}
library_dependencies {
  library_index: 100
  library_dep_index: 97
  library_dep_index: 0
}
library_dependencies {
  library_index: 101
  library_dep_index: 102
}
library_dependencies {
  library_index: 105
  library_dep_index: 106
}
library_dependencies {
  library_index: 106
  library_dep_index: 107
  library_dep_index: 105
  library_dep_index: 108
  library_dep_index: 109
  library_dep_index: 110
}
library_dependencies {
  library_index: 107
  library_dep_index: 106
}
library_dependencies {
  library_index: 108
  library_dep_index: 107
  library_dep_index: 105
  library_dep_index: 106
}
library_dependencies {
  library_index: 109
  library_dep_index: 105
  library_dep_index: 108
  library_dep_index: 106
}
library_dependencies {
  library_index: 110
  library_dep_index: 107
  library_dep_index: 105
  library_dep_index: 108
  library_dep_index: 106
}
library_dependencies {
  library_index: 112
  library_dep_index: 62
  library_dep_index: 81
  library_dep_index: 85
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 37
  library_dep_index: 113
  library_dep_index: 1
  library_dep_index: 113
  library_dep_index: 114
  library_dep_index: 116
  library_dep_index: 115
}
library_dependencies {
  library_index: 113
  library_dep_index: 114
  library_dep_index: 116
  library_dep_index: 114
  library_dep_index: 112
  library_dep_index: 116
  library_dep_index: 115
}
library_dependencies {
  library_index: 114
  library_dep_index: 115
  library_dep_index: 115
  library_dep_index: 112
  library_dep_index: 116
  library_dep_index: 113
}
library_dependencies {
  library_index: 115
  library_dep_index: 6
  library_dep_index: 12
  library_dep_index: 5
  library_dep_index: 21
  library_dep_index: 33
  library_dep_index: 55
  library_dep_index: 47
  library_dep_index: 61
  library_dep_index: 54
  library_dep_index: 1
  library_dep_index: 114
  library_dep_index: 112
  library_dep_index: 116
  library_dep_index: 113
}
library_dependencies {
  library_index: 116
  library_dep_index: 45
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 33
  library_dep_index: 55
  library_dep_index: 115
  library_dep_index: 1
  library_dep_index: 115
  library_dep_index: 114
  library_dep_index: 112
  library_dep_index: 113
}
library_dependencies {
  library_index: 117
  library_dep_index: 118
}
library_dependencies {
  library_index: 118
  library_dep_index: 119
  library_dep_index: 105
  library_dep_index: 107
  library_dep_index: 108
  library_dep_index: 110
  library_dep_index: 109
  library_dep_index: 97
  library_dep_index: 120
  library_dep_index: 124
}
library_dependencies {
  library_index: 120
  library_dep_index: 121
  library_dep_index: 123
}
library_dependencies {
  library_index: 121
  library_dep_index: 122
}
library_dependencies {
  library_index: 123
  library_dep_index: 121
}
library_dependencies {
  library_index: 125
  library_dep_index: 97
}
library_dependencies {
  library_index: 126
  library_dep_index: 125
  library_dep_index: 111
}
library_dependencies {
  library_index: 127
  library_dep_index: 62
  library_dep_index: 79
  library_dep_index: 26
  library_dep_index: 128
  library_dep_index: 0
}
library_dependencies {
  library_index: 128
  library_dep_index: 129
}
library_dependencies {
  library_index: 129
  library_dep_index: 1
  library_dep_index: 4
}
library_dependencies {
  library_index: 130
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 131
  library_dep_index: 132
  library_dep_index: 135
  library_dep_index: 137
  library_dep_index: 138
  library_dep_index: 139
  library_dep_index: 136
}
library_dependencies {
  library_index: 131
  library_dep_index: 6
}
library_dependencies {
  library_index: 132
  library_dep_index: 6
  library_dep_index: 133
  library_dep_index: 9
  library_dep_index: 135
  library_dep_index: 136
  library_dep_index: 137
  library_dep_index: 138
  library_dep_index: 130
  library_dep_index: 139
  library_dep_index: 140
  library_dep_index: 142
}
library_dependencies {
  library_index: 133
  library_dep_index: 134
  library_dep_index: 14
}
library_dependencies {
  library_index: 135
  library_dep_index: 132
  library_dep_index: 6
}
library_dependencies {
  library_index: 136
  library_dep_index: 132
  library_dep_index: 6
}
library_dependencies {
  library_index: 137
  library_dep_index: 132
  library_dep_index: 136
  library_dep_index: 6
  library_dep_index: 131
}
library_dependencies {
  library_index: 138
  library_dep_index: 132
  library_dep_index: 6
}
library_dependencies {
  library_index: 139
  library_dep_index: 6
  library_dep_index: 132
  library_dep_index: 135
  library_dep_index: 138
}
library_dependencies {
  library_index: 140
  library_dep_index: 137
  library_dep_index: 10
  library_dep_index: 141
  library_dep_index: 8
  library_dep_index: 132
}
library_dependencies {
  library_index: 141
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
}
library_dependencies {
  library_index: 142
  library_dep_index: 132
  library_dep_index: 141
  library_dep_index: 6
  library_dep_index: 143
}
library_dependencies {
  library_index: 143
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 144
  library_dep_index: 78
}
library_dependencies {
  library_index: 144
  library_dep_index: 6
  library_dep_index: 8
}
library_dependencies {
  library_index: 145
  library_dep_index: 146
  library_dep_index: 152
  library_dep_index: 0
}
library_dependencies {
  library_index: 146
  library_dep_index: 5
  library_dep_index: 147
  library_dep_index: 148
  library_dep_index: 79
  library_dep_index: 0
}
library_dependencies {
  library_index: 147
  library_dep_index: 43
  library_dep_index: 26
  library_dep_index: 0
}
library_dependencies {
  library_index: 148
  library_dep_index: 6
  library_dep_index: 149
  library_dep_index: 10
  library_dep_index: 5
  library_dep_index: 131
  library_dep_index: 61
  library_dep_index: 17
  library_dep_index: 26
  library_dep_index: 1
  library_dep_index: 97
  library_dep_index: 98
}
library_dependencies {
  library_index: 149
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 150
  library_dep_index: 151
}
library_dependencies {
  library_index: 150
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 10
}
library_dependencies {
  library_index: 151
  library_dep_index: 150
  library_dep_index: 16
  library_dep_index: 10
}
library_dependencies {
  library_index: 152
  library_dep_index: 148
  library_dep_index: 0
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 33
  dependency_index: 62
  dependency_index: 88
  dependency_index: 97
  dependency_index: 100
  dependency_index: 101
  dependency_index: 102
  dependency_index: 103
  dependency_index: 104
  dependency_index: 105
  dependency_index: 108
  dependency_index: 107
  dependency_index: 111
  dependency_index: 43
  dependency_index: 68
  dependency_index: 75
  dependency_index: 93
  dependency_index: 89
  dependency_index: 81
  dependency_index: 79
  dependency_index: 112
  dependency_index: 117
  dependency_index: 118
  dependency_index: 125
  dependency_index: 126
  dependency_index: 26
  dependency_index: 37
  dependency_index: 32
  dependency_index: 127
  dependency_index: 130
  dependency_index: 142
  dependency_index: 132
  dependency_index: 140
  dependency_index: 145
  dependency_index: 153
  dependency_index: 154
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
