#!/bin/bash

# 华为云MPS媒体处理服务配置自动化脚本
# 用途：自动创建和配置学习监督项目的媒体处理模板和工作流

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 配置变量
BUCKET_NAME="learning-supervision-obs"
REGION="cn-north-4"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查华为云CLI是否已安装和配置
check_hcloud_cli() {
    log_info "检查华为云CLI配置..."
    
    if ! command -v hcloud &> /dev/null; then
        log_error "华为云CLI未安装，请先安装华为云CLI"
        exit 1
    fi
    
    if ! hcloud configure list &> /dev/null; then
        log_error "华为云CLI未配置，请先运行 'hcloud configure' 配置认证信息"
        exit 1
    fi
    
    log_info "华为云CLI配置检查通过"
}

# 创建视频处理模板
create_video_templates() {
    log_info "创建视频处理模板..."
    
    # 视频合并模板
    cat > "$CONFIG_DIR/video_merge_template.json" << EOF
{
  "template_name": "video_merge_template",
  "template_type": "merge",
  "description": "学习监督视频分片合并模板",
  "input_config": {
    "source_type": "obs",
    "bucket": "$BUCKET_NAME",
    "input_pattern": "video/{deviceId}/raw/{date}/{sessionId}/segment_*.mp4",
    "sort_rule": {
      "sort_by": "filename",
      "order": "ascending"
    }
  },
  "output_config": {
    "target_type": "obs",
    "bucket": "$BUCKET_NAME",
    "output_path": "video/{deviceId}/processed/{date}/complete_{sessionId}.mp4"
  },
  "merge_config": {
    "video_codec": "h264",
    "audio_codec": "aac",
    "container_format": "mp4",
    "merge_mode": "concat"
  }
}
EOF

    # 视频转码模板
    cat > "$CONFIG_DIR/video_transcode_template.json" << EOF
{
  "template_name": "video_transcode_template",
  "template_type": "transcode",
  "description": "学习监督视频转码优化模板",
  "video_config": {
    "codec": "h264",
    "profile": "main",
    "level": "3.1",
    "bitrate_control": "vbr",
    "quality_presets": [
      {
        "name": "high_quality",
        "resolution": "1280x720",
        "bitrate": "1500k",
        "fps": 30,
        "gop_size": 60
      },
      {
        "name": "medium_quality",
        "resolution": "854x480",
        "bitrate": "800k",
        "fps": 25,
        "gop_size": 50
      },
      {
        "name": "low_quality",
        "resolution": "640x360",
        "bitrate": "400k",
        "fps": 20,
        "gop_size": 40
      }
    ]
  },
  "audio_config": {
    "codec": "aac",
    "bitrate": "128k",
    "sample_rate": "44100",
    "channels": 2
  }
}
EOF

    # 缩略图生成模板
    cat > "$CONFIG_DIR/video_thumbnail_template.json" << EOF
{
  "template_name": "video_thumbnail_template",
  "template_type": "thumbnail",
  "description": "视频缩略图生成模板",
  "thumbnail_config": {
    "output_format": "jpg",
    "quality": 85,
    "sizes": [
      {"width": 320, "height": 240, "suffix": "_small"},
      {"width": 640, "height": 480, "suffix": "_medium"},
      {"width": 1280, "height": 720, "suffix": "_large"}
    ],
    "capture_points": [
      {"type": "time", "value": "00:00:05"},
      {"type": "time", "value": "00:01:00"},
      {"type": "percentage", "value": 50}
    ]
  }
}
EOF

    log_info "✅ 视频处理模板创建完成"
}

# 创建音频处理模板
create_audio_templates() {
    log_info "创建音频处理模板..."
    
    # 音频合并模板
    cat > "$CONFIG_DIR/audio_merge_template.json" << EOF
{
  "template_name": "audio_merge_template",
  "template_type": "merge",
  "description": "学习监督音频分片合并模板",
  "input_config": {
    "source_type": "obs",
    "bucket": "$BUCKET_NAME",
    "input_pattern": "audio/{deviceId}/raw/{date}/{sessionId}/segment_*.mp3",
    "sort_rule": {
      "sort_by": "filename",
      "order": "ascending"
    }
  },
  "output_config": {
    "target_type": "obs",
    "bucket": "$BUCKET_NAME",
    "output_path": "audio/{deviceId}/processed/{date}/complete_{sessionId}.mp3"
  },
  "merge_config": {
    "audio_codec": "mp3",
    "container_format": "mp3",
    "merge_mode": "concat"
  }
}
EOF

    # 音频转码模板
    cat > "$CONFIG_DIR/audio_transcode_template.json" << EOF
{
  "template_name": "audio_transcode_template",
  "template_type": "transcode",
  "description": "学习监督音频转码优化模板",
  "audio_config": {
    "codec": "aac",
    "quality_presets": [
      {
        "name": "high_quality",
        "bitrate": "256k",
        "sample_rate": "48000",
        "channels": 2
      },
      {
        "name": "medium_quality",
        "bitrate": "128k",
        "sample_rate": "44100",
        "channels": 2
      },
      {
        "name": "low_quality",
        "bitrate": "64k",
        "sample_rate": "22050",
        "channels": 1
      }
    ],
    "audio_enhancement": {
      "noise_reduction": true,
      "volume_normalization": true,
      "dynamic_range_compression": true
    }
  }
}
EOF

    # 音频波形生成模板
    cat > "$CONFIG_DIR/audio_waveform_template.json" << EOF
{
  "template_name": "audio_waveform_template",
  "template_type": "analysis",
  "description": "音频波形分析模板",
  "waveform_config": {
    "output_format": "png",
    "width": 800,
    "height": 200,
    "background_color": "#ffffff",
    "waveform_color": "#3498db",
    "sample_points": 1000,
    "time_markers": true
  }
}
EOF

    log_info "✅ 音频处理模板创建完成"
}

# 创建处理工作流
create_workflows() {
    log_info "创建媒体处理工作流..."
    
    # 视频处理工作流
    cat > "$CONFIG_DIR/video_processing_workflow.json" << EOF
{
  "workflow_name": "video_processing_workflow",
  "description": "学习监督视频完整处理工作流",
  "trigger": {
    "type": "obs_event",
    "event_type": "ObjectCreated",
    "filter": {
      "bucket": "$BUCKET_NAME",
      "prefix": "video/",
      "suffix": ".mp4"
    },
    "condition": "session_complete"
  },
  "tasks": [
    {
      "task_id": "merge_video_segments",
      "task_type": "merge",
      "template_id": "video_merge_template",
      "input": {
        "source": "obs://$BUCKET_NAME/video/{deviceId}/raw/{date}/{sessionId}/",
        "pattern": "segment_*.mp4"
      },
      "output": {
        "target": "obs://$BUCKET_NAME/video/{deviceId}/processed/{date}/complete_{sessionId}.mp4"
      },
      "retry_config": {
        "max_retries": 3,
        "retry_interval": 60
      }
    },
    {
      "task_id": "transcode_video",
      "task_type": "transcode",
      "template_id": "video_transcode_template",
      "depends_on": ["merge_video_segments"],
      "input": {
        "source": "obs://$BUCKET_NAME/video/{deviceId}/processed/{date}/complete_{sessionId}.mp4"
      },
      "output": {
        "targets": [
          "obs://$BUCKET_NAME/video/{deviceId}/processed/{date}/high_quality_{sessionId}.mp4",
          "obs://$BUCKET_NAME/video/{deviceId}/processed/{date}/medium_quality_{sessionId}.mp4",
          "obs://$BUCKET_NAME/video/{deviceId}/processed/{date}/low_quality_{sessionId}.mp4"
        ]
      }
    },
    {
      "task_id": "generate_thumbnails",
      "task_type": "thumbnail",
      "template_id": "video_thumbnail_template",
      "depends_on": ["merge_video_segments"],
      "input": {
        "source": "obs://$BUCKET_NAME/video/{deviceId}/processed/{date}/complete_{sessionId}.mp4"
      },
      "output": {
        "target": "obs://$BUCKET_NAME/video/{deviceId}/processed/{date}/thumbnails/"
      }
    }
  ],
  "notification": {
    "success": {
      "type": "webhook",
      "url": "https://api.learningsupervision.com/mps/video/success"
    },
    "failure": {
      "type": "webhook",
      "url": "https://api.learningsupervision.com/mps/video/failure"
    }
  }
}
EOF

    # 音频处理工作流
    cat > "$CONFIG_DIR/audio_processing_workflow.json" << EOF
{
  "workflow_name": "audio_processing_workflow",
  "description": "学习监督音频完整处理工作流",
  "trigger": {
    "type": "obs_event",
    "event_type": "ObjectCreated",
    "filter": {
      "bucket": "$BUCKET_NAME",
      "prefix": "audio/",
      "suffix": ".mp3"
    },
    "condition": "session_complete"
  },
  "tasks": [
    {
      "task_id": "merge_audio_segments",
      "task_type": "merge",
      "template_id": "audio_merge_template",
      "input": {
        "source": "obs://$BUCKET_NAME/audio/{deviceId}/raw/{date}/{sessionId}/",
        "pattern": "segment_*.mp3"
      },
      "output": {
        "target": "obs://$BUCKET_NAME/audio/{deviceId}/processed/{date}/complete_{sessionId}.mp3"
      },
      "retry_config": {
        "max_retries": 3,
        "retry_interval": 60
      }
    },
    {
      "task_id": "transcode_audio",
      "task_type": "transcode",
      "template_id": "audio_transcode_template",
      "depends_on": ["merge_audio_segments"],
      "input": {
        "source": "obs://$BUCKET_NAME/audio/{deviceId}/processed/{date}/complete_{sessionId}.mp3"
      },
      "output": {
        "targets": [
          "obs://$BUCKET_NAME/audio/{deviceId}/processed/{date}/high_quality_{sessionId}.aac",
          "obs://$BUCKET_NAME/audio/{deviceId}/processed/{date}/medium_quality_{sessionId}.aac",
          "obs://$BUCKET_NAME/audio/{deviceId}/processed/{date}/low_quality_{sessionId}.aac"
        ]
      }
    },
    {
      "task_id": "generate_waveform",
      "task_type": "analysis",
      "template_id": "audio_waveform_template",
      "depends_on": ["merge_audio_segments"],
      "input": {
        "source": "obs://$BUCKET_NAME/audio/{deviceId}/processed/{date}/complete_{sessionId}.mp3"
      },
      "output": {
        "target": "obs://$BUCKET_NAME/audio/{deviceId}/processed/{date}/waveform_{sessionId}.png"
      }
    }
  ],
  "notification": {
    "success": {
      "type": "webhook",
      "url": "https://api.learningsupervision.com/mps/audio/success"
    },
    "failure": {
      "type": "webhook",
      "url": "https://api.learningsupervision.com/mps/audio/failure"
    }
  }
}
EOF

    log_info "✅ 媒体处理工作流创建完成"
}

# 上传模板到MPS服务
upload_templates() {
    log_info "上传处理模板到MPS服务..."
    
    local templates=(
        "video_merge_template.json"
        "video_transcode_template.json"
        "video_thumbnail_template.json"
        "audio_merge_template.json"
        "audio_transcode_template.json"
        "audio_waveform_template.json"
    )
    
    for template in "${templates[@]}"; do
        local template_file="$CONFIG_DIR/$template"
        local template_name=$(basename "$template" .json)
        
        if [ -f "$template_file" ]; then
            if hcloud mps create-template \
                --template-file "$template_file" \
                --region "$REGION" 2>/dev/null; then
                log_info "✅ 模板上传成功: $template_name"
            else
                log_warn "模板上传可能失败: $template_name"
            fi
        else
            log_error "模板文件不存在: $template_file"
        fi
    done
}

# 创建工作流
create_mps_workflows() {
    log_info "创建MPS工作流..."
    
    local workflows=(
        "video_processing_workflow.json"
        "audio_processing_workflow.json"
    )
    
    for workflow in "${workflows[@]}"; do
        local workflow_file="$CONFIG_DIR/$workflow"
        local workflow_name=$(basename "$workflow" .json)
        
        if [ -f "$workflow_file" ]; then
            if hcloud mps create-workflow \
                --workflow-file "$workflow_file" \
                --region "$REGION" 2>/dev/null; then
                log_info "✅ 工作流创建成功: $workflow_name"
            else
                log_warn "工作流创建可能失败: $workflow_name"
            fi
        else
            log_error "工作流文件不存在: $workflow_file"
        fi
    done
}

# 配置监控告警
configure_monitoring() {
    log_info "配置MPS监控告警..."
    
    # 创建监控配置
    cat > "$CONFIG_DIR/mps_monitoring_config.json" << EOF
{
  "monitoring_config": {
    "metrics": [
      {
        "metric_name": "workflow_success_rate",
        "description": "工作流成功率",
        "threshold": {
          "warning": 95,
          "critical": 90
        }
      },
      {
        "metric_name": "processing_duration",
        "description": "处理耗时",
        "threshold": {
          "warning": 300,
          "critical": 600
        }
      },
      {
        "metric_name": "queue_length",
        "description": "处理队列长度",
        "threshold": {
          "warning": 10,
          "critical": 20
        }
      }
    ],
    "alerts": [
      {
        "alert_name": "workflow_failure_alert",
        "condition": "workflow_success_rate < 90",
        "notification": {
          "type": "email",
          "recipients": ["<EMAIL>"]
        }
      },
      {
        "alert_name": "processing_delay_alert",
        "condition": "processing_duration > 600",
        "notification": {
          "type": "sms",
          "recipients": ["+86138xxxxxxxx"]
        }
      }
    ]
  }
}
EOF

    # 创建告警规则
    if hcloud ces create-alarm \
        --alarm-name "mps_workflow_failure" \
        --metric-name "workflow_success_rate" \
        --namespace "SYS.MPS" \
        --threshold 90 \
        --comparison-operator "LessThanThreshold" \
        --region "$REGION" 2>/dev/null; then
        log_info "✅ 监控告警配置成功"
    else
        log_warn "监控告警配置可能失败"
    fi
}

# 创建测试脚本
create_test_script() {
    log_info "创建MPS功能测试脚本..."
    
    cat > "$CONFIG_DIR/test_mps_workflow.sh" << 'EOF'
#!/bin/bash

# MPS工作流测试脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUCKET_NAME="learning-supervision-obs"

echo "=== MPS工作流测试 ==="

# 创建测试视频分片
create_test_video_segments() {
    local device_id="TEST_DEVICE_001"
    local date=$(date +%Y%m%d)
    local session_id="test_session_$(date +%s)"
    
    echo "创建测试视频分片..."
    
    # 创建测试目录
    local test_dir="video/$device_id/raw/$date/$session_id"
    
    # 上传测试分片（需要实际的视频文件）
    for i in {001..005}; do
        local segment_file="segment_$i.mp4"
        echo "上传测试分片: $segment_file"
        # hcloud obs cp test_video_segment.mp4 obs://$BUCKET_NAME/$test_dir/$segment_file
    done
    
    echo "测试视频分片创建完成"
    echo "设备ID: $device_id"
    echo "会话ID: $session_id"
    echo "日期: $date"
}

# 创建测试音频分片
create_test_audio_segments() {
    local device_id="TEST_DEVICE_001"
    local date=$(date +%Y%m%d)
    local session_id="test_session_$(date +%s)"
    
    echo "创建测试音频分片..."
    
    # 创建测试目录
    local test_dir="audio/$device_id/raw/$date/$session_id"
    
    # 上传测试分片（需要实际的音频文件）
    for i in {001..003}; do
        local segment_file="segment_$i.mp3"
        echo "上传测试分片: $segment_file"
        # hcloud obs cp test_audio_segment.mp3 obs://$BUCKET_NAME/$test_dir/$segment_file
    done
    
    echo "测试音频分片创建完成"
    echo "设备ID: $device_id"
    echo "会话ID: $session_id"
    echo "日期: $date"
}

# 检查处理结果
check_processing_results() {
    echo "检查处理结果..."
    
    # 检查视频处理结果
    echo "检查视频处理结果..."
    hcloud obs ls obs://$BUCKET_NAME/video/TEST_DEVICE_001/processed/
    
    # 检查音频处理结果
    echo "检查音频处理结果..."
    hcloud obs ls obs://$BUCKET_NAME/audio/TEST_DEVICE_001/processed/
}

# 主测试流程
main() {
    echo "开始MPS工作流测试..."
    
    create_test_video_segments
    create_test_audio_segments
    
    echo "等待处理完成（预计5-10分钟）..."
    sleep 300
    
    check_processing_results
    
    echo "MPS工作流测试完成"
}

# 执行测试
main "$@"
EOF

    chmod +x "$CONFIG_DIR/test_mps_workflow.sh"
    log_info "✅ MPS测试脚本创建成功"
}

# 创建配置摘要
create_configuration_summary() {
    log_info "创建MPS配置摘要..."
    
    cat > "$CONFIG_DIR/mps_configuration_summary.md" << EOF
# MPS媒体处理服务配置摘要

## 处理模板
- **视频合并模板**: video_merge_template
- **视频转码模板**: video_transcode_template  
- **视频缩略图模板**: video_thumbnail_template
- **音频合并模板**: audio_merge_template
- **音频转码模板**: audio_transcode_template
- **音频波形模板**: audio_waveform_template

## 处理工作流
- **视频处理工作流**: video_processing_workflow
- **音频处理工作流**: audio_processing_workflow

## 触发条件
- **触发方式**: IoT设备会话完成事件
- **处理延迟**: 通常5-10分钟
- **重试机制**: 最多3次重试

## 输出格式
### 视频输出
- 完整视频: complete_{sessionId}.mp4
- 高质量: high_quality_{sessionId}.mp4 (720p)
- 中等质量: medium_quality_{sessionId}.mp4 (480p)
- 低质量: low_quality_{sessionId}.mp4 (360p)
- 缩略图: thumbnails/

### 音频输出
- 完整音频: complete_{sessionId}.mp3
- 高质量: high_quality_{sessionId}.aac (256k)
- 中等质量: medium_quality_{sessionId}.aac (128k)
- 低质量: low_quality_{sessionId}.aac (64k)
- 波形图: waveform_{sessionId}.png

## 监控告警
- 工作流成功率监控
- 处理耗时监控
- 队列长度监控

## 测试方法
运行测试脚本: ./test_mps_workflow.sh

---
配置时间: $(date)
EOF

    log_info "✅ MPS配置摘要已保存"
}

# 主函数
main() {
    log_info "开始MPS媒体处理服务配置..."
    
    # 创建配置目录
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    CONFIG_DIR="$script_dir/../configs"
    mkdir -p "$CONFIG_DIR"
    
    check_hcloud_cli
    create_video_templates
    create_audio_templates
    create_workflows
    upload_templates
    create_mps_workflows
    configure_monitoring
    create_test_script
    create_configuration_summary
    
    log_info "🎉 MPS媒体处理服务配置完成！"
    log_info "配置文件位置: $CONFIG_DIR"
    log_info "运行测试: cd $CONFIG_DIR && ./test_mps_workflow.sh"
    log_warn "⚠️  请确保IoT设备端能正确发送会话完成事件"
}

# 执行主函数
main "$@"
