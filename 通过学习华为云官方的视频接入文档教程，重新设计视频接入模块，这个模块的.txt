通过学习华为云官方的视频接入文档教程，重新设计视频接入模块，这个模块的功能要求是只需要
调用设备端的数据实时显示视频直播画面，在视频显示界面下方增添一个语音交互功能用来进行录制语音发送给设备端
除此之外不需要其它多余功能
https://support.huaweicloud.com/api-vis/CreateSubscription.html
https://support.huaweicloud.com/productdesc-vis/vis_01_0006.html
https://support.huaweicloud.com/productdesc-vis/vis_01_0003.html
https://support.huaweicloud.com/productdesc-vis/vis_01_0005.html
https://support.huaweicloud.com/api-vis/CreateSubscription.html
https://support.huaweicloud.com/api-vis/ListSubscriptions.html
https://support.huaweicloud.com/api-vis/CreateStream.html
https://support.huaweicloud.com/api-vis/ListStreams.html
https://support.huaweicloud.com/api-vis/UpdateStream.html
https://support.huaweicloud.com/api-vis/ListStreamInfos.html
https://support.huaweicloud.com/api-vis/DeleteStream.html
https://support.huaweicloud.com/api-vis/UpdateRetention.html
https://support.huaweicloud.com/api-vis/ListStreamsAddresses.html