C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:40: Error: Class referenced in the manifest, org.eclipse.paho.android.service.MqttService, was not found in the project or the libraries [MissingClass]
        <service android:name="org.eclipse.paho.android.service.MqttService" />
                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "MissingClass":
   If a class is referenced in the manifest or in a layout file, it must also
   exist in the project (or in one of the libraries included by the project.
   This check helps uncover typos in registration names, or attempts to rename
   or move classes without updating the XML references properly.

   https://developer.android.com/guide/topics/manifest/manifest-intro.html

C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\AdvancedFatigueDisplay.kt:233: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                text = String.format("%.1f", fatigueIndex),
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\AdvancedFatigueDisplay.kt:274: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                text = String.format("%.0f", alertnessLevel * 100),
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\AdvancedFatigueDisplay.kt:520: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                StatisticItem("平均疲劳", String.format("%.1f", statistics.averageFatigue))
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\AdvancedFatigueDisplay.kt:521: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                StatisticItem("最高疲劳", String.format("%.1f", statistics.maxFatigue))
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\AdvancedFatigueDisplay.kt:522: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                StatisticItem("平均警觉", String.format("%.0f%%", statistics.averageAlertness * 100))
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\AdvancedFocusDisplay.kt:206: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                text = String.format("%.1f", focusIndex),
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\AdvancedFocusDisplay.kt:400: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                StatisticItem("平均", String.format("%.1f", statistics.averageFocus))
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\AdvancedFocusDisplay.kt:401: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                StatisticItem("最高", String.format("%.1f", statistics.maxFocus))
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\player\AudioPlayerScreen.kt:506: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        String.format("%02d:%02d:%02d", hours, minutes, seconds)
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\player\AudioPlayerScreen.kt:508: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        String.format("%02d:%02d", minutes, seconds)
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\ComprehensiveHealthDashboard.kt:416: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = String.format("%.0f", animatedScore),
                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\EEGData.kt:482: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            "专注度指数" to String.format("%.1f/10", focusIndex),
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\EEGData.kt:484: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            "置信度" to String.format("%.1f%%", confidence * 100),
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\EEGData.kt:485: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            "Beta/Alpha比值" to String.format("%.2f", betaAlphaRatio),
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\EEGData.kt:486: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            "Theta抑制" to String.format("%.1f%%", thetaSuppressionIndex * 100),
                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\EEGData.kt:487: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            "Gamma参与度" to String.format("%.1f%%", gammaEngagementIndex * 100),
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\EEGData.kt:488: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            "频谱一致性" to String.format("%.1f%%", spectralCoherenceIndex * 100),
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\EEGData.kt:489: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            "稳定性" to String.format("%.1f%%", stabilityIndex * 100),
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\EEGData.kt:490: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            "年龄调整" to String.format("%.2f", ageAdjustmentFactor),
                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\EEGData.kt:491: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            "时长调整" to String.format("%.2f", durationAdjustment)
                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\EEGData.kt:656: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            "疲劳指数" to String.format("%.1f/10", fatigueIndex),
                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\EEGData.kt:658: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            "警觉性水平" to String.format("%.1f%%", alertnessLevel * 100),
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\EEGData.kt:659: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            "置信度" to String.format("%.1f%%", confidence * 100),
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\EEGData.kt:660: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            "Theta/Beta比值" to String.format("%.2f", thetaBetaRatio),
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\EEGData.kt:661: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            "Alpha波功率" to String.format("%.1f%%", alphaPowerIndex * 100),
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\EEGData.kt:662: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            "Delta波功率" to String.format("%.1f%%", deltaPowerIndex * 100),
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\EEGData.kt:663: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            "眨眼频率" to String.format("%.1f%%", eyeBlinkIndex * 100),
                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\EEGData.kt:664: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            "反应时间" to String.format("%.1f%%", reactionTimeIndex * 100),
                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\EEGData.kt:666: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            "年龄调整" to String.format("%.2f", ageAdjustmentFactor),
                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\EEGData.kt:667: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            "环境调整" to String.format("%.2f", environmentalAdjustment),
                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\EEGData.kt:668: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            "时间调整" to String.format("%.2f", timeAdjustment)
                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\EEGSpectrumAnalyzer.kt:370: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            String.format("%.1f", value),
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\EEGSpectrumAnalyzer.kt:551: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                SpectrumStatItem("主导频率", "${String.format("%.1f", frequencyAnalysis.dominantFrequency)}Hz")
                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\EEGSpectrumAnalyzer.kt:552: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                SpectrumStatItem("总功率", String.format("%.1f", frequencyAnalysis.totalPower))
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\EEGSpectrumAnalyzer.kt:553: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                SpectrumStatItem("频谱熵", String.format("%.3f", frequencyAnalysis.spectralEntropy))
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\EEGWaveformDisplay.kt:513: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                StatItem("主导频率", "${String.format("%.1f", eegData.dominantFrequency)}Hz")
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\EEGWaveformDisplay.kt:514: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                StatItem("总功率", String.format("%.1f", eegData.totalPower))
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\EEGWaveformDisplay.kt:515: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                StatItem("频谱熵", String.format("%.3f", eegData.spectralEntropy))
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.kt:183: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  Delta: ${String.format("%.2f", result.eegData.deltaWave)}")
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.kt:184: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  Theta: ${String.format("%.2f", result.eegData.thetaWave)}")
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.kt:185: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  Alpha: ${String.format("%.2f", result.eegData.alphaWave)}")
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.kt:186: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  Beta:  ${String.format("%.2f", result.eegData.betaWave)}")
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.kt:187: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  Gamma: ${String.format("%.2f", result.eegData.gammaWave)}")
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.kt:188: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  主导频率: ${String.format("%.1f", result.eegData.dominantFrequency)}Hz")
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.kt:189: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  频谱熵: ${String.format("%.3f", result.eegData.spectralEntropy)}")
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.kt:193: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  疲劳指数: ${String.format("%.2f", result.childNormalResult.fatigueIndex)}/10")
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.kt:195: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  警觉性: ${String.format("%.1f", result.childNormalResult.alertnessLevel * 100)}%")
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.kt:196: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  置信度: ${String.format("%.1f", result.childNormalResult.confidence * 100)}%")
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.kt:201: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  疲劳指数: ${String.format("%.2f", result.childPoorResult.fatigueIndex)}/10")
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.kt:203: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  警觉性: ${String.format("%.1f", result.childPoorResult.alertnessLevel * 100)}%")
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.kt:204: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  环境影响: ${String.format("%.2f", result.childPoorResult.environmentalAdjustment)}倍")
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.kt:205: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  时间影响: ${String.format("%.2f", result.childPoorResult.timeAdjustment)}倍")
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.kt:209: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  疲劳指数: ${String.format("%.2f", result.teenNormalResult.fatigueIndex)}/10")
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.kt:211: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  警觉性: ${String.format("%.1f", result.teenNormalResult.alertnessLevel * 100)}%")
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.kt:212: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  年龄调整: ${String.format("%.2f", result.teenNormalResult.ageAdjustmentFactor)}倍")
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.kt:217: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  环境影响: ${String.format("%.2f", environmentalImpact)} (恶劣环境 - 正常环境)")
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.kt:223: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  年龄差异: ${String.format("%.2f", ageDiff)} (青少年 - 儿童)")
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.kt:237: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        report.appendLine("儿童正常环境平均疲劳度: ${String.format("%.2f", childNormalAvg)}")
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.kt:238: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        report.appendLine("儿童恶劣环境平均疲劳度: ${String.format("%.2f", childPoorAvg)}")
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.kt:239: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        report.appendLine("青少年正常环境平均疲劳度: ${String.format("%.2f", teenNormalAvg)}")
                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.kt:240: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        report.appendLine("环境影响: ${String.format("%.2f", childPoorAvg - childNormalAvg)}")
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.kt:241: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        report.appendLine("年龄影响: ${String.format("%.2f", teenNormalAvg - childNormalAvg)}")
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FocusCalculationDemo.kt:154: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  Delta: ${String.format("%.2f", result.eegData.deltaWave)}")
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FocusCalculationDemo.kt:155: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  Theta: ${String.format("%.2f", result.eegData.thetaWave)}")
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FocusCalculationDemo.kt:156: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  Alpha: ${String.format("%.2f", result.eegData.alphaWave)}")
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FocusCalculationDemo.kt:157: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  Beta:  ${String.format("%.2f", result.eegData.betaWave)}")
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FocusCalculationDemo.kt:158: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  Gamma: ${String.format("%.2f", result.eegData.gammaWave)}")
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FocusCalculationDemo.kt:159: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  主导频率: ${String.format("%.1f", result.eegData.dominantFrequency)}Hz")
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FocusCalculationDemo.kt:160: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  频谱熵: ${String.format("%.3f", result.eegData.spectralEntropy)}")
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FocusCalculationDemo.kt:164: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  专注度指数: ${String.format("%.2f", result.childResult.focusIndex)}/10")
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FocusCalculationDemo.kt:166: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  置信度: ${String.format("%.1f", result.childResult.confidence * 100)}%")
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FocusCalculationDemo.kt:167: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  Beta/Alpha比值: ${String.format("%.2f", result.childResult.betaAlphaRatio)}")
                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FocusCalculationDemo.kt:168: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  Theta抑制: ${String.format("%.1f", result.childResult.thetaSuppressionIndex * 100)}%")
                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FocusCalculationDemo.kt:169: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  年龄调整因子: ${String.format("%.2f", result.childResult.ageAdjustmentFactor)}")
                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FocusCalculationDemo.kt:173: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  专注度指数: ${String.format("%.2f", result.teenResult.focusIndex)}/10")
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FocusCalculationDemo.kt:175: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  置信度: ${String.format("%.1f", result.teenResult.confidence * 100)}%")
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FocusCalculationDemo.kt:176: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  Beta/Alpha比值: ${String.format("%.2f", result.teenResult.betaAlphaRatio)}")
                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FocusCalculationDemo.kt:177: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  Theta抑制: ${String.format("%.1f", result.teenResult.thetaSuppressionIndex * 100)}%")
                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FocusCalculationDemo.kt:178: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  年龄调整因子: ${String.format("%.2f", result.teenResult.ageAdjustmentFactor)}")
                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FocusCalculationDemo.kt:183: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            report.appendLine("  专注度差异: ${String.format("%.2f", focusDiff)} (青少年 - 儿童)")
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FocusCalculationDemo.kt:196: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        report.appendLine("儿童平均专注度: ${String.format("%.2f", childFocusAvg)}")
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FocusCalculationDemo.kt:197: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        report.appendLine("青少年平均专注度: ${String.format("%.2f", teenFocusAvg)}")
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FocusCalculationDemo.kt:198: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        report.appendLine("年龄差异: ${String.format("%.2f", teenFocusAvg - childFocusAvg)}")
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\iot\HuaweiCloudOkHttpClient.kt:717: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            put("angle_x", String.format("%.2f", angleX).toFloat())
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\iot\HuaweiCloudOkHttpClient.kt:718: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            put("angle_y", String.format("%.2f", angleY).toFloat())
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\iot\HuaweiCloudOkHttpClient.kt:719: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            put("angle_z", String.format("%.2f", angleZ).toFloat())
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\screen\MainScreen.kt:1645: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        hours > 0 -> String.format("%02d:%02d:%02d", hours, minutes % 60, seconds % 60)
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\screen\MainScreen.kt:1646: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        minutes > 0 -> String.format("%02d:%02d", minutes, seconds % 60)
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\screen\MainScreen.kt:1647: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        else -> String.format("00:%02d", seconds)
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\obs\MediaModels.kt:56: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            hours > 0 -> String.format("%02d:%02d:%02d", hours, minutes, seconds)
                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\obs\MediaModels.kt:57: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            else -> String.format("%02d:%02d", minutes, seconds)
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\obs\MediaModels.kt:113: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            hours > 0 -> String.format("%02d:%02d:%02d", hours, minutes, seconds)
                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\obs\MediaModels.kt:114: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            else -> String.format("%02d:%02d", minutes, seconds)
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\media\MediaThumbnailComponents.kt:429: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        String.format("%02d:%02d:%02d", hours, minutes, seconds)
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\media\MediaThumbnailComponents.kt:431: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        String.format("%02d:%02d", minutes, seconds)
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\iot\MqttIoTClient.kt:335: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            put("angle_x", String.format("%.2f", angleX).toFloat())
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\iot\MqttIoTClient.kt:336: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            put("angle_y", String.format("%.2f", angleY).toFloat())
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\iot\MqttIoTClient.kt:337: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            put("angle_z", String.format("%.2f", angleZ).toFloat())
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\PostureData.kt:86: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        return "X: ${String.format("%.1f", angleX)}° Y: ${String.format("%.1f", angleY)}° Z: ${String.format("%.1f", angleZ)}°"
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\PostureData.kt:86: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        return "X: ${String.format("%.1f", angleX)}° Y: ${String.format("%.1f", angleY)}° Z: ${String.format("%.1f", angleZ)}°"
                                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\PostureData.kt:86: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        return "X: ${String.format("%.1f", angleX)}° Y: ${String.format("%.1f", angleY)}° Z: ${String.format("%.1f", angleZ)}°"
                                                                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\PostureVisualization3D.kt:262: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            text = "${String.format("%.1f", angle)}°",
                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\VideoData.kt:130: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            hours > 0 -> String.format("%02d:%02d:%02d", hours, minutes % 60, seconds % 60)
                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\VideoData.kt:131: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            minutes > 0 -> String.format("%02d:%02d", minutes, seconds % 60)
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\VideoData.kt:132: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            else -> String.format("00:%02d", seconds)
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\VideoData.kt:141: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            fileSize >= 1024 * 1024 * 1024 -> String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0))
                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\VideoData.kt:142: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            fileSize >= 1024 * 1024 -> String.format("%.1f MB", fileSize / (1024.0 * 1024.0))
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\model\VideoData.kt:143: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            fileSize >= 1024 -> String.format("%.1f KB", fileSize / 1024.0)
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\player\VideoPlayerScreen.kt:416: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        String.format("%02d:%02d:%02d", hours, minutes, seconds)
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\player\VideoPlayerScreen.kt:418: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        String.format("%02d:%02d", minutes, seconds)
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "DefaultLocale":
   Calling String#toLowerCase() or #toUpperCase() without specifying an
   explicit locale is a common source of bugs. The reason for that is that
   those methods will use the current locale on the user's device, and even
   though the code appears to work correctly when you are developing the app,
   it will fail in some locales. For example, in the Turkish locale, the
   uppercase replacement for i is not I.

   If you want the methods to just perform ASCII replacement, for example to
   convert an enum name, call String#toUpperCase(Locale.ROOT) instead. If you
   really want to use the current locale, call
   String#toUpperCase(Locale.getDefault()) instead.

   https://developer.android.com/reference/java/util/Locale.html#default_locale

C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:14: Warning: Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details. [OldTargetApi]
        targetSdk = 35
        ~~~~~~~~~~~~~~

   Explanation for issues of type "OldTargetApi":
   When your application or sdk runs on a version of Android that is more
   recent than your targetSdkVersion specifies that it has been tested with,
   various compatibility modes kick in. This ensures that your application
   continues to work, but it may look out of place. For example, if the
   targetSdkVersion is less than 14, your app may get an option button in the
   UI.

   To fix this issue, set the targetSdkVersion to the highest available value.
   Then test your app to make sure everything works correctly. You may want to
   consult the compatibility notes to see what changes apply to each version
   you are adding support for:
   https://developer.android.com/reference/android/os/Build.VERSION_CODES.html
   as well as follow this guide:
   https://developer.android.com/distribute/best-practices/develop/target-sdk.
   html

   https://developer.android.com/distribute/best-practices/develop/target-sdk.html

C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\ComprehensiveHealthDashboard.kt:148: Warning: To get local formatting use getDateInstance(), getDateTimeInstance(), or getTimeInstance(), or use new SimpleDateFormat(String template, Locale locale) with for example Locale.US for ASCII dates. [SimpleDateFormat]
                    java.text.SimpleDateFormat("HH:mm:ss").format(java.util.Date(lastUpdate))
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\EEGWaveformDisplay.kt:516: Warning: To get local formatting use getDateInstance(), getDateTimeInstance(), or getTimeInstance(), or use new SimpleDateFormat(String template, Locale locale) with for example Locale.US for ASCII dates. [SimpleDateFormat]
                StatItem("采样时间", java.text.SimpleDateFormat("HH:mm:ss").format(java.util.Date(eegData.timestamp)))
                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FatigueDetectionDemo.kt:174: Warning: To get local formatting use getDateInstance(), getDateTimeInstance(), or getTimeInstance(), or use new SimpleDateFormat(String template, Locale locale) with for example Locale.US for ASCII dates. [SimpleDateFormat]
        report.appendLine("生成时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date())}")
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\eeg\FocusCalculationDemo.kt:145: Warning: To get local formatting use getDateInstance(), getDateTimeInstance(), or getTimeInstance(), or use new SimpleDateFormat(String template, Locale locale) with for example Locale.US for ASCII dates. [SimpleDateFormat]
        report.appendLine("生成时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date())}")
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\viewmodel\MainViewModel.kt:1400: Warning: To get local formatting use getDateInstance(), getDateTimeInstance(), or getTimeInstance(), or use new SimpleDateFormat(String template, Locale locale) with for example Locale.US for ASCII dates. [SimpleDateFormat]
            appendLine("最后更新: ${if (lastUpdate > 0) java.text.SimpleDateFormat("HH:mm:ss").format(java.util.Date(lastUpdate)) else "无"}")
                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\viewmodel\MainViewModel.kt:1418: Warning: To get local formatting use getDateInstance(), getDateTimeInstance(), or getTimeInstance(), or use new SimpleDateFormat(String template, Locale locale) with for example Locale.US for ASCII dates. [SimpleDateFormat]
                    appendLine("诊断时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date(diagnosticReport.timestamp))}")
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\obs\ObsApiTestHelper.kt:211: Warning: To get local formatting use getDateInstance(), getDateTimeInstance(), or getTimeInstance(), or use new SimpleDateFormat(String template, Locale locale) with for example Locale.US for ASCII dates. [SimpleDateFormat]
        report.appendLine("测试时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date())}")
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\obs\RealTimeVideoVerifier.kt:392: Warning: To get local formatting use getDateInstance(), getDateTimeInstance(), or getTimeInstance(), or use new SimpleDateFormat(String template, Locale locale) with for example Locale.US for ASCII dates. [SimpleDateFormat]
        sb.appendLine("验证时间: ${SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(Date(report.timestamp))}")
                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\test\VoiceModuleTestHelper.kt:236: Warning: To get local formatting use getDateInstance(), getDateTimeInstance(), or getTimeInstance(), or use new SimpleDateFormat(String template, Locale locale) with for example Locale.US for ASCII dates. [SimpleDateFormat]
            appendLine("测试时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date())}")
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SimpleDateFormat":
   Almost all callers should use getDateInstance(), getDateTimeInstance(), or
   getTimeInstance() to get a ready-made instance of SimpleDateFormat suitable
   for the user's locale. The main reason you'd create an instance this class
   directly is because you need to format/parse a specific machine-readable
   format, in which case you almost certainly want to explicitly ask for US to
   ensure that you get ASCII digits (rather than, say, Arabic digits).

   Therefore, you should either use the form of the SimpleDateFormat
   constructor where you pass in an explicit locale, such as Locale.US, or use
   one of the get instance methods, or suppress this error if really know what
   you are doing.

   https://developer.android.com/reference/java/text/SimpleDateFormat.html

C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\AndroidManifest.xml:30: Warning: Redundant label can be removed [RedundantLabel]
            android:label="@string/app_name"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RedundantLabel":
   When an activity does not have a label attribute, it will use the one from
   the application tag. Since the application has already specified the same
   label, the label on this activity can be omitted.

C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:80: Warning: A newer version of org.apache.httpcomponents:httpcore than 4.4.15 is available: 4.4.16 [GradleDependency]
    implementation("org.apache.httpcomponents:httpcore:4.4.15")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:83: Warning: A newer version of commons-codec:commons-codec than 1.11 is available: 1.15 [GradleDependency]
    implementation("commons-codec:commons-codec:1.11")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:87: Warning: A newer version of com.fasterxml.jackson.core:jackson-core than 2.8.1 is available: 2.15.2 [GradleDependency]
    implementation("com.fasterxml.jackson.core:jackson-core:2.8.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:88: Warning: A newer version of com.fasterxml.jackson.core:jackson-databind than 2.8.1 is available: 2.15.2 [GradleDependency]
    implementation("com.fasterxml.jackson.core:jackson-databind:2.8.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:89: Warning: A newer version of com.fasterxml.jackson.core:jackson-annotations than 2.8.1 is available: 2.15.2 [GradleDependency]
    implementation("com.fasterxml.jackson.core:jackson-annotations:2.8.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:92: Warning: A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0 [GradleDependency]
    implementation("com.google.code.gson:gson:2.10.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:99: Warning: A newer version of androidx.compose.material:material-icons-extended than 1.5.8 is available: 1.7.0 [GradleDependency]
    implementation("androidx.compose.material:material-icons-extended:1.5.8")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:102: Warning: A newer version of androidx.compose.animation:animation than 1.5.8 is available: 1.7.8 [GradleDependency]
    implementation("androidx.compose.animation:animation:1.5.8")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:105: Warning: Upgrade androidx.compose.foundation for keyboard and mouse support [GradleDependency]
    implementation("androidx.compose.foundation:foundation:1.5.8")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:132: Warning: A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0 [GradleDependency]
    implementation("com.google.code.gson:gson:2.10.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:138: Warning: A newer version of androidx.lifecycle:lifecycle-viewmodel-compose than 2.7.0 is available: 2.9.0 [GradleDependency]
    implementation("androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:139: Warning: A newer version of androidx.lifecycle:lifecycle-livedata-ktx than 2.7.0 is available: 2.9.0 [GradleDependency]
    implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.7.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:157: Warning: A newer version of androidx.activity:activity-compose than 1.8.2 is available: 1.10.1 [GradleDependency]
    implementation("androidx.activity:activity-compose:1.8.2")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\player\AudioPlayerManager.kt:88: Error: This declaration is opt-in and its usage should be marked with @androidx.media3.common.util.UnstableApi or @OptIn(markerClass = androidx.media3.common.util.UnstableApi.class) [UnsafeOptInUsageError from androidx.annotation.experimental]
            val dataSourceFactory = DefaultDataSourceFactory(
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\player\AudioPlayerManager.kt:88: Error: This declaration is opt-in and its usage should be marked with @androidx.media3.common.util.UnstableApi or @OptIn(markerClass = androidx.media3.common.util.UnstableApi.class) [UnsafeOptInUsageError from androidx.annotation.experimental]
            val dataSourceFactory = DefaultDataSourceFactory(
                                    ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\player\AudioPlayerManager.kt:91: Error: This declaration is opt-in and its usage should be marked with @androidx.media3.common.util.UnstableApi or @OptIn(markerClass = androidx.media3.common.util.UnstableApi.class) [UnsafeOptInUsageError from androidx.annotation.experimental]
                    .setUserAgent("LearningSupervisionApp/1.0")
                     ~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\player\AudioPlayerManager.kt:92: Error: This declaration is opt-in and its usage should be marked with @androidx.media3.common.util.UnstableApi or @OptIn(markerClass = androidx.media3.common.util.UnstableApi.class) [UnsafeOptInUsageError from androidx.annotation.experimental]
                    .setConnectTimeoutMs(30000)
                     ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\player\AudioPlayerManager.kt:93: Error: This declaration is opt-in and its usage should be marked with @androidx.media3.common.util.UnstableApi or @OptIn(markerClass = androidx.media3.common.util.UnstableApi.class) [UnsafeOptInUsageError from androidx.annotation.experimental]
                    .setReadTimeoutMs(30000)
                     ~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\player\AudioPlayerManager.kt:97: Error: This declaration is opt-in and its usage should be marked with @androidx.media3.common.util.UnstableApi or @OptIn(markerClass = androidx.media3.common.util.UnstableApi.class) [UnsafeOptInUsageError from androidx.annotation.experimental]
            val mediaSourceFactory = DefaultMediaSourceFactory(dataSourceFactory)
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\player\VideoPlayerManager.kt:65: Error: This declaration is opt-in and its usage should be marked with @androidx.media3.common.util.UnstableApi or @OptIn(markerClass = androidx.media3.common.util.UnstableApi.class) [UnsafeOptInUsageError from androidx.annotation.experimental]
            val dataSourceFactory = DefaultDataSourceFactory(
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\player\VideoPlayerManager.kt:65: Error: This declaration is opt-in and its usage should be marked with @androidx.media3.common.util.UnstableApi or @OptIn(markerClass = androidx.media3.common.util.UnstableApi.class) [UnsafeOptInUsageError from androidx.annotation.experimental]
            val dataSourceFactory = DefaultDataSourceFactory(
                                    ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\player\VideoPlayerManager.kt:68: Error: This declaration is opt-in and its usage should be marked with @androidx.media3.common.util.UnstableApi or @OptIn(markerClass = androidx.media3.common.util.UnstableApi.class) [UnsafeOptInUsageError from androidx.annotation.experimental]
                    .setUserAgent("LearningSupervisionApp/1.0")
                     ~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\player\VideoPlayerManager.kt:69: Error: This declaration is opt-in and its usage should be marked with @androidx.media3.common.util.UnstableApi or @OptIn(markerClass = androidx.media3.common.util.UnstableApi.class) [UnsafeOptInUsageError from androidx.annotation.experimental]
                    .setConnectTimeoutMs(30000)
                     ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\player\VideoPlayerManager.kt:70: Error: This declaration is opt-in and its usage should be marked with @androidx.media3.common.util.UnstableApi or @OptIn(markerClass = androidx.media3.common.util.UnstableApi.class) [UnsafeOptInUsageError from androidx.annotation.experimental]
                    .setReadTimeoutMs(30000)
                     ~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\player\VideoPlayerManager.kt:74: Error: This declaration is opt-in and its usage should be marked with @androidx.media3.common.util.UnstableApi or @OptIn(markerClass = androidx.media3.common.util.UnstableApi.class) [UnsafeOptInUsageError from androidx.annotation.experimental]
            val mediaSourceFactory = DefaultMediaSourceFactory(dataSourceFactory)
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\player\VideoPlayerScreen.kt:82: Error: This declaration is opt-in and its usage should be marked with @androidx.media3.common.util.UnstableApi or @OptIn(markerClass = androidx.media3.common.util.UnstableApi.class) [UnsafeOptInUsageError from androidx.annotation.experimental]
                        setShowBuffering(PlayerView.SHOW_BUFFERING_WHEN_PLAYING)
                        ~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\player\VideoPlayerScreen.kt:82: Error: This declaration is opt-in and its usage should be marked with @androidx.media3.common.util.UnstableApi or @OptIn(markerClass = androidx.media3.common.util.UnstableApi.class) [UnsafeOptInUsageError from androidx.annotation.experimental]
                        setShowBuffering(PlayerView.SHOW_BUFFERING_WHEN_PLAYING)
                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnsafeOptInUsageError":
   This API has been flagged as opt-in with error-level severity.

   Any declaration annotated with this marker is considered part of an
   unstable or
   otherwise non-standard API surface and its call sites should accept the
   opt-in
   aspect of it by using the @OptIn annotation, using the marker annotation
   --
   effectively causing further propagation of the opt-in aspect -- or
   configuring
   the UnsafeOptInUsageError check's options for project-wide opt-in.

   To configure project-wide opt-in, specify the opt-in option value in
   lint.xml
   as a comma-delimited list of opted-in annotations:

   <lint>
       <issue id="UnsafeOptInUsageError">
           <option name="opt-in" value="com.foo.ExperimentalBarAnnotation" />
       </issue>
   </lint>

   Vendor: Android Open Source Project
   Identifier: androidx.annotation.experimental
   Feedback: https://issuetracker.google.com/issues/new?component=459778

C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\AdvancedFatigueDisplay.kt:34: Warning: Modifier parameter should be the first optional parameter [ModifierParameter from androidx.compose.ui]
    modifier: Modifier = Modifier
    ~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\AdvancedFocusDisplay.kt:34: Warning: Modifier parameter should be the first optional parameter [ModifierParameter from androidx.compose.ui]
    modifier: Modifier = Modifier
    ~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\ComprehensiveHealthDashboard.kt:36: Warning: Modifier parameter should be the first optional parameter [ModifierParameter from androidx.compose.ui]
    modifier: Modifier = Modifier
    ~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\EEGBrainwaveChart.kt:31: Warning: Modifier parameter should be the first optional parameter [ModifierParameter from androidx.compose.ui]
    modifier: Modifier = Modifier
    ~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\EEGFocusGauge.kt:32: Warning: Modifier parameter should be the first optional parameter [ModifierParameter from androidx.compose.ui]
    modifier: Modifier = Modifier
    ~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\EEGSpectrumAnalyzer.kt:35: Warning: Modifier parameter should be the first optional parameter [ModifierParameter from androidx.compose.ui]
    modifier: Modifier = Modifier
    ~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\EEGWaveformDisplay.kt:33: Warning: Modifier parameter should be the first optional parameter [ModifierParameter from androidx.compose.ui]
    modifier: Modifier = Modifier
    ~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\ErrorHandling.kt:144: Warning: Modifier parameter should be the first optional parameter [ModifierParameter from androidx.compose.ui]
    modifier: Modifier = Modifier
    ~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\media\MediaThumbnailComponents.kt:156: Warning: Modifier parameter should be the first optional parameter [ModifierParameter from androidx.compose.ui]
    modifier: Modifier = Modifier
    ~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\media\MediaThumbnailComponents.kt:311: Warning: Modifier parameter should be the first optional parameter [ModifierParameter from androidx.compose.ui]
    modifier: Modifier = Modifier
    ~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\StatsCard.kt:177: Warning: Modifier parameter should be the first optional parameter [ModifierParameter from androidx.compose.ui]
    modifier: Modifier = Modifier
    ~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\VISVideoPlayerCard.kt:36: Warning: Modifier parameter should be the first optional parameter [ModifierParameter from androidx.compose.ui]
    modifier: Modifier = Modifier
    ~~~~~~~~

   Explanation for issues of type "ModifierParameter":
   The first (or only) Modifier parameter in a Composable function should
   follow the following rules:
   - Be named modifier
   - Have a type of Modifier
   - Either have no default value, or have a default value of Modifier
   - If optional, be the first optional parameter in the parameter list

   Vendor: Jetpack Compose
   Identifier: androidx.compose.ui
   Feedback: https://issuetracker.google.com/issues/new?component=612128

C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.huaweicloud.sdk\huaweicloud-sdk-core\3.1.153\48a128c5558b727c003a7a7f03337d7154daf6f6\huaweicloud-sdk-core-3.1.153.jar: Warning: checkClientTrusted is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers [TrustAllX509TrustManager]
C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.huaweicloud.sdk\huaweicloud-sdk-core\3.1.153\48a128c5558b727c003a7a7f03337d7154daf6f6\huaweicloud-sdk-core-3.1.153.jar: Warning: checkServerTrusted is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers [TrustAllX509TrustManager]

   Explanation for issues of type "TrustAllX509TrustManager":
   This check looks for X509TrustManager implementations whose
   checkServerTrusted or checkClientTrusted methods do nothing (thus trusting
   any certificate chain) which could result in insecure network traffic
   caused by trusting arbitrary TLS/SSL certificates presented by peers.

   https://goo.gle/TrustAllX509TrustManager

C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\AdvancedFatigueDisplay.kt:133: Hint: Prefer mutableFloatStateOf instead of mutableStateOf [AutoboxingStateCreation from androidx.compose.runtime]
    var animatedFatigueIndex by remember { mutableStateOf(0f) }
                                           ~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\AdvancedFatigueDisplay.kt:134: Hint: Prefer mutableFloatStateOf instead of mutableStateOf [AutoboxingStateCreation from androidx.compose.runtime]
    var animatedAlertnessLevel by remember { mutableStateOf(0f) }
                                             ~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\AdvancedFocusDisplay.kt:133: Hint: Prefer mutableFloatStateOf instead of mutableStateOf [AutoboxingStateCreation from androidx.compose.runtime]
    var animatedFocusIndex by remember { mutableStateOf(0f) }
                                         ~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\player\AudioPlayerScreen.kt:415: Hint: Prefer mutableLongStateOf instead of mutableStateOf [AutoboxingStateCreation from androidx.compose.runtime]
            var dragPosition by remember { mutableStateOf(currentPosition) }
                                           ~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\ComprehensiveHealthDashboard.kt:372: Hint: Prefer mutableFloatStateOf instead of mutableStateOf [AutoboxingStateCreation from androidx.compose.runtime]
    var animatedScore by remember { mutableStateOf(0f) }
                                    ~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\EEGFocusGauge.kt:34: Hint: Prefer mutableFloatStateOf instead of mutableStateOf [AutoboxingStateCreation from androidx.compose.runtime]
    var animatedScore by remember { mutableStateOf(0f) }
                                    ~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\EEGWaveformDisplay.kt:237: Hint: Prefer mutableFloatStateOf instead of mutableStateOf [AutoboxingStateCreation from androidx.compose.runtime]
    var animationProgress by remember { mutableStateOf(0f) }
                                        ~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\screen\LearningSupervisionScreen.kt:119: Hint: Prefer mutableIntStateOf instead of mutableStateOf [AutoboxingStateCreation from androidx.compose.runtime]
    var targetDuration by remember { mutableStateOf(45) }
                                     ~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\screen\LearningSupervisionScreen.kt:751: Hint: Prefer mutableIntStateOf instead of mutableStateOf [AutoboxingStateCreation from androidx.compose.runtime]
    var selectedDuration by remember { mutableStateOf(currentDuration) }
                                       ~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\player\VideoPlayerScreen.kt:316: Hint: Prefer mutableLongStateOf instead of mutableStateOf [AutoboxingStateCreation from androidx.compose.runtime]
            var dragPosition by remember { mutableStateOf(currentPosition) }
                                           ~~~~~~~~~~~~~~

   Explanation for issues of type "AutoboxingStateCreation":
   Calling mutableStateOf<T>() when T is either backed by a primitive type on
   the JVM or is a value class results in a state implementation that requires
   all state values to be boxed. This usually causes an additional allocation
   for each state write, and adds some additional work to auto-unbox values
   when reading the value of the state. Instead, prefer to use a specialized
   primitive state implementation for Int, Long, Float, and Double when the
   state does not need to track null values and does not override the default
   SnapshotMutationPolicy. See mutableIntStateOf(), mutableLongStateOf(),
   mutableFloatStateOf(), and mutableDoubleStateOf() for more information.

   Vendor: Jetpack Compose
   Identifier: androidx.compose.runtime
   Feedback: https://issuetracker.google.com/issues/new?component=612128

C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\res\values\colors.xml:3: Warning: The resource R.color.purple_200 appears to be unused [UnusedResources]
    <color name="purple_200">#FFBB86FC</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\res\values\colors.xml:4: Warning: The resource R.color.purple_500 appears to be unused [UnusedResources]
    <color name="purple_500">#FF6200EE</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\res\values\colors.xml:5: Warning: The resource R.color.purple_700 appears to be unused [UnusedResources]
    <color name="purple_700">#FF3700B3</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\res\values\colors.xml:6: Warning: The resource R.color.teal_200 appears to be unused [UnusedResources]
    <color name="teal_200">#FF03DAC5</color>
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\res\values\colors.xml:7: Warning: The resource R.color.teal_700 appears to be unused [UnusedResources]
    <color name="teal_700">#FF018786</color>
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\res\values\colors.xml:8: Warning: The resource R.color.black appears to be unused [UnusedResources]
    <color name="black">#FF000000</color>
           ~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\res\values\colors.xml:9: Warning: The resource R.color.white appears to be unused [UnusedResources]
    <color name="white">#FFFFFFFF</color>
           ~~~~~~~~~~~~

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

   Available options:

   **skip-libraries** (default is true):
   Whether the unused resource check should skip reporting unused resources in libraries.

   Many libraries will declare resources that are part of the library surface; other modules depending on the library will also reference the resources. To avoid reporting all these resources as unused (in the context of a library), the unused resource check normally skips reporting unused resources in libraries. Instead, run the unused resource check on the consuming app module (along with `checkDependencies=true`).

   However, there are cases where you want to check that all the resources declared in a library are used; in that case, you can disable the skip option.

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UnusedResources">
           <option name="skip-libraries" value="true" />
       </issue>
   </lint>
   ```

C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\player\AudioPlayerManager.kt:189: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    Uri.parse(session.streamUrl)
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\viewmodel\MainViewModel.kt:1596: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
                    prefs.edit()
                    ~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\auth\TokenManager.kt:306: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        prefs.edit()
        ~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\auth\TokenManager.kt:319: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        prefs.edit()
        ~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\settings\UserSettingsManager.kt:51: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
            sharedPreferences.edit()
            ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\ui\components\VISVideoPlayerCard.kt:92: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                            setVideoURI(Uri.parse(url))
                                        ~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\src\main\java\com\example\iotandroidv20\player\VideoPlayerManager.kt:131: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                Uri.parse(videoSession.streamUrl)
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseKtx":
   The Android KTX libraries decorates the Android platform SDK as well as
   various libraries with more convenient extension functions available from
   Kotlin, allowing you to use default parameters, named parameters, and
   more.

   Available options:

   **remove-defaults** (default is true):
   Whether to skip arguments that match the defaults provided by the extension.

   Extensions often provide default values for some of the parameters. For example:
   ```kotlin
   fun Path.readLines(charset: Charset = Charsets.UTF_8): List<String> { return Files.readAllLines(this, charset) }
   ```
   This lint check will by default automatically omit parameters that match the default, so if your code was calling

   ```kotlin
   Files.readAllLines(file, Charset.UTF_8)
   ```
   lint would replace this with
   ```kotlin
   file.readLines()
   ```
   rather than

   ```kotlin
   file.readLines(Charset.UTF_8
   ```
   You can turn this behavior off using this option.

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UseKtx">
           <option name="remove-defaults" value="true" />
       </issue>
   </lint>
   ```

   **require-present** (default is true):
   Whether to only offer extensions already available.

   This option lets you only have lint suggest extension replacements if those extensions are already available on the class path (in other words, you're already depending on the library containing the extension method.)

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UseKtx">
           <option name="require-present" value="true" />
       </issue>
   </lint>
   ```

C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:72: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.squareup.okhttp3:okhttp:4.12.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:73: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.squareup.okhttp3:logging-interceptor:4.12.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:80: Warning: Use version catalog instead [UseTomlInstead]
    implementation("org.apache.httpcomponents:httpcore:4.4.15")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:83: Warning: Use version catalog instead [UseTomlInstead]
    implementation("commons-codec:commons-codec:1.11")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:84: Warning: Use version catalog instead [UseTomlInstead]
    implementation("commons-logging:commons-logging:1.2")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:87: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.fasterxml.jackson.core:jackson-core:2.8.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:88: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.fasterxml.jackson.core:jackson-databind:2.8.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:89: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.fasterxml.jackson.core:jackson-annotations:2.8.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:92: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.google.code.gson:gson:2.10.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:99: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.compose.material:material-icons-extended:1.5.8")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:102: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.compose.animation:animation:1.5.8")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:105: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.compose.foundation:foundation:1.5.8")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:108: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.navigation:navigation-compose:2.7.6")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:115: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.huaweicloud.sdk:huaweicloud-sdk-iotda:3.1.153")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:116: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.huaweicloud.sdk:huaweicloud-sdk-core:3.1.153")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:119: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.squareup.okhttp3:okhttp:4.12.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:122: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.fasterxml.jackson.core:jackson-core:2.15.2")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:123: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.fasterxml.jackson.core:jackson-databind:2.15.2")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:124: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.fasterxml.jackson.core:jackson-annotations:2.15.2")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:127: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.squareup.okhttp3:okhttp:4.12.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:128: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.squareup.retrofit2:retrofit:2.9.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:129: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:132: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.google.code.gson:gson:2.10.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:135: Warning: Use version catalog instead [UseTomlInstead]
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:138: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:139: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.7.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:142: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.google.accompanist:accompanist-permissions:0.32.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:148: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.media3:media3-exoplayer:1.2.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:149: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.media3:media3-ui:1.2.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:150: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.media3:media3-common:1.2.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:151: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.media3:media3-session:1.2.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:154: Warning: Use version catalog instead [UseTomlInstead]
    implementation("io.coil-kt:coil-compose:2.5.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\IotAndroidV20\app\build.gradle.kts:157: Warning: Use version catalog instead (androidx.activity:activity-compose is already available as androidx-activity-compose, but using version 1.10.1 instead) [UseTomlInstead]
    implementation("androidx.activity:activity-compose:1.8.2")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseTomlInstead":
   If your project is using a libs.versions.toml file, you should place all
   Gradle dependencies in the TOML file. This lint check looks for version
   declarations outside of the TOML file and suggests moving them (and in the
   IDE, provides a quickfix to performing the operation automatically).

15 errors, 195 warnings, 10 hints
