<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.huaweicloud</groupId>
	<artifactId>esdk-obs-android</artifactId>
	<version>3.25.5</version>
	<packaging>jar</packaging>

	<name>OBS SDK for Android</name>
	<description>The OBS SDK for Android used for accessing Object Storage Service</description>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<maven.javadoc.skip>true</maven.javadoc.skip>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
			<version>4.12.0</version>
		</dependency>
		<dependency>
			<groupId>com.squareup.okio</groupId>
			<artifactId>okio</artifactId>
			<version>3.8.0</version>
		</dependency>
		
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>2.15.4</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.15.4</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>2.15.4</version>
		</dependency>

	</dependencies>

	<build>
		<sourceDirectory>app/src/main/java/com</sourceDirectory>

		<resources>
			<resource>
				<directory>app/src/main/resource</directory>
				<excludes>
					<exclude>log4j2.xml</exclude>
				</excludes>
			</resource>
		</resources>
		<testSourceDirectory>app/src/test/java</testSourceDirectory>
		<plugins>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-clean-plugin</artifactId>
				<configuration>
					<filesets>
						<fileset>
							<directory>logs</directory>
						</fileset>
					</filesets>
				</configuration>
			</plugin>

			<!-- compile plugin -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<encoding>UTF-8</encoding>
					<source>1.8</source>
					<target>1.8</target>
				</configuration>
				<executions>
					<execution>
						<phase>compile</phase>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>3.0.0-M1</version>
				<configuration>
					<source>1.8</source>
					<docencoding>UTF-8</docencoding>
					<charset>UTF-8</charset>
					<excludePackageNames>*.internal.*:*.log:*.proxy:okio</excludePackageNames>
					<destDir>${project.build.directory}/doc</destDir>
				</configuration>
				<executions>
					<execution>
						<id>attach-javadocs</id>
						<phase>install</phase>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<configuration>
					<excludes>
						<exclude>okio/*</exclude> 
					</excludes>
				</configuration>
			</plugin>
			
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-assembly-plugin</artifactId>
				<configuration>
					<archive>
						<manifest>
							<mainClass>com.test.MainClassName</mainClass>
						</manifest>
					</archive>
					<descriptorRefs>
						<descriptorRef>jar-with-dependencies</descriptorRef>
					</descriptorRefs>
				</configuration>
				<executions>
					<execution>
						<id>make-assembly</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>


</project>
