# 🔧 IoT Android 学习监督项目 - 技术实现细节文档

## 📋 文档说明

本文档是《项目交接文档.md》的技术细节补充，包含具体的代码实现、API调用、数据模型和关键算法等技术信息，帮助新AI开发者快速理解项目的技术实现。

---

## 🏗️ 核心架构实现

### MVVM架构模式
```kotlin
// MainViewModel.kt - 主要状态管理
class MainViewModel : ViewModel() {
    private val _uiState = MutableStateFlow(MainUiState())
    val uiState: StateFlow<MainUiState> = _uiState.asStateFlow()
    
    // 学习监督管理器
    private val learningSupervisionManager = LearningSupervisionManager.getInstance()
    
    // 多模态数据分析器
    private val multiModalAnalyzer = MultiModalDataAnalyzer.getInstance()
}
```

### 华为云IoT连接实现
```kotlin
// IoTManager.kt - 核心连接逻辑
class IoTManager {
    // Token认证 (推荐方式)
    private fun authenticateWithToken(): String {
        val tokenRequest = TokenRequest(
            username = "mkloopjnb",
            password = "jzzaurezz337",
            domain = "945cda04010d48e0902f7ff7923c0ab5"
        )
        return huaweiCloudApi.getToken(tokenRequest)
    }
    
    // 设备数据获取
    fun getDeviceProperties(deviceId: String): DeviceProperties {
        val url = "https://iotda.cn-north-4.myhuaweicloud.com/v5/iot/projects/17da5bacd5dd47589ca000607ae61ccc/devices/$deviceId/properties"
        return httpClient.get(url, headers = mapOf("X-Auth-Token" to token))
    }
}
```

---

## 🧠 多模态传感器实现

### EEG脑电波处理
```kotlin
// EEGManager.kt - 核心算法
class EEGManager {
    // 专注度计算 (基于Alpha/Beta波比例)
    fun calculateFocusLevel(eegData: EEGData): Float {
        val alphaRatio = eegData.alphaPower / eegData.totalPower
        val betaRatio = eegData.betaPower / eegData.totalPower
        val thetaRatio = eegData.thetaPower / eegData.totalPower
        
        // 专注度算法: Beta波增强，Alpha波适中，Theta波降低
        return (betaRatio * 0.6f + alphaRatio * 0.3f - thetaRatio * 0.1f)
            .coerceIn(0f, 1f)
    }
    
    // 疲劳检测 (基于Theta/Alpha比例)
    fun detectFatigue(eegData: EEGData): FatigueLevel {
        val fatigueIndex = eegData.thetaPower / eegData.alphaPower
        return when {
            fatigueIndex > 1.5f -> FatigueLevel.HIGH
            fatigueIndex > 1.0f -> FatigueLevel.MEDIUM
            else -> FatigueLevel.LOW
        }
    }
}
```

### 语音交互实现
```kotlin
// VoiceManager.kt - ASR/TTS集成
class VoiceManager {
    // 语音识别 (华为云ASR)
    suspend fun recognizeSpeech(audioData: ByteArray): String {
        val request = ASRRequest(
            audioFormat = "wav",
            sampleRate = 16000,
            audioData = audioData.toBase64()
        )
        return huaweiCloudASR.recognize(request).text
    }
    
    // 语音合成 (华为云TTS)
    suspend fun synthesizeSpeech(text: String): ByteArray {
        val request = TTSRequest(
            text = text,
            voice = "xiaoyan",
            audioFormat = "wav"
        )
        return huaweiCloudTTS.synthesize(request).audioData
    }
}
```

---

## 📱 OBS音视频模块实现

### OBS管理器核心实现
```kotlin
// ObsManager.kt - HTTP API实现
class ObsManager {
    // 获取预签名URL
    suspend fun getPresignedUrl(objectKey: String, method: String = "GET"): String {
        val canonicalRequest = buildCanonicalRequest(method, objectKey)
        val signature = calculateSignature(canonicalRequest)
        
        return "$endpoint/$objectKey?${buildQueryParams(signature)}"
    }
    
    // 文件上传
    suspend fun uploadFile(objectKey: String, fileData: ByteArray): Boolean {
        val presignedUrl = getPresignedUrl(objectKey, "PUT")
        val response = httpClient.put(presignedUrl, fileData)
        return response.isSuccessful
    }
    
    // 文件下载
    suspend fun downloadFile(objectKey: String): ByteArray? {
        val presignedUrl = getPresignedUrl(objectKey, "GET")
        return httpClient.get(presignedUrl).body?.bytes()
    }
}
```

### 媒体播放器实现
```kotlin
// VideoPlayerManager.kt - ExoPlayer集成
class VideoPlayerManager(private val context: Context) {
    private var exoPlayer: ExoPlayer? = null
    
    fun initializePlayer() {
        exoPlayer = ExoPlayer.Builder(context)
            .setMediaSourceFactory(DefaultMediaSourceFactory(dataSourceFactory))
            .build()
            .apply {
                addListener(object : Player.Listener {
                    override fun onPlaybackStateChanged(playbackState: Int) {
                        updatePlayerState(playbackState)
                    }
                })
            }
    }
    
    fun playVideoSession(videoSession: VideoSession) {
        val uri = if (videoSession.isDownloaded()) {
            Uri.fromFile(File(videoSession.localFilePath))
        } else {
            Uri.parse(videoSession.streamUrl)
        }
        
        val mediaItem = MediaItem.Builder()
            .setUri(uri)
            .setMediaId(videoSession.sessionId)
            .build()
            
        exoPlayer?.apply {
            setMediaItem(mediaItem)
            prepare()
            playWhenReady = true
        }
    }
}
```

---

## 📊 数据模型定义

### 核心数据模型
```kotlin
// 视频会话模型
data class VideoSession(
    val sessionId: String,
    val deviceId: String,
    val date: String,
    val fileName: String,
    val fileSize: Long,
    val lastModified: Date,
    val objectKey: String,
    val streamUrl: String,
    val downloadStatus: DownloadStatus = DownloadStatus.NOT_DOWNLOADED,
    val localFilePath: String? = null,
    val thumbnailUrl: String? = null,
    val duration: Long = 0L, // 秒
    val resolution: String = "1280x720",
    val bitrate: Int = 1500, // kbps
    val codec: String = "H.264"
)

// 音频会话模型
data class AudioSession(
    val sessionId: String,
    val deviceId: String,
    val date: String,
    val fileName: String,
    val fileSize: Long,
    val lastModified: Date,
    val objectKey: String,
    val streamUrl: String,
    val downloadStatus: DownloadStatus = DownloadStatus.NOT_DOWNLOADED,
    val localFilePath: String? = null,
    val duration: Long = 0L, // 秒
    val sampleRate: Int = 44100, // Hz
    val bitrate: Int = 128, // kbps
    val channels: Int = 2, // 1=单声道, 2=立体声
    val codec: String = "MP3"
)

// EEG数据模型
data class EEGData(
    val timestamp: Long,
    val deviceId: String,
    val rawSignal: FloatArray,
    val alphaPower: Float,
    val betaPower: Float,
    val thetaPower: Float,
    val deltaPower: Float,
    val totalPower: Float,
    val focusLevel: Float,
    val fatigueLevel: FatigueLevel,
    val quality: SignalQuality
)
```

### 学习监督数据模型
```kotlin
// 学习会话模型
data class LearningSession(
    val sessionId: String,
    val startTime: Long,
    val endTime: Long?,
    val studentAge: Int,
    val environment: EnvironmentData,
    val postureData: List<PostureData>,
    val eegData: List<EEGData>,
    val voiceInteractions: List<VoiceInteraction>,
    val videoRecords: List<VideoSession>,
    val audioRecords: List<AudioSession>,
    val overallAssessment: LearningAssessment
)

// 学习评估模型
data class LearningAssessment(
    val focusScore: Float, // 0-100
    val postureScore: Float, // 0-100
    val fatigueLevel: FatigueLevel,
    val recommendations: List<String>,
    val improvementAreas: List<String>,
    val sessionSummary: String
)
```

---

## 🔧 关键算法实现

### 多模态数据融合算法
```kotlin
// MultiModalDataAnalyzer.kt
class MultiModalDataAnalyzer {
    fun analyzeMultiModalData(
        postureData: PostureData,
        eegData: EEGData,
        environmentData: EnvironmentData,
        studentAge: Int
    ): LearningAssessment {
        
        // 权重配置 (可根据年龄调整)
        val weights = getAgeAdjustedWeights(studentAge)
        
        // 各项指标计算
        val postureScore = calculatePostureScore(postureData) * weights.posture
        val focusScore = eegData.focusLevel * 100 * weights.focus
        val environmentScore = calculateEnvironmentScore(environmentData) * weights.environment
        
        // 综合评分
        val overallScore = (postureScore + focusScore + environmentScore) / 
                          (weights.posture + weights.focus + weights.environment)
        
        // 生成建议
        val recommendations = generateRecommendations(postureScore, focusScore, environmentScore)
        
        return LearningAssessment(
            focusScore = focusScore,
            postureScore = postureScore,
            fatigueLevel = eegData.fatigueLevel,
            recommendations = recommendations,
            improvementAreas = identifyImprovementAreas(postureScore, focusScore),
            sessionSummary = generateSessionSummary(overallScore)
        )
    }
    
    private fun getAgeAdjustedWeights(age: Int): AnalysisWeights {
        return when (age) {
            in 6..8 -> AnalysisWeights(posture = 0.5f, focus = 0.3f, environment = 0.2f)
            in 9..12 -> AnalysisWeights(posture = 0.4f, focus = 0.4f, environment = 0.2f)
            in 13..18 -> AnalysisWeights(posture = 0.3f, focus = 0.5f, environment = 0.2f)
            else -> AnalysisWeights(posture = 0.4f, focus = 0.4f, environment = 0.2f)
        }
    }
}
```

### 智能推荐算法
```kotlin
// IntelligentGuidanceEngine.kt
class IntelligentGuidanceEngine {
    fun generatePersonalizedGuidance(
        learningHistory: List<LearningSession>,
        currentAssessment: LearningAssessment,
        studentProfile: StudentProfile
    ): PersonalizedGuidance {
        
        // 分析历史趋势
        val trends = analyzeLearningTrends(learningHistory)
        
        // 识别问题模式
        val problemPatterns = identifyProblemPatterns(learningHistory)
        
        // 生成个性化建议
        val recommendations = when {
            currentAssessment.focusScore < 60 -> generateFocusImprovementPlan(trends, studentProfile)
            currentAssessment.postureScore < 70 -> generatePostureImprovementPlan(problemPatterns)
            currentAssessment.fatigueLevel == FatigueLevel.HIGH -> generateRestRecommendations()
            else -> generateMaintenancePlan(currentAssessment)
        }
        
        return PersonalizedGuidance(
            immediateActions = recommendations.immediate,
            longTermGoals = recommendations.longTerm,
            environmentAdjustments = recommendations.environment,
            scheduleOptimization = optimizeStudySchedule(trends, studentProfile)
        )
    }
}
```

---

## 🧪 测试和调试

### 测试工具使用
```kotlin
// MediaTestUtils.kt - 测试工具
object MediaTestUtils {
    // 运行完整测试套件
    fun runFullTestSuite(context: Context, scope: CoroutineScope) {
        // 1. 基础配置测试
        val configOk = testObsConfiguration(context)
        val managerOk = testObsManagerInitialization(context)
        
        if (configOk && managerOk) {
            // 2. 功能测试
            testMediaRepository(context, scope)
            testPlayerFunctionality(context)
            
            // 3. 性能测试
            performanceTest(context, scope)
        }
    }
    
    // 创建测试数据
    fun createTestData(): Pair<List<VideoSession>, List<AudioSession>> {
        // 生成模拟的音视频会话数据
        // 用于UI测试和功能验证
    }
}
```

### 调试技巧
1. **日志标签**: 每个模块使用统一的TAG格式
2. **状态监控**: 使用StateFlow观察数据变化
3. **网络调试**: OkHttp拦截器记录API调用
4. **性能分析**: 使用Android Profiler监控内存和CPU

---

## 🔗 API接口文档

### 华为云IoT API
```
基础URL: https://iotda.cn-north-4.myhuaweicloud.com/v5/iot
认证方式: X-Auth-Token (Token认证)

主要接口:
- GET /projects/{project_id}/devices/{device_id}/properties - 获取设备属性
- POST /projects/{project_id}/devices/{device_id}/commands - 发送设备命令
- GET /projects/{project_id}/devices/{device_id}/messages - 获取设备消息
```

### 华为云OBS API
```
基础URL: https://{bucket-name}.obs.cn-north-4.myhuaweicloud.com
认证方式: AWS Signature V4

主要操作:
- PUT /{object-key} - 上传文件
- GET /{object-key} - 下载文件
- HEAD /{object-key} - 获取文件元数据
- DELETE /{object-key} - 删除文件
```

---

## 📝 配置文件示例

### OBS配置示例
```kotlin
// ObsConfig配置示例
val obsConfig = ObsConfig.getInstance().apply {
    updateConfig(
        accessKey = "HPUAFQCTHCE7ZQ854RXI",
        secretKey = "your-secret-key",
        endpoint = "obs.cn-north-4.myhuaweicloud.com",
        bucketName = "learning-supervision-obs",
        region = "cn-north-4",
        pathPrefix = "media/",
        enableHttps = true,
        connectionTimeout = 30,
        socketTimeout = 60
    )
}
```

### 设备配置示例
```json
{
  "deviceId": "682d4e2e84adf272cda5ad878_L610_TEST",
  "deviceSecret": "jzzaurezz337",
  "projectId": "17da5bacd5dd47589ca000607ae61ccc",
  "region": "cn-north-4",
  "capabilities": {
    "posture_detection": true,
    "camera": true,
    "microphone": true,
    "eeg_sensor": true,
    "voice_reminder": true
  }
}
```

---

**本技术文档提供了项目的详细实现信息，配合《项目交接文档.md》使用，可以帮助新AI开发者快速理解和继续项目开发。** 🔧

**重点关注OBS音视频模块和学习监督系统的实现，这是当前项目的核心功能。** 🎯

---
*文档创建时间: 2025年1月3日*
*适用版本: IoT Android v3.0*
*下次更新: 解决编译问题后更新*
