package com.example.iotandroidv20.model

/**
 * 学习监督模式 - 集成坐姿、专注度、疲劳度的综合监控
 */
data class LearningSupervisionSession(
    val sessionId: String,
    val childId: String,
    val startTime: Long,
    val endTime: Long? = null,
    val sessionType: LearningSessionType,
    val targetDuration: Int, // 分钟
    val actualDuration: Int = 0, // 分钟
    val status: SessionStatus,
    val overallScore: Float = 0f, // 0-100 综合评分
    val metrics: LearningMetrics,
    val milestones: List<SessionMilestone> = emptyList(),
    val interventions: List<Intervention> = emptyList(),
    val summary: SessionSummary? = null
)

/**
 * 学习会话类型
 */
enum class LearningSessionType(val displayName: String, val recommendedDuration: Int) {
    HOMEWORK("作业时间", 45),
    READING("阅读时间", 30),
    CREATIVE("创意活动", 60),
    PRACTICE("练习时间", 30),
    REVIEW("复习时间", 25),
    PROJECT("项目学习", 90),
    FREE_STUDY("自由学习", 60)
}

/**
 * 会话状态
 */
enum class SessionStatus(val displayName: String) {
    PREPARING("准备中"),
    ACTIVE("进行中"),
    BREAK("休息中"),
    PAUSED("暂停"),
    COMPLETED("已完成"),
    INTERRUPTED("被中断"),
    CANCELLED("已取消")
}

// 注意：FocusLevel、FatigueLevel、PostureQuality 已在其他文件中定义
// 这里不再重复定义，直接使用导入的版本

/**
 * 学习指标
 */
data class LearningMetrics(
    val postureMetrics: PostureMetrics,
    val focusMetrics: FocusMetrics,
    val fatigueMetrics: FatigueMetrics,
    val engagementMetrics: EngagementMetrics,
    val environmentMetrics: EnvironmentMetrics,
    val progressMetrics: ProgressMetrics
)

/**
 * 坐姿指标
 */
data class PostureMetrics(
    val averageScore: Float, // 0-100
    val goodPosturePercentage: Float, // 0-100
    val poorPostureEvents: Int,
    val longestGoodPostureStreak: Int, // 分钟
    val postureStability: Float, // 0-1 稳定性
    val improvementTrend: Trend,
    val commonIssues: List<PostureIssue>
)

/**
 * 专注度指标
 */
data class FocusMetrics(
    val averageFocusScore: Float, // 0-100
    val peakFocusScore: Float,
    val focusStability: Float, // 0-1
    val deepFocusDuration: Int, // 分钟
    val distractionEvents: Int,
    val focusRecoveryTime: Float, // 平均恢复时间（分钟）
    val focusPattern: FocusPattern,
    val optimalFocusWindows: List<TimeWindow>
)

/**
 * 疲劳度指标
 */
data class FatigueMetrics(
    val averageFatigueLevel: Float, // 0-100
    val fatigueOnsetTime: Int, // 分钟，疲劳开始时间
    val fatigueAcceleration: Float, // 疲劳加速度
    val recoveryEffectiveness: Float, // 休息恢复效果 0-1
    val mentalFatigueScore: Float,
    val physicalFatigueScore: Float,
    val fatigueResistance: Float // 抗疲劳能力 0-1
)

/**
 * 参与度指标
 */
data class EngagementMetrics(
    val overallEngagement: Float, // 0-100
    val activeParticipation: Float,
    val taskSwitchingFrequency: Int,
    val selfRegulationEvents: Int, // 自我调节次数
    val motivationLevel: Float, // 0-100
    val persistenceScore: Float // 坚持度 0-100
)

/**
 * 环境指标
 */
data class EnvironmentMetrics(
    val environmentStability: Float, // 0-1
    val optimalConditionPercentage: Float, // 0-100
    val environmentalDisruptions: Int,
    val adaptationScore: Float, // 环境适应性 0-1
    val environmentQualityScore: Float // 0-100
)

/**
 * 进度指标
 */
data class ProgressMetrics(
    val taskCompletionRate: Float, // 0-100
    val qualityScore: Float, // 0-100
    val efficiencyScore: Float, // 0-100
    val learningVelocity: Float, // 学习速度
    val skillDevelopmentRate: Float, // 技能发展速度
    val goalAchievementRate: Float // 目标达成率 0-100
)

/**
 * 专注模式
 */
enum class FocusPattern(val displayName: String) {
    STEADY_FOCUS("稳定专注型"),
    BURST_FOCUS("爆发专注型"),
    GRADUAL_BUILD("渐进建立型"),
    FLUCTUATING("波动型"),
    DECLINING("递减型"),
    IRREGULAR("不规律型")
}

/**
 * 会话里程碑
 */
data class SessionMilestone(
    val timestamp: Long,
    val type: MilestoneType,
    val description: String,
    val value: Float,
    val significance: MilestoneSignificance
)

enum class MilestoneType(val displayName: String) {
    FOCUS_PEAK("专注高峰"),
    POSTURE_IMPROVEMENT("坐姿改善"),
    FATIGUE_ONSET("疲劳开始"),
    RECOVERY_SUCCESS("成功恢复"),
    GOAL_ACHIEVEMENT("目标达成"),
    BREAKTHROUGH("突破时刻"),
    CHALLENGE_OVERCOME("克服困难")
}

enum class MilestoneSignificance(val displayName: String) {
    MINOR("轻微"),
    MODERATE("中等"),
    SIGNIFICANT("重要"),
    MAJOR("重大")
}

/**
 * 干预措施
 */
data class Intervention(
    val timestamp: Long,
    val type: InterventionType,
    val trigger: InterventionTrigger,
    val action: String,
    val effectiveness: Float? = null, // 0-1，事后评估
    val userResponse: UserResponse? = null
)

enum class InterventionType(val displayName: String) {
    POSTURE_REMINDER("坐姿提醒"),
    BREAK_SUGGESTION("休息建议"),
    ENVIRONMENT_ADJUSTMENT("环境调整"),
    FOCUS_ENHANCEMENT("专注提升"),
    MOTIVATION_BOOST("动机激励"),
    TASK_MODIFICATION("任务调整")
}

enum class InterventionTrigger(val displayName: String) {
    POOR_POSTURE("坐姿不良"),
    LOW_FOCUS("专注度低"),
    HIGH_FATIGUE("疲劳度高"),
    DISTRACTION_DETECTED("检测到分心"),
    TIME_THRESHOLD("时间阈值"),
    PATTERN_ANOMALY("模式异常")
}

enum class UserResponse(val displayName: String) {
    ACCEPTED("接受"),
    IGNORED("忽略"),
    POSTPONED("延后"),
    CUSTOMIZED("自定义"),
    REJECTED("拒绝")
}

/**
 * 会话总结
 */
data class SessionSummary(
    val overallPerformance: PerformanceLevel,
    val strengths: List<String>,
    val areasForImprovement: List<String>,
    val keyInsights: List<String>,
    val recommendations: List<String>,
    val nextSessionPreparation: List<String>,
    val parentGuidance: List<String>,
    val celebrationMoments: List<String>
)

enum class PerformanceLevel(val displayName: String, val scoreRange: IntRange) {
    EXCELLENT("优秀", 90..100),
    GOOD("良好", 75..89),
    SATISFACTORY("满意", 60..74),
    NEEDS_IMPROVEMENT("需要改进", 40..59),
    CONCERNING("需要关注", 0..39)
}

/**
 * 学习监督配置
 */
data class LearningSupervisionConfig(
    val childProfile: ChildProfile,
    val monitoringSettings: MonitoringSettings,
    val interventionSettings: InterventionSettings,
    val reportingSettings: ReportingSettings,
    val privacySettings: PrivacySettings
)

// MonitoringSettings 已在 UserSettings.kt 中定义

enum class AlertSensitivity(val displayName: String) {
    LOW("低敏感度"),
    MEDIUM("中等敏感度"),
    HIGH("高敏感度"),
    CUSTOM("自定义")
}

/**
 * 干预设置
 */
data class InterventionSettings(
    val enableAutomaticInterventions: Boolean = true,
    val interventionDelay: Int = 30, // 秒，检测到问题后的延迟
    val maxInterventionsPerHour: Int = 6,
    val gentleReminderMode: Boolean = true,
    val parentNotificationEnabled: Boolean = true,
    val adaptiveInterventions: Boolean = true // 根据效果调整干预策略
)

/**
 * 报告设置
 */
data class ReportingSettings(
    val generateDailyReports: Boolean = true,
    val generateWeeklyReports: Boolean = true,
    val generateMonthlyReports: Boolean = true,
    val includeDetailedAnalytics: Boolean = true,
    val shareWithEducators: Boolean = false,
    val anonymizeData: Boolean = true
)

/**
 * 隐私设置
 */
data class PrivacySettings(
    val dataRetentionDays: Int = 90,
    val allowDataSharing: Boolean = false,
    val encryptSensitiveData: Boolean = true,
    val requireParentalConsent: Boolean = true,
    val allowResearchParticipation: Boolean = false
)

/**
 * 实时状态
 */
data class RealTimeLearningStatus(
    val currentFocusLevel: FocusLevel,
    val currentFatigueLevel: FatigueLevel,
    val currentPostureQuality: PostureQuality,
    val sessionProgress: Float, // 0-1
    val timeRemaining: Int, // 分钟
    val currentRecommendation: String,
    val nextMilestone: String,
    val parentDashboardData: ParentDashboardData
)

/**
 * 家长仪表板数据
 */
data class ParentDashboardData(
    val childCurrentStatus: String,
    val todayProgress: Float, // 0-1
    val weeklyTrends: Map<String, Trend>,
    val upcomingRecommendations: List<String>,
    val celebrationMoments: List<String>,
    val concernAreas: List<String>
)

/**
 * 坐姿问题类型
 */
enum class PostureIssue(val displayName: String) {
    FORWARD_HEAD("头部前倾"),
    ROUNDED_SHOULDERS("圆肩"),
    SLOUCHING("驼背"),
    TILTED_PELVIS("骨盆倾斜"),
    UNEVEN_SHOULDERS("肩膀不平"),
    POOR_BACK_SUPPORT("背部支撑不足"),
    CROSSED_LEGS("翘腿"),
    FEET_NOT_FLAT("脚部悬空"),
    LEANING_TO_SIDE("身体侧倾"),
    HUNCHED_BACK("弓背")
}
