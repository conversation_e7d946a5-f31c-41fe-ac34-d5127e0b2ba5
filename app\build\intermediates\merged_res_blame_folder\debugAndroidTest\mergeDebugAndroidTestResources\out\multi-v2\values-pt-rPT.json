{"logs": [{"outputFile": "com.example.iotandroidv20.test.app-mergeDebugAndroidTestResources-1:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\752167a3e67da85263f648420d161268\\transformed\\ui-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,380,479,565,644,741,832,919,1004,1094,1170,1246,1325,1400,1476,1548", "endColumns": "94,82,96,98,85,78,96,90,86,84,89,75,75,78,74,75,71,121", "endOffsets": "195,278,375,474,560,639,736,827,914,999,1089,1165,1241,1320,1395,1471,1543,1665"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "837,932,1015,1112,1211,1297,1376,1473,1564,1651,1736,1826,1902,1978,2057,2233,2309,2381", "endColumns": "94,82,96,98,85,78,96,90,86,84,89,75,75,78,74,75,71,121", "endOffsets": "927,1010,1107,1206,1292,1371,1468,1559,1646,1731,1821,1897,1973,2052,2127,2304,2376,2498"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\IotAndroidv10\\caches\\8.11.1\\transforms\\31c73cb6dfb12faac49eb1bae4282a74\\transformed\\core-1.16.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,24", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,403,503,610,716,2132", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "197,299,398,498,605,711,832,2228"}}]}]}